<template>
 
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      :before-open="beforeOpen"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <!-- <template #menu-left>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">收入总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
      </template> -->
      <template #menu="{ row }">
        <el-button type="primary" @click="$refs.crud.rowEdit(row)" icon="edit" text>编辑</el-button>
        <el-button type="primary" @click="$refs.crud.rowDel(row)" icon="delete" text
          >删除</el-button
        >
      </template>
      <template #fileIds="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
      <template #expenseStatus="{ row }">
        <el-tag effect="plain" size="mini" :type="row.expenseStatus == 0 ? 'danger' : 'success'">
          {{ row.$expenseStatus }}
        </el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
 
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
let { proxy } = getCurrentInstance();
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
  logicIds:String
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 150,
  menu:false,
  border: true,
  column: [
    // {
    //   type: 'input',
    //   label: '合同名称',
    //   span: 12,
    //   display: false,
    //   width: 110,
    //   hide: true,
    //   search: true,
    //   prop: 'contractName',
    // },
    // {
    //   type: 'input',
    //   label: '客户名称',
    //   span: 12,
    //   display: false,
    //   hide: true,
    //   search: true,
    //   width: 110,
    //   prop: 'customerName',
    // },
    // {
    //   label: '关联合同',
    //   type: 'input',
    //   component: 'wf-contract-select',
    //   span: 24,
    //   params: {
    //     Url: '/api/vt-admin/sealContract/pageForReimbursement',
    //   },
    //   prop: 'sealContractId',
    //   formatter: row => {
    //     return row.contractName;
    //   },
    //   change: value => {
    //     axios
    //       .get('/api/vt-admin/sealContract/detail', {
    //         params: {
    //           id: value.value,
    //         },
    //       })
    //       .then(res => {
    //         form.value.customerId = res.data.data.customerId;
    //       });
    //   },
    // },
    // {
    //   label: '关联客户',
    //   type: 'input',
    //   component: 'wf-customer-select',
    //   span: 24,
    //   prop: 'customerId',
    //   formatter: row => {
    //     return row.customerName;
    //   },
    //   params: {
    //     Url: '/api/vt-admin/customer/pageByCustomerName',
    //   },
    // },
    {
      type: 'select',
      label: '收入类型',
      dicUrl: '/blade-system/dict/dictionary-tree?code=incomeType',
      cascader: [],
      span: 12,

     
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'incomeType',
      parent: false,
      rules: [
        {
          required: true,
          message: '请选择收入类型',
        },
      ],
    },
    // {
    //   type: 'number',
    //   label: '票据张数',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      type: 'number',
      label: '收入金额',
      span: 12,
      display: true,
      rules: [
        {
          required: true,
          message: '请输入收入金额',
          trigger: 'blur',
        },
      ],
      prop: 'incomePrice',
    },

    // {
    //   type: 'textarea',
    //   label: '用途',
    //   span: 24,
    //   display: true,
    //   prop: 'purpose',
    //   showWordLimit: true,
    // },

    // // {
    // //   type: 'textarea',
    // //   label: '备注',
    // //   span: 24,
    // //   display: true,
    // //   prop: 'remark',
    // //   showWordLimit: true,
    // // },
    {
      type: 'select',
      label: '收款账号',

      cascader: [],
      span: 24,
      // search: true,
      display: true,

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择收款账号',
          trigger: 'blur',
        },
      ],
      prop: 'collectionAccount',
    },
    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      overHidden: true,
      // align: 'center',
      width: 250,
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    // {
    //   label: '状态',
    //   type: 'select',
    //   width: 90,
    //   search: true,
    //   display: false,
    //   dicData: [
    //     {
    //       value: 0,
    //       label: '未报销',
    //     },
    //     {
    //       value: 1,
    //       label: '已报销',
    //     },
    //   ],
    //   prop: 'expenseStatus',
    // },
    {
      type: 'input',
      label: '登记人',
      span: 12,

      display: true,
      readonly: true,
      prop: 'createName',
      value: proxy.$store.getters.userInfo.real_name,
      showWordLimit: true,
    },
    {
      type: 'date',
      label: '收入日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'incomeDate1',
      disabled: false,
      readonly: false,
      hide: true,
      component: 'wf-daterange-search',
     
      display: false,
      searchSpan: 6,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '收入日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'incomeDate',
      disabled: false,

      readonly: false,

      searchSpan: 6,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/secondaryIncome/save';
const delUrl = '/api/vt-admin/secondaryIncome/remove?ids=';
const updateUrl = '/api/vt-admin/secondaryIncome/update';
const tableUrl = '/api/vt-admin/secondaryIncome/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();

let loading = ref(false);
let totalPrice = ref(0);
watch(
  () => props.logicIds,
  () => {
    onLoad();
  },
  {
    immediate: true,
  }
);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,

        startTime: params.value.incomeDate1 && params.value.incomeDate1[0],
        endTime: params.value.incomeDate1 && params.value.incomeDate1[1],
        incomeDate1: null,
        ids:props.logicIds
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/secondaryIncome/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.incomeDate && params.value.incomeDate[0],
        endTime: params.value.incomeDate && params.value.incomeDate[1],
        incomeDate: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,

    fileIds: form.fileIds && form.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    fileIds: row.fileIds && row.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.fileIds =
      form.value.attachList &&
      form.value.attachList.map(item => {
        return {
          label: item.originalName,
          value: item.id,
        };
      });
  }
  done();
}
function beforeClose(done, type) {
  if (type == 'edit') {
    form.value = {};
  }
  done();
}
function done() {
  onLoad();
  done();
  done();
}
</script>

<style lang="scss" scoped></style>
