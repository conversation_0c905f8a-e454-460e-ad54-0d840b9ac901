import axios from 'axios';
import { remindTypeData } from '@/const/const.js';
export default {
  state: {
    isHidden: false,
    messageList: [],
  },
  actions: {
    getMessageList({ commit },type) {
      axios.get('/api/vt-admin/statistics/importanceRemind').then(res => {
        commit('SET_MESSAGELIST', {messageList:res.data.data,type});
        
      });
    },
  },
  mutations: {
    SET_MESSAGELIST(state, {messageList,type}) {
      state.messageList = messageList;
      
      if(type){
        state.isHidden =
        messageList.reduce((pre, cur) => {
          return pre + cur.number.length;
        }, 0) == 0;
      }
    },
    SET_ISHIDDEN(state, isHidden) {
      console.log(state, isHidden);

      state.isHidden = isHidden;
    },
  },
  getters: {
    getDataNumberByUrl :(state) => (url) => {
     
      const value = remindTypeData.filter(item => url == item.link);
      console.log(value);
      if(value.length == 1){
       
        const data = state.messageList.find(item => item.type == value[0].value);
      
        if(data){
          return {
            number:data.number.length,
            message:data.message
          };
        }else{
          return {
            number:0,
            message:''
          }
        }
      }else{
        return {
          number:0,
          message:''
        }
      }
     
    }
  }
};
