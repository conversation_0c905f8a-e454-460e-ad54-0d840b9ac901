# 核心业务指标筛选功能实现

## 修改概述
为销售工作台的核心业务指标部分添加了筛选功能，用户可以选择查看"个人"或"全部"数据。

## 修改内容

### 1. 前端界面修改
- 在核心业务指标标题旁边添加了筛选器
- 使用 `el-radio-group` 组件实现筛选选项
- 筛选选项：0-个人，1-全部

### 2. 响应式数据添加
```javascript
// 核心业务指标筛选类型：0-个人，1-全部
const metricsType = ref(0);
```

### 3. API调用修改
修改了以下API调用，将固定的 `type: 0` 改为动态的 `type: metricsType.value`：

- `getCoreBusiness()` - 核心业务指标数据
- `getOverdue()` - 超期未跟进数据  
- `getReportExpire()` - 报备到期数据
- `getIndustry()` - 客户行业统计数据
- `getRenewal()` - 续费商机数据

### 4. 事件处理函数
```javascript
// 处理核心业务指标筛选类型变化
const handleMetricsTypeChange = (value) => {
  console.log('筛选类型变化:', value);
  // 重新获取所有相关数据
  getCoreBusiness();
  getOverdue();
  getReportExpire();
  getIndustry();
  getRenewal();
};
```

### 5. CSS样式添加
为筛选器添加了完整的样式定义：
- `.section-header` - 标题和筛选器的容器布局
- `.metrics-filter` - 筛选器容器
- `.metrics-filter-group` - 筛选按钮组样式
- 包含悬停效果和激活状态样式

## 功能说明
1. 默认显示"个人"数据（type=0）
2. 用户点击"全部"时，所有相关API会重新请求数据（type=1）
3. 筛选变化时会同时更新所有相关的业务数据，包括：
   - 核心业务指标
   - 超期未跟进商机
   - 报备商机提醒
   - 客户多维度分析
   - 合同续费商机

## 文件修改
- `src/mainPage/views/CRM/salesWorkbench/index.vue`

## 注意事项
- 所有相关的API接口都需要支持 `type` 参数
- 筛选变化时会重新请求所有数据，可能会有短暂的加载时间
- 样式使用了 Element Plus 的设计规范，保持界面一致性
