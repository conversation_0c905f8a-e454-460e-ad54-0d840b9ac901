<template>
  <basic-container>
    <el-header class="p_header" v-if="!$route.query.findex">
      <el-radio-group v-model="status">
        <el-radio-button label="1">已发布</el-radio-button>
        <el-radio-button label="0">草稿夹</el-radio-button>
        <el-radio-button label="2">垃圾桶</el-radio-button>
      </el-radio-group>
    </el-header>
    <el-main class="b_main">
      <avue-crud
        :option="option"
        :data="data"
        ref="crud"
        @row-del="onRemoveIsDelete"
        v-model="form"
        @row-update="rowUpdate"
        :table-loading="loading"
        v-model:page="page"
        v-model:search="query"
        @row-save="rowSave"
        :before-open="beforeOpen"
        @search-change="searchChange"
        @search-reset="searchReset"
        @refresh-change="refreshChange"
        :upload-after="uploadAfter"
        :upload-exceed="uploadExceed"
        @size-change="onLoad"
        :defaults.sync="defaults"
        :upload-preview="uploadPreview"
        @current-change="onLoad"
      >
        <template #range="{ row }">
          <span v-if="row.isAllPath == 1">全员发布</span>
          <span v-else>{{ row.$range }}</span>
        </template>
        <template #menu-left>
          <el-button v-if="status == 0" icon="plus" type="primary" @click.native="toAdd()"
            >新增
          </el-button>
        </template>
        <template #title="{ row }">
          <el-link @click="handleCommon(row, index)" type="primary" :underline="false">
            <span class="hover_under">
              <span
                v-if="row['sort'] === 0 && status == 1"
                style="color: #f56c6c; margin-right: 2px"
                >[置顶]</span
              >{{ row['title'] }}</span
            >
          </el-link>
        </template>

        <template #menu="{ row }">
          <el-button
            icon="el-icon-refresh-right"
            text
            type="primary"
            v-if="status === '1'"
            @click="onRevoke(row)"
            >撤稿</el-button
          >
          <el-button
            icon="el-icon-upload"
            text
            type="primary"
            v-if="status === '1' && row.sort != 0"
            @click="onTopping(row)"
          >
            推送</el-button
          >
          <el-button
            icon="el-icon-circle-close"
            text
            type="primary"
            v-if="status === '1' && row.sort == 0"
            @click="onCancelTopping(row)"
          >
            取消推送</el-button
          >
          <el-button
            icon="el-icon-folder-delete"
            text
            type="primary"
            @click="onRemove(row)"
            v-if="status == 2"
            >彻底删除</el-button
          >
          <el-button
            icon="el-icon-upload"
            text
            type="primary"
            v-if="status === '2'"
            @click="reduction(row)"
          >
            还原</el-button
          >
          <el-button
            text
            type="primary"
            v-if="status === '0'"
            icon="el-icon-edit"
            @click="toUpdate(row)"
            >编辑</el-button
          >
          <el-button
            icon="el-icon-upload"
            text
            type="primary"
            v-if="status === '0'"
            @click="onRelease(row)"
          >
            发布</el-button
          >
          <el-dropdown>
            <el-button text type="primary" icon="more">
              更多<i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item>
                  <el-button
                    text
                    v-if="status == 0"
                    icon="el-icon-delete"
                    @click="$refs.crud.rowDel(row)"
                    >删除</el-button
                  ></el-dropdown-item
                >
                <el-dropdown-item>
                  <el-button icon="el-icon-top" text @click="onMoveUp(row)" v-if="row.sort != 0"
                    >上移
                  </el-button></el-dropdown-item
                >
                <el-dropdown-item>
                  <el-button
                    icon="el-icon-bottom"
                    text
                    v-if="row.sort != 0"
                    @click="onMoveDown(row)"
                  >
                    下移</el-button
                  ></el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <!-- <el-button
                icon="el-icon-refresh-right"
                :size="size"
                :type="type"
                v-if="status === '1'"
                @click="onRevokeTopping(row)"
                >撤销置顶</el-button
              > -->

          <!-- <el-button
                icon="el-icon-folder-delete"
                :size="size"
                :type="type"
                @click="onRemove(row)"
                v-if="status === '2'"
                >彻底删除</el-button
              >

              <el-button
                icon="el-icon-folder-opened"
                :size="size"
                :type="type"
                @click="onViewFile(row)"
                >浏览文件
              </el-button>
              <el-button
                icon="el-icon-download"
                :size="size"
                :type="type"
                @click="onDownloadFile(row)"
                >下载文件
              </el-button> <el-button
                icon="el-icon-download"
                :size="size"
                :type="type"
                @click="onDownloadFile(row)"
                >下载文件
              </el-button>-->
        </template>
        <template slot-scope="{ row }" slot="releaseTime">
          {{ row.releaseTime || '未发布' }}
        </template>
        <template slot-scope="{ row }" slot="releaseName">
          {{ row.releaseName || '未发布' }}
        </template>
      </avue-crud>
      <!-- 新增。编辑表单 -->
      <el-dialog
        :title="form.id ? '编 辑' : '新 增'"
        v-model="updateDialog"
        append-to-body
        destroy-on-close
        class="avue-dialog avue-dialog--top"
        @close="cancel"
      >
        <avue-form :option="updateOption" v-model="form" ref="form"> </avue-form>
        <span slot="footer" class="avue-dialog__footer">
          <el-button type="primary" @click="toSubmit(1)">保存并发布</el-button>
          <el-button type="primary" @click="toSubmit">保存草稿</el-button>
          <el-button @click="cancel">取消</el-button>
        </span></el-dialog
      >
    </el-main>
    <!-- 弹窗详情 -->
    <el-drawer
      :title="detailTitle"
      v-model="detailDialog"
      direction="rtl"
      size="70%"
      append-to-body
      :destroy-on-close="true"
      :show-close="true"
      :wrapperClosable="true"
    >
      <div class="announcement-detail">
        <div class="stick">
          <div class="m-title">{{ detailObj['title'] }}</div>
          <div class="m-publication">
            <div class="m-time">
              {{ detailObj['time'] }}
              <p>
                已阅读
                {{ detailObj['number'] }} 次(<span style="color: var(--el-color-info)"
                  >{{ detailObj.readNames }}已读</span
                >)
              </p>
            </div>
            <div class="m-person">{{ detailObj['releaseName'] }}</div>
          </div>
        </div>
        <div class="m-contentBox">
          <div class="m-content" v-html="detailObj['content']"></div>
        </div>
        <p>
          <File :fileList="detailObj.fileList || []"></File>
        </p>
      </div>
    </el-drawer>
  </basic-container>
</template>

<script>
// import { getDictionary } from '@/api/system/dictbiz';
export default {
  data() {
    return {
      updateDialog: false,
      detailTitle: '', //弹窗标题
      detailDialog: false, //弹窗显示
      detailObj: {}, //弹窗详情
      form: {},
      query: {},
      rangeList: [], //范围列表
      regionIdList: [], //范围id列表
      loading: false,
      noticeTypelist: [], //文章类型列表
      status: '1',
      data: [],
      defaults: {},
      isEditImg: false,
      saveBtn: false,
      cancelBtn: false,
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      treeData: [
        {
          label: '公告箱',
          id: '1',
          children: [
            {
              label: '已发布',
              id: '1',
            },
            {
              label: '草稿箱',
              id: '0',
            },
            {
              label: '垃圾箱',
              id: '2',
            },
          ],
        },
      ],
    };
  },

  computed: {
    ...mapGetters(['userInfo']),
    option() {
      var validDate = (rule, value, callback) => {
        if (this.isAllPath && this.isAllPath[0] == 0 && this.form.range == '') {
          callback(new Error('请选择发布范围'));
        } else {
          callback();
        }
      };
      return {
        height: 'auto',
        menuWidth: 220,
        calcHeight: 20,
        dialogWidth: 950,
        loading: false,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        indexFixed: false,
        // 查看
        viewBtn: false,
        columnBtn: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menu: !this.$route.query.findex,
        // addBtn: Number(this.status) === 0,
        // editBtn: Number(this.status) === 0,
        // delBtn: Number(this.status) === 0,
        selection: false,
        excelBtn: false,
        dialogClickModal: false,
        saveBtn: false,
        cancelBtn: false,
        column: [
          {
            label: '关键字',
            prop: 'key',
            display: false,
            hide: true,
            search: true,
          },

          {
            label: '文章标题',
            prop: 'title',
            row: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入文章标题',
                trigger: 'blur',
              },
            ],
            overHidden: true,
          },
          {
            label: '封面图片',
            type: 'upload',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/api/blade-resource/attach/upload',
            display: true,
            span: 8,
            listType: 'picture-img',
            tip: '仅限上传图片，不超过10M',
            accept: 'image/png, image/jpeg, image/jpg',
            fontSize: 10,
            prop: 'coverUrl',
            formatter: res => {
              return res.coverList && res.coverList[0]?.link;
            },
            uploadAfter: (val, done) => {
              this.form.coverIds = val.id;
              done();
            },
            rules:[{
              required: true,
              message: '请上传封面图片',
              trigger: 'blur',
            }]
          },
          {
            label: '发布范围',
            prop: 'range',
            type: 'tree',
            overHidden: true,
            span: 20,
            multiple: true,
            dicUrl: `/api/blade-system/dept/tree?tenantId=${this.userInfo.tenant_id}`,
            props: {
              label: 'title',
            },
            // dataType: "string",
            slot: true,
            rules: [
              {
                validator: validDate.bind(this),
                trigger: 'change',
                required: true,
              },
            ],
          },

          {
            type: 'checkbox',
            label: '全员发布',
            display: true,
            span: 4,
            hide: true,
            dicData: [
              {
                label: '是',
                value: 1,
              },
            ],
            rules: [
              {
                validator: validDate.bind(this),
                trigger: 'change',
                required: true,
              },
            ],
            control: e => {
              console.log(e);
              console.log(this.form.isAllPath);
              if (e[0] == 1) {
                return {
                  range: {
                    disabled: true,
                  },
                };
              } else {
                return {
                  range: {
                    disabled: false,
                  },
                };
              }
            },
            prop: 'isAllPath',
          },
          {
            label: '作者',
            prop: 'author',
            type: 'input',
            span: 12,
            rules: [
              {
                required: true,
                message: '请输入作者',
                trigger: 'blur',
              },
            ],
            value: this.userInfo.real_name,
          },
          {
            label: '文章类型',
            prop: 'articleType',
            span: 12,
            width: 118,
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=notice_type',
            props: {
              label: 'dictValue',
              value: 'id',
            },

            rules: [
              {
                required: true,
                message: '请输入文章类型',
                trigger: 'change',
              },
            ],
          },
          {
            label: '发布时间',
            prop: 'releaseTime',
            type: 'date',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'yyyy-MM-DD HH:mm',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入通知日期',
                trigger: 'click',
              },
            ],
            width: 150,
            align: 'center',
            hide: Number(this.status) !== 1,
          },

          {
            label: '发布者',
            prop: 'releaseName',
            width: 100,
            align: 'center',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入发布者',
                trigger: 'blur',
              },
            ],
            hide: Number(this.status) !== 1,
          },

          {
            label: '撤稿时间',
            prop: 'revokeTime',
            display: false,
            type: 'date',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'yyyy-MM-DD HH:mm',
            width: 150,
            align: 'center',
            hide: Number(this.status) !== 0,
          },
          {
            label: '撤稿人',
            prop: 'revokeName',
            width: 100,
            align: 'center',
            display: false,
            hide: Number(this.status) !== 0,
          },
          {
            label: '删除时间',
            prop: 'deleteTime',
            display: false,
            type: 'date',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'yyyy-MM-DD HH:mm',
            width: 150,
            align: 'center',
            hide: Number(this.status) !== 2,
          },
          {
            label: '删除人',
            prop: 'deleteName',
            width: 100,
            align: 'center',
            display: false,
            hide: Number(this.status) !== 2,
          },
          {
            label: '创建人',
            prop: 'creator',
            width: 100,
            align: 'center',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入公告标题',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '创建时间',
            prop: 'createdTime',
            type: 'date',
            width: 150,
            align: 'center',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'yyyy-MM-DD HH:mm',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入创建时间',
                trigger: 'blur',
              },
            ],
          },

          {
            label: '阅读数',
            prop: 'number',
            width: 100,
            align: 'center',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入阅读数',
                trigger: 'blur',
              },
            ],
          },

          // {
          //   label: "上传附件",
          //   prop: "path",
          //   type: "upload",
          //   dataType: "array",
          //   listType: "text",
          //   span: 24,
          //   dragFile: true,
          //   limit: 1,
          //   propsHttp: {
          //     res: "data",
          //     url: "link",
          //     name: "originalName",
          //   },
          //   rules: [
          //     {
          //       required: false,
          //       message: "请上传附件",
          //       trigger: "blur",
          //     },
          //   ],
          //   tip: "支持扩展名：.rar .zip .doc .docx .pdf .jpg等文件 文件不超过100M",
          //   action: "/api/blade-resource/oss/endpoint/put-file",
          //   hide: true,
          //   viewDisplay: false,
          // },

          {
            label: '上传附件',
            type: 'upload',
            dragFile: true,
            listType: 'text',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/api/blade-resource/attach/upload',

            display: true,
            hide: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 1,
            prop: 'path',
            uploadDelete: (a, b) => {
              console.log(a, b);
              this.form.path = [];
            },
          },
          {
            label: '稿件内容',
            prop: 'content',
            component: 'AvueUeditor',
            action: '/api/blade-resource/attach/upload',
            options: {
              action: '/api/blade-resource/attach/upload',
              accept: 'image/png, image/jpeg, image/jpg,.mp4',
            },
            propsHttp: {
              res: 'data',
              url: 'link',
            },

            rules: [
              {
                required: true,
                message: '请输入稿件内容',
                trigger: 'blur',
              },
            ],
            hide: true,
            minRows: 6,
            span: 24,
          },
        ],
      };
    },
    updateOption() {
      var validDate = (rule, value, callback) => {
        if (this.isAllPath && this.isAllPath[0] == 0 && this.form.range == '') {
          callback(new Error('请选择发布范围'));
        } else {
          callback();
        }
      };
      return {
        height: 'auto',
        menuWidth: 200,
        calcHeight: 20,
        dialogWidth: 950,
        loading: false,
        tip: false,

        addBtn: Number(this.status) === 0,
        editBtn: Number(this.status) === 0,
        delBtn: Number(this.status) === 0,
        selection: false,
        excelBtn: false,
        dialogClickModal: false,
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '文章标题',
            prop: 'title',
            row: true,
            span: 24,
            rules: [
              {
                required: true,
                message: '请输入文章标题',
                trigger: 'blur',
              },
            ],
            overHidden: true,
          },
          {
            label: '发布范围',
            prop: 'range',
            type: 'tree',
            span: 20,
            multiple: true,
            dicUrl: `/api/blade-system/dept/tree?tenantId=${this.userInfo.tenant_id}`,
            props: {
              label: 'title',
            },
            // dataType: "string",
            slot: true,
            rules: [
              {
                validator: validDate.bind(this),
                trigger: 'change',
                required: true,
              },
            ],
          },

          {
            type: 'switch',
            label: '全员发布',
            display: true,
            span: 4,
            hide: true,
            dicData: [
              {
                label: '否',
                value: 0,
              },
              {
                label: '是',
                value: 1,
              },
            ],
            rules: [
              {
                validator: validDate.bind(this),
                trigger: 'change',
                required: true,
              },
            ],
            control: e => {
              if (e == 1) {
                return {
                  range: {
                    disabled: true,
                  },
                };
              } else {
                return {
                  range: {
                    disabled: false,
                  },
                };
              }
            },
            prop: 'isAllPath',
          },
          {
            label: '作者',
            prop: 'author',
            type: 'input',
            span: 12,
            rules: [
              {
                required: true,
                message: '请输入作者',
                trigger: 'blur',
              },
            ],
            value: this.userInfo.real_name,
          },
          {
            label: '文章类型',
            prop: 'articleType',
            span: 12,
            width: 118,
            type: 'select',
            dicUrl: '/api/blade-system/dict/dictionary?code=notice_type',
            props: {
              label: 'dictValue',
              value: 'id',
            },

            rules: [
              {
                required: true,
                message: '请输入文章类型',
                trigger: 'change',
              },
            ],
          },

          // {
          //   label: "上传附件",
          //   prop: "path",
          //   type: "upload",
          //   dataType: "array",
          //   listType: "text",
          //   span: 24,
          //   dragFile: true,
          //   limit: 1,
          //   propsHttp: {
          //     res: "data",
          //     url: "link",
          //     name: "originalName",
          //   },
          //   rules: [
          //     {
          //       required: false,
          //       message: "请上传附件",
          //       trigger: "blur",
          //     },
          //   ],
          //   tip: "支持扩展名：.rar .zip .doc .docx .pdf .jpg等文件 文件不超过100M",
          //   action: "/api/blade-resource/oss/endpoint/put-file",
          //   hide: true,
          //   viewDisplay: false,
          // },
          {
            label: '封面图片',
            type: 'upload',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/api/blade-resource/attach/upload',
            display: true,
            span: 8,
            listType: 'picture-img',

            accept: 'image/png, image/jpeg, image/jpg',
            // fontSize: 10,
            prop: 'coverUrl',
            formatter: res => {
              return res.coverList && res.coverList[0].link;
            },
            uploadAfter: (val, done) => {
              this.form.coverIds = val.id;
              console.log(this.form);
              done();
            },
          },
          {
            label: '上传附件',
            type: 'upload',
            dragFile: true,
            dataType: 'object',
            propsHttp: {
              res: 'data',
              url: 'id',
              name: 'originalName',
            },
            action: '/api/blade-resource/attach/upload',
            tip: '支持扩展名：.rar .zip .doc .docx .pdf .jpg等文件 文件不超过100M',
            display: true,
            hide: true,
            span: 24,
            showFileList: true,
            multiple: true,
            limit: 1,
            prop: 'fileList',
            uploadAfter: (val, done) => {
              this.form.fileIds = val.id;
              done();
            }
          },
          {
            label: '稿件内容',
            prop: 'content',
            component: 'AvueUeditor',
            action: '/api/blade-resource/attach/upload',
            options: {
              action: '/api/blade-resource/attach/upload',
              accept: 'image/png, image/jpeg, image/jpg,.mp4',
            },
            propsHttp: {
              res: 'data',
              url: 'link',
            },

            rules: [
              {
                required: true,
                message: '请输入稿件内容',
                trigger: 'blur',
              },
            ],
            hide: true,
            minRows: 6,
            span: 24,
          },
        ],
      };
    },

    userInfo() {
      return this.$store.getters.userInfo;
    },
  },
  watch: {
    // form: {
    //   handler(val) {
    //     if (val.isAllPath !== undefined) {
    //       if (val.isAllPath == 0) {
    //         this.defaults.range.disabled = false;
    //       } else {
    //         this.defaults.range.disabled = true;
    //       }
    //     }
    //   },
    //   deep: true,
    // },
    status() {
      if (this.$refs.crud) {
        this.$refs.crud.refreshTable();
      }
      this.page['currentPage'] = 1;
      this.page['pageSize'] = 10;
      this.onLoad();
    },
  },
  created() {
    this.onLoad();
    // this.getNoticeType();
    // 获取范围树状图
    axios.get(`/api/blade-system/dept/tree?tenantId=${this.userInfo.tenant_id}`).then(e => {
      if (e.data.code == 200) {
        this.rangeList = e.data.data;
        this.rangeList.forEach(item => {
          this.getRegionds(item);
        });
      }
    });
  },
  mounted() {
    console.log(this.$refs.form);
  },
  methods: {
    // 获取范围id
    getRegionds(data) {
      if (data.children instanceof Array) {
        this.regionIdList.push(data.id);
        data.children.forEach(item => {
          this.regionIdList.push(item.id);
          this.getRegionds(item);
        });
      }
    },
    // // 获取文章类型
    // getNoticeType() {
    //   getDictionary({ code: 'article_type' }).then(res => {
    //     this.noticeTypelist = res.data.data;
    //   });
    // },
    onLoad() {
      this.showProgress();
      this.loading = true;
      axios
        .get('/api/vt-admin/publishNotice/getList', {
          params: {
            current: this.page['currentPage'],
            size: this.page['pageSize'],
            status: this.status,
            key: this.query.key,
          },
        })
        .then(resp => {
          console.log(resp);
          let d = resp.data.data.records;
          this.page['total'] = resp.data.data.total;
          this.data = d;
          this.loading = false;
        });
    },
    // 新增
    toAdd() {
      this.updateDialog = true;
    },
    toUpdate(row) {
      this.updateDialog = true;
      this.form = row;
      if (this.form.articleType == 0) {
        this.form.articleType = '';
      }
      if (this.form.coverIds) {
        this.form.coverUrl = this.form.coverList[0].link;
      }
      if (this.form.fileIds) {
        this.form.fileList = this.form.fileList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        });
      } else {
        this.form.fileList = [];
      }
    
      console.log(this.form);
    },
    // 公告弹窗
    handleCommon(row) {
      // if (row.articleType != 0 && row.articleType != -1) {
      //   this.detailTitle = this.noticeTypelist.find(item => row.articleType == item.id).dictValue;
      // } else {
      //   this.detailTitle = '';
      // }

      // getCompanyDetailAndRead(row.id).then((e) => {
      axios
        .get('/api/vt-admin/publishNotice/getById', {
          params: {
            id: row.id,
          },
        })
        .then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.detailObj = {
              title: data.data['title'],
              time: data.data['publishTime'],
              content: data.data['content'],
              number: row['number'],
              path: row['path'],
              fileList: data.data['fileList'],
              readNames: data.data['readNames'],
            };
            this.detailTitle = data.data.articleTypeName;
            this.detailDialog = true;
          }
        });
    },
    cancel() {
      this.$refs.form.resetForm();
      this.form.coverIds = '';
      this.form.author = this.userInfo.real_name;
      this.updateDialog = false;
    },
    onMoveDown(e) {
      axios
        .post('/api/vt-admin/publishNotice/moveDown', {
          id: e.id,
          status: e.status,
        })
        .then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
          this.showProgress(false);
        });
    },
    showProgress() {},
    procError() {},
    onMoveUp(e) {
      axios
        .post('/api/vt-admin/publishNotice/moveUp', {
          id: e.id,
          status: e.status,
        })
        .then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
        });
    },
    // 下载文件
    toDownLoad() {
      let file = JSON.parse(this.detailObj.path)[0];
      this.downloadFiles(file.value, file.label);
    },
    // 保存并发布/保存草稿
    toSubmit(type) {
      let p = {};
      if (this.form.isAllPath == 1) {
        this.form.range = this.regionIdList;
       
      } 
      console.log(this.form);
      p.isAllPath = this.form.isAllPath;
      p.articleType = this.form.articleType;
      p.author = this.form.author;
      if (type == 1) {
        p.status = 1;
      }
      // if (row.coverImgList.length > 0) {
      //   p.coverName = row.coverImgList[0].label;
      //   p.coverUrl = row.coverImgList[0].value;
      // }
      p.coverUrl = this.form.coverUrl;
      p.coverIds = this.form.coverIds;
      p.fileIds = this.form.fileIds;
      p.content = this.form.content;
      console.log(this.form);
      p.fileIds = this.form.fileList.map(item => item.value).join(',');
      if (this.form.range && this.form.range instanceof Array) {
        p.range = this.form.range.join(',');
      } else {
        p.range = this.form.range;
      }
      p.title = this.form.title;

      if (this.form.id) {
        p.id = this.form.id;
        axios.post('/api/vt-admin/publishNotice/update', p).then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
          this.updateDialog = false;
          this.$refs.form.resetForm();
          this.form.author = this.userInfo.real_name;
        });
      } else {
        axios.post('/api/vt-admin/publishNotice/add', p).then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
          this.$refs.form.resetForm();
          this.updateDialog = false;
          this.form.author = this.userInfo.real_name;
        });
      }
    },
    // 预览附件
    toView() {
      this.$file.previewFile(JSON.parse(this.detailObj.path)[0].value);
    },
    rowSave(row, done, loading, type) {
      let p = {};
      if (row.isAllPath && row.isAllPath[0] == 1) {
        row.range = this.regionIdList;
        p.isAllPath = 1;
      } else {
        p.isAllPath = 0;
      }
      p.articleType = row.articleType;
      p.author = row.author;
      if (type == 1) {
        p.status = 1;
      }
      // if (row.coverImgList.length > 0) {
      //   p.coverName = row.coverImgList[0].label;
      //   p.coverUrl = row.coverImgList[0].value;
      // }
      p.coverUrl = row.coverUrl;
      p.content = row.content;
      if (row.range && row.range instanceof Array) {
        p.range = row.range.join(',');
      } else {
        p.range = row.range;
      }
      p.title = row.title;

      axios
        .post('/api/vt-admin/publishNotice/add', p)
        .then(resp => {
          return this.checkResp(resp, r => r.data.code === 200 && r.data.success === true);
        })
        .then(data => {
          this.onLoad();
          this.$message.success(data.msg);

          loading();
          done();
        })
        .catch(error => {
          loading();
          this.showProgress(false);
          this.procError(error);
        });
    },

    rowUpdate(row, index, done, loading) {
      let p = {};
      p.id = row.id;
      p.content = row.content;
      // if (row.coverImgList.length > 0) {
      //   p.coverName = row.coverImgList[0].label;
      //   p.coverUrl = row.coverImgList[0].value;
      // }
      p.articleType = row.articleType;
      p.author = row.author;
      p.coverUrl = row.coverUrl;
      console.log(row.coverUrl);
      if (row.isAllPath && row.isAllPath[0] == 1) {
        row.range = this.regionIdList;
        p.isAllPath = 1;
      } else {
        p.isAllPath = 0;
      }
      if (row.range && row.range instanceof Array) {
        p.range = row.range.join(',');
      } else {
        p.range = row.range;
      }
      p.title = row.title;

      axios
        .post('/api/vt-admin/publishNotice/update', p)
        .then(resp => {
          return this.checkResp(resp, r => r.data.code === 200 && r.data.success === true);
        })
        .then(data => {
          this.onLoad();
          this.$message.success(data.msg);
          loading();
          done();
        })
        .catch(error => {
          loading();
          this.showProgress(false);
          this.procError(error);
        });
    },

    onRemove(e) {
      this.$confirm('确定将选择数据永久删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios
          .post('/api/vt-admin/publishNotice/remove', {
            id: e.id,
          })
          .then(resp => {
            this.onLoad();
            this.$message.success(resp.data.msg);
          });
      });
    },

    onRemoveIsDelete(e) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios
          .post('/api/vt-admin/publishNotice/removeIsDelete', {
            id: e.id,
            status: '2',
          })
          .then(resp => {
            this.onLoad();
            this.$message.success(resp.data.msg);
          });
      });
    },

    onRelease(e) {
      this.$confirm('是否发布?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios
          .post('/api/vt-admin/publishNotice/release', {
            id: e.id,
            status: '1',
          })
          .then(resp => {
            this.onLoad();
            this.$message.success(resp.data.msg);
          });
      });
    },

    onRevoke(e) {
      this.$confirm('是否撤销该文稿?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios
          .post('/api/vt-admin/publishNotice/revoke', {
            id: e.id,
            status: '0',
          })
          .then(resp => {
            this.onLoad();
            this.$message.success(resp.data.msg);
          });
      });
    },

    onTopping(e) {
      let topList = this.data.filter(item => {
        return item.sort == 0;
      });
      if (topList.length > 5) {
        this.$message.error('仅限推送6条公告');
        return;
      }
      if (!e.coverIds) {
        this.$message.error('请先上传封面');
        return;
      }
      axios
        .post('/api/vt-admin/publishNotice/topping', {
          id: e.id,
          sort: 1,
        })
        .then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
        });
    },

    onCancelTopping(e) {
      axios
        .post('/api/vt-admin/publishNotice/revokeTopping', {
          id: e.id,
        })
        .then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
        });
    },
    onRevokeTopping(e) {
      axios
        .post('/api/vt-admin/publishNotice/topping', {
          id: e.id,
          sort: 0,
        })
        .then(resp => {
          this.onLoad();
          this.$message.success(resp.data.msg);
        });
    },

    onViewFile(e) {
      if (['JPG', 'JPEG', 'PNG'].includes(e.type)) {
        this.$ImagePreview(
          [
            {
              thumbUrl: e.path,
              url: e.path,
            },
          ],
          0,
          {
            closeOnClickModal: true,
          }
        );
      } else {
        window.open(e.path);
      }
    },

    onDownloadFile(e) {
      this.downloadFiles(JSON.parse(e.path)[0], e.title);
    },

    searchReset() {
      this.page.currentPage = 1;
      this.onLoad();
    },
    // 自定义预览
    uploadPreview(file, column, done) {
      console.log(file);
      this.$file.previewFile(file.url);
    },
    searchChange(params, done) {
      this.page.currentPage = 1;
      this.onLoad();
      done();
    },

    beforeOpen(done, type) {
      console.log('编辑', type);
      if (['edit', 'view'].includes(type)) {
        if (this.form.path) {
          this.form.path = JSON.parse(this.form.path);
        }
        // 全员发布则清空范围

        if (this.form.isAllPath == 1) {
          this.form.range = '';
        }
        this.form.isAllPath = [this.form.isAllPath];

        console.log(this.form.isAllPath);
      }
      done();
    },

    uploadExceed() {
      this.$message.warning('请先移除原有文件！');
    },
    uploadRemove() {
      this.form.path = [];
    },
    refreshChange() {
      this.onLoad();
    },

    uploadAfter(res, done) {
      // this.form.path = res.path
      done();
    },

    handleSelect(key) {
      this.status = key;
      this.page = {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      };
      this.onLoad();
    },
    // 还原
    reduction(e) {
      this.$confirm('是否还原该数据?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios
          .post('/api/vt-admin/publishNotice/reduction', {
            id: e.id,
          })
          .then(resp => {
            this.onLoad();
            this.$message.success(resp.data.msg);
          });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.bulletin {
  height: calc(100% - 11px);
  margin-top: 14px;

  .b_container {
    height: 100%;
    overflow: hidden auto;
    // border: none;

    .b_aside {
      height: 100%;
      border-radius: 10px;

      .basic-container {
        height: 100%;

        // /deep/.el-card {
        //   height: 100%;
        //   .el-card__body {
        //     padding: 16px;
        //   }
        // }
      }
    }
    .p_header {
      background-color: #ffffff;
      border-top-left-radius: 10px;
      border-top-right-radius: 10px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 8px !important;

      // /deep/ .el-tabs__header {
      //   padding: 16px 0;
      //   margin: 0 !important;
      // }

      .p_header_right {
        display: flex;
        justify-content: flex-end;
        align-items: center;

        .input-search {
          margin-right: 12px;
        }
      }
    }
    .b_main {
      background-color: #ffffff;
      border-bottom-left-radius: 10px;
      border-bottom-right-radius: 10px;
    }
  }
}
// 弹窗
::v-deep .announcement-detail {
  width: 100%;
  padding: 0 30px;
  box-sizing: border-box;

  // /deep/ table {
  //   td {
  //     border: 1px solid #ebeef5;
  //   }
  // }

  .m-title {
    font-size: 21px;
    color: #2c2c2c;
  }
  .m-time {
    font-size: 16px;
    color: #ababab;
    margin-top: 10px;
  }
  .m-time > p {
    font-size: 14px;
    display: inline;
    margin-left: 20px;
  }
  .m-content {
    width: 100%;
    margin-top: 15px;
    img {
      display: block;
      max-width: 100%;
    }
  }
}
.el-dropdown-link {
  font-size: 12px;
  color: #2046a0;
  margin-left: 10px;
}
::v-deep.el-menu-item.is-active {
  background-color: #e8f1f5;
}
::v-deep .el-dialog__body {
  border-top: 1px solid #eee;
  border-bottom: 1px solid #eee;
  height: 500px;
  overflow: auto;
}
.file_btn {
  margin-right: 20px;
  cursor: pointer;
  color: #2046a0;
  margin-right: 15px;
}
</style>

<style lang="scss">
.el-upload-list__item {
  cursor: pointer;
}
.hover_under:hover {
  text-decoration: underline;
}
[class^='avue-upload__icon'],
[class*='avue-upload__icon'] {
  line-height: 178px !important;
}
</style>
