# CRM销售工作台

## 概述
这是一个Vue3版本的CRM销售工作台页面，从原始HTML文件转换而来。该组件提供了全面的销售数据概览和业务管理功能。

## 功能特性

### 1. 核心业务指标
- 新增客户数：本周/本月/本年平行展示，各自显示数值和增长率
- 活跃商机：本周/本月/本年平行展示，各自显示数值和增长率，附加超期提醒
- 合同金额：本周/本月/本年平行展示，各自显示数值和增长率
- 已收款金额：本周/本月/本年平行展示，各自显示数值和增长率，附加逾期提醒

### 2. 超期商机管理
- 超期未跟进商机列表
- 商机阶段分布统计
- 一键跟进功能

### 3. 客户多维度分析
- 客户行业分布饼图
- 客户销售额TOP5排行
- 客户收款额TOP5排行

### 4. 合同续费管理
- 合同续费商机提醒
- 续费价值分析
- 制定续费方案功能

### 5. 合同与收款
- 合同金额与收款对比图表
- 收款预警提醒
- 合同到期提醒

### 6. 销售趋势分析
- 销售趋势折线图
- 多维度数据对比

## 技术栈
- Vue 3 Composition API
- Element Plus UI组件库
- ECharts图表库
- 响应式设计

## 组件结构
```
salesWorkbench/
├── index.vue          # 主组件文件
└── README.md          # 说明文档
```

## 使用方法

### 路由配置
组件已经在路由中配置，可以通过以下路径访问：
```
/salesWorkbench
```

### 数据接口
组件使用模拟数据，实际使用时需要替换为真实的API接口：

1. 核心指标数据
2. 商机数据
3. 客户数据
4. 合同数据
5. 收款数据

### 自定义配置
可以通过修改组件内的响应式数据来自定义显示内容：

```javascript
// 修改核心指标
const metrics = reactive({
  totalCustomers: 2841,
  customerGrowth: 12.5,
  // ...其他指标
})

// 修改商机数据
const overdueOpportunitiesList = ref([
  // 商机列表数据
])
```

## 响应式设计
组件支持多种屏幕尺寸：
- 桌面端：完整布局
- 平板端：自适应网格
- 移动端：单列布局

## 浏览器兼容性
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 开发说明

### 本地开发
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build:prod
```

## 布局说明

### 密集布局设计
页面采用密集的网格布局，四个主要业务卡片每行显示三个：

1. **超期未跟进商机** - 显示超期商机列表和阶段分布
2. **合同续费商机提醒** - 显示续费提醒和价值分析
3. **客户多维度分析** - 显示行业分布图表和TOP3客户排行
4. **合同与收款** - 显示收款预警和合同到期提醒

### 响应式布局
- 桌面端（>1200px）：每行3个卡片
- 平板端（768px-1200px）：每行2个卡片
- 移动端（<768px）：每行1个卡片

### 卡片特性
- 自适应高度，无滚动条设计
- 最小高度380px，最大高度450px
- 紧凑的内边距和间距设计
- 优化的图表尺寸（150px高度）
- 简化的数据展示（TOP3客户，前2个商机/续费项目）
- 精简的预警信息（每类只显示1个最重要的）

## 更新日志
- v1.6.0: 精简核心业务指标，移除超期/逾期信息，减少卡片高度和间距，提升空间利用率
- v1.5.0: 优化核心业务指标视觉效果，增大字体和元素尺寸，减少空白面积，提升可读性
- v1.4.0: 重构核心业务指标展示，本周/本月/本年平行显示，每个维度显示增长率，移除单独的当前值显示
- v1.3.0: 修改核心业务指标，客户总数改为新增客户数，每个指标增加本周/本月/本年维度数据
- v1.2.0: 优化卡片内布局，移除滚动条，限制数据显示数量，提升视觉体验
- v1.1.0: 调整为密集布局，每行三个卡片，优化响应式设计
- v1.0.0: 初始版本，从HTML转换为Vue3组件
