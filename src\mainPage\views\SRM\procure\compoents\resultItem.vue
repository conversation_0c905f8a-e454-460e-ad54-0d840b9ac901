<template>
  <div>
    <el-row :gutter="10">
      <el-col :span="6">
        <el-descriptions border>
          <template #title
            >{{ info.supplierName }}
            <el-tag
              effect="plain"
              :type="
                info.inquiryStatus == 0
                  ? 'info'
                  : info.inquiryStatus == 1 ?'warning': info.inquiryStatus == 2
                  ? 'success'
                  : 'danger'
              "
              >{{
                info.inquiryStatus == 0
                  ? '待提交'
                  : info.inquiryStatus == 1
                  ? '待报价'
                  : info.inquiryStatus == 2
                  ? '已报价'
                  : '已过期'
              }}</el-tag
            >
           </template
          >
          <el-descriptions-item :rowspan="2" :width="100" label="询价二维码" align="center">
            <div style="display: flex; flex-direction: column; align-items: center; gap: 8px;">
              <el-image
                style="width: 100px; height: 100px;border: 1px solid var(--el-color-primary);"
                :src="'data:image/jpg;base64,' + info.qrCodeLink"
              />
              <div v-if="info.id" style="text-align: center; width: 100%;">
                 <div style="font-size: 12px; color: #666; margin-bottom: 4px; word-break: break-all; line-height: 1.2;">
                   <!-- {{ getInquiryLink(info.id) }} -->
                 </div>
                 <el-button 
                   type="primary" 
                   size="small" 
                   @click="copyToClipboard(getInquiryLink(info.id))"
                   style="font-size: 11px; padding: 2px 8px;"
                 >
                   复制链接
                 </el-button>
               </div>
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </el-col>
      <el-col :span="18">
        <avue-crud :option="option" :data="info.detailVOS"></avue-crud>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import axios from 'axios';
import { watch } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps(['info']);

// 生成询价链接的方法
const getInquiryLink = (id) => {
  const currentDomain = window.location.origin;
  return `${currentDomain}/inquiry.html?type=0&id=${id}`;
};

// 复制到剪贴板的方法
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text);
    ElMessage.success('链接已复制到剪贴板');
  } catch (err) {
    // 降级方案：使用传统的复制方法
    const textArea = document.createElement('textarea');
    textArea.value = text;
    document.body.appendChild(textArea);
    textArea.select();
    try {
      document.execCommand('copy');
      ElMessage.success('链接已复制到剪贴板');
    } catch (fallbackErr) {
      ElMessage.error('复制失败，请手动复制');
    }
    document.body.removeChild(textArea);
  }
};
let option = ref({
  align: 'center',
  addBtn: false,
  header: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,

      rules: [
        {
          required: true,
          message: '请输入产品名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '品牌',
      prop: 'productBrand',
      width: 150,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
    },
    {
      label: '数量',
      prop: 'number',
      width: 80,
    },
    {
      label: '单价',
      prop: 'inquiryPrice',
      type: 'number',
      controls: false,
      cell: false,
      span: 12,
      width: 100,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      width: 110,
      controls: false,
      cell: false,
      span: 12,
      formatter: row => {
        return (row.inquiryPrice * row.number).toFixed(2);
      },
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'switch',
      value: 1,
      width: 100,
      cell: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      span: 12,
      formatter: row => {
        row.isHasTax = !row.isHasTax ? 0 : 1;
        return row.isStop == 1 ? '' : ['否', '是'][row.isHasTax];
      },
    },
    {
      label: '税率',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      width: 80,
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter: row => {
        return row.isStop == 1 ? '' : row.taxRate && parseFloat(row.taxRate) + '%';
      },
    },
    {
      label: '质保期（年）',
      prop: 'guaranteePeriod',
      type: 'number',
      width: 120,
      controls: false,
      cell: false,

      span: 12,
      formatter: row => {
        return row.isStop == 1 ? '' : row.guaranteePeriod;
      },
    },
    {
      label: '供货周期（天）',
      prop: 'deliveryCycle',
      type: 'number',
      width: 150,
      controls: false,
      cell: false,
      span: 12,
      formatter: row => {
        return row.deliveryCycle === 0 && !row.isStop
          ? '现货'
          : row.deliveryCycleDays
          ? row.deliveryCycleDays + '天'
          : '';
      },
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      span: 12,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      cell: false,
      formatter: row => {
        return row.isStop == 1 ? '' : row.offerDate;
      },
    },
  ],
});

</script>

<style lang="scss" scoped></style>
