/**
 * 全站http配置
 *
 * axios参数说明
 * isSerialize是否开启form表单提交
 * isToken是否需要token
 */
import axios from 'axios';

import { getToken, removeToken, removeRefreshToken } from 'utils/auth';

import { ElMessage } from 'element-plus';
import website from '@/config/website';
import NProgress from 'nprogress'; // progress bar
import 'nprogress/nprogress.css'; // progress bar style
import { Base64 } from 'js-base64';
import { baseUrl } from '@/config/env';
import crypto from '@/utils/crypto';
// 全局未授权错误提示状态，只提示一次
let isErrorShown = false;
// 全局锁机制相关变量
let isRefreshing = false; // 标记当前是否正在刷新token
let refreshTokenPromise = null; // 刷新token的Promise，避免重复请求
axios.defaults.timeout = 6000 * 1000;
//返回其他状态吗
axios.defaults.validateStatus = function (status) {
  return status >= 200 && status <= 500; // 默认的
};
//跨域请求，允许保存cookie
axios.defaults.withCredentials = true;
// NProgress Configuration
NProgress.configure({
  showSpinner: false,
});
//HTTPrequest拦截
axios.interceptors.request.use(
  config => {
    // start progress bar
    NProgress.start();

   

    config.headers[website.tokenHeader] = 'bearer ' + getToken();
    return config;
  },
  error => {
    return Promise.reject(error);
  }
);
axios.interceptors.response.use(
  response => {
    
   if(response.data.code != 200){
    ElMessage.warning(response.data.msg)
   }
    NProgress.done();
    return response;
  },
  error => {
    // end progress bar
    NProgress.done();
    return Promise.reject(error);
  }
);

export default axios;
