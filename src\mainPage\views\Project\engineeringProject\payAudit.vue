<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button text @click="viewDetail(row)" icon="view" type="primary">详情</el-button>
        <el-button
          text
          @click="viewDetail(row)"
          icon="edit"
          type="primary"
         v-if="row.payStatus == 0"
          >审核</el-button
        >
      </template>
      <template #projectName="{ row, index }">
        <el-link @click="handleView(row)" type="primary">{{ row.projectName }}</el-link>
      </template>
      <template #payStatus="{ row }">
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="row.auditRemark"
          placement="top-start"
          :disabled="!row.auditRemark"
        >
          <el-tag
            :type="
              row.payStatus == 0
                ? 'info'
                : row.payStatus == 1 || row.payStatus == 2
                ? 'success'
                : 'danger'
            "
            size="normal"
            effect="plain"
            >{{ row.$payStatus }}</el-tag
          >
        </el-tooltip>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="申请详情" v-model="applyDrawer" size="80%">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card shadow="never">
            <avue-form ref="addFormRef" :option="applyOptionFormFormOther" v-model="detailForm">
              <template #files>
                <File :fileList="detailForm.filesList"></File>
              </template>
            </avue-form>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never">
            <avue-crud :option="applyOptionFortable" :data="detailForm.detailVOList"></avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <template     v-if="detailForm.payStatus == 0"  #footer>

        <div
          style="
            display: flex;
            justify-content: flex-start;
            height: 200px;
            width: 100%;
            border-top: 1px solid var(--el-color-info);
            padding-top: 5px;
          "
        >
          <el-form ref="form" :model="form" style="width: 100%" label-width="80px">
            <el-row :gutter="10">
              <el-col :span="12">
                <el-form-item label="审核结果">
                  <el-radio-group v-model="auditForm.payStatus">
                    <el-radio :label="1">通过</el-radio>
                    <el-radio :label="3">驳回</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="变更金额">
                  <div style="width: 100%">
                    <el-input style="width: 100%" v-model="auditForm.changeSalary" placeholder="请输入变更金额，如果没有变更无需填写" type="number" @input="calculateActualAmount"></el-input>
                    <div style="margin-top: 5px; color: #666;">实际支付金额: {{ detailForm.totalSalary * 1 +  (auditForm.changeSalary * 1 || 0) }}</div>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              <el-input
                style="width: 100%"
                v-model="auditForm.auditRemark"
                placeholder="请输入审核备注"
                
                type="textarea"
              ></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div style="width: 100%; text-align: right">
          <el-button @click="applyDrawer = false">关 闭</el-button>
          <el-button type="primary" @click="confirm">提 交</el-button>
        </div>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted,watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '项目名称',
      prop: 'projectName',
      width: 250,
      overHidden: true,
      component: 'wf-project-drop',
      search: true,
    },
    {
      label: '收款工人/公司',
      prop: 'humanName',
      width: 200,
      searchLabelWidth: 150,
      search: true,
    },

    {
      label: '付款金额',
      prop: 'salary',
      width: 110,
    },

    {
      label: '总工时',
      prop: 'hours',
      width: 110,
    },
    {
      label: '开始时间',
      prop: 'startTime',
      width: 110,
    },
    {
      label: '结束时间',
      prop: 'endTime',
      width: 110,
    },
    {
      label: '状态',
      prop: 'payStatus',
      type: 'select',
      search: true,
      width: 110,
      dicData: [
        {
          value: 0,
          label: '待审核',
        },
        {
          value: 1,
          label: '审核通过',
        },
        {
          value: 2,
          label: '已付款',
        },
        {
          value: 3,
          label: '审核失败',
        },
      ],
    },
    {
      label: '备注',
      prop: 'workContent',
      overHidden: true,
    },
    {
      label: '审核备注',
      prop: 'auditRemark',
      overHidden: true,
    },
    {
      label: '申请人',
      prop: 'createUserName',
      width: 110,
    },
    {
      label: '申请时间',
      prop: 'createTime',
      width: 110,
      overHidden: true,
      valueFormat: 'YYYY-MM-DD',
      component: 'wf-daterange-search',
      //   search: true,
      searchSpan: 6,
      valueFormat: 'YYYY-MM-DD',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/projectHumanSalary/remove?ids=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectHumanSalary/page';
let route = useRoute();
let params = ref({
  payStatus: route.query.type? 0 : null,
});
watch(() => route.query.type, () => {
  params.value = {
    payStatus: route.query.type? 0 : null,
  }
  onLoad()
})
let tableData = ref([]);
let { proxy } = getCurrentInstance();

let loading = ref(false);

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 1,
        startTime: params.value.createTime && params.value.createTime[0],
        endTime: params.value.createTime && params.value.createTime[1],
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let applyDrawer = ref(false);
let detailForm = ref({});
function viewDetail(row) {
  axios.get('/api/vt-admin/projectHumanSalary/detail?id=' + row.id).then(res => {
    detailForm.value = res.data.data;
    axios.get('/api/vt-admin/humanResource/detail?id=' + row.humanId).then(res => {
      const { name, phone, bankNumber, invoiceType, taxRate, openBank, contact } = res.data.data;
      detailForm.value = {
        ...detailForm.value,
        name,
        phone,
        bankNumber,
        invoiceType,
        taxRate,
        openBank,
        contact,
      };
      applyDrawer.value = true;

      currentId.value = row.id;
      auditForm.value = {
        payStatus: 1,
        salary: row.salary * 1,
        totalSalary: row.totalSalary * 1,

        changeSalary: row.changeSalary * 1
      };
    });
  });
}
let applyOptionFortable = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,

  addRowBtn: false,
  editBtn: true,
  delBtn: true,
  header: false,
  calcHeight: 150,
  searchMenuSpan: 4,
  cellBtn: true,
  searchSpan: 4,
  labelWidth: 120,
  menu: false,
  index: true,
  // showSummary: true,
  border: true,
  column: {
    userName: {
      label: '工人/团队姓名',
      hide: true,
      width: 120,
      overHidden: true,
      click: () => {
        proxy.$refs.humanSelectRef.open();
      },
    },

    // customSalary: {
    //   label: '自定义工价',
    //   width: 120,
    //   // hide: true,
    //   cell: true,
    //   //   addDisplay: false,
    // },
    hours: {
      label: '工时（人·天）',
      width: 120,
      //   addDisplay: false,
      cell: true,
      type: 'number',
    },
    customSalary: {
      label: '工价（人·天）',
      type: 'select',
      width: 130,
      cell: true,
      dicData: [],
      allowCreate: true,
      filterable: true,
      // hide: true,

      props: {
        value: 'salary',
        label: 'salary',
      },
    },
    type: {
      label: '工人类型',
      width: 120,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      //   addDisplay: false,
      hide: true,
    },
    startTime: {
      label: '工作开始时间',

      type: 'date',
      width: 130,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    endTime: {
      label: '工作结束时间',
      width: 130,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    planName: {
      label: '关联计划',
      width: 150,
      type: 'input',
      clearable: false,
      overHidden: true,
      click: () => {
        planDialogVisible.value = true;
      },
      span: 24,
      cell: true,
    },
    workContent: {
      label: '工作内容',
      overHidden: true,
      type: 'textarea',
      span: 24,
      cell: true,
      // width:160,
      row: 2,
    },
  },
});
let applyOptionFormFormOther = ref({
  submitBtn: false,
  emptyBtn: false,
  detail: true,
  group: [
    {
      label: '基本信息',
      column: [
         {
          label:'总计',
           prop: 'totalSalary',
          span: 24,
          type: 'number',
        
        },
        {
          label:'变更金额',
          prop: 'changeSalary',
          span: 24,
          type: 'number',
        
         

        },
        {
          label: '付款金额',
          prop: 'salary',
          span: 24,
        },
        {
          label: '总工时',
          prop: 'hours',
          span: 24,
        },
        {
          label: '备注',
          prop: 'workContent',
          type: 'textarea',
          span: 24,
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          viewDisplay: false,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '工人信息',
      column: [
        {
          label: '姓名',
          prop: 'name',
          span: 24,
          readonly: true,
        },
        {
          label: '联系电话',
          prop: 'phone',
          readonly: true,
          span: 24,
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          readonly: true,
          span: 24,
          // search: true,
          display: true,
          disabled: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          span: 24,
          readonly: true,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          disabled: true,
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '开户行',
          prop: 'openBank',
          span: 24,
          readonly: true,
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
          span: 24,
          readonly: true,
        },
      ],
    },
  ],
});
let applyOptionFormFormCompany = ref({
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '付款金额',
          prop: 'salary',
          span: 24,
        },
        {
          label: '总工时',
          prop: 'hours',
          span: 24,
        },
        {
          label: '备注',
          prop: 'workContent',
          type: 'textarea',
          span: 24,
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          viewDisplay: false,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      label: '公司信息',
      column: [
        {
          label: '公司名称',
          prop: 'name',
          span: 24,
          readonly: true,
        },
        {
          label: '联系人',
          prop: 'contact',
          readonly: true,
          span: 24,
        },
        {
          label: '联系电话',
          prop: 'phone',
          readonly: true,
          span: 24,
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          readonly: true,
          span: 24,
          // search: true,
          display: true,
          disabled: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          span: 24,
          readonly: true,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          disabled: true,
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '开户行',
          prop: 'openBank',
          span: 24,
          readonly: true,
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
          span: 24,
          readonly: true,
        },
      ],
    },
  ],
});
function confirm() {
  debugger
  axios
    .post('/api/vt-admin/projectHumanSalary/audit', {
      id: currentId.value,
      ...auditForm.value,
      salary : auditForm.value.totalSalary * 1 +  (auditForm.value.changeSalary * 1 || 0),
    })
    .then(e => {
      proxy.$message.success('操作成功');
      proxy.$store.dispatch('getMessageList');
      onLoad();
      applyDrawer.value = false;  
    });
}
let currentId = ref(null);
function handleView(row) {
  router.push({
    path: '/Project/detail/detail',
    query: {
      id: row.projectId,
      name: row.projectName + '-详情',
    },
  });
}
let auditForm = ref({
  payStatus: 1,
  salary: 0,
});
</script>

<style lang="scss" scoped></style>
