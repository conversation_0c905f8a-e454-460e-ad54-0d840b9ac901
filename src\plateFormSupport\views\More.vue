<template>
  <div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold mb-6">更多支持</h1>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">技术支持</h2>
      <p class="mb-4">如果您在使用我们的产品过程中遇到任何技术问题，请随时联系我们的技术支持团队。</p>
      <ul class="list-disc pl-5 mb-4">
        <li class="mb-2">电子邮件：<EMAIL></li>
        <li class="mb-2">电话：400-123-4567</li>
        <li class="mb-2">工作时间：周一至周五 9:00-18:00</li>
      </ul>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6 mb-6">
      <h2 class="text-xl font-semibold mb-4">常见问题</h2>
      <div class="space-y-4">
        <div>
          <h3 class="font-medium">如何重置我的密码？</h3>
          <p class="text-gray-600">您可以在登录页面点击"忘记密码"链接，按照提示操作即可重置密码。</p>
        </div>
        <div>
          <h3 class="font-medium">如何更新我的账户信息？</h3>
          <p class="text-gray-600">登录后，点击右上角的用户头像，选择"账户设置"即可更新您的个人信息。</p>
        </div>
        <div>
          <h3 class="font-medium">如何查看我的订单历史？</h3>
          <p class="text-gray-600">登录后，在用户中心可以查看您的所有订单历史记录。</p>
        </div>
      </div>
    </div>
    
    <div class="bg-white shadow-md rounded-lg p-6">
      <h2 class="text-xl font-semibold mb-4">资源下载</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="border rounded p-4">
          <h3 class="font-medium mb-2">用户手册</h3>
          <p class="text-gray-600 mb-3">详细的产品使用说明和功能介绍</p>
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">下载</button>
        </div>
        <div class="border rounded p-4">
          <h3 class="font-medium mb-2">API文档</h3>
          <p class="text-gray-600 mb-3">开发者接口文档和集成指南</p>
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">下载</button>
        </div>
        <div class="border rounded p-4">
          <h3 class="font-medium mb-2">常见问题解答</h3>
          <p class="text-gray-600 mb-3">常见问题和解决方案汇总</p>
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">下载</button>
        </div>
        <div class="border rounded p-4">
          <h3 class="font-medium mb-2">视频教程</h3>
          <p class="text-gray-600 mb-3">产品使用视频教程和演示</p>
          <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">下载</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 可以根据需要添加更多的交互功能
</script>

<style scoped>
/* 可以添加额外的样式 */
</style>