<template>
  <div class="global-uploader" v-if="visible && files.length > 0">
    <div class="uploader-header">
      <h3>文件上传</h3>
      <div class="header-controls">
        <span class="minimize-btn" @click="isMinimized = !isMinimized">{{
          isMinimized ? '⬆' : '⬇'
        }}</span>
        <span class="close-btn" @click="close">×</span>
      </div>
    </div>
    <transition-group name="fade" tag="div" :style="{maxHeight:isMinimized?0:'400px'}" class="upload-list">
      <div
        v-for="file in visibleFiles"
        :key="file.id"
        class="upload-item"
        @animationend="handleAnimationEnd(file)"
      >
        <div class="file-info">
          <span class="filename" :title="file.fileName" style="font-size:14px">{{ file.fileName }}</span>
          <span class="status" style="font-size:12px">{{ getStatusText(file)  }}</span>
          <div class="actions" v-if="file.status === 'error'">
            <el-icon title="重试" style="color:var(--el-color-primary);cursor:pointer" class="retry-icon" @click.stop="handleRetry(file)" >
              <Refresh />
            </el-icon>
            <el-icon title="取消上传" style="color:var(--el-color-primary);cursor:pointer" class="retry-icon" @click.stop="handleRemove(file)" >
              <Close />
            </el-icon>
          </div>
        </div>
        <div class="progress-bar">
          <div
            class="progress"
            :style="{ width: file.progress + '%' }"
            :class="{ success: file.status === 'success', error: file.status === 'error' }"
          ></div>
        </div>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { ref, computed } from 'vue';
import uploadService from './uploadService';
import { Refresh, Close, } from '@element-plus/icons-vue';
import { ElButton, ElIcon, } from 'element-plus';
export default {
  name: 'GlobalUploader',
  components: { ElButton, Refresh, Close, ElIcon },
  setup() {
    const visible = ref(false);
    const files = ref([]);
    const isMinimized = ref(false);
    const visibleFiles = computed(() => {
     
      return files.value.filter(file => !file.shouldRemove);
    });

    const getStatusText = file => {
      switch (file.status) {
        case 'uploading':
          return `${file.progress}%`;
        case 'success':
          return '上传成功';
        case 'error':
          return '上传失败';
        default:
          return '等待上传';
      }
    };

    const close = () => {
      visible.value = false;
      // 可以添加清理逻辑
    
    };

    const handleRetry = file => {
      // 调用上传服务的重试逻辑
      uploadService.retryUpload(file);
    };

    const handleRemove = file => {
      files.value = files.value.filter(f => f.id !== file.id);
    };

    const handleAnimationEnd = file => {
      if (file.status === 'success') {
        file.shouldRemove = true;
        files.value = [...files.value]; // 触发数组更新
      }
    };

    return {
      visible,
      files,
      isMinimized,
      visibleFiles,
      getStatusText,
      close,
      handleAnimationEnd,
      handleRetry,
      handleRemove,
    };
  },
};
</script>

<style scoped>
.global-uploader {
  position: fixed;
  right: 20px;
  bottom: 20px;
  width: 350px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 9999;
  overflow: hidden;
}

.uploader-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 16px;
  background: #f5f5f5;
  border-bottom: 1px solid #eee;
}

.close-btn {
  cursor: pointer;
  font-size: 20px;
  line-height: 1;
}

.upload-list {
  max-height: 400px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f5f5f5;
}

.upload-list::-webkit-scrollbar {
  width: 8px;
}

.upload-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 4px;
}

.upload-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.upload-item {
  padding: 12px 16px;
  border-bottom: 1px solid #eee;
}

.file-info {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  margin-bottom: 8px;
  align-items: center;
}

.filename {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 70%;
}

.progress-bar {
  height: 6px;
  background: #e9e9e9;
  border-radius: 3px;
  overflow: hidden;
}

.progress {
  height: 100%;
  background: #409eff;
  transition: width 0.3s;
}

.progress.success {
  background: #67c23a;
}

.progress.error {
  background: #f56c6c;
}

.global-uploader {
  transition: all 0.3s ease;
}

.fade-leave-active {
  transition: all 0.5s ease;
  opacity: 1;
  max-height: 100px;
}

.fade-leave-to {
  opacity: 0;
  max-height: 0;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.minimize-btn {
  cursor: pointer;
  transition: transform 0.2s;
}

.minimize-btn:hover {
  transform: scale(1.2);
}

.upload-list {
  transition: max-height 0.3s ease-out;
 
  overflow: auto;
}
</style>
