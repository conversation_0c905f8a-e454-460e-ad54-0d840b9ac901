<template>
  <div>
    <el-empty :description="'报价暂未完成'" ></el-empty>
   
  </div>
</template>

<script setup>
import detail from '@/views/CRM/quotation/compoents/OfferDetail.vue';
import History from '@/views/CRM/quotation/compoents/history.vue';
const props = defineProps({
  offerId: {
    type: Number,
    default: '',
  },
  stageStatus: {
    type: Number,
    default: -1,
  },
});
let activeNames = ref(['detail', 'history']);
let currentOption = ref({
  //   height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  search: false,
  //   calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 6,
  menuWidth: 270,
  header: false,
  border: true,
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      width: 250,
      overHidden: true,
    },

    {
      label: '报价时间',
      prop: 'offerDate',

      searchSpan: 6,
      searchRange: true,
      type: 'date',
    },
    {
      label: '报价版本',
      prop: 'version',
      type: 'input',
      formatter: row => {
        return `v${row.version}.0`;
      },
    },
  ],
});
</script>

<style lang="scss" scoped></style>
