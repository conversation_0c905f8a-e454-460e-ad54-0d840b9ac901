<template>
    <basic-container shadow="never">
      <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #orderNo="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.orderNo }}</el-link>
      </template>
    </avue-crud>
    </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps(['supplierId', 'productId', 'productInfo']);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  addBtn: false,
  delBtn: true,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  border: true,
  column: [
    {
      label: '采购订单',
      prop: 'orderNo',
      width: 200,
    },
    {
      label: '供应商名称',
      prop: 'supplierName',
    
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      span: 12,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '产品质保期（年）',
      prop: 'warrantyPeriod',
      type: 'number',
      span: 12,
     
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'switch',
      value: 1,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      width:100,
      span: 12,
    },
    {
      label: '税率',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      width:100,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter: val => {
        return parseFloat(val.taxRate || 0) + '%';
      },
    },
    {
      label: '价格',
      prop: 'purchasePrice',
      
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
onMounted(() => {
  onLoad();
})
watch(() => props.productId,() => {
  onLoad();
})
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/supplierProduct/historyPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
       
        productId: props.productId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let drawer = ref(false);
function open() {
  drawer.value = true;
}
function toDetail(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: row.orderId,
      activeName,
    },
  });
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
