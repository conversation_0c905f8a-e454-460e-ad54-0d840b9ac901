<template>
  <div style="margin-bottom: 10px">
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>总利润分析</div>
          <div style="display: flex; gap: 20px">
            <el-date-picker
              style="width: 150px"
              v-model="query.year"
              value-format="YYYY"
              @change="getDetail"
              type="year"
              :clearable="false"
              placeholder=""
            ></el-date-picker>
            <!-- <el-radio-group @change="getDetail" v-model="query.type1">
                <el-radio-button label="0">月</el-radio-button>
                <el-radio-button label="1">季度</el-radio-button>
                <el-radio-button label="2">年</el-radio-button>
              </el-radio-group> -->
            <wfUserSelectDrop
              v-model="query.userId"
              @change="getDetail"
              style="width: 100px"
            ></wfUserSelectDrop>
            <el-input
              style="width: 150px"
              v-model="query.customerName"
              @change="getDetail"
              placeholder="请输入客户名称"
            ></el-input>
            <el-select
              style="width: 200px"
              v-model="query.businessOpportunityType"
              @change="getDetail"
              clearable
              placeholder="请选择板块"
            >
              <el-option
                v-for="item in businessOpportunityType"
                :key="item.id"
                :label="item.dictValue"
                :value="item.id"
              ></el-option>
            </el-select>
          </div>
        </div>
      </template>
      <!-- 图表 -->
      <div style="height: 400px">
        <div ref="chartRef" style="height: 100%"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { inject } from 'vue';
import wfUserSelectDrop from '@/views/desk/components/userSelect.vue';
import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';
import moment from 'moment';
import axios from 'axios';
const chartRef = ref(null);
const radio2 = ref('月');
onMounted(() => {
  getDetail();
});
const form = inject('form');
watch(form.value, getDetail);
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        var name = params.name;
        return (
          '<div style="display:flex; align-items:center;">' +
          '<span style="display:inline-block;width:10px;height:10px;border-radius:50%;background-color:' +
          params.color +
          ';margin-right:5px;"></span>' +
          name +
          ': ' +
          params.percent +
          '</div>'
        );
      },
    },
    series: [
      {
        name: 'Access From',
        type: 'pie',
        radius: ['0%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 100,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: true,
        },
        data: [
          { value: 15, name: '采购成本' },
          { value: 15, name: '人工成本' },
          { value: 10, name: '税金' },
          { value: 5, name: '利润' },
        ],
        label: {
          show: true,
          formatter: function (params) {
            var name = params.name;
            var percent = params.percent;
            return name + '\n' + percent;
          },
        },
      },
    ],
  };
  chart.setOption(option);
}
let query = ref({
  year: moment(new Date()).format('YYYY'),
});
let detailForm = ref({ y: [] });
function getDetail() {
  axios
    .get('/api/vt-admin/statistics/followNumberStatistics', {
      params: {
        ...query.value,
        ...form.value,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      initChart();
    });
}
let businessOpportunityType = ref([]);
function getType() {
  axios.get('/api/blade-system/dict/dictionary?code=businessOpportunityType').then(res => {
    businessOpportunityType.value = res.data.data;
  });
}
onMounted(() => {
  getType();
});
</script>

<style lang="scss" scoped>
.el-card {
  height: 500px;
}
</style>
