<template>
  <div class="list">
    <div class="list-title">
      {{ todoDate }}
      <el-icon v-if="type == 'self'" style="cursor: pointer" @click="toggleEdit">
        <Edit></Edit>
      </el-icon>
    </div>
    <div style="padding: 10px; height: calc(100% - 40px); overflow-y: auto; box-sizing: border-box">
      <el-form
        v-if="!detail && type == 'self'"
        ref="formRef"
        label-position="top"
        :rules="rules"
        :model="form"
        label-width="80px"
      >
        <el-form-item label="今日工作总结" prop="todayWork">
          <template #label>
            <div style="display: flex;align-items: center;gap: 10px;">
              <span>今日工作总结</span>
            <el-checkbox v-model="isViewSystemLog" label="查看系统日志" active-color="#13ce66" inactive-color="#ff4949"></el-checkbox>
            </div>
          </template>
          <el-input
            type="textarea"
            placeholder="请输入今日工作总结"
            rows="4"
            :autosize="{
              minRows: 5,
            }"
            style="width: 100%"
            v-model="form.todayWork"
          ></el-input>
        </el-form-item>
        <el-form-item label="系统日志" v-if="isViewSystemLog" >
          <template #label>
            <span>系统日志</span>
            
            <el-table border v-if="workLogList.length > 0" size="small" :max-height="300" :data="workLogList" style="width: 100%">
            <el-table-column type="index" width="40" />
            <el-table-column prop="logDescription" label="事项"> </el-table-column>
          </el-table>
          <span
            v-else
            style="font-size: 14px; color: #666"
            >!暂无日志</span
          >
          </template>
        </el-form-item>
        <el-form-item label="明日工作计划">
          <el-input
            type="textarea"
            placeholder="请输入明日工作计划"
            rows="4"
            :autosize="{
              minRows: 5,
            }"
            style="width: 100%"
            v-model="form.tomorrowWorkPlan"
          ></el-input>
        </el-form-item>
        <el-form-item label="其它事项">
          <el-input
            type="textarea"
            placeholder="请输入其它事项"
            rows="4"
            :autosize="{
              minRows: 5,
            }"
            style="width: 100%"
            v-model="form.remark"
          ></el-input>
        </el-form-item>
        <el-form-item label="附件">
          <el-upload
            style="width: 100%"
            action="/api/blade-resource/attach/upload"
            multiple
            v-model:file-list="form.fileList"
            :headers="headersObj"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
          >
            <el-button icon="upload" type="primary">点击上传</el-button>
          </el-upload>
        </el-form-item>
        <el-form-item label="">
          <div style="display: flex; align-items: center; justify-content: flex-end; width: 100%">
            <el-button style="width: 100%" @click="toggleEdit">取消</el-button>
            <el-button style="width: 100%" type="primary" plain @click="handleSubmit(0)">保存草稿</el-button>
            <el-button type="primary" style="width: 100%" @click="handleSubmit(1)">保存</el-button>
          </div>
        </el-form-item>
      </el-form>
      <div v-else-if="detail && form.logStatus == 1">
        <div
          class="detail-view"
          style="background-color: #f9fafc; padding: 20px; border-radius: 8px"
        >
          <div
            class="title"
            style="
              font-size: 14px;
              color: var(--el-color-primary);
              font-weight: bold;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            今日工作总结
          </div>
          <p style="white-space: pre-wrap; padding: 0 10px; color: #606266; line-height: 1.6">
            {{ form.todayWork }}
          </p>
          <div
            class="title"
            style="
              font-size: 14px;
              color: var(--el-color-primary);
              font-weight: bold;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            系统日志
          </div>
          <el-table border v-if="workLogList.length > 0" size="small" :max-height="300" :data="workLogList" style="width: 100%">
            <el-table-column type="index" width="40" />
            <el-table-column prop="logDescription" label="事项"> </el-table-column>
          </el-table>
          <span
            v-else
            style="font-size: 14px; color: #666"
            >!暂无日志</span
          >
          <div
            class="title"
            style="
              font-size: 14px;
              color: var(--el-color-primary);
              font-weight: bold;
              margin-top: 20px;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            明日工作计划
          </div>
          <p style="white-space: pre-wrap; padding: 0 10px; color: #606266; line-height: 1.6">
            {{ form.tomorrowWorkPlan }}
          </p>
          <div
            class="title"
            style="
              font-size: 14px;
              color: var(--el-color-primary);
              font-weight: bold;
              margin-top: 20px;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            其它事项
          </div>
          <p style="white-space: pre-wrap; padding: 0 10px; color: #606266; line-height: 1.6">
            {{ form.remark }}
          </p>
          <div
            class="title"
            style="
              font-size: 14px;
              color: var(--el-color-primary);
              font-weight: bold;
              margin-top: 20px;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            附件
          </div>
          <File style="padding: 0 10px" :fileList="form.fileList"></File>
          <span
            v-if="!form.fileList || form.fileList.length == 0"
            style="font-size: 14px; color: #666"
            >!暂无附件</span
          >
        </div>
        <!-- 评论展示区域 -->
        <div class="comment-section" style="padding-bottom: 50px">
          <div
            class="comment-title"
            style="
              font-size: 18px;
              color: #333;
              font-weight: bold;
              margin-bottom: 15px;
              margin-top: 5px;
              border-bottom: 2px solid #409eff;
              padding-bottom: 5px;
            "
          >
            评论
          </div>
          <!-- 显示已有评论 -->
          <avue-comment
            :reverse="item.createUser == $store.getters.userInfo.user_id"
            v-for="(item, index) in form.commentVOList"
            :data="item"
            :option="option"
          >
            <el-button
              type="primary"
              icon="delete"
              v-if="item.createUser == $store.getters.userInfo.user_id"
              @click="removeComment(item)"
              text
              >删除</el-button
            >
          </avue-comment>
          <!-- 评论表单 -->
          <el-form
            ref="commentFormRef"
            :model="commentForm"
            label-width="80px"
            @submit.prevent="handleCommentSubmit"
            style="
              position: absolute;
              bottom: 0;
              background-color: white;
              padding: 0;
              z-index: 1;
              opacity: 1;
              bottom: 0;
              box-sizing: border-box;
              left: 0;
              right: 0;
              padding-top: 10px;
              box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.1); /* 给表单添加顶部阴影 */
              width: auto;
            "
          >
            <el-form-item label="评论内容" prop="body">
              <div
                style="
                  width: 100%;
                  display: flex;
                  justify-content: space-between;
                  align-items: center;
                "
              >
                <el-input
                  placeholder="请输入评论内容"
                  v-model="commentForm.body"
                  rows="3"
                  style="width: 100%; border: none; outline: none"
                ></el-input>
                <el-button style="height: 100%" type="primary" @click="handleCommentSubmit"
                  >提交评论</el-button
                >
              </div>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div v-else>
        <div
          class="detail-view"
          style="background-color: #f9fafc; padding: 20px; border-radius: 8px"
        >
          <div
            class="title"
            style="
              font-size: 14px;
              color: #666;
              font-weight: bold;
              margin-bottom: 10px;
              padding-bottom: 5px;
              border-bottom: 1px solid #e4e7ed;
            "
          >
            系统日志
          </div>
          <el-table border v-if="workLogList.length > 0" size="small" :data="workLogList" style="width: 100%">
            <el-table-column type="index" width="40" />
            <el-table-column prop="logDescription" label="事项"> </el-table-column>
          </el-table>
          <span
            v-else
            style="font-size: 14px; color: #666"
            >!暂无日志</span
          >
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';
import { Plus, Edit, QuestionFilled } from '@element-plus/icons-vue';
import website from '@/config/website';
import { Base64 } from 'js-base64';
import { getToken } from 'utils/auth';
import { option } from '../../CRM/compoents/option';
import moment from 'moment';
export default {
  name: 'MToDoList',
  props: {
    data: {
      type: Array,
    },
    date: {
      type: String,
    },
    type: {
      type: String,
    },
    workLogList: {
      type: Array,
    },
  },
  data() {
    return {
      todoDate: '',
      form: {
        fileList: [],
      },
      detail: false,
      rules: {
        // todayWork: [{ required: true, message: '请输入今日工作总结', trigger: 'change' }],
      },
      commentForm: {},

      option: {
        props: {
          avatar: 'avatar',
          author: 'createName',
          body: 'content',
        },
      },
      workLogPage: {
        current: 1,
        size: 10,
        total: 0,
      },
      isViewSystemLog: false,
    };
  },
  emits: ['getDetail'],
  watch: {
    data: function (val) {
      if (!val.logStatus) {
        this.detail = false;
      } else {
        this.detail = true;
      }
      this.form = {
        ...val,
        fileList: val.fileList
          ? val.fileList.map(item => {
              return {
                ...item,
                name: item.originalName,
                response: { data: { id: item.id } },
                url: item.filePath,
              };
            })
          : [],
        commentVOList: val.commentVOList
          ? val.commentVOList.map(item => {
              return {
                ...item,

                avatar: item.avatar,
                body: item.content,
                author: item.createName + `(${item.createTime})`,
              };
            })
          : [],
      };
    },
    date: function (val) {
      this.todoDate = val;
    },
  },
  computed: {
    headersObj() {
      return {
        Authorization: `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
        [website.tokenHeader]: 'bearer ' + getToken(),
      };
    },
  },
  created() {
    this.todoDate = this.date;
  },
  methods: {
    handleSubmit(logStatus) {
      this.$refs.formRef.validate((valid, err) => {
        if (valid) {
          console.log(this.form);

          const data = {
            ...this.form,
            logDate: this.date,
            logStatus,
            files:
              this.form.fileList &&
              this.form.fileList
                .map(item => {
                  return item.response.data.id;
                })
                .join(','),
          };
          axios.post('/api/vt-admin/businessWorkLog/save', data).then(res => {
            this.$message.success('保存成功');
            this.$emit('getDetail');
          });
        } else {
          console.log(err);
          return false;
        }
      });
    },
    handleCommentSubmit() {
      if (!this.commentForm.body) return;
      axios
        .post('/api/vt-admin/businessWorkLogComment/save', {
          logId: this.form.id,
          content: this.commentForm.body,
        })
        .then(res => {
          this.commentForm.body = '';
          this.$message.success('评论成功');
          this.$emit('getDetail');
        });
    },
    toggleEdit() {
      this.detail = !this.detail;
      if (this.detail) {
        this.$emit('getDetail');
      }
    },
    removeComment(item) {
      this.$confirm('确定要删除该评论吗？', '提示', {}).then(() => {
        axios.post('/api/vt-admin/businessWorkLogComment/remove?ids=' + item.id).then(res => {
          this.$message.success('删除成功');
          this.$emit('getDetail');
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;
  // padding-top: 40px;
  // box-sizing: border-box;

  .list-title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #409eff;
    color: #fff;
    font-size: 16px;
    padding-left: 15px;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 15px;
    box-sizing: border-box;
  }
}

::v-deep .el-textarea__inner {
  // box-shadow: none !important;
  resize: none !important;
}
.todo-item {
  width: 100%;
  padding: 5px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #ebeef5;
  position: relative;

  .complete {
    width: 100%;
    padding: 5px 15px;
    box-sizing: border-box;
    color: #606266;
    text-decoration: line-through;
    font-size: 14px;
  }

  .control {
    height: 100%;

    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    visibility: hidden;

    padding-right: 10px;
    box-sizing: border-box;

    .control-item {
      width: 20px;
      height: 20px;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      color: #fff;

      cursor: pointer;

      &.enter {
        background-color: var(--el-color-primary);
        margin-right: 10px;
      }
      &.close {
        background-color: var(--el-color-danger);
      }
    }
  }

  &:hover {
    .control {
      visibility: visible;
    }
  }
}
// /deep/ .el-card__body {
//   padding: 5px 10px;
// }

.detail-view {
  .title {
    font-size: 14px;
    color: #606266;
    margin-bottom: 5px;
    &::before {
      content: '';
      display: inline-block;
      width: 5px;
      height: 5px;
      vertical-align: middle;
      border-radius: 100%;
      background-color: #409eff;
      color: #409eff;
    }
  }
}
p {
  font-size: 16px;
  font-weight: bolder;
  color: rgb(70, 67, 67) !important;
}

</style>
