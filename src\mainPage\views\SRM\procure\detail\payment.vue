<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    :before-open="beforeOpen"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #invoiceId-form>
      <wfInvoiceDrop v-model="form.invoiceId" :id="props.id"></wfInvoiceDrop>
    </template>
    <template #menu="{ row,$index }">
      <el-button type="primary" text icon="edit" v-if="row.paymentStatus == 0" @click="$refs.crud.rowEdit(row,$index)">编辑</el-button>
      <el-button type="primary" text icon="Clock" @click="payListRecord(row)">付款记录</el-button>
    </template>
    <template #paymentStatus="{ row }">
      <el-tag effect='plain' v-if="row.paymentStatus == 0" type="danger">未付款</el-tag>
      <el-tag effect='plain' v-if="row.paymentStatus == 1" type="danger">部分付款</el-tag>
      <el-tag effect='plain' v-if="row.paymentStatus == 2" type="success">已付款</el-tag>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-dialog title="付款记录" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <!-- <slot ></slot> -->
    <payList :purchaseContractPlanId="currentId"></payList>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
    </div>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted,watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfInvoiceDrop from '../compoents/wf-invoice-drop.vue';
import payList from '../../../Finance/payment/compoents/payList.vue';
const props = defineProps({
  contractCode: String,
  supplierName: String,
  id: String,
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  labelWidth: 120,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'input',
      label: '合同编号',
      span: 12,
      display: true,
      hide: true,
      prop: 'contractCode',
      disabled: true,
    },
    {
      type: 'input',
      label: '关联发票',
      span: 12,
      display: true,
      hide: true,
      prop: 'invoiceId',
    },
    // {
    //   type: 'input',
    //   label: '供应商名称',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'supplierName',
    //   disabled: true
    // },
    // {
    //   type: 'input',
    //   label: '产品状态',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'arriveStatus',
    //   disabled: true
    // },
    {
      type: 'input',
      label: '计划名称',
      span: 12,
      display: true,
      prop: 'planName',
    },
    {
      type: 'date',
      label: '计划付款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planPaymentDate',
    },
    {
      type: 'number',
      label: '计划付款金额',
      controls: true,
      span: 24,
      display: true,
      prop: 'planPaymentPrice',
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'input',
      label: '实际付款金额',
      span: 12,
      addDisplay: false,
      editDisplay: false,

      prop: 'actualPaymentPrice',
    },
    {
      type: 'input',
      label: '实际付款时间',
      span: 12,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualPaymentDate',
    },
    {
      type: 'input',
      label: '付款人',
      span: 12,
      addDisplay: false,
      editDisplay: false,
      prop: 'paymentUserName',
    },
    {
      type: 'select',
      label: '付款状态',
      span: 12,
      addDisplay: false,
      editDisplay: false,
      prop: 'paymentStatus',
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
    {
      type: 'select',
      label: '发票状态',
      span: 12,
      addDisplay: false,
      editDisplay: false,
      prop: 'invoiceStatus',
      dicData: [
        {
          label: '已开票',
          value: 0,
        },
        {
          label: '已认证',
          value: 1,
        },
      ],
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
  ],
});
let form = ref({
  contractCode: props.contractCode,
  purchaseContractId: props.id,
  supplierName: props.supplierName,
});
function beforeOpen(done,type) {
  if(type == 'add'){
    form.value = {
    contractCode: props.contractCode,
    purchaseContractId: props.id,
    supplierName: props.supplierName,
  };
  }
  done();
}
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContractPlanPayment/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseContractPlanPayment/update';
const tableUrl = '/api/vt-admin/purchaseContractPlanPayment/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
watch(
  () => props.id,
  () => {
    onLoad();
  })
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(`${tableUrl}?purchaseContractId=${props.id}`, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
let dialogVisible = ref(false);
let currentId = ref('');
function payListRecord(row) {
  currentId.value = row.id;
  dialogVisible.value = true;
}
</script>

<style lang="scss" scoped></style>
