<template>
 
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-button type="primary" icon="plus" @click="invoiceApply" plain>新增</el-button>
      </template>
      <template #cycle-form>
                <div style="width: 100%;display: flex;align-items: center;">
                  <el-input v-model.number="form.cycleNumber" placeholder="请输入" style="width: 20%;margin-right: 5px;" />
                  <el-radio v-model="form.cycleType" :label="0">天</el-radio>
                  <el-radio v-model="form.cycleType" :label="1">月</el-radio>
                  <el-radio v-model="form.cycleType" :label="2">年</el-radio>
                </div>
              </template>
    </avue-crud>
    <el-drawer v-model="drawer" size="80%" title="新增续费产品">
      <el-row style="height: 100%" :gutter="8">
        <el-col style="height: 100%" :span="10">
          <el-card class="box-card" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
              <template #cycle>
                <div style="width: 100%;display: flex;align-items: center;">
                  <el-input v-model.number="form.cycleNumber" placeholder="请输入" style="width: 30%;margin-right: 5px;" />
                  <el-radio v-model="form.cycleType" :label="0">天</el-radio>
                  <el-radio v-model="form.cycleType" :label="1">月</el-radio>
                  <el-radio v-model="form.cycleType" :label="2">年</el-radio>
                </div>
              </template>
            </avue-form>
          </el-card>
        </el-col>

        <el-col style="height: 100%" :span="14">
          <el-card class="box-card" style="height: 100%">
            <avue-crud
              :option="selectProductOption"
              @row-del="productRowDel"
              :data="selectProductList"
            >
              <template #menu-left>
                <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRef.submit()">确认 申请</el-button>
        </div>
      </template>
      <el-drawer title="选择产品" v-model="innerDrawer" size="40%" append-to-body>
        <el-row style="height: 100%" :gutter="20">
         
          <el-col style="height: 100%" :span="24">
            <el-card class="box-card" style="height: 100%">
              <avue-crud
                ref="addAvue"
                :option="productOption"
                v-model:page="pageOption"
                :table-loading="selectLoading"
                @current-change="queryProductByContractId"
                @size-change="queryProductByContractId"
                @refresh-change="queryProductByContractId"
                @selection-change="handleChange"
                :data="productList"
              >
                <template #isInvoice="{ row }">
                  <el-tag effect='plain' v-if="row.isInvoice === 1" type="success">是</el-tag>
                  <el-tag effect='plain' v-else type="danger">否</el-tag>
                </template>
              </avue-crud>
            </el-card>
          </el-col>
        </el-row>
        <template #footer>
          <div style="flex: auto">
            <el-button type="primary" @click="confirmAddProduct">确 认</el-button>
          </div>
        </template>
      </el-drawer>
    </el-drawer>
    <dialogForm ref="dialogForm"></dialogForm>
 
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  labelWidth:130,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '产品',
      prop: 'customProductName',
      editDisplay: false,
      overHidden:true,
    },

    {
      label: '规格型号',
      prop: 'customProductSpecification',
      editDisplay: false,
      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      editDisplay: false,
      type: 'number',
      span: 12,
      cell: false,
    },

    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      editDisplay: false,
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'hsze',
      type: 'number',
      span: 12,
      editDisplay: false,
      cell: false,
    },
    {
      type: 'date',
      label: '开始使用日期',
      span: 24,
      width: 130,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },
    {
      type: 'date',
      label: '到期时间',
      span: 24,
      width: 130,
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'overDate',
    },
    {
      type: 'input',
      label: '使用周期',
      width: 110,
      span: 24,
      display: true,
      prop: 'cycle',
      formatter: (row) => {
        return row.cycleNumber + `${['天','月','年'][row.cycleType]}`
      }
    },
    {
      type: 'select',
      label: '提醒时间',
      width: 110,
      span: 24,
      display: true,
      props:{
        value:'value',
        label:'label'
      },
      dicData:(() => {
        let arr = []
        for(let i = 1; i <= 31; i++){
          arr.push({label: '到期' + i + '天前',value:i})
        }
        return arr
      })(),
      prop: 'beforeDays',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      overHidden:true,
      type: 'textarea',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/vt-admin/customerProductRenew/remove?ids=';
const updateUrl = '/api//vt-admin/vt-admin/customerProductRenew/update';
const tableUrl = '/api/vt-admin/customerProductRenew/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId:route.query.id
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

//   开票申请
let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      type: 'date',
      label: '开始使用日期',
      span: 24,
      width: 100,
      rules: [
        {
          required: true,
          message: '请输入开始使用日期',
      }],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },

    {
      type: 'input',
      label: '使用周期',
      width: 110,
      span: 24,
      display: true,
      
      prop: 'cycle',
    },
    
    {
      label:'是否提醒',
      prop:'isRemind',
      type:'switch',
      value:1,
      dicData:[{label:'否',value:0},{label:'是',value:1},],
      control:val => {
        return {
          beforeDays:{
            display:!!val
          }
        }
      }
    },
    {
      type: 'select',
      label: '提醒时间',
      width: 110,
      span: 24,
      display: true,
      props:{
        value:'value',
        label:'label'
      },
      dicData:(() => {
        let arr = []
        for(let i = 1; i <= 31; i++){
          arr.push({label: '到期' + i + '天前',value:i})
        }
        return arr
      })(),
      prop: 'beforeDays',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
    },
   
  ],
});
let productOption = ref({
  header: true,
  menu: false,
  addBtn: false,
  height: 'auto',
  calcHeight: '20',
  selection: true,
  reserveSelection: true,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
    },
    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
  ],
});
let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  height: 'auto',
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
    },
    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
  ],
});
let contractList = ref([]);
let currentContractId = ref('');


function invoiceApply() {
  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();
  });
}
function addProduct() {
  
  queryProductByContractId()
  innerDrawer.value = true;
}

let productList = ref([]);
let selectLoading = ref(false);
function queryProductByContractId() {
  selectLoading.value = true;
  axios
    .get('/api/vt-admin/businessOpportunity/getTransactionProducts', {
      params: {
        sealContractId: route.query.id,
        current: pageOption.value.currentPage,
        isRenew:1,
        size: pageOption.value.pageSize,
      },
    })
    .then(res => {
      productList.value = res.data.data.records;
      pageOption.value.total = res.data.data.total;
      selectLoading.value = false;
    });
}
let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {
  selectProductList.value.push(
    ...selectList.value.map(i => {
      return {
        ...i,
        product: {
          ...i.product,
        },
      };
    })
  );
  //   selectList.value = [];
  proxy.$refs.addAvue.toggleSelection();
  innerDrawer.value = false;
 
}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      
    })
    .catch(() => {});
}

function submit(form, done, loading) {
 
  
  console.log(form);
  form.createTime = null;
  const data = selectProductList.value.map(item => {
    return {
      ...form,
      detailId:item.id,
      id:null
    }
  })
  axios.post('/api/vt-admin/customerProductRenew/save', data).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      onLoad();
      drawer.value = false;
      clearAll();
    }
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();
  selectProductList.value = [];
  pageOption.value.currentPage = 1;
  pageContract.value.current = 1;
  form.value.cycleType = 1
}
let pageContract = ref({
  current: 1,
  size: 20,
});

</script>

<style lang="scss" scoped></style>
