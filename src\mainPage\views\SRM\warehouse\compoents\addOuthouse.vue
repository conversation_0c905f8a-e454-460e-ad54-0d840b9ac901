<template>
  <basic-container>
    <Title
      >新增出库
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form
      :option="option"
      ref="addForm"
      style="margin-top: 5px"
      @submit="submit"
      v-model="form"
    >
      <template #product>
        <el-table
          class="avue-crud"
          :data="form.detailDTOList"
          border
          show-summary
          :summary-method="productSum"
        >
          <el-table-column label="序号" type="index" :index="1" width="80">
            <template #header>
              <el-button
                type="primary"
                circle
                icon="plus"
                @click="addProduct"
                v-if="
                  form.outStorageType == 1 || form.outStorageType == 2 || form.outStorageType == 3
                "
              ></el-button>
            </template>
          </el-table-column>
          <el-table-column
            label="设备名称"
            show-overflow-tooltip
            prop="productName"
          ></el-table-column>
          <el-table-column
            label="供应商"
            show-overflow-tooltip
            prop="supplierName"
            #default="{ row }"
          >
            <!-- <WfSupplierSelect v-if="form."
              v-model="row.supplierId"
              placeholder="请选择供应商"
              size="small"
              style="width: 100%"
            ></WfSupplierSelect> -->
          </el-table-column>
          <el-table-column
            label="规格型号"
            show-overflow-tooltip
            prop="productSpecification"
          ></el-table-column>
          <el-table-column label="产品图片" #default="{ row }">
            <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
          </el-table-column>
          <el-table-column label="品牌" width="90" prop="productBrand"></el-table-column>
          <el-table-column label="单位" prop="unitName"></el-table-column>
          <el-table-column
            label="订单所需数量"
            width="110"
            v-if="form.outStorageType == 0"
            #default="{ row }"
            prop="number"
          >
          </el-table-column>
          <el-table-column
            label="使用库存数量"
            width="110"
            v-if="form.outStorageType == 0"
            #default="{ row }"
            prop="inventoryNumber"
          >
          </el-table-column>
          <el-table-column
            label="入库数量"
            v-if="form.outStorageType == 0"
            width="100"
            #default="{ row }"
            prop="arriveNumber"
          >
            {{ row.arriveNumber * 1 - row.outNumber * 1 }}
          </el-table-column>
          <el-table-column
            label="已出库数量"
            v-if="form.outStorageType == 0"
            #default="{ row }"
            prop="outNumber"
          >
          </el-table-column>
          <el-table-column label="出库数量" align="center" width="180" #default="{ row }">
            <el-input-number
              controls-position="right"
              :max="row.arriveNumber * 1 - row.outNumber * 1"
              :min="0"
              v-model="row.outStorageNumber"
            ></el-input-number>
          </el-table-column>
          <el-table-column
            label="单价"
            v-if="form.outStorageType !== 0"
            #default="{ row }"
            align="center"
            prop="costPrice"
          >
            <el-input-number
              v-model="row.costPrice"
              style="width: 100%"
              placeholder=""
            ></el-input-number>
          </el-table-column>
          <!-- <el-table-column label="序列号" align="center">
            <template #default="{ row }">
              <el-select v-model="row.serialNumber" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in row.serialNumberList"
                  :key="item"
                  :label="item"
                  :value="item"
                ></el-option>
              </el-select>
            </template>
          </el-table-column> -->

          <el-table-column label="操作" #default="{ row, $index }" width="140" align="center">
            <div>
              <!-- <el-button type="primary" text icon="Switch" @click="hanldeSplit(row, $index)"
                >拆分入库</el-button
              > -->
              <el-button type="primary" text icon="CopyDocument" @click="handleCopy(row, $index)"
                >粘贴序列号</el-button
              >
            </div>
          </el-table-column>
        </el-table>
      </template>
      <template #menu-form>
        <el-button type="primary" plain @click="printOrder" icon="Printer">送货单 <el-tooltip content="如果涉及拆解产品 请到列表打印送货单" placement="top" effect="dark">

        <el-icon><QuestionFilled /></el-icon>
        </el-tooltip>
        </el-button>
      </template>
    </avue-form>
    <div style="margin-top: 20px; padding-left: 75px"></div>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleUserSelectConfirm"
    ></wf-product-select>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog
      :show-close="false"
      :close-on-click-modal="false"
      title="粘贴"
      v-model="clipboardVisible"
      class="avue-dialog avue-dialog--top"
      @close="splitData = ''"
    >
      <el-form>
        <el-form-item label="序列号">
          <el-input
            v-model="clipboardText"
            placeholder="请粘贴序列号"
            rows="15"
            @input="handleData"
            type="textarea"
          ></el-input>
        </el-form-item>
      </el-form>
      <el-alert
        type="warning"
        style="margin-bottom: 5px"
        :closable="false"
        v-for="item in duplicData"
        >{{ `${item.value}重复`
        }}<span style="color: var(--el-color-primary)">{{ `${item.count}` }}</span
        >次</el-alert
      >
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="clipboardVisible = false">取 消</el-button>
        <!-- <el-button @click="handleDuplic" plain type="primary">去 重</el-button> -->
        <el-button @click="handleCliboardConfirm" type="primary">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 打印 -->
    <el-drawer :with-header="false" size="90%" v-model="printDrawer">
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="10">
          <el-card style="height: 100%">
            <avue-form :option="printOption" v-model="printForm">
              <template #productList>
                <avue-crud
                  :option="productOption"
                  @selection-change="selectionChange"
                  :data="productList"
                  ref="productRef"
                >
                  <template #number="{ row }">
                    <el-input
                      size="small"
                      style="width: 90%"
                      controls-position="right"
                      v-model="row.number"
                    ></el-input>
                  </template>
                </avue-crud>
              </template>
            </avue-form>
          </el-card>
        </el-col>
        <el-col :span="14">
          <el-card style="padding: 0; overflow-y: scroll; height: 100%">
            <div id="printBox">
              <div
                class="header"
                style="display: flex; align-items: center; border: 1px dashed #ccc"
              >
                <div style="width: 150px; margin-right: 30px">
                  <img
                    style="width: 150px"
                    :src="printForm.companyInfo?.exportLogoUrl"
                    alt="暂无logo"
                  />
                </div>
                <div class="title">
                  <div class="address">
                    {{ printForm.companyInfo?.address }}
                  </div>
                  <div class="phone">电话：{{ printForm.companyInfo?.contactPhone }}</div>
                  <div style="display: flex; align-items: center; justify-content: space-between">
                    <p class="name" style="font-size: 22px; margin-right: 20px; font-weight: bold">
                      {{ printForm.companyInfo?.companyName }}
                    </p>
                    <p class="name">送货单号：{{ printForm.deliveryNumber }}</p>
                  </div>
                </div>
              </div>
              <div style="border: 5px solid #00b0f0; height: 0"></div>
              <table border style="width: 100%">
                <colgroup>
                  <col style="width: 10%" />

                  <col style="width: 30%" />

                  <col style="width: 10%" />

                  <col style="width: 20%" />
                </colgroup>
                <tr>
                  <td style="width: 100px; text-align: right">项目名称：</td>
                  <td>{{ printForm.sealContractName }}</td>
                  <!-- <td></td> -->
                  <td style="width: 100px; text-align: right">客户名称：</td>
                  <td>{{ printForm.customerName }}</td>
                </tr>
                <tr>
                  <td rowspan="2" style="width: 100px; text-align: right">送货地址：</td>
                  <td rowspan="2">{{ printForm.deliveryAddress }}</td>
                  <td style="width: 100px; text-align: right">联 系 人：</td>
                  <td>{{ printForm.deliveryUser }}</td>
                </tr>

                <tr>
                  <!-- <td style="width: 100px">签收日期：</td>
                  <td></td> -->
                  <td style="width: 100px; text-align: right">电 话：</td>
                  <td>{{ printForm.deliveryContact }}</td>
                </tr>
                <tr>
                  <td style="width: 100px; text-align: right">订单号：</td>
                  <td colspan="1">{{ printForm.purchaseCode }}</td>
                  <td style="width: 100px; text-align: right">送货日期：</td>
                  <td colspan="1">{{ printForm.deliveryDate }}</td>
                </tr>
              </table>
              <div style="border: 5px solid #00b0f0; height: 0"></div>
              <table center style="width: 100%; text-align: center" border>
                <thead>
                  <tr>
                    <th style="min-width: 130px">商品编码</th>
                    <th style="min-width: 130px">商品名称</th>
                    <th style="min-width: 50px">品牌</th>
                    <th style="min-width: 130px">型号规格</th>
                    <th style="min-width: 50px">单位</th>
                    <th style="min-width: 50px">数量</th>
                    <th v-if="printForm.type == 1" style="min-width: 100px">单价</th>
                    <th v-if="printForm.type == 1" style="min-width: 100px">金额</th>
                    <th>备注</th>
                  </tr>
                </thead>
                <tbody>
                  <tr v-for="item in selectList">
                    <td>{{ item.product?.productCode }}</td>
                    <td>{{ item.customProductName }}</td>
                    <td>{{ item.productBrand }}</td>
                    <td>{{ item.customProductSpecification }}</td>
                    <td>{{ item.customUnit || item.product?.unitName }}</td>
                    <td>{{ item.number }}</td>
                    <td v-if="printForm.type == 1">{{ item.sealPrice }}</td>
                    <td v-if="printForm.type == 1">{{ item.number * 1 * item.sealPrice }}</td>
                    <td style="max-width: 150px" contenteditable="true"></td>
                  </tr>
                  <tr v-if="printForm.type == 1">
                    <td>合计金额：（RMB）</td>
                    <td style="text-align: left" colspan="5">（大写）{{ totalPriceText() }}</td>
                    <td style="text-align: left">（小写）</td>
                    <td>{{ totalPrice().toFixed(2) }}</td>
                  </tr>
                </tbody>
              </table>
              <h4>备注：</h4>
              <p>
                {{ printForm.remark }}
              </p>
              <el-row :gutter="20">
                <el-col :span="12" style="border-bottom: 2px solid black">
                  <span>送（发）货经手人签字：</span>
                </el-col>
                <el-col :span="12" style="border-bottom: 2px solid black">
                  <span>客户签收：</span>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <el-button @click="printDrawer = false">取消</el-button>
        <!-- <el-button @click="exportDocx" type="primary">导出为word</el-button> -->
        <el-button type="primary" v-print="print">确定 打印</el-button>
        <el-button type="primary" @click="exportExcel">导出 excel</el-button>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import moment from 'moment';
import { DX } from '@/utils/util';
import { nextTick } from 'vue';
let route = useRoute();
let router = useRouter();
let form = ref({
  detailDTOList: [],
  files: [],
  outDate: moment(new Date()).format('YYYY-MM-DD'),
});
let { proxy } = getCurrentInstance();
let isEdit = ref(true);
let option = ref({
  submitBtn: true,
  labelWidth: 140,
  detail: false,
  emptyBtn: true,
  column: [
    {
      label: '出库人',
      prop: 'userName',
      disabled: true,
      value: proxy.$store.getters.userInfo.real_name,
    },
    {
      label: '出库日期',
      prop: 'outDate',
      type: 'date',

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      value: new Date(),
    },
    {
      label: '出库类型',
      prop: 'outStorageType',
      type: 'radio',
      value: 0,
      dicData: [
        {
          value: 0,
          label: '订单出库',
        },
        {
          value: 1,
          label: '借测出库',
        },
        // {
        //   value: 3,
        //   label: '采购退货',
        // },
        {
          value: 2,
          label: '其他',
        },

        // {
        //   value: 4,
        //   label: '其他',
        // },
      ],
      rules: [{ required: true, message: '请选择入库类型' }],
      control: val => {
        form.value.detailDTOList = [];
        return {
          orderId: {
            display: val == 0,
          },
          customerId: {
            display: val != 0 && val != 3,
          },
          supplierId: {
            display: val == 3,
          },
        };
      },
    },
    {
      label: '关联订单',
      prop: 'orderId',
      value: route.query.orderId,
      control: val => {
        if (!val) return;
        getDetailList(val);
        getOrderDetail(val);
      },
      rules: [
        {
          required: true,
        },
      ],
      component: 'wf-order-select',
      params: {
        Url: '/api/vt-admin/purchaseOrder/pageForOut',
      },
      // format: 'YYYY-MM-DD',
      // valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '客户名称',
      prop: 'customerId',
      overHidden: true,
      span: 12,
      component: 'wf-customer-select',
    },
    {
      label: '供应商名称',
      prop: 'supplierId',
      overHidden: true,
      span: 12,
      component: 'wf-supplier-select',
    },
    {
      label: '收货人',
      prop: 'addressee',
    },
    {
      label: '收件人地址',
      prop: 'addresseeAddress',
    },
    {
      label: '收件人电话',
      prop: 'addresseePhone',
    },
    {
      label: '快递单号',
      prop: 'expressNumber',
    },
    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    {
      label: '产品信息',
      prop: 'product',
      slot: true,
      span: 24,
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
});

function submit(form, done) {
  console.log(form.detailDTOList);
  const {
    addressee,
    addresseeAddress,
    addresseePhone,
    expressNumber,
    outStorageType,
    customerId,
    outDate,
    supplierId,
  } = form;
  const data = {
    orderId: form.orderId,
    files: form.files.map(item => item.value).join(','),
    remark: form.remark,
    addressee,
    addresseeAddress,
    addresseePhone,
    expressNumber,
    outStorageType,
    customerId,
    supplierId,
    outDate,
    detailDTOList: form.detailDTOList
      .map(item => {
        return {
          orderDetailId: item.id,
          productId: item.productId,
          id: null,
          costPrice: item.costPrice,
          contractDetailId: item.contractDetailId,
          number: item.outStorageNumber,
          productSerialNumber: item.productSerialNumber,
        };
      })
      .filter(item => item.number > 0),
  };
  console.log(form.detailDTOList);
  axios.post('/api/vt-admin/purchaseOutStorage/save', data).then(
    res => {
      proxy.$message.success('出库成功');
      proxy.$router.$avueRouter.closeTag();
      proxy.$router.back();
    },
    err => {
      done();
    }
  );
}
function getDetail(id) {
  axios
    .get('/api/vt-admin/offer/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      const {
        detailVOList,
        // id: businessOpportunityId,
        customerId,
        businessOpportunityId,
        contactPerson,
        offerName,
      } = res.data.data;
      form.value.detailList = detailVOList.map(item => {
        delete item.product.id;
        return {
          number: item.number,
          ...item.product,
          productId: item.productId,
          id: item.id,
        };
      });
      form.value.businessOpportunityId = businessOpportunityId;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = offerName;
    });
}
function addProduct() {
  proxy.$refs['product-select'].visible = true;
}
function productSum(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => Number(item[column.property]));

      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'totalPrice') {
      const values = data.map(item => Number(item.number * item.unitPrice));

      sums[index] = `￥ ${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    }
  });

  return sums;
}

function getDetailList(id) {
  axios
    .get('/api/vt-admin/purchaseOrder/detailForOutStorage', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.detailDTOList = res.data.data.detailList.map(item => {
        return {
          ...item.productVO,
          ...item,

          outStorageNumber: 0,
          productSerialNumber: item.serialNumber,
        };
      });
    });
}
function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      form.value.detailDTOList.push({
        ...r.data.data,
        productId: r.data.data.id,
        number: 1,
        id: null,
        inStorageAddress: 0,
        supplierId: null,
      });
    });
  });
}
function getOrderDetail(id) {
  axios.get('/api/vt-admin/purchaseOrder/detail?id=' + id).then(r => {
    form.value.addresseeAddress = r.data.data.deliveryAddress;
    form.value.addressee = r.data.data.deliveryUser;
    form.value.addresseePhone = r.data.data.deliveryContact;
  });
}
let clipboardVisible = ref(false);
let clipboardText = ref('');
let duplicData = ref([]);
let splitData = ref([]);
let currentIndex = ref(null);
function handleCopy(row, index) {
  currentIndex.value = index;
  clipboardVisible.value = true;
  clipboardText.value = row.productSerialNumber;
  if (!clipboardText.value) {
    navigator.clipboard
      .readText()
      .then(clipboardData => {
        proxy.$message.success('自动读取粘贴板内容');
        console.log('剪贴板内容:', clipboardData);
        console.log();

        clipboardText.value = clipboardData;
        handleData();
      })
      .catch(err => {
        console.error('无法读取剪贴板内容:', err);
      });
  } else {
    handleData();
  }
  console.log(form.value.detailDTOList[currentIndex.value]);
}
function handleData() {
  splitData.value = clipboardText.value.split('\n').map(item => {
    return item.replace('\r', '');
  });

  duplicData.value = countDuplicates(splitData.value); // 统计重复数据
}
function handleCliboardConfirm() {
  const { outStorageNumber } = form.value.detailDTOList[currentIndex.value];

  console.log(splitData.value);
  if (splitData.value.length != outStorageNumber)
    return proxy.$message.error('序列号数量和入库数量不一致，请核对');
  // const { id, supplierName, productName, supplierId, contractDetailId, productVO,productId } =
  //   form.value.detailDTOList[currentIndex.value];
  // form.value.detailDTOList[currentIndex.value].splitList = splitData.value.map(item => {
  //   return {
  //     id,
  //     supplierName,
  //     supplierId,
  //     productName,
  //     contractDetailId,
  //     productId: productVO? productVO.id : productId,
  //     inStorageAddress:0,
  //     uuid: Date.now(),
  //     inStorageNumber: 1,
  //     productSerialNumber: item,
  //   };
  // });
  // addForm.value.splitList = form.value.detailDTOList[currentIndex.value].splitList;
  form.value.detailDTOList[currentIndex.value].productSerialNumber = clipboardText.value;
  // dialogVisible.value = true;
  clipboardVisible.value = false;
}
function countDuplicates(arr) {
  const countMap = {};

  arr.forEach(item => {
    if (countMap[item]) {
      // 如果值已存在于countMap中，则增加计数
      countMap[item]++;
    } else {
      // 否则将其添加到countMap中，初始计数为1
      countMap[item] = 1;
    }
  });

  // 从countMap中提取重复值和它们的计数
  const result = Object.entries(countMap)
    .filter(([key, value]) => value > 1)
    .map(([key, value]) => ({ value: key, count: value }));

  return result;
}

// 打印
let printDrawer = ref(false);
let companyList = ref([]);
let printForm = ref({});
let printOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: {
    deliverCompany: {
      label: '发货公司',
      type: 'select',
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        companyList.value = res.data.records;
        printForm.value.deliverCompany = companyList.value[0].id;
        return res.data.records;
      },
      span: 24,
      change: val => {
        const data = companyList.value.find(item => item.id === val.value);
        printForm.value.companyInfo = data;
      },
      overHidden: true,
      cell: false,

      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    deliveryDate: {
      span: 24,
      label: '送货时间',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    type: {
      label: '类型',
      type: 'radio',
      span: 24,
      value: 1,
      dicData: [
        {
          value: 1,
          label: '有价格',
        },
        {
          value: 0,
          label: '无价格',
        },
      ],
    },
    productList: {
      label: '产品列表',
      type: 'input',
      span: 24,
    },
    remark: {
      span: 24,
      label: '备注',
      type: 'textarea',
      overHidden: true,
    },
  },
});

let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
}
const print = ref({
  id: 'printBox',
});
function printOrder(row) {
  if (form.value.detailDTOList.filter(item => item.outStorageNumber > 0).length == 0) {
    return proxy.$message.error('请选择出库数量');
  }

  axios
    .get('/api/vt-admin/purchaseOrder/print', {
      params: {
        id: route.query.orderId,
      },
    })
    .then(res => {
      printDrawer.value = true;
      getOrderDetail1(route.query.orderId);
      printForm.value = {
        ...res.data.data,
        id: route.query.orderId,
        deliveryNumber: res.data.data.deliveryNumber,
        type: 0,
      };
    });
}
let productList = ref([]);
function getOrderDetail1(id) {
  axios
    .get('/api/vt-admin/purchaseOrder/printList', {
      params: {
        id: id,
      },
    })
    .then(res => {
      const outProductList = form.value.detailDTOList.filter(item => item.outStorageNumber > 0);
      debugger;
      productList.value = [
        ...res.data.data
          .filter(item => outProductList.some(i => i.detailId == item.id))
          .map(item => {
            return {
              ...item,
              number: outProductList.find(i => i.detailId == item.id).outStorageNumber,
            };
          }),
      ];
      proxy.$refs.productRef.$refs.table.toggleAllSelection();
    });
}
let productOption = {
  menu: false,
  selection: false,
  tip: false,
  border: true,
  header: false,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
      formatter: row => {
        return row.customProductName || row.productVO?.productName;
      },
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
      formatter: row => {
        return row.customProductSpecification || row.productVO?.productSpecification;
      },
    },

    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
      formatter: row => {
        return row.productBrand || row.productVO?.productBrand;
      },
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      width: 80,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],

      prop: 'customUnit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
      formatter: row => {
        return row.customUnit || row.productVO?.unitName;
      },
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'inpput',
      overHidden: true,
    },
  ],
};
function total() {
  return selectList.value.reduce((a, b) => {
    a += b.zhhsze * 1;
    return a;
  }, 0);
}
function totalPriceText() {
  return DX(total());
}
function totalPrice() {
  return total();
}
function exportExcel() {
  const {
    deliverCompany: companyName,
    type: isHasPrice,
    deliveryDate,
    remark,
    id,
  } = printForm.value;
  const data = {
    companyName,
    isHasPrice,
    deliveryDate,
    remark,
    id,
    detailDTOList: selectList.value,
  };
  axios({
    url: '/api/vt-admin/purchaseOrder/printDeliveryNote',
    data,
    method: 'post',
  }).then(res => {
    window.open(res.data.data);
  });
}
</script>

<style lang="scss" scoped></style>
