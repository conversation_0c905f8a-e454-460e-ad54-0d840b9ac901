<template>
  <ul class="ml-6 mt-2 space-y-1">
    <li v-for="category in categories" :key="category.id">
      <div 
        class="category-item flex items-center justify-between p-2 rounded cursor-pointer transition-colors duration-200"
        :class="{ 'bg-blue-50 text-blue-600': selectedCategory === category.id }"
        @click.stop="handleCategoryClick(category)"
      >
        <div class="flex items-center">
          <span 
            v-if="category[childrenField] && category[childrenField].length > 0" 
            class="mr-2 transition-transform duration-200"
            :class="{ 'rotate-45': isCategoryExpanded(category.id) }"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </span>
          <span>{{ category[labelField] }}</span>
        </div>
      </div>
      
      <!-- 递归显示子分类 -->
      <category-tree
        v-if="category[childrenField] && category[childrenField].length > 0 && isCategoryExpanded(category.id)"
        :categories="category[childrenField]"
        :selected-category="selectedCategory"
        :expanded-categories="expandedCategories"
        :label-field="labelField"
        :children-field="childrenField"
        @category-click="$emit('category-click', $event)"
      />
    </li>
  </ul>
</template>

<script setup>
import { defineProps, defineEmits } from 'vue';

// 定义组件属性
const props = defineProps({
  categories: {
    type: Array,
    required: true
  },
  selectedCategory: {
    type: [String, Number],
    default: null
  },
  expandedCategories: {
    type: Set,
    required: true
  },
  labelField: {
    type: String,
    default: 'name'
  },
  childrenField: {
    type: String,
    default: 'children'
  }
});

// 定义事件
const emit = defineEmits(['category-click']);

// 检查分类是否展开
const isCategoryExpanded = (categoryId) => {
  return props.expandedCategories.has(categoryId);
};

// 处理分类点击
const handleCategoryClick = (category) => {
  emit('category-click', category);
};
</script>

<style scoped>
.category-item:hover {
  background-color: #f3f4f6;
}
</style>