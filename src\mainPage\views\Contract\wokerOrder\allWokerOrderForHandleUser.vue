<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div class="stats-container" style="display: flex;">
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><document /></el-icon>
              工单总数
            </div>
            <el-text class="stat-value" type="primary">{{ totalNum }}</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><clock /></el-icon>
              进行中的工单
            </div>
            <el-text class="stat-value" type="warning">{{ inProgressNum }}</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><circle-check /></el-icon>
              已完成工单
            </div>
            <el-text class="stat-value" type="success">{{ completeNum }}</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><alarm-clock /></el-icon>
              总工时
            </div>
            <el-text class="stat-value" type="info">{{ completeHours }}h</el-text>
          </div>
          <div class="stat-item">
            <div class="stat-label">
              <el-icon><coin /></el-icon>
              工单总金额
            </div>
            <el-text class="stat-value" type="danger">¥{{ totalPrice }}</el-text>
          </div>
          <div class="stat-item">
  <div class="stat-label">
    <el-icon><money /></el-icon>
    已结算金额
  </div>
  <el-text class="stat-value" type="success">¥{{ paymentPrice }}</el-text>
</div>
          <div class="stat-item">
  <div class="stat-label">
    <el-icon><wallet /></el-icon>
    未结算金额
  </div>
  <el-text class="stat-value" type="danger">¥{{ noPaymentPrice }}</el-text>
</div>
        </div>
      </template>
      <template #totalNum="{ row }">
        <el-link
          type="primary"
          :underline="false"
          @click="viewDetail(row)"
          href=""
          target="_blank"
          >{{ row.totalNum }}</el-link
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <el-drawer title="详情" v-model="drawer" size="80%">
      <allWokerOrderForOrder :query="currentQuery"></allWokerOrderForOrder>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import { Document, Clock, CircleCheck, Coin, Money, AlarmClock, Wallet } from '@element-plus/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import allWokerOrderForOrder from './allWokerOrderForOrder.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  menu: false,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,

  column: [
    {
      label: '工程师',
      prop: 'handleUserName',

      display: false,
      search: true,
    },
    {
      label: '发布时间',
      prop: 'createTime',
      type: 'date',
      search: true,
      hide: true,
      component: 'wf-daterange-search',
      searchSpan: 6,
    },
    {
      label: '工单总数',
      prop: 'totalNum',
      type: 'number',
    },
    {
      label: '进行中的工单',
      prop: 'inProgressNum',
      type: 'number',
    },
    {
      label: '已完成工单',
      prop: 'completeNum',
      type: 'number',
    },
    {
      label: '总工时',
      prop: 'completeHours',
      type: 'number',
    },
    {
      label: '工单金额',
      prop: 'totalPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,

      labelWidth: 120,
      display: false,
    },
    {
      label: '工单已结算金额',
      prop: 'paymentPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,

      labelWidth: 120,
      display: false,
    },
    {
      label: '工单未结算金额',
      prop: 'noPaymentPrice',
      placeholder: '请输入工单价格',
      type: 'number',
      span: 12,

      labelWidth: 120,
      display: false,
    },
  ],
});
let form = ref({
  externalHandleUserList: [],
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/sealContractObject/technologyUserStatistics';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNum = ref(0); // 工单总数
let inProgressNum = ref(0); // 进行中工单数
let completeNum = ref(0); // 完成工单数
let completeHours = ref(0); // 完成工时
let totalPrice = ref(0); // 工单总金额
let paymentPrice = ref(0); // 工单已结算金额
let noPaymentPrice = ref(0); // 工单未结算金额
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',

        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });

  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractObject/technologyUserStatisticsMap', {
      params: {
        startDateStr:
          params.value.createTime && params.value.createTime[0] ? params.value.createTime[0] : '',
        endDateStr:
          params.value.createTime && params.value.createTime[1] ? params.value.createTime[1] : '',
        ...params.value,
        createTime: null,
      },
    })
    .then(res => {
      totalNum.value = res.data.data.totalNum;
      inProgressNum.value = res.data.data.inProgressNum;
      completeNum.value = res.data.data.completeNum;
      completeHours.value = res.data.data.completeHours;
      totalPrice.value = res.data.data.totalPrice;
      paymentPrice.value = res.data.data.paymentPrice;
      noPaymentPrice.value = res.data.data.noPaymentPrice;
    });
}
let router = useRouter();
function beforeOpen(done, type) {
  if (['add', 'view'].includes(type)) {
    if (type == 'add') {
      option.value.group[0].display = false;
    }
    if (type == 'view') {
      if (form.value.createUser == proxy.$store.getters.userInfo.user_id) {
        option.value.group[0].display = false;
      } else {
        option.value.group[0].display = true;
      }
    }
  } else {
    form.value.files =
      form.value.fileList &&
      form.value.fileList.map(item => {
        // 修正拼写错误，将 reuturn 改为 return
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    if (form.value.externalHandleUser) {
      form.value.externalHandleUserList = [
        {
          id: form.externalHandleUser,
          realName: form.value.handleUserName,
        },
      ];
    }
    form.value.objectName = form.value.objectName || form.value.labelName;
    form.value.remark = form.value.remark || form.value.taskDescription;
    form.value.planTime = form.value.planTime || form.value.planStartTime;
    if (form.value.createUser == proxy.$store.getters.userInfo.user_id) {
      option.value.group[0].display = false;
      option.value.updateBtnText = '提交';
    } else {
      option.value.group[0].display = true;
      option.value.updateBtnText = '派单';
    }
  }
  done();
}
function rowSave(form, done, loading) {
  if (form.sealContractId) {
    const data = {
      ...form,

      files: form.files && form.files.map(item => item.url).join(','),
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  } else {
    addContract(form, done, loading);
  }
}

function addContract(form, done, loading) {
  const data = {
    ...form,
    finalCustomerConcat: form.contact,
    finalCustomerPhone: form.contactPhone,
    contractType: 2,
    deliveryAddress: form.distributionAddress,
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post('/api/vt-admin/sealContract/saveWorkOrderSealContract', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
        form.sealContractId = res.data.data;
        rowSave(form, done, loading);
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const url =
    row.createUser == proxy.$store.getters.userInfo.user_id
      ? updateUrl
      : '/api/vt-admin/sealContractObject/sendOrder';
  ;
  const data = {
    ...row,
    files: row.files && row.files.map(item => item.value).join(','),
  };
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

let drawer = ref(false);
let currentRow = ref({});
let currentQuery = ref({});
function viewDetail(row) {
  currentRow.value = {
    ...row,
  };
  currentQuery.value = {
    ...params.value,
    handleUserName: row.handleUserName,
  };
  nextTick(() => {
    drawer.value = true;
  });
}
</script>

<style lang="scss" scoped>
.stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1.5rem;
  padding: 5px;
  background: #f8fafc;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: transform 0.2s;
}

.stat-item:hover {
  transform: translateY(-2px);
}

.stat-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #64748b;
  margin-bottom: 0.5rem;
}

.stat-value {
  font-size: 1.4rem;
  font-weight: 600;
  letter-spacing: -0.025em;
}

.el-icon {
  font-size: 1.2rem;
  color: #3b82f6;
}
</style>
