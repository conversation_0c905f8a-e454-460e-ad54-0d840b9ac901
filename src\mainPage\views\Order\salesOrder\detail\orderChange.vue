<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #menu-left>
      <el-button type="primary" @click="handleAdd" icon="plus">新增</el-button>
    </template>
    <template #menu="{ row }">
      <el-button
        text
        type="primary"
        @click="handleComplete(row)"
        v-if="row.deliveryContact == 0"
        icon="CircleCheckFilled"
        >完成</el-button
      >
      <el-button text type="primary" @click="edit(row)" icon="edit" v-if="row.changeStatus == 0"
        >编辑</el-button
      >
      <el-button
        text
        type="primary"
        @click="complete(row)"
        icon="SuccessFilled"
        v-if="row.changeStatus == 0"
        >完成</el-button
      >
      <el-button text type="primary" @click="handleView(row)" icon="view">详情</el-button>
      <el-button type="primary" text icon="Printer" @click="printChange(row)">打印</el-button>
    </template>
    <template #files="{ row }">
      <file :fileList="row.attachList"></file>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-drawer v-model="drawer" destroy-on-close size="95%" title="新增变更">
    <el-row style="height: 100%" :gutter="8">
      <el-col style="height: 100%" :span="10">
        <el-card class="box-card" shadow="never" style="height: 100%; overflow: auto">
          <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
          </avue-form>
        </el-card>
      </el-col>

      <el-col style="height: 100%" :span="14">
        <el-card class="box-card" shadow="never" style="height: 100%">
          <avue-crud
            :option="selectProductOption"
            @row-del="productRowDel"
            :data="selectProductList"
          >
            <template #menu-left>
              <el-button type="primary" icon="plus" @click="addProduct">选择产品</el-button>
              <el-button type="primary" icon="plus" @click="$refs.wfProductSelectRef.visible = true"
                >添加产品</el-button
              >
            </template>
            <template #number="{ row }">
              <el-input-number
                :disabled="isDetail"
                :min="0"
                size="small"
                style="width: 80%"
                v-model="row.number"
              ></el-input-number>
            </template>
            <template #changeType="{ row }">
              <avue-select
                v-model="row.changeType"
                :disabled="row.uuid || isDetail"
                placeholder="请选择内容"
                :dic="[
                  { value: 0, label: '增加' },
                  { value: 2, label: '减少' },
                ]"
              ></avue-select>
            </template>
            <template #zhhsdj="{ row }">
              <el-input
                :min="0"
                size="small"
                :disabled="isDetail"
                style="width: 100%"
                v-model="row.sealPrice"
              ></el-input>
            </template>
            <!-- <template #classify="{ row }">
              <div v-if="row.id">
                <span>{{ row.classify }}</span>
              </div>
              <avue-input-tree
                default-expand-all
                v-else
                v-model="row.classify"
                :parent="false"
                :node-click="
                  node => {
                    classifyNodeClick(node, row);
                  }
                "
                placeholder="请选择内容"
                :dic="$refs.productSelectRef.treeData"
              ></avue-input-tree>
            </template> -->
          </avue-crud>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <div style="flex: auto">
        <!-- <el-button type="primary" v-if="!isDetail" @click="handlePrint">预览 打印</el-button> -->
        <el-button type="primary" v-if="!isDetail" @click="$refs.addFormRef.submit()"
          >确认 {{ form.id ? '编辑' : '新增' }}</el-button
        >
      </div>
    </template>
    
  </el-drawer>
  <wfProductSelect
    ref="wfProductSelectRef"
    @onConfirm="handleProductSelectConfirm"
  ></wfProductSelect>
  <el-drawer v-model="printDrawer" :with-header="false" size="50%">
    <div id="print_box" style="height: calc(100% - 50px)">
      <h3 style="text-align: center; text-decoration: underline">变更签证单</h3>
      <div style="display: flex; justify-content: space-between">
        <div>编制人：{{ projectForm.technologyUserName }}</div>
        <div>项目编号：{{ projectForm.projectCode }}</div>
      </div>
      <table border="1" style="width: 100%">
        <colgroup>
          <col style="width: 20%" />
        </colgroup>
        <tbody>
          <tr>
            <td>项目名称：</td>
            <td style="font-weight: bold">{{ projectForm.projectName }}</td>
          </tr>
          <tr>
            <td>变更原因：</td>
            <td>{{ form.changeReason }}</td>
          </tr>
        </tbody>
      </table>
      <table border="1" v-if="printData.addList.length > 0" style="width: 100%">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
        </colgroup>
        <thead>
          <tr>
            <th colspan="5" style="text-align: left">一、增项内容</th>
          </tr>
          <tr>
            <th>产品名称</th>
            <th>规格型号</th>
            <th>品牌</th>
            <th>数量</th>
            <th>单价</th>
            <th>金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in printData.addList">
            <td>{{ item.productName }}</td>
            <td>{{ item.productSpecification }}</td>
            <td>{{ item.productBrand }}</td>
            <td>{{ item.number }}</td>
            <td>{{ (item.sealPrice * 1).toFixed(2) }}</td>
            <td>{{ (item.number * item.sealPrice).toFixed(2) }}</td>
          </tr>
          <tr>
            <td style="text-align: center">合计(A=)</td>
            <td style="text-align: right" colspan="5">￥{{ printData.addTotal.toFixed(2) }}</td>
          </tr>
        </tbody>
      </table>
      <table border="1" v-if="printData.reduceList.length > 0" style="width: 100%">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 20%" />
          <col style="width: 20%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
          <col style="width: 10%" />
        </colgroup>
        <thead>
          <tr>
            <th colspan="5" style="text-align: left">
              {{ printData.addList.length > 0 ? '二' : '一' }}、减项内容
            </th>
          </tr>
          <tr>
            <th>产品名称</th>
            <th>规格型号</th>
            <th>品牌</th>
            <th>数量</th>
            <th>单价</th>
            <th>金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in printData.reduceList">
            <td>{{ item.productName }}</td>
            <td>{{ item.productSpecification }}</td>
            <td>{{ item.productBrand }}</td>
            <td>{{ item.number }}</td>
            <td>{{ (item.sealPrice * 1).toFixed(2) }}</td>
            <td>{{ (item.number * item.sealPrice).toFixed(2) }}</td>
          </tr>
          <tr>
            <td style="text-align: center">合计(B=)</td>
            <td style="text-align: right" colspan="5">
              ￥ -{{ printData.reduceTotal.toFixed(2) }}
            </td>
          </tr>
        </tbody>
      </table>
      <table border="1" v-if="printData.otherList.length > 0" style="width: 100%">
        <colgroup>
          <col style="width: 30%" />
          <col style="width: 30%" />
          <col style="width: 30%" />
        </colgroup>
        <thead>
          <tr>
            <th colspan="5" style="text-align: left">
              {{
                printData.addList.length > 0 && printData.reduceList.length > 0
                  ? '三'
                  : printData.addList.length > 0 || printData.reduceList.length > 0
                  ? '二'
                  : '一'
              }}、其他费内容
            </th>
          </tr>
          <tr>
            <th>收费内容</th>
            <th>收费标准(%)</th>
            <th>金额</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="item in printData.otherList">
            <td>{{ item.productName }}</td>
            <td>{{ item.chargePercentage }}</td>
            <td>{{ (item.totalPrice * 1).toFixed(2) }}</td>
          </tr>
          <tr>
            <td style="text-align: center">合计(C=)</td>
            <td style="text-align: right" colspan="5">
              ￥{{ (printData.otherTotal * 1).toFixed(2) }}
            </td>
          </tr>
        </tbody>
      </table>
      <table border="1" style="width: 100%">
        <colgroup>
          <col style="width: 50%" />
          <col style="width: 50%" />
        </colgroup>
        <tbody>
          <tr>
            <td colspan="2" style="text-align: left">
              {{
                ['一', '二', '三', '四'][
                  [
                    printData.addList?.length,
                    printData.reduceList?.length,
                    printData.otherList?.length,
                  ].filter(item => item == true).length
                ]
              }}、合计：{{
                printData.addTotal * 1 - printData.reduceTotal * 1 + printData.otherTotal * 1
              }}
              元，人民币大写：{{
                chieseNumMoney(
                  printData.addTotal * 1 - printData.reduceTotal * 1 + printData.otherTotal * 1 < 0
                    ? (printData.addTotal * 1 - printData.reduceTotal * 1 + printData.otherTotal) *
                        -1
                    : printData.addTotal * 1 - printData.reduceTotal * 1 + printData.otherTotal * 1
                )
              }}
            </td>
          </tr>
          <tr>
            <td style="text-align: left">
              <br />施工单位意见：<br /><br />施工单位：{{ form.constructionRepresentative }}
              <br /><br />施工单位代表： <br /><br />提交日期：<br />
            </td>
            <td style="text-align: left">
              <br />审核单位意见：<br /><br />审核单位：{{ projectForm.customerName }}
              <br /><br />审核单位代表： <br /><br />审核日期：<br />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
    <template #footer>
      <el-button @click="printDrawer = false">取消</el-button>
      <el-button type="primary" v-print="print">确定 打印</el-button>
    </template>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
import productSelect from '../compoents/productSelect.vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { randomLenNum, DX } from '@/utils/util';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 280,
  border: true,
  column: [
  {
      label: '变更类型',
      span: 24,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '退货',
        },
        {
          value: 1,
          label: '换货',
        },
      ],
    },
    {
      label: '变更主题',
      prop: 'title',
      width: 250,
      overHidden: true,
    },
    {
      label: '变更原因',
      prop: 'changeReason',
      overHidden: true,
    },
    {
      label: '变更人',
      prop: 'changeUser',
      type: 'input',
    },
    {
      label: '变更日期',
      prop: 'changeDate',
      searchSpan: 6,
      searchRange: true,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    // {
    //   label: '变更类型',
    //   prop: 'remark',
    //   overHidden: true,
    // },

    // {
    //   label: '备注',
    //   prop: 'deliveryUser',
    // },

    // {
    //   label: '创建人',
    //   prop: 'deliveryAddress',
    //   type: 'select',
    // },
    // {
    //   label: '创建时间',
    //   prop: 'deliveryContact',
    //   searchSpan: 6,
    //   searchRange: true,
    //   type: 'date',
    // },
    {
      label: '变更签证',
      prop: 'files',
      type: 'upload',
      value: [],
      dataType: 'object',
      loadText: '变更签证上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '变更状态',
      prop: 'changeStatus',
      dicData: [
        {
          value: 0,
          label: '申请中',
        },
        {
          value: 1,
          label: '已完成',
        },
        {
          value: 2,
          label: '已取消',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  projectPrice: String,
});
const emit = defineEmits(['success']);
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectChange/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

//   请购新增

let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 100,
  column: [
    {
      label: '变更类型',
      span: 24,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '退货',
        },
        {
          value: 1,
          label: '换货',
        },
      ],
    },
    {
      label: '变更主题',
      prop: 'title',
      width: 250,
      span: 24,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请输入变更主题',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '变更原因',
      prop: 'changeReason',
      span: 24,
      type: 'textarea',
    },
    {
      label: '变更人',
      prop: 'changeUser',
      type: 'input',
      span: 24,
    },
    {
      label: '变更日期',
      prop: 'changeDate',
      searchSpan: 6,
      span: 24,
      searchRange: true,

      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    // {
    //   label: '变更类型',
    //   prop: 'remark',
    //   overHidden: true,
    // },

    // {
    //   label: '备注',
    //   prop: 'deliveryUser',
    // },

    // {
    //   label: '变更签证',
    //   prop: 'files',
    //   type: 'upload',
    //   value: [],
    //   dataType: 'object',
    //   loadText: '变更签证上传中，请稍等',
    //   span: 24,
    //   slot: true,
    //   // align: 'center',
    //   propsHttp: {
    //     res: 'data',
    //     url: 'id',
    //     name: 'originalName',
    //     // home: 'https://www.w3school.com.cn',
    //   },
    //   action: '/blade-resource/attach/upload',
    //   uploadPreview: (res, data, done, file) => {
    //     console.log(res, data, done, file);
    //   },
    // },
    // {
    //   label: '施工单位',
    //   prop: 'constructionRepresentative',
    //   type: 'select',
    //   props: {
    //     label: 'dictValue',
    //     value: 'dictValue',
    //   },
    //   span: 24,
    //   overHidden: true,
    //   cell: false,
    //   dicUrl: '/blade-system/dict-biz/dictionary?code=billingCompany',
    // },
    // {
    //   type: 'dynamic',
    //   prop: 'otherCost',
    //   label: '其他费用',
    //   span: 24,
    //   children: {
    //     column: [
    //       {
    //         label: '收费内容',
    //         prop: 'productName',
    //       },
    //       {
    //         label: '收费标准（%）',
    //         prop: 'chargePercentage',
    //       },
    //       {
    //         label: '小计',
    //         prop: 'totalPrice',
    //         type: 'number',
    //       },
    //     ],
    //   },
    // },
  ],
});

let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  height: 'auto',
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'productName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 90,
      span: 12,
    },
    // {
    //   label: '所属分类',
    //   prop: 'classify',
    // },
    // {
    //   label: '清单数量',
    //   prop: 'deepenNumber',
    //   type: 'number',
    //   align: 'center',
    //   span: 12,
    //   width: 120,
    //   cell: false,
    // },
    {
      label: '变更类型',
      prop: 'changeType',
    },
    {
      label: '变更数量',
      prop: 'number',
      width: 130,
    },
    // {
    //   label: '价格',
    //   prop: 'beforeTotalPrice	',
    //   width: 100,
    // },
    {
      label: '单价',
      prop: 'zhhsdj',
      width: 100,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      formatter: row => {
        return row.number * row.sealPrice;
      },
      width: 100,
    },
  ],
});

let contractList = ref([]);
let currentContractId = ref('');
// 查询客户合同
function queryContract(id) {
  axios
    .get('/api/vt-admin/sealContract/pageForStatement', {
      params: {
        customerId: form.value.customerId,
        ...applyQuery.value,
        ...pageContract.value,
        selectType: 5,
      },
    })
    .then(res => {
      contractList.value = [...contractList.value, ...res.data.data.records];
    });
}

function handleAdd() {
  isDetail.value = false;
  formOption.value.detail = false;
  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();
  });
}
function addProduct() {
  innerDrawer.value = true;
}
let productList = ref([]);
let selectLoading = ref(false);

let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {
  selectProductList.value.push(
    ...proxy.$refs.productSelectRef.selectList.map(i => {
      return {
        ...i,
        productName: i.customProductName,
        productSpecification: i.customProductSpecification,
        unitName: i.product.unitName,
        sealPrice: i.zhhsdj,
        detailId: i.id,
        id: null,
        number: 1,
      };
    })
  );
  //   selectList.value = [];
  // 清空选择
  proxy.$refs.productSelectRef.clearToggle();

  innerDrawer.value = false;
  console.log(selectProductList.value);
}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      if (row.id) {
        selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      } else {
        selectProductList.value = selectProductList.value.filter(item => item.uuid !== row.uuid);
      }
    })
    .catch(() => {});
}

function totalPrice() {
  return {
    addTotal: selectProductList.value
      .filter(item => item.changeType == 0)
      .reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0),
    reduceTotal: selectProductList.value
      .filter(item => item.changeType == 2)
      .reduce((pre, cur) => {
        return pre + cur.number * cur.sealPrice;
      }, 0),
    otherTotal: form.value.otherCost.reduce((pre, cur) => {
      return pre + cur.totalPrice * 1;
    }, 0),
  };
}
function submit(form, done, loading) {
  console.log(form, selectProductList.value);
  const productList = selectProductList.value.map(item => {
    return {
      ...item,
      type: 0,
    };
  });
  const otherList = form.otherCost.map(item => {
    return {
      ...item,
      type: 1,
    };
  });
  const data = {
    ...form,
    detailDTOList: [...productList, ...otherList],
    totalPrice: totalPrice().addTotal - totalPrice().reduceTotal + totalPrice().otherTotal,
    projectId: route.query.id,
  };
  data.createTime = null;
  console.log(data);
  let url = form.id ? '/api/vt-admin/projectChange/update' : '/api/vt-admin/projectChange/save';
  axios
    .post(url, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        done();
        onLoad();
        drawer.value = false;
        clearAll();
        emit('success');
      }
    })
    .catch(() => {
      done();
    });
}
function clearAll() {
  proxy.$refs.addFormRef.resetFields();
  selectProductList.value = [];
  form.id = null;
  form.value = {};
}
let pageContract = ref({
  current: 1,
  size: 20,
});
function loadMore() {
  console.log(1111);
  pageContract.value.current += 1;
  queryContract();
}
function handleProductSelectConfirm(id) {
  axios
    .get('/api/vt-admin/product/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      let data = res.data.data;
      data.number = 1;
      data.productId = res.data.data.id;
      data.id = null;
      data.uuid = randomLenNum(10);
      data.customProductName = data.productName;
      data.customProductSpecification = data.productSpecification;
      data.product = {
        unitName: data.unitName,
      };
      data.changeType = 0;
      selectProductList.value.push(data);
    });
}

function handleComplete(row) {
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'datetime',
          label: '实际收货时间',
          span: 24,
          value: dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDeliveryDate',
        },
        {
          label: '变更签证',
          prop: 'files',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '变更签证上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        files: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectApplyPurchase/complete', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let isDetail = ref(false);
function handleView(row) {
  drawer.value = true;
  isDetail.value = true;
  formOption.value.detail = true;
  drawer.value = true;
}
// function classifyNodeClick(node, row) {
//   row.moduleId = node.parentId;
// }
let printDrawer = ref(false);
const print = ref({
  id: 'print_box',
});
let printData = ref({});
function handlePrint() {
  console.log(form.value.otherCost);
  const data = {
    addList: selectProductList.value.filter(item => item.changeType == 0),
    reduceList: selectProductList.value.filter(item => item.changeType == 2),
    otherList: form.value.otherCost,
    addTotal: totalPrice().addTotal,
    reduceTotal: totalPrice().reduceTotal,
    otherTotal: totalPrice().otherTotal,
  };
  printData.value = data;
  printDrawer.value = true;
}
function totalText() {
  const A = 'A';
  const B = '-B';
  const C = '+C';
  return A + B + C;
}
function chieseNumMoney(number) {
  return DX(number);
}

onMounted(() => {
  getProjectDetail();
});
let projectForm = ref({});
function getProjectDetail() {
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      projectForm.value = res.data.data;
    });
}
function parseData(data) {
  form.value.otherCost = data.filter(item => item.type == 1);
  selectProductList.value = data.filter(item => item.type == 0);
}

// 编辑
function edit(row) {
  parseData(row.detailVOS);
  isDetail.value = false;
  formOption.value.detail = false;
  drawer.value = true;
  form.value = {
    ...form.value,
    ...row,
  };
  console.log(form.value);
}
// 打印
function printChange(row) {
  parseData(row.detailVOS);
  handlePrint();
  form.value = {
    ...row,
  };
}
function complete(row) {
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      labelWidth: 120,
      column: [
        {
          label: '状态',
          prop: 'changeStatus',
          type: 'radio',
          span: 24,
          dicData: [
            {
              value: 1,
              label: '完成',
            },
            {
              value: 2,
              label: '取消',
            },
          ],
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        files: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectChange/complete', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
const indexText = computed(() => value => {});
// function indexText(value) {
//    const arr = [printData.addList?.length > 0,printData.reduceList?.length > 0,printData.otherList?.length > 0].filter(item => item);
//   const arr1 = ['一','二','三','四']
//   if(value > arr.length){
//     return arr1[arr.length]
//   }else{
//     return arr[value]
//   }
// }
</script>

<style lang="scss" scoped>
table td {
  text-align: center;
}
</style>
<style media="print">
@page {
  size: auto;
  margin: 3mm;
}
@media print {
  html {
    height: auto !important;
  }
}
</style>
