<template>
  <div>
    <el-drawer v-model="drawer" size="90%" title="生成询价单">
      <el-row style="height: 100%" :gutter="8">
        <el-col style="height: 100%" :span="10">
          <el-card shadow="hover" class="box-card" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
            </avue-form>
          </el-card>
        </el-col>

        <el-col style="height: 100%" :span="14">
          <el-card class="box-card" shadow="hover" style="height: 100%">
            <avue-crud
              :option="selectProductOption"
              @row-del="productRowDel"
              :data="selectProductList"
            >
              <template #menu-left>
                <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button>
              </template>
              <!-- <template #number="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  size="small"
                  style="width: 80%"
                  v-model="row.number"
                ></el-input-number>
              </template> -->
            </avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="handleSubmit(0)" plain>保存</el-button>
          <el-button type="primary" @click="handleSubmit(1)">保存并提交</el-button>
        </div>
      </template>
      <el-drawer title="选择产品" v-model="innerDrawer" size="70%" append-to-body>
        <el-row style="height: 100%" :gutter="20">
          <el-col style="height: 100%" :span="10">
            <el-card
              class="box-card"
              style="height: 100%; overflow-y: auto"
              v-infinite-scroll="loadMore"
               shadow="hover"
              :infinite-scroll-disabled="oderList.length < 16"
            >
              <el-input
                placeholder="请输入合同名称,订单号模糊搜索"
                @input="handleInput"
                v-model="applyQuery.contractName"
                style="margin-bottom: 10px"
              ></el-input>
              <el-radio-group v-model="form.orderId" @change="handleCheckChange">
                <el-row v-for="item in oderList" style="width: 100%">
                  <el-col :span="24">
                    <el-radio
                      :label="item.id"
                      style="height: 60px; width: 100%; padding: 5px"
                      :style="{
                        border: `1px solid ${
                          form.orderId == item.id ? 'var(--el-color-primary)' : '#ccc'
                        }`,
                      }"
                      ><div>
                        <div><span style="color: #999">订单编号：</span>{{ item.orderNo }}</div>
                        <div><span style="color: #999">报价名称：</span>{{ item.offerName }}</div>

                        <div>
                          <span style="color: #999">业务员：</span>{{ item.businessName || '--' }}
                        </div>
                      </div></el-radio
                    >
                  </el-col>
                </el-row>
              </el-radio-group>
              <el-empty v-if="oderList.length == 0" description="该客户没有合同"></el-empty>
            </el-card>
          </el-col>
          <el-col style="height: 100%" :span="14">
            <el-card  shadow="hover" class="box-card" style="height: 100%">
              <avue-crud
                ref="addAvue"
                :option="productOption"
                v-model:page="pageOption"
                :table-loading="selectLoading"
                @current-change="queryProductByContractId"
                @size-change="queryProductByContractId"
                @refresh-change="queryProductByContractId"
                @selection-change="handleChange"
                :data="productList"
              >
              </avue-crud>
            </el-card>
          </el-col>
        </el-row>
        <template #footer>
          <div style="flex: auto">
            <el-button type="primary" @click="confirmAddProduct">确 认</el-button>
          </div>
        </template>
      </el-drawer>
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, watch } from 'vue';
import { useStore } from 'vuex';
import { deepClone } from '@/utils/util';
const props = defineProps({
  data: {
    type: Object,
    default: () => [],
  },
});
const { proxy } = getCurrentInstance();
const store = useStore();
let form = ref({});
let userInfo = computed(() => store.getters.userInfo);
//   新增询价单
let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
function open() {
  drawer.value = true;
}
defineExpose({
  open,
});
let selectProductList = ref([]);
let addFormRef = ref(null);
watch(
  () => props.data,
  val => {
   
    form.value = deepClone(props.data);
    selectProductList.value = props.data.detailDTOList || [];
    if (!props.data.id && addFormRef.value) {
      addFormRef.value.resetForm();
      form.value.inquirySource = 0;
    }
    
    // if(addFormRef.value){
    //     addFormRef.value.updateDic('supplierContactId')
    // }
  },
  {
    deep: true,
  }
);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 130,
  column: [
    {
      label: '询价类型',
      prop: 'inquirySource',
      overHidden: true,

      type: 'radio',

      span: 24,
      value: 0,
      dicData: [
        {
          label: '采购询价',
          value: 0,
        },
      ],
    },
    {
      label: '询价有效期（天）',
      prop: 'overDays',
      type: 'number',
      width: 150,
      span: 24,
    },
    {
      label: '询价人',
      prop: 'contactPerson',
      span: 24,
      value: userInfo.value.user_id,
      component: 'wf-user-select',
      formatter: row => {
        return row.contactName;
      },
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
      overHidden: true,
    },
    {
      label: '关联供应商',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
          trigger: 'blur',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '关联供应商',
            prop: 'supplierId',
            // search: true,
            rules: [
              {
                required: true,
                message: '请选择供应商',
                trigger: 'blur',
              },
            ],
            component: 'wf-supplier-select',
            cascader: ['supplierContactId'],
            change: (value, a, b) => {
              // setBaseInfo(value);
            },
            overHidden: true,
          },
          {
            label: '供应商联系人',
            prop: 'supplierContactId',
            type: 'select',
            props: {
              value: 'id',
              label: 'concatName',
              desc: 'concatPhone',
            },
            dicFormatter: res => {
              return res.data.supplierConcatVOList;
            },
            cell: true,
            dicUrl: `/api/vt-admin/supplier/detail?id={{key}}`,
            // dicData: [],
            // formatter: row => {
            //   return row.supplierContactName;
            // },
          },
        ],
      },
      hide: true,
    },
  ],
});
let productOption = ref({
  header: true,
  menu: false,
  addBtn: false,
  height: 'auto',
  calcHeight: '20',
  selection: true,
  reserveSelection: true,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'customUnit',

      span: 12,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
  ],
});
let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  height: 'auto',
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 150,
      span: 12,
      cell: true,
    },
    {
      label: '单位',
      prop: 'customUnit',

      span: 12,
    },
  ],
});
let oderList = ref([]);
let currentOrderId = ref('');
// 查询客户合同
function queryOrder(id) {
  axios
    .get('/api/vt-admin/purchaseOrder/page', {
      params: {
        ...applyQuery.value,
        ...pageOrder.value,
        auditType: 0,
      },
    })
    .then(res => {
      oderList.value = [...oderList.value, ...res.data.data.records];
    });
}
function handleInput() {
  oderList.value = [];
  pageOrder.value.current = 1;
  //   queryOrder();
  const fn = debounce(queryOrder, 500);
  fn();
}
function invoiceApply() {
  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();
  });
}
function addProduct() {
  queryOrder();
  innerDrawer.value = true;
}
function handleCheckChange(value) {
  currentOrderId.value = value;
  queryProductByContractId();
}
let productList = ref([]);
let selectLoading = ref(false);
function queryProductByContractId() {
  selectLoading.value = true;
  axios
    .get('/api/vt-admin/purchaseOrder/detailForInquiry', {
      params: {
        id: currentOrderId.value,

        current: pageOption.value.currentPage,
        size: pageOption.value.pageSize,
      },
    })
    .then(res => {
      productList.value = res.data.data.detailList;
      
      selectLoading.value = false;
    });
}
let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {
  selectProductList.value.push(
    ...selectList.value.map(i => {
      return {
        ...i,
        number: i.number * 1,
        product: {
          ...i.product,
        },
      };
    })
  );
  //   selectList.value = [];
  proxy.$refs.addAvue.toggleSelection();
  innerDrawer.value = false;
  setTotalAmount();
}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      setTotalAmount();
    })
    .catch(() => {});
}
function setTotalAmount() {
  const totalPrice = selectProductList.value.reduce(
    (total, item) => (total += item.number * 1 * (item.sealPrice * 1)),
    0
  );
  form.value.invoicePrice = totalPrice;
}
function handleSubmit(value) {
  form.value.inquiryStatus = value;
  proxy.$refs.addFormRef.submit();
}
function submit(form, done, loading) {
  if (selectProductList.value.length == 0) {
    proxy.$message.error('请选择产品');
    done();
    return;
  }

  axios.get('/api/blade-system/user/info').then(res => {
    const data = {
      ...form,
      contactPerson: userInfo.value.user_id,
      contactPhone: res.data.data.phone,
      inquirySource: 0,
      detailDTOList: selectProductList.value.map(item => {
        return {
          ...item,
          id: null,

          productId: item.productId,
          orderDetailId: item.id,
        };
      }),
    };
    data.purchaseId = selectProductList.value
      .reduce((pre, cur) => {
        if (pre.includes(cur.orderId)) return pre;
        return [...pre, cur.orderId];
      }, [])
      .join(',');
    axios
      .post(`/api/vt-admin/purchaseInquiry/${data.id?'update':'save'}`, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          done();

          drawer.value = false;
        }
      })
      .catch(err => {
        done();
      });
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();
  oderList.value = [];
  selectProductList.value = [];
  form.value.createName = proxy.$store.getters.userInfo.nick_name;
  form.value.createTime = dateFormat(new Date(), 'yyyy-MM-dd');
  pageOption.value.currentPage = 1;
  pageOrder.value.current = 1;
}
let pageOrder = ref({
  current: 1,
  size: 20,
});
function loadMore() {
  console.log(1111);
  pageOrder.value.current += 1;
  queryOrder();
}
</script>

<style lang="scss" scoped></style>
