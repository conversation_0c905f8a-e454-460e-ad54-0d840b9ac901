<template>
  <el-select
    style="width: 100%"
    v-model="value"
    multiple
    allow-create
    filterable
    @change="$emit('update:modelValue', $event.join(','))"
    value-key="value"
    placeholder="请选择模板标签"
  >
    <el-option
      v-for="item in options"
      style="display: inline-block; height: auto; line-height: auto; padding: 2px 4px"
      :key="item.value"
      :label="item.label"
      :value="item.value"
    >
      <div>
        <el-tag effect='plain'>{{ item.label }}</el-tag>
      </div>
    </el-option>
  </el-select>
</template>

<script setup>
import { onMounted, ref ,watch} from 'vue';

const options = ref([]);
const value = ref('');
const isAdding = ref(false);
const optionName = ref('');
onMounted(() => {
  getOptionData();
});
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  search:{
    type:Boolean,
    default:false
  }
});
watch(() => props.modelValue, (val) => {
  if(val){
    value.value = val.split(',')
  }else{
    value.value = []
  }
},{
    immediate:true,
});
const getOptionData = () => {
  axios.get('/api/vt-admin/optionTemplate/getLabels').then(res => {
    options.value = res.data.data.map(item => {
      return {
        label: item,
        value: item,
      };
    });
  });
};

const clear = () => {
  isAdding.value = false;
  optionName.value = '';
};
const onConfirm = () => {
  const data = {
    value: optionName.value,
    label: optionName.value,
  };
  options.value.push(data);
};
</script>

<style lang="scss" scoped></style>
