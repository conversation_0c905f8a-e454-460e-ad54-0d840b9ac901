<template>
  <el-dialog title="费用选择" v-model="dialogVisible" top="5vh" width="70%">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      :before-open="beforeOpen"
      ref="crud"
      @keyup.enter="onLoad"
      @selection-change="handleChange"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #expenseFiles="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
      <template #expenseStatus="{row}">
        <el-tag effect='plain' size="mini" :type="row.expenseStatus == 0?'danger':'success'" >
          {{row.$expenseStatus}}
        </el-tag>
        
      </template>
    </avue-crud>
    <template #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
let { proxy } = getCurrentInstance();
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
});
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  reserveSelection:true,
  searchMenuSpan: 4,
  selection: true,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 270,
  border: true,
  menu: false,
  column: [
    {
      label: '关联合同',
      type: 'input',
      component: 'wf-contract-select',
      span: 24,

      prop: 'sealContractId',
      formatter: row => {
        return row.contractName;
      },
      change: value => {
        axios
          .get('/api/vt-admin/sealContract/detail', {
            params: {
              id: value.value,
            },
          })
          .then(res => {
            form.value.customerId = res.data.data.customerId;
          });
      },
    },
    {
      label: '关联客户',
      type: 'input',
      component: 'wf-customer-select',
      span: 24,
      prop: 'customerId',
      formatter: row => {
        return row.customerName;
      }
    },
    {
      type: 'tree',
      label: '费用类型',
      dicUrl: '/blade-system/dict/dictionary-tree?code=expenseType',
      cascader: [],
      span: 12,
      width: 110,
      // search: true,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'expenseType',parent:false,
    },
    // {
    //   type: 'number',
    //   label: '票据张数',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      type: 'number',
      label: '费用金额',
      span: 12,
      display: true,
      width: 110,
      prop: 'expensePrice',
    },

    {
      type: 'textarea',
      label: '用途',
      span: 24,
      display: true,
      prop: 'purpose',
      showWordLimit: true,
    },

    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
    },
    {
      label: '证明附件',
      prop: 'expenseFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      overHidden: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '状态',
      type: 'select',
      
      width:90,
      dicData: [
        {
          value: 0,
          label: '未报销',
        },
        {
          value: 1,
          label: '已报销',
        },
       
      ],
      prop:'expenseStatus'
    },
    {
      type: 'input',
      label: '登记人',
      span: 12,
      width: 110,
      display: true,
      component: 'wf-user-select',
      prop: 'reimbursementUser',
      value: proxy.$store.getters.userInfo.user_id,
      showWordLimit: true,
      formatter: (row, column, cellValue) => {
        return row.reimbursementUserName;
      },
    },
    {
      type: 'date',
      label: '日期',
      span: 12,
      display: true,
      width: 110,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'expenseDate',
      disabled: false,
      readonly: false,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const emits = defineEmits(['handleConfirm'])
const addUrl = '/api/vt-admin/sealContractExpense/save';
const delUrl = '/api/vt-admin/sealContractExpense/remove?ids=';
const updateUrl = '/api/vt-admin/sealContractExpense/update';
const tableUrl = '/api/vt-admin/sealContractExpense/pageForReimbursement';
let params = ref({
    expenseStatus:0,
    selectType: 0,
});
let tableData = ref([]);

let route = useRoute();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    expenseFiles: form.expenseFiles && form.expenseFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    expenseFiles: row.expenseFiles && row.expenseFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.expenseFiles = form.value.attachList.map(item => {
      return {
        label: item.originalName,
        value: item.id,
      };
    });
  }
  done();
}
let dialogVisible = ref(false);
function open() {
  dialogVisible.value = true;
  onLoad()
}
let selectList = ref([])
let crud = ref()
function handleChange(list) {
    selectList.value = list
}
function handleConfirm() {
   
    dialogVisible.value = false
    emits('handleConfirm', selectList.value)
    crud.value.toggleSelection()
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
