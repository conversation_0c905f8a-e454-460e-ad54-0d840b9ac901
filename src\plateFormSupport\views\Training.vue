<template>
  <div class="training-container pt-20 min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-8">
      <div class="flex gap-6">
        <!-- 左侧分类导航 -->
        <div class="w-64 bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-bold text-gray-800 mb-6">培训分类</h2>
          <div class="space-y-2">
            <div 
              v-for="category in categories" 
              :key="category.id"
              @click="selectCategory(category.id)"
              class="cursor-pointer p-3 rounded-lg transition-colors duration-200"
              :class="{
                'bg-blue-600 text-white': selectedCategory === category.id,
                'text-gray-700 hover:bg-gray-100': selectedCategory !== category.id
              }"
            >
              <div class="flex items-center">
                <i :class="category.icon" class="mr-3"></i>
                <span class="font-medium">{{ category.name }}</span>
              </div>
              <div class="text-sm mt-1 opacity-75">
                {{ category.count }} 个培训
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧培训内容 -->
        <div class="flex-1">
          <!-- 搜索栏 -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex items-center gap-4">
              <div class="flex-1 relative">
                <input 
                  v-model="searchQuery"
                  type="text" 
                  placeholder="搜索培训内容..."
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                <i class="fas fa-search absolute right-3 top-3 text-gray-400"></i>
              </div>
              <button class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200">
                搜索
              </button>
            </div>
          </div>

          <!-- 培训列表 -->
          <div class="bg-white rounded-lg shadow-md">
            <div class="p-6 border-b border-gray-200">
              <h3 class="text-lg font-semibold text-gray-800">
                {{ getCurrentCategoryName() }} 
                <span class="text-sm text-gray-500 font-normal">
                  (共 {{ filteredTrainings.length }} 条)
                </span>
              </h3>
            </div>
            
            <!-- 培训卡片列表 -->
            <div class="p-6">
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <div 
                  v-for="training in filteredTrainings" 
                  :key="training.id"
                  class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 overflow-hidden"
                >
                  <!-- 卡片头部 -->
                  <div class="p-4 border-b border-gray-100">
                    <div class="flex items-center justify-between mb-2">
                      <div class="flex items-center">
                        <div class="w-10 h-10 rounded-lg flex items-center justify-center"
                             :class="getIconBgClass(training.type)">
                          <i :class="getFileIcon(training.type)" class="text-lg text-white"></i>
                        </div>
                        <div class="ml-3">
                          <h3 class="font-semibold text-gray-900 text-sm leading-tight">
                            {{ training.name }}
                          </h3>
                          <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium mt-1"
                                :class="getTypeClass(training.type)">
                            {{ training.type }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- 卡片内容 -->
                  <div class="p-4">
                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                      {{ training.description }}
                    </p>
                    
                    <!-- 培训信息 -->
                    <div class="space-y-2 mb-4">
                      <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-user mr-2"></i>
                        <span>创建者: {{ training.creator }}</span>
                      </div>
                      <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-calendar mr-2"></i>
                        <span>{{ training.createTime }}</span>
                      </div>
                      <div class="flex items-center text-xs text-gray-500">
                        <i class="fas fa-file-alt mr-2"></i>
                        <span>大小: {{ training.size }}</span>
                      </div>
                    </div>
                    
                    <!-- 操作按钮 -->
                    <div class="flex items-center justify-between pt-3 border-t border-gray-100">
                      <button class="text-blue-600 hover:text-blue-800 text-xs font-medium px-3 py-1 rounded-md hover:bg-blue-50 transition-colors duration-200">
                        <i class="fas fa-eye mr-1"></i>
                        预览
                      </button>
                      <button class="text-green-600 hover:text-green-800 text-xs font-medium px-3 py-1 rounded-md hover:bg-green-50 transition-colors duration-200">
                        <i class="fas fa-download mr-1"></i>
                        查看
                      </button>
                      <button class="text-red-600 hover:text-red-800 text-xs font-medium px-3 py-1 rounded-md hover:bg-red-50 transition-colors duration-200">
                        <i class="fas fa-trash mr-1"></i>
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- 空状态 -->
              <div v-if="filteredTrainings.length === 0" class="text-center py-12">
                <div class="text-gray-400 text-4xl mb-4">
                  <i class="fas fa-folder-open"></i>
                </div>
                <p class="text-gray-500 text-lg">暂无培训内容</p>
                <p class="text-gray-400 text-sm mt-2">请尝试调整搜索条件或选择其他分类</p>
              </div>
            </div>

            <!-- 分页 -->
            <div class="px-6 py-4 border-t border-gray-200 flex items-center justify-between">
              <div class="text-sm text-gray-600">
                共 {{ filteredTrainings.length }} 条
              </div>
              <div class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">前往</span>
                <input 
                  type="number" 
                  min="1" 
                  :max="totalPages"
                  v-model="currentPage"
                  class="w-16 px-2 py-1 border border-gray-300 rounded text-center text-sm"
                >
                <span class="text-sm text-gray-600">页</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 响应式数据
const selectedCategory = ref('all')
const searchQuery = ref('')
const currentPage = ref(1)

// 培训分类数据
const categories = ref([
  { id: 'all', name: '全部培训', icon: 'fas fa-list', count: 15 },
  { id: 'tech', name: '技术培训', icon: 'fas fa-code', count: 8 },
  { id: 'product', name: '产品培训', icon: 'fas fa-box', count: 4 },
  { id: 'business', name: '企业培训', icon: 'fas fa-building', count: 3 },
  { id: 'hr', name: '入职培训', icon: 'fas fa-users', count: 2 },
  { id: 'finance', name: '年终总结', icon: 'fas fa-chart-line', count: 1 },
  { id: 'tech-summary', name: '技术年终总结', icon: 'fas fa-laptop-code', count: 1 },
  { id: 'sales', name: '商务培训', icon: 'fas fa-handshake', count: 1 },
  { id: 'business-dept', name: '业务部', icon: 'fas fa-briefcase', count: 1 }
])

// 模拟培训数据
const trainings = ref([
  {
    id: 1,
    name: 'Vue.js 基础培训',
    description: 'Vue.js 框架基础知识和实战应用',
    creator: 'admin',
    createTime: '2025-01-18',
    type: 'video',
    size: '125MB',
    category: 'tech'
  },
  {
    id: 2,
    name: 'React 进阶培训',
    description: 'React 高级特性和性能优化',
    creator: 'admin',
    createTime: '2025-01-17',
    type: 'doc',
    size: '2.5MB',
    category: 'tech'
  },
  {
    id: 3,
    name: '产品设计思维',
    description: '产品设计方法论和用户体验',
    creator: 'admin',
    createTime: '2025-01-16',
    type: 'ppt',
    size: '15MB',
    category: 'product'
  },
  {
    id: 4,
    name: 'JavaScript 高级编程',
    description: 'JavaScript 深入理解和最佳实践',
    creator: 'admin',
    createTime: '2025-01-15',
    type: 'video',
    size: '200MB',
    category: 'tech'
  },
  {
    id: 5,
    name: '企业文化培训',
    description: '公司文化价值观和行为准则',
    creator: 'admin',
    createTime: '2025-01-14',
    type: 'doc',
    size: '1.2MB',
    category: 'business'
  },
  {
    id: 6,
    name: '新员工入职指南',
    description: '新员工入职流程和注意事项',
    creator: 'admin',
    createTime: '2025-01-13',
    type: 'pdf',
    size: '3.8MB',
    category: 'hr'
  },
  {
    id: 7,
    name: 'Node.js 后端开发',
    description: 'Node.js 服务端开发实战',
    creator: 'admin',
    createTime: '2025-01-12',
    type: 'video',
    size: '180MB',
    category: 'tech'
  },
  {
    id: 8,
    name: '数据库设计原理',
    description: '关系型数据库设计和优化',
    creator: 'admin',
    createTime: '2025-01-11',
    type: 'doc',
    size: '4.2MB',
    category: 'tech'
  },
  {
    id: 9,
    name: '产品需求分析',
    description: '产品需求收集和分析方法',
    creator: 'admin',
    createTime: '2025-01-10',
    type: 'ppt',
    size: '12MB',
    category: 'product'
  },
  {
    id: 10,
    name: '团队协作培训',
    description: '高效团队协作和沟通技巧',
    creator: 'admin',
    createTime: '2025-01-09',
    type: 'video',
    size: '95MB',
    category: 'business'
  },
  {
    id: 11,
    name: '安全培训手册',
    description: '信息安全和数据保护培训',
    creator: 'admin',
    createTime: '2025-01-08',
    type: 'pdf',
    size: '6.5MB',
    category: 'business'
  },
  {
    id: 12,
    name: '职场礼仪培训',
    description: '职场基本礼仪和商务礼仪',
    creator: 'admin',
    createTime: '2025-01-07',
    type: 'doc',
    size: '2.1MB',
    category: 'hr'
  },
  {
    id: 13,
    name: '2024年度总结',
    description: '公司2024年度工作总结报告',
    creator: 'admin',
    createTime: '2025-01-06',
    type: 'ppt',
    size: '25MB',
    category: 'finance'
  },
  {
    id: 14,
    name: '技术部年终总结',
    description: '技术部2024年度工作总结',
    creator: 'admin',
    createTime: '2025-01-05',
    type: 'doc',
    size: '3.2MB',
    category: 'tech-summary'
  },
  {
    id: 15,
    name: '商务谈判技巧',
    description: '商务谈判策略和实战技巧',
    creator: 'admin',
    createTime: '2025-01-04',
    type: 'video',
    size: '150MB',
    category: 'sales'
  }
])

// 计算属性
const filteredTrainings = computed(() => {
  let filtered = trainings.value
  
  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    filtered = filtered.filter(training => training.category === selectedCategory.value)
  }
  
  // 按搜索关键词筛选
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(training => 
      training.name.toLowerCase().includes(query) ||
      training.description.toLowerCase().includes(query)
    )
  }
  
  return filtered
})

const totalPages = computed(() => {
  return Math.ceil(filteredTrainings.value.length / 10)
})

// 方法
const selectCategory = (categoryId) => {
  selectedCategory.value = categoryId
  currentPage.value = 1
}

const getCurrentCategoryName = () => {
  const category = categories.value.find(cat => cat.id === selectedCategory.value)
  return category ? category.name : '全部培训'
}

const getFileIcon = (type) => {
  const icons = {
    'video': 'fas fa-play-circle',
    'doc': 'fas fa-file-word',
    'pdf': 'fas fa-file-pdf',
    'ppt': 'fas fa-file-powerpoint'
  }
  return icons[type] || 'fas fa-file'
}

const getTypeClass = (type) => {
  const classes = {
    'video': 'bg-red-100 text-red-800',
    'doc': 'bg-blue-100 text-blue-800',
    'pdf': 'bg-green-100 text-green-800',
    'ppt': 'bg-orange-100 text-orange-800'
  }
  return classes[type] || 'bg-gray-100 text-gray-800'
}

const getIconBgClass = (type) => {
  const classes = {
    'video': 'bg-red-500',
    'doc': 'bg-blue-500',
    'pdf': 'bg-green-500',
    'ppt': 'bg-orange-500'
  }
  return classes[type] || 'bg-gray-500'
}
</script>

<style scoped>
/* 自定义样式 */
.training-container {
  min-height: calc(100vh - 64px);
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 卡片悬停效果 */
.bg-white:hover {
  transform: translateY(-2px);
  transition: all 0.2s ease-in-out;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式网格调整 */
@media (max-width: 768px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.xl\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}

@media (min-width: 768px) and (max-width: 1024px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.xl\:grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) and (max-width: 1280px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.xl\:grid-cols-4 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }
}

@media (min-width: 1280px) {
  .grid-cols-1.md\:grid-cols-2.lg\:grid-cols-3.xl\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}
</style>