<template>
  <div class="box">
    <el-row :gutter="20">
      <el-col :span="24">
        <div>
          <el-card :body-style="{ padding: '5px' }" style="margin-bottom: 10px">
            <div style="display: flex; justify-content: flex-end;gap: 20px;">
              <el-date-picker
              v-model="form.year"
              value-format="YYYY"
            
              type="year"
              :clearable="false"
              placeholder=""
            ></el-date-picker>
           
            <wfUserSelectDrop
              v-model="form.userId"
             
              v-if="form.selectType != 0"
              style="width: 100px"
            ></wfUserSelectDrop>
              <el-select
                :disabled="!permission['desk:viewAll']"
                v-model="form.selectType"
                placeholder=""
                style="width: 200px;"
              >
                <el-option label="个人" value="0">个人</el-option>
                <!-- <el-option label="板块" value="1">板块</el-option> -->
                <el-option label="全部" value="1">全部</el-option>
              </el-select>
              <!-- <el-button type="primary" icon="tools" text>视图设置</el-button> -->
            </div>
          </el-card>
          <el-row :gutter="20">
            <el-col :span="12" v-if="permission['desk:newCustomerNums']">
              <newCustomerStatisic ></newCustomerStatisic
            ></el-col>
            <el-col :span="12" v-if="permission['desk:followNumberStatistics']">
              <followStatisic ></followStatisic
            ></el-col>

            <el-col :span="12" v-if="permission['desk:businessStatisic']">
              <businessStatisic ></businessStatisic
            ></el-col>
            <el-col :span="12" v-if="permission['desk:reNewAmount']">
              <reNewAmount ></reNewAmount
            ></el-col>
            <el-col :span="12" v-if="permission['desk:offerStatisic']">
              <offerStatisic ></offerStatisic
            ></el-col>

            <el-col :span="12" v-if="permission['desk:contractAmountStatisic']">
              <contractAmountStatisic
                
              ></contractAmountStatisic
            ></el-col>
            <el-col :span="12" v-if="permission['desk:objectNumberStatisic']">
              <objectNumberStatisic
                
              ></objectNumberStatisic
            ></el-col>

            <el-col :span="12" v-if="permission['desk:purchaseAmount']">
              <purchaseAmount></purchaseAmount
            ></el-col>
            <el-col :span="12" v-if="permission['desk:optionNumberStatistics']">
              <programmeStatisic
                
              ></programmeStatisic
            ></el-col>
          </el-row>
        </div>
      </el-col>
      <!-- <el-col :span="8"> <el-card></el-card></el-col> -->
    </el-row>
  </div>
</template>

<script setup>
import { provide, computed } from 'vue';
import { useStore } from 'vuex';
import newCustomerStatisic from './components/newCustomerStatisic.vue';
import followStatisic from './components/followStatisic.vue';
import businessStatisic from './components/businessStatisic.vue';
import offerStatisic from './components/offerStatisic.vue';
import contractAmountStatisic from './components/contractAmountStatisic.vue';
import objectNumberStatisic from './components/objectNumberStatisic.vue';
import purchaseAmount from './components/purchaseAmount.vue';
import reNewAmount from './components/renewAmountStatistics.vue';
import programmeStatisic from './components/programmeStatisic.vue';
import wfUserSelectDrop from './components/userSelect.vue';
import moment from 'moment';
let form = ref({
  selectType: '0',
   year: moment(new Date()).format('YYYY'),
});
const store = useStore();
const permission = computed(() => {
  return store.getters.permission;
});

provide('form', form);
</script>

<style lang="scss" scoped>
.box {
  padding: 15px;
  box-sizing: border-box;
}
</style>
