<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      ref="crud"
      v-model="form"
      v-model:page="page"
      :permission="permissionList"
      :before-open="beforeOpen"
      @row-del="rowDel"
      row-key="id"
      @select="hanldeSelect"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @refresh-change="refreshChange"
      @on-load="onLoad"
    >
      <template #menu="{ row }">
        <el-button
          text
          type="primary"
          icon="el-icon-setting"
          @click="handleRole(row)"
          v-if="userInfo.role_name.includes('admin') || userInfo.role_name.includes('manager')"
          plain
          >权限设置
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="用户权限配置" append-to-body v-model="box" width="500px">
      <el-tabs type="border-card">
        <el-tab-pane label="菜单权限">
          <el-tree
            :data="menuGrantList"
            show-checkbox
            node-key="id"
            ref="treeMenu"
            :default-checked-keys="[...menuTreeObj,...userMenuTreeObj]"
            :props="props"
          >
            <template #default="{ node, data }">{{ node.label }}</template>
          </el-tree>
        </el-tab-pane>
        <!-- <el-tab-pane label="数据权限">
          <el-tree
            :data="dataScopeGrantList"
            show-checkbox
            node-key="id"
            ref="treeDataScope"
            @check="handleChecked"
            :default-checked-keys="dataScopeTreeObj"
            :props="props"
          >
          </el-tree>
        </el-tab-pane>
        <el-tab-pane label="接口权限">
          <el-tree
            :data="apiScopeGrantList"
            show-checkbox
            node-key="id"
            ref="treeApiScope"
            :default-checked-keys="apiScopeTreeObj"
            :props="props"
          >
          </el-tree>
        </el-tab-pane> -->
      </el-tabs>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="box = false">取 消</el-button>
          <el-button type="primary" @click="submit">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getList,
  grant,
  grantTree,
  getRole,
  // remove,
  // update,
} from '@/api/system/userAuthority';
import { mapGetters } from 'vuex';
import website from '@/config/website';

export default {
  data() {
    return {
      form: {},
      box: false,
      props: {
        label: 'title',
        value: 'key',
        disabled: 'disabled',
      },
      menuGrantList: [],
      dataScopeGrantList: [],
      apiScopeGrantList: [],
      apiGrantList: [],
      menuTreeObj: [],
      userMenuTreeObj: [],
      dataScopeTreeObj: [],
      apiScopeTreeObj: [],
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      option: {
        tip: false,
        addBtn: false,
        //   simplePage: true,
        searchShow: true,
        searchMenuSpan: 6,
        //   tree: true,
        border: true,
        editBtn: false,
        delBtn: false,
        menuWidth: 130,
        height: 'auto',
        calcHeight: 30,
        index: true,
        selection: false,
        parent: false,
        viewBtn: false,
        dialogWidth: 900,
        dialogClickModal: false,
        column: [
          {
            label: '用户姓名',
            prop: 'realName',
            search: true,
            display: false,
          },
          {
            label: '所属角色',
            prop: 'roleName',
            slot: true,
            display: false,
          },
          {
            label: '所属部门',
            prop: 'deptName',
            slot: true,
            display: false,
          },
        ],
      },
      data: [],
      currentRow: {},
    };
  },
  computed: {
    ...mapGetters(['userInfo', 'permission']),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.role_add, false),
        viewBtn: this.validData(this.permission.role_view, false),
        delBtn: this.validData(this.permission.role_delete, false),
        editBtn: this.validData(this.permission.role_edit, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(',');
    },
    idsArray() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids;
    },
  },
  methods: {
    initData(roleId) {
      getRoleTreeById(roleId).then(res => {
        const column = this.findObject(this.option.column, 'parentId');
        column.dicData = res.data.data;
      });
    },
    submit() {
      const menuList = this.$refs.treeMenu.getCheckedKeys();
    //   const menuHalfList = this.$refs.treeMenu.getHalfCheckedKeys();

      // const allList  = menuList.concat(menuHalfList);
    //   const dataScopeList = this.$refs.treeDataScope.getCheckedKeys();
    //   const apiScopeList = this.$refs.treeApiScope.getCheckedKeys();
      grant(this.currentRow.id, menuList).then(() => {
        this.box = false;
        this.$message({
          type: 'success',
          message: '操作成功!',
        });
        this.onLoad(this.page);
      });
    },
    rowSave(row, done, loading) {
      add(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          done();
          this.onLoad(this.page);
        },
        error => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowUpdate(row, index, done, loading) {
      update(row).then(
        () => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          done();
        },
        error => {
          window.console.log(error);
          loading();
        }
      );
    },
    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
        });
    },

    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    // selectionChange(list) {
    //   this.selectionList = list;
    // },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    beforeOpen(done, type) {
      if (['add', 'edit'].includes(type)) {
        this.initData(this.form.id);
      }
      done();
    },
    handleData(data) {
      data.forEach(item => {
        item.disabled = this.menuTreeObj.findIndex(i => i == item.id) > -1;
        if (item.children) {
          this.handleData(item.children);
        }
      });
    },
    handleRole(row) {
      this.currentRow = row;
      this.menuTreeObj = [];
      this.userMenuTreeObj = [];
      this.dataScopeTreeObj = [];
      this.apiScopeTreeObj = [];

      getRole(row.id).then(res => {
        this.menuTreeObj = res.data.data.menu;
        this.userMenuTreeObj = res.data.data.userMenu;
        this.dataScopeTreeObj = res.data.data.dataScope;
        this.apiScopeTreeObj = res.data.data.apiScope;
        this.box = true;
        grantTree().then(res => {
          this.menuGrantList = res.data.data.menu;
          this.dataScopeGrantList = res.data.data.dataScope;
          this.apiScopeGrantList = res.data.data.apiScope;
          this.handleData(this.menuGrantList);
          console.log(this.menuGrantList);
        });
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据');
        return;
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: 'success',
            message: '操作成功!',
          });
          this.$refs.crud.toggleSelection();
        });
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {status:1}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        this.data = res.data.data.records;
        this.page.total = res.data.data.total;
        this.loading = false;

        this.selectionClear();
      });
    },
    handleChecked(a, b, c, d) {
      console.log(a, b, c, d);
    },
    hanldeSelect(selection, row) {
      this.selectionList = selection;
      if (row.children && row.children.length > 0) {
        row.children.map(item => {
          this.$refs.crud.toggleRowSelection(item, false);
          this.selectionList = this.selectionList.filter(i => i.id !== item.id);
        });
      }
    },
  },
};
</script>
