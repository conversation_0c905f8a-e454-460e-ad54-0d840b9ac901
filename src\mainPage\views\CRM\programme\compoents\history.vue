<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #menu="{ row }">
      <el-button text icon="Operation" type="primary" v-if="props.currentStatus" @click="transCurrent(row)"
        >转为当前版本</el-button
      >
      <el-button text @click="handleView(row)" icon="view" type="primary">详情</el-button>
    </template>
  </avue-crud>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  labelWidth: 120,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '方案名称',
      prop: 'optionName',
      overHidden:true,
    },
    {
      label: '方案创建日期',
      prop: 'createTime',
    },
    {
      label: '方案版本',
      prop: 'version',
      type: 'input',
      formatter: row => {
        return `v${row.version}.0`;
      },
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  id: String,
  currentStatus:Boolean
});
watchEffect(() => {
  if (props.id) {
    onLoad();
  }
});

const tableUrl = '/api/vt-admin/businessOpportunityOptionHistory/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        optionId: props.id,
        size,
        current,
        ...params.value,
        versionType:0
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function searchChange(params, done) {
  onLoad();
  done();
}
function handleView(row) {
  if (row.dataJson) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        type: 'detail',
        isHistory: 1,
      },
    });
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        type: 'detail',
        isHistory:1
      },
    });
  }
}

function transCurrent(row) {
  proxy.$confirm('此操作将会覆盖当前版本的数据，请确认是否备份', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(res => {
      router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        type: 'edit',
        name: row.offerName,
        // businessOpportunityId: row.id,
        isTranslate: 1,
      },
    });
    });
}
</script>

<style lang="scss" scoped></style>
