<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowSupplierUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowSupplierDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  <template #menu="{row}">
    <el-button type="primary" @click="viewHistory(row)" text icon="clock">历史</el-button>
  </template>
  </avue-crud>

  <dialogForm ref="dialogForm"></dialogForm>
  <productHistoryList  ref="productHistoryListRef" :supplierId="supplierId" :productId="currentRow.productId" :productInfo="currentRow.productVO"></productHistoryList>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productHistoryList from './productHistoryList.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  //   header:false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 220,
  labelWidth:150,
  border: true,
  column: [
    {
      label: '产品编号',
      prop: 'productCode',
      overHidden: true,
      // placeholder: '自动生成',
      disabled: true,
      editDisplay: false,
      bind: 'productVO.productCode',
      // search: true,
    },
    {
      label: '产品分类',
      prop: 'categoryId',
      // search: true,
      hide: true,
      editDisplay: false,
      bind: 'productVO.categoryId',
      type: 'tree',
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      // search: true,
      editDisabled: true,
      bind: 'productVO.productName',
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      editDisplay: false,
      // search: true,
      bind: 'productVO.productBrand',
    },
    {
      label: '产品图片',
      prop: 'coverUrl',
      type: 'upload',
      editDisplay: false,
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      bind: 'productVO.coverUrl',
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        form.value.coverId = res.id;
        console.log(form.value);
        done();
      },
    },
    {
      label: '单位',
      type: 'select',
      editDisplay: false,
      width:80,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      bind: 'productVO.unit',
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },

    {
      label: '规格型号',
      editDisplay: false,
      prop: 'productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      bind: 'productVO.productSpecification',
      type: 'input',
    },
    {
            label: '报价时间',
            prop: 'offerDate',
            type: 'date',
            span: 12,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
    {
      label: '产品质保期（年）',
      prop: 'warrantyPeriod',
      type: 'number',
      span: 12,
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'switch',
      value: 1,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      span: 12,
    },
    {
      label: '税率',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      label: '价格',
      prop: 'unitPrice',
    },
    
  ],
});
const props = defineProps({
  supplierId: String,
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/supplierProduct/remove';
const updateUrl = '/api/vt-admin/supplierProduct/update';
const tableUrl = '/api/vt-admin/supplierProduct/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowSupplierUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/supplierProduct/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowSupplierDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/supplierProduct/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
let currentRow = ref({})
let productHistoryListRef= ref()
function viewHistory(row) {
    currentRow.value = row
    productHistoryListRef.value.open()
}
defineExpose({
  onLoad,
});
</script>

<style lang="scss" scoped></style>
