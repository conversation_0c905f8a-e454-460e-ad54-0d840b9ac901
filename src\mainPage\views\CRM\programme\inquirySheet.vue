<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button type="text" @click="editInquirySheet(row)" icon="edit">编辑产品</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="编辑询价单" v-model="isEditInquirySheet" size="80%">
      <avue-crud
        @row-dblclick="handleRowDBLClick"
        @row-del="handleRowDel"
        ref="fileCrudRef"
        @row-update="handleRowUpdate"
        :option="inquirySheetOption"
        :data="inquirySheetData"
      >
        <template #header>
          <el-alert type="success" size="small">双击可编辑</el-alert>
        </template>
        <template #totalPrice="{ row }">
          {{ (row.number * row.costPrice).toFixed(2) }}
        </template>
      </avue-crud>
      <!-- <template #footer>
        <div style="flex: auto">
          <el-button  @click="isEditInquirySheet = false">取 消</el-button>
          <el-button type="primary" @click="submit">保 存</el-button>
        </div>
      </template> -->
    </el-drawer>
  </basic-container>
</template>

<script setup>
import template from './compoents/template.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 120,
  menuWidth: 200,
  searchLabelWidth: 120,
  border: true,
  column: [
    {
      label: '询价单名称',
      prop: 'businessName',
      width: 250,
      overHidden: true,
      // search: true,
    },
    {
      label: '关联商机',
      prop: 'businessName',component: 'wf-bussiness-drop',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '关联品牌',
      prop: 'productBrand',
      width: 120,
      overHidden: true,
      search: true,
    },
    {
      label: '关联供应商',
      prop: 'supplierName',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '报价人',

      prop: 'offerPerson',
      width: 110,
      overHidden: true,
    },
    {
      label: '报价人联系电话',
      prop: 'offerPhone',

      width: 150,
      overHidden: true,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      format: 'YYYY-MM-DD',
     
      type: 'dateTime',
      search: true,
      
      component: 'wf-daterange-search',
      search: true,
    
      valueFormat: 'YYYY-MM-DD',
      searchSpan: 6,
     
      width: 150,
     
    },
    {
      label: '备注',
      prop: 'remark',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/businessInquiry/remove?ids=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/businessInquiry/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        offerStartDate: params.value.offerDate ? params.value.offerDate[0] : null,
        offerEndDate: params.value.offerDate ? params.value.offerDate[1] : null,
       
        ...params.value,
        offerDate:null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
let detailForm = ref({});
function editInquirySheet(row) {
  axios.get('/api/vt-admin/businessInquiry/detail?id=' + row.id).then(res => {
    detailForm.value = res.data.data;

    inquirySheetData.value = res.data.data.detailVOS;

    isEditInquirySheet.value = true;
  });
}
let isEditInquirySheet = ref(false);
let inquirySheetData = ref([]);
let inquirySheetOption = ref({
  menuWidth: 100,
  editBtn: false,
  addBtn: false,
  delBtn: true,
  showSummary: true,
  height:'auto',
  sumColumnList: [
    {
      name: 'totalPrice',
      type: 'sum',
    },
  ],
  border: true,
  index: false,
  column: {
    serialNumber: {
      label: '序号',
      overHidden: true,
    },
    customProductName: {
      label: '产品名称',
      overHidden: true,
    },
    productBrand: {
      label: '品牌',
      overHidden: true,
    },
    customProductSpecification: {
      label: '规格型号',
      overHidden: true,
      span: 24,
    },
    customProductDescription: {
      label: '产品描述',
      overHidden: true,
      span: 24,
      type: 'textarea',
    },
    customUnit: {
      label: '单位',
      overHidden: true,
      label: '单位',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictValue',
        desc: 'desc',
      },

      dicUrl: '/blade-system/dict/dictionary?code=unit',
    },
    number: {
      label: '数量',
      overHidden: true,
    },
    sealPrice: {
      label: '产品单价',
      overHidden: true,
      width:100,
    },
    laborCost: {
      label: '人工单价',
      overHidden: true,width:100,
    },
    ybhsdj: {
      label: '延保单价',
      overHidden: true,width:100,
    },
    qthsdj: {
      label: '其他单价',
      overHidden: true,width:100,
    },
    costPrice: {
      label: '产品成本单价',
      overHidden: true,
    },
    // totalPrice: {
    //   label: '金额',
    //   overHidden: true,
    //   editDisplay: false,
    // },
    rgcbdj: {
      label: '人工成本单价',
      overHidden: true,
    },
    ybcbdj: {
      label: '延保成本单价',
      overHidden: true,
    },
    qtcbdj: {
      label: '其他成本单价',
      overHidden: true,
    },
  },
});
function handleRowDBLClick(row, event) {
  proxy.$refs.fileCrudRef.rowEdit(row, row.$index);
}
function handleRowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/businessInquiryDetail/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        inquirySheetData.value[index] = row;
        done();
      }
    })
    .catch(err => {
      done();
    });


}
function handleRowDel(form, index, done) {
  console.log(form, index, done);

  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      inquirySheetData.value = inquirySheetData.value.filter((item, i) => index != i);
      done();
      proxy.$message({
        type: 'success',
        message: '删除成功!',
      });
    })
    .catch(() => {});
}
function submit() {
  const data = {
    ...detailForm.value,
    dtoList: inquirySheetData.value,
  };
  axios.post('/api/vt-admin/businessInquiry/update', data).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success('修改成功');
      isEditInquirySheet.value = false
    }
  });
}
</script>

<style lang="scss" scoped></style>
