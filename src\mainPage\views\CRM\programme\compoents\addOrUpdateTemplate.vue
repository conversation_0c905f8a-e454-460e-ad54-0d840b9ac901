<template>
  <div style="height: calc(100vh - 170px)" :class="{ isFullSreen: isFullSreen }">
    <Title  style="margin: 0 15px; background-color: #fff"
      >{{ isEdit ? '编辑模板' : `${form.templateName || ''}模板详情` }}
      <template #foot>
        <div>
          <el-button
            icon="FullScreen"
            :title="isFullSreen ? '取消全屏' : '全屏'"
            @click="handleScreen"
          ></el-button>

          <!-- <el-button type="primary" @click="baseDrawer = true">基本信息</el-button> -->
          <el-button type="primary" @click="draft('confirm')">保存</el-button>
          <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        >
        </div>
      </template></Title
    >
    <div style="height:calc(100% - 40px)">
      <edit ref="sheet" :data="form"></edit>
    </div>
  </div>
  <!-- <el-drawer title="基本信息" v-model="baseDrawer">
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
      <template #distributionOption-header="column">
        <span class="avue-group__title" style="margin-right: 10px; color: var(--el-color-warning)"
          >主管备注</span
        >
      </template>
      <template #optionFiles v-if="option.detail">
        <File :fileList="form.optionFileList"></File>
      </template>
      <template #gatherFiles>
        <File :fileList="form.gatherFilesList"></File>
      </template>
      <template #optionFileType-type="{ item, value, label }">
        <div style="display: flex; align-items: center; justify-content: space-between">
          <span>{{ item.label }}</span>
          <img
            :src="item.url"
            style="height: 20px; width: 20px"
            @click.stop="$ImagePreview([{ thumbUrl: item.url, url: item.url }], 0)"
            alt=""
          />
        </div>
      </template>
    </avue-form>
    <template #footer>
      <div style="flex: auto">
        <el-button type="primary" @click="baseDrawer = false">确认</el-button>
      </div>
    </template>
  </el-drawer> -->
  <!-- <dialogForm ref="dialogForm"></dialogForm> -->
</template>

<script setup>
import { ref, getCurrentInstance, onBeforeUnmount, onMounted, onUnmounted, handleError } from 'vue';
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router';

import edit from './template.vue';
const route = useRoute();
const router = useRouter();
let isEdit = ref(route.query.type == 'edit' || route.query.type == 'add');
let form = ref({
  moduleDTOList: [
    {
      moduleName: '汇总',
    },
  ],
});


let loading = ref(false);
const isDeep = ref(route.query.deep == '1');
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
  isEdit.value = route.query.type == 'edit' || route.query.type == 'add';
});
onMounted(() => {
  // if (route.query.id) {
  //   getDetail();
  // }
});

// let option = ref({
//   submitBtn: false,
//   labelWidth: 120,
//   detail: !isEdit.value || isDeep.value,
//   emptyBtn: false,
//   group: [
//     {
//       label: '基本信息',
//       prop: 'baseInfo',
//       arrow: true,
//       column: [
//         {
//           label: '方案名称',
//           prop: 'optionName',
//           span: 24,
//           rules: [
//             {
//               required: true,
//               message: '请填写方案名称',
//             },
//             {
//               validator: (rule, value, callback) => {
//                 const reg = /^[^/\\?？\[\]]*$/;
//                 console.log(value, rule, reg);
//                 if (!reg.test(value)) {
//                   callback(new Error('不能包含特殊字符"/\?？[]"'));
//                 } else {
//                   callback();
//                 }
//               },
//               trigger: 'change',
//             },
//           ],
//         },
//         {
//           label: '客户名称',
//           prop: 'customerName',
//           readonly: true,
//           span: 24,
//         },
//         {
//           label: '关联联系人',
//           prop: 'concatName',
//           span: 24,
//           readonly: true,
//         },
//         // {
//         //   label: '人工费',
//         //   prop: 'isNeedLabor',
//         //   span:24,
//         //   readonly: true,
//         //   type: 'switch',

//         //   dicData: [
//         //     {
//         //       value: 0,
//         //       label: '否',
//         //     },
//         //     {
//         //       value: 1,
//         //       label: '是',
//         //     },
//         //   ],
//         // },
//         // {
//         //   label: '自定义',
//         //   prop: 'isHasCustom',
//         //   readonly: true,
//         //   type: 'switch',
//         //   labelTip: '打开后点击文字即可编辑',
//         //   span: 4,
//         //   dicData: [
//         //     {
//         //       value: 0,
//         //       label: '否',
//         //     },
//         //     {
//         //       value: 1,
//         //       label: '是',
//         //     },
//         //   ],
//         // },
//         // {
//         //   label: '专项报价',
//         //   prop: 'isHasSpecialPrice',
//         //   type: 'radio',
//         //   span: 24,
//         //   value: 0,
//         //   dicData: [
//         //     {
//         //       label: '是',
//         //       value: 1,
//         //     },
//         //     {
//         //       label: '否',
//         //       value: 0,
//         //     },
//         //   ],
//         // },
//         {
//           label: '备注',
//           prop: 'remark',
//           type: 'textarea',
//           span: 24,

//           // format: 'YYYY-MM-DD',
//           // valueFormat: 'YYYY-MM-DD',
//         },
//         {
//           label: '上传附件',
//           prop: 'optionFiles',
//           type: 'upload',
//           span: 24,
//           dragFile: true,
//           // rules: [
//           //   {
//           //     required: true,
//           //     validator: validatorPath,
//           //     trigger: "change",
//           //   },
//           // ],
//           dataType: 'object',
//           propsHttp: {
//             res: 'data',
//             url: 'id',
//             name: 'originalName',
//           },
//           action: '/api/blade-resource/attach/upload',
//           hide: true,
//           viewDisplay: false,
//           uploadPreview: (file, data) => {
//             console.log(file, data);
//             const { originalName, link } =
//               form.value.optionFileList.find(item => item.id == file.url) || {};
//             loadFile(link, originalName);
//           },
//         },
//         {
//           label: '报价公司',
//           type: 'select',
//           props: {
//             label: 'label',
//             value: 'value',
//           },
//           value: '深圳市非常聚成科技有限公司',
//           prop: 'offerCompany',
//           span: 24,
//           overHidden: true,
//           cell: false,
//           dicData: [
//             {
//               value: '深圳市非常聚成科技有限公司',
//               label: '深圳市非常聚成科技有限公司',
//             },
//             {
//               value: '深圳市非常聚成智能化技术有限公司',
//               label: '深圳市非常聚成智能化技术有限公司',
//             },
//           ],
//         },
//         {
//           label: '设备保修期(年)',
//           prop: 'freeWarrantyYear',
//           span: 24,
//           type: 'number',
//         },
//         {
//           label: '导出模板',
//           prop: 'optionFileType',
//           span: 24,
//           type: 'select',
//           rules: [
//             {
//               required: true,
//               message: '请选择输出模板',
//               trigger: 'change',
//             },
//           ],
//           dicData: [
//             {
//               label: '模板A型',
//               value: 0,
//               url: templateA,
//             },
//             {
//               label: '模板B型',
//               value: 1,
//               url: templateB,
//             },
//             {
//               label: '模板C型',
//               value: 2,
//               url: templateC,
//             },
//             {
//               label: '模板D型',
//               value: 3,
//               url: templateD,
//             },
//           ],
//         },
//       ],
//     },
//     {
//       label: '主管备注',
//       prop: 'distributionOption',
//       arrow: true,
//       column: [
//         {
//           label: '',
//           type: 'textarea',
//           span: 24,
//           readonly: true,
//           prop: 'distributionOptionRemark',
//         },
//       ],
//     },
//     {
//       label: '需求采集信息',
//       prop: 'gatherInfo',
//       arrow: true,
//       column: [
//         {
//           label: '采集表附件',
//           type: 'input',
//           span: 24,
//           readonly: true,
//           prop: 'gatherFiles',
//         },
//       ],
//     },
//   ],
// });
let { proxy } = getCurrentInstance();

// 是否是深化设计

function getDetail() {
  let url = '/api/vt-admin/optionTemplate/detailForAdd';

  loading.value = true;
  axios
    .get(url, {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      const data = res.data.data;
      form.value = data;
    });
}


function draft(type) {
  const sheetData = proxy.$refs.sheet.getData();
  axios.post('/vt-admin/optionTemplate/update',sheetData).then(res=>{
    proxy.$message.success(res.data.msg);
    proxy.$router.$avueRouter.closeTag();
    proxy.$router.back();
  })
}








let baseDrawer = ref(true);

let isFullSreen = ref(false);
function handleScreen() {
  isFullSreen.value = !isFullSreen.value;
  proxy.$nextTick(() => {
    luckysheet.resize();
  });
}



</script>

<style scoped lang="scss">
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
::v-deep .el-table .el-table__cell {
  padding: 0;
}
::v-deep .el-collapse-item__header {
  line-height: 33px;
}
::v-deep .el-form-item {
  /* margin-bottom: 5px; */
}
::v-deep .el-tabs__header {
  margin-bottom: 5px;
}
.el-collapse-item__content {
  padding-bottom: 8px;
}
.el-table .el-form-item {
  margin-bottom: 0;
}
.fullScreen {
  position: fixed;

  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  left: 0;
  top: 0;
  z-index: 999;
  background-color: #fff;
  padding: 15px;
}
::v-deep .affix .el-table .el-table__empty-block {
  display: none !important;
}
::v-deep .affix .el-table__body-wrapper {
  display: none !important;
}
.tab_box .move1 {
  display: none;
}
.tab_box:hover .move1 {
  display: inline-block;
}
.warningInput {
  /* border: 1px solid var(--el-color-warning) !important; */
}
::v-deep .warningInput .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}
.warningInput {
  color: var(--el-color-danger);
}
.isFullSreen {
  position: fixed;
  height: 100vh !important;
  width: 100vw;
  background-color: #fff;
  top: 0;
  left: 0;
  z-index: 1000;
}
.programmeSelect {
  position: fixed;
  padding: 5px;
  top: calc(50% - 250px);
  right: -500px;
  border-radius: 5px;
  height: 500px;
  width: 300px;
  transition: all 0.3s;
  z-index: 99999;
  // border: 1px solid #ccc;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  .content {
    padding: 15px;
    padding-bottom: 0px;
    overflow: hidden;
    .title {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ccc;
    }
    .btn_box {
      // background-color: red;
      height: 25px;
      transition: all 0.2s;
      line-height: 25px;
      div {
        width: 50%;
        text-align: center;
      }
    }
  }
}
.active {
  right: 20px;
}
.select {
  border: 1px solid $color-primary;
  .btn {
    height: 25px !important;
    margin-bottom: 5px;
  }
}
</style>
