<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-button type="primary" @click="handleAdd" icon="plus">新增</el-button>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          text
          icon="TakeawayBox"
          @click="wareHousing(row)"
          v-if="row.orderStatus == 1 && row.sendStatus == 0"
          >入库</el-button
        >
        <el-button text type="primary" @click="handleComplete(row)" icon="view">详情</el-button>
        <el-button text type="primary" @click="handleSend(row)" icon="view"  v-if="row.sendStatus == 0">发货</el-button>
        <el-button text type="primary" @click="viewOrder(row)" icon="view">询价单</el-button>
      </template>
      <template #orderStatus="{ row }">
        <el-tag effect='plain' size="small" v-if="row.orderStatus == 0" type="warning">待采购</el-tag>
        <el-tag effect='plain' size="small" v-if="row.orderStatus == 1" type="warning">采购中</el-tag>
        <el-tag effect='plain' size="small" type="success" v-else-if="row.orderStatus == 3">采购完成</el-tag>
        <el-tag effect='plain' size="small" type="warning" v-else-if="row.orderStatus == 2">询价中</el-tag>
      </template>
      <template #file="{ row }">
        <file :fileList="row.attachList"></file>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" size="90%" title="请购单详情">
      <el-row style="height: 100%" :gutter="8">
        <el-col style="height: 100%" :span="8">
          <el-card class="box-card" style="height: 100%; overflow: auto">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
              <template #customerInvoiceInfoId>
                <wfInvoiceDrop
                  v-model="form.customerInvoiceInfoId"
                  :id="form.customerId"
                ></wfInvoiceDrop>
              </template>
            </avue-form>
          </el-card>
        </el-col>

        <el-col style="height: 100%" :span="16">
          <el-card class="box-card" style="height: 100%">
            <avue-crud :option="selectProductOption" :data="selectProductList"> </avue-crud>
          </el-card>
        </el-col>
      </el-row>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 280,
  border: true,
  column: [
    {
      label: '关联项目',
      prop: 'projectName',
      width: 250,
      overHidden: true,
    },
    {
      label: '请购人',
      prop: 'applyName',
      width: 250,
      overHidden: true,
    },
    {
      label: '请购时间',
      prop: 'createTime',
      format:'YYYY-MM-DD HH:mm',
      type:'date'
    },

    {
      label: '备注',
      prop: 'remark',
      overHidden: true,
    },

    {
      label: '收货人',
      prop: 'deliveryUser',
    },
    {
      label: '收货地址',
      prop: 'deliveryAddress',
      type: 'select',
    },
    {
      label: '联系方式',
      prop: 'deliveryContact',
      searchSpan: 6,
      searchRange: true,
      type: 'date',
    },
    {
      label: '订单状态',
      prop: 'orderStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '采购中',
        },
        {
          value: 1,
          label: '采购完成',
        },
        {
          value: 2,
          label: '询价中',
        },
      ],
      slot: true,
      search: true,
    },
    {
      label: '发货状态',
      prop: 'sendStatus',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '未发货',
        },
        {
          value: 1,
          label: '已发货',
        },
      ],
      slot: true,
      search: true,
    },

    //   {
    //     label: '收货状态',
    //     prop: 'deliveryStatus',
    //     type:'select',
    //     dicData:[{
    //       value:0,
    //       label:'未收货'
    //     },
    //     {
    //       value:1,
    //       label:'已收货'
    //     }]
    //   },
    //   {
    //     label: '附件',
    //     prop: 'file',
    //   },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectApplyPurchase/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

//   请购新增

let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      column: [
        {
          label: '申请人',
          type: 'input',
          value: proxy.$store.getters.userInfo.nick_name,
          prop: 'createName',
          width: 80,
          span: 24,
          readonly: true,
        },
        {
          type: 'date',
          label: '申请时间',
          span: 24,
          display: true,
          format: 'YYYY-MM-DD',
          readonly: true,
          width: 100,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          valueFormat: 'YYYY-MM-DD',
          prop: 'createTime',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },

        // {
        //   label: '附件',
        //   prop: 'file',
        //   type: 'upload',
        //   value: [],
        //   dataType: 'object',
        //   loadText: '附件上传中，请稍等',
        //   span: 12,
        //   slot: true,
        //   // align: 'center',
        //   propsHttp: {
        //     res: 'data',
        //     url: 'id',
        //     name: 'originalName',
        //     // home: 'https://www.w3school.com.cn',
        //   },
        //   action: '/blade-resource/attach/upload',
        // },
      ],
    },
    {
      label: '收货信息',
      prop: 'receiveInfo',
      column: [
        {
          label: '收货人',
          prop: 'deliveryUser',
          span: 24,
        },
        {
          label: '收货地址',
          prop: 'deliveryAddress',
          type: 'input',
          span: 24,
        },
        {
          label: '联系方式',
          prop: 'deliveryContact',

          span: 24,
        },
      ],
    },
  ],
});

let selectProductOption = ref({
  header: true,
  menu: false,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  height: 'auto',
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',
      bind: 'productVO.productName',
      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      bind: 'productVO.productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 90,
      bind: 'productVO.unitName',

      span: 12,
    },
    //   {
    //     label: '清单数量',
    //     prop: 'deepenNumber',
    //     type: 'number',
    //     align: 'center',
    //     span: 12,
    //     cell: false,
    //   },
    {
      label: '采购数量',
      prop: 'number',
      type: 'number',
      align: 'center',
      span: 12,
      cell: false,
    },
    {
      label: '备注',
      prop: 'deepenRemark',
      type: 'textarea',
      overHidden: true,
      align: 'center',
      span: 12,
      cell: false,
    },
  ],
});

// 产品列表
let productListDrawer = ref(false);
let currentId = ref(null);

function handleComplete(row) {
  drawer.value = true;
  axios
    .get('/api/vt-admin/projectApplyPurchase/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      selectProductList.value = res.data.data.detailVOS;
      form.value = res.data.data;
    });
}
function viewOrder(row) {
  router.push({
    path: '/SRM/procure/compoents/inquirySheet',
    query: {
      id: row.orderId,
      type: 0,
    },
  });
}
function wareHousing(row) {
  router.push({
    path: '/SRM/warehouse/compoents/addInhouse',
    query: {
      orderId: row.orderId,
    },
  });
  // tableLoading.value = true;
  // wareHouseVisible.value = true;
  // currentId.value = row.id;
  // axios
  //   .get('/api/vt-admin/purchaseOrder/detailForInStorage', {
  //     params: {
  //       id: row.id,
  //     },
  //   })
  //   .then(res => {
  //     tableLoading.value = false;
  //     currentTabledata.value = res.data.data.detailList.map(item => {
  //       return {
  //         ...item.productVO,
  //         ...item,
  //         inStorageNumber: 0,
  //         inStorageAddress: 0,
  //       };
  //     });
  //   });
}
function handleSend(row) {
  proxy.$refs.dialogForm.show({
    title: '发货',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'datetime',
          label: '发货时间',
          span: 24,
          value: dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDeliveryDate',
        },
        {
          label: '附件',
          prop: 'sendFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
     
      const data = {
        id: row.id,
        ...res.data,
        sendFiles: res.data.sendFiles && res.data.sendFiles.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/projectApplyPurchase/send', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
</script>

<style lang="scss" scoped></style>
