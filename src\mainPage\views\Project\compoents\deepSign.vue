<template>
  <el-empty description="深化设计未完成" v-if="props.deepenStatus != 3 && props.isView"></el-empty>
  <div v-else style="height: 100%">
    <!-- <div style="height: 100px; margin-bottom: 20px">
      <el-card shadow="never">
        <avue-form :option="baseOption" ref="addForm" v-model="form">
          <template #baseInfo-header="column">
            <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
            <el-button @click.stop="viewBusiness" type="primary" size="small">商机详情</el-button>
          </template>
        </avue-form>
      </el-card>
    </div> -->
    <div style="height: 100%" v-loading="loading" element-loading-text="刷新数据中">
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="5">
          <el-card shadow="never" style="height: 100%">
            <avue-tree
              ref="tree"
              :option="treeOption"
              :data="treeData"
              @node-click="handleNodeClick"
            ></avue-tree>
          </el-card>
        </el-col>
        <el-col :span="19">
          <el-card
            shadow="never"
            style="height: 500px; overflow-y: auto"
            :style="{ height: height + 'px' }"
          >
            <!-- <el-button
              type="primary"
              icon="plus"
              v-if="!props.detail"
              style="margin-bottom: 5px"
              @click="$refs.productSelectRef.visible = true"
            >
              新增</el-button
            > -->
            <div :ref="`box_${item.moduleId}`" v-for="(item, index) in treeData" :key="item.id">
              <h3>{{ item.moduleName }}</h3>
              <el-table
                class="avue-crud"
                v-if="item.children && item.children.length > 0"
                :span-method="arraySpanMethod"
                :data="item.children"
                :row-class-name="rowClassName"
                border
                :expand-row-keys="item.children && item.children.map(item => item.id)"
                row-key="id"
                align="center"
              >
                <el-table-column type="expand">
                  <template #default="{ row: rowP }">
                    <div
                      style="
                        box-sizing: border-box;
                        margin-top: -9px;
                        display: flex;
                        align-items: flex-end;
                      "
                    >
                      <div style="width: 49px; text-align: center">
                        <el-button
                          type="primary"
                          circle=""
                          icon="plus"
                          v-if="!props.detail"
                          style="margin-bottom: 5px"
                          @click="handleAddProduct(rowP, item)"
                        ></el-button>
                      </div>
                      <el-table
                        class="avue-crud"
                        :show-header="false"
                        ref="tableItem"
                        :data="rowP.detailVOList"
                        :row-class-name="tableRowClassName"
                        border
                        row-key="id"
                        align="center"
                        :cell-style="cellStyle"
                        :tree-props="{
                          children: 'splitDTOList',
                        }"
                        :row-style="rowStyle"
                      >
                        <el-table-column
                          label="设备名称"
                          #default="{ row }"
                          show-overflow-tooltip
                          prop="customProductName"
                          width="130"
                        >
                          <el-tag
                            size="small"
                            type="danger"
                            v-if="row.splitDTOList && row.splitDTOList.length > 0"
                            effect="plain"
                            >拆</el-tag
                          >
                          <span>{{ row.customProductName }}</span>
                        </el-table-column>
                        <el-table-column
                          label="规格型号"
                          show-overflow-tooltip
                          #default="{ row }"
                          prop="customProductSpecification"
                          width="130"
                        >
                          {{
                            row.customProductSpecification || row.product?.productSpecification
                          }}</el-table-column
                        >

                        <el-table-column
                          label="产品描述"
                          show-overflow-tooltip
                          #default="{ row }"
                          prop="customProductDescription"
                        >
                          {{ row.customProductDescription || row.product?.description }}
                        </el-table-column>
                        <el-table-column
                          label="品牌"
                          align="center"
                          #default="{ row }"
                          prop="productBrand"
                          width="80"
                        >
                          {{ row.productBrand || row.product?.productBrand }}
                        </el-table-column>
                        <el-table-column
                          label="单位"
                          align="center"
                          #default="{ row }"
                          width="80"
                          prop="customUnit"
                        >
                          <el-popover v-if="!props.detail" placement="right" trigger="click">
                            <template #default>
                              <el-input
                                v-model="row.customUnit"
                                placeholder="请输入单位"
                              ></el-input>
                            </template>
                            <template #reference>
                              <div style="height: 100%; width: 100%; cursor: pointer">
                                {{ row.customUnit || row.product?.unitName || '---' }}
                              </div>
                            </template>
                          </el-popover>
                          <span v-else> {{ row.customUnit || row.product?.unitName }}</span>
                        </el-table-column>
                        <el-table-column
                          label="数量"
                          align="center"
                          width="80"
                          #default="{ row }"
                          prop="number"
                        >
                          <span>{{ row.splitDTOList ? row.number : 0 }}</span>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          :label="form.deepenType == 0 ? '深化数量' : '售前深化数量'"
                          #default="{ row }"
                          width="120"
                          prop="deepenNumber"
                        >
                          <el-input-number
                            v-model="row.deepenNumber"
                            style="width: 80%"
                            v-if="
                              !props.detail &&
                              form.deepenType == 0 &&
                              (!row.splitDTOList || row.splitDTOList.length == 0)
                            "
                            size="small"
                            controls-position="right"
                            laceholder=""
                          ></el-input-number>
                        </el-table-column>
                        <el-table-column
                          align="center"
                          :label="
                            form.deepenType == 1 && !props.detail ? '深化数量' : '项目经理深化数量'
                          "
                          v-if="form.deepenType == 1 || props.detail"
                          #default="{ row }"
                          width="120"
                          prop="leaderDeepenNumber"
                        >
                          <el-input-number
                            v-model="row.leaderDeepenNumber"
                            style="width: 80%"
                            controls-position="right"
                            v-if="
                              !props.detail &&
                              form.deepenType == 1 &&
                              (!row.splitDTOList || row.splitDTOList.length == 0)
                            "
                            size="small"
                            laceholder=""
                          ></el-input-number>
                        </el-table-column>
                        <el-table-column
                          show-overflow-tooltip
                          label="深化备注"
                          #default="{ row }"
                          :width="!props.detail ? 210 : 300"
                          prop="deepenRemark"
                        >
                          <el-input
                            v-if="!props.detail"
                            v-model="row.deepenRemark"
                            type="textarea"
                            placeholder=""
                          ></el-input>
                        </el-table-column>
                        <el-table-column
                          label="操作"
                          #default="{ row, $index }"
                          align="center"
                          fixed="right"
                          v-if="!props.detail"
                          width="90"
                          prop="menu"
                        >
                          <div>
                            <el-button
                              type="primary"
                              text
                              icon="delete"
                              v-if="row.uuid && row.splitDTOList"
                              @click="deleteProduct(rowP, $index)"
                              >删除</el-button
                            >
                            <el-button
                              type="primary"
                              text
                              v-if="row.splitDTOList"
                              icon="Operation"
                              @click="handleDempose(row)"
                              >拆解</el-button
                            >
                          </div>
                        </el-table-column>
                      </el-table>
                    </div>
                  </template>
                </el-table-column>
                <!-- <el-table-column label="所属分类" prop="classify" width="120"> </el-table-column> -->
                <el-table-column
                  label="设备名称"
                  #default="{ row }"
                  show-overflow-tooltip
                  prop="customProductName"
                  width="130"
                >
                  <span style="font-size: 16px; font-weight: bolder; color: black">{{
                    row.value
                  }}</span></el-table-column
                >
                <el-table-column
                  label="规格型号"
                  show-overflow-tooltip
                  #default="{ row }"
                  prop="customProductSpecification"
                  width="130"
                >
                  {{
                    row.customProductSpecification || row.product?.productSpecification
                  }}</el-table-column
                >

                <el-table-column
                  label="产品描述"
                  show-overflow-tooltip
                  #default="{ row }"
                  prop="customProductDescription"
                >
                  {{ row.customProductDescription || row.product?.description }}
                </el-table-column>
                <el-table-column
                  label="品牌"
                  align="center"
                  width="80"
                  #default="{ row }"
                  prop="productBrand"
                >
                  {{ row.productBrand || row.product?.productBrand }}
                </el-table-column>
                <el-table-column
                  label="单位"
                  align="center"
                  width="80"
                  #default="{ row }"
                  prop="product.unitName"
                >
                  {{ row.customUnit || row.product?.unitName }}
                </el-table-column>
                <el-table-column
                  label="数量"
                  width="80"
                  align="center"
                  #default="{ row }"
                  prop="number"
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  :label="form.deepenType == 0 ? '深化数量' : '售前深化数量'"
                  #default="{ row }"
                  width="120"
                  prop="deepenNumber"
                >
                </el-table-column>
                <el-table-column
                  align="center"
                  :label="form.deepenType == 1 && !props.detail ? '深化数量' : '项目经理深化数量'"
                  v-if="form.deepenType == 1 || props.detail"
                  #default="{ row }"
                  width="120"
                  prop="leaderDeepenNumber"
                >
                </el-table-column>
                <el-table-column
                  show-overflow-tooltip
                  label="深化备注"
                  #default="{ row }"
                  width="300"
                  prop="deepenRemark"
                >
                </el-table-column>
              </el-table>
              <div v-else style="padding-left: 48px; box-sizing: border-box">
                <el-table
                  class="avue-crud"
                  ref="tableItem"
                  :data="item.detailVOList"
                  :row-class-name="tableRowClassName"
                  border
                  row-key="id"
                  align="center"
                  :tree-props="{
                    children: 'splitDTOList',
                  }"
                  :cell-style="cellStyle"
                  :row-style="rowStyle"
                >
                  <el-table-column
                    label="设备名称"
                    #default="{ row }"
                    show-overflow-tooltip
                    prop="customProductName"
                    width="130"
                  >
                    <el-tag
                      size="small"
                      type="danger"
                      v-if="row.splitDTOList && row.splitDTOList.length > 0"
                      effect="plain"
                      >拆</el-tag
                    >
                    <span>{{ row.customProductName }}</span>
                  </el-table-column>
                  <el-table-column
                    label="规格型号"
                    show-overflow-tooltip
                    #default="{ row }"
                    prop="customProductSpecification"
                    width="130"
                  >
                    {{
                      row.customProductSpecification || row.product?.productSpecification
                    }}</el-table-column
                  >

                  <el-table-column
                    label="产品描述"
                    show-overflow-tooltip
                    #default="{ row }"
                    prop="customProductDescription"
                  >
                    {{ row.customProductDescription || row.product?.description }}
                  </el-table-column>
                  <el-table-column
                    label="品牌"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="productBrand"
                  >
                    {{ row.productBrand || row.product?.productBrand }}
                  </el-table-column>
                  <el-table-column
                    label="单位"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="product.unitName"
                  >
                    <el-popover v-if="!props.detail" placement="right" trigger="click">
                      <template #default>
                        <el-input v-model="row.customUnit" placeholder="请输入单位"></el-input>
                      </template>
                      <template #reference>
                        <div style="height: 100%; width: 100%; cursor: pointer">
                          {{ row.customUnit || row.product?.unitName || '---' }}
                        </div>
                      </template>
                    </el-popover>
                    <span v-else> {{ row.customUnit || row.product?.unitName }}</span>
                  </el-table-column>
                  <el-table-column
                    label="数量"
                    width="80"
                    align="center"
                    #default="{ row }"
                    prop="number"
                  >
                    <span>{{ row.splitDTOList ? row.number : 0 }}</span>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    :label="form.deepenType == 0 ? '深化数量' : '售前深化数量'"
                    #default="{ row }"
                    width="120"
                    prop="deepenNumber"
                  >
                    <el-input-number
                      v-model="row.deepenNumber"
                      style="width: 80%"
                      v-if="!props.detail && form.deepenType == 0"
                      size="small"
                      controls-position="right"
                      laceholder=""
                    ></el-input-number>
                  </el-table-column>
                  <el-table-column
                    align="center"
                    :label="form.deepenType == 1 && !props.detail ? '深化数量' : '项目经理深化数量'"
                    v-if="form.deepenType == 1 || props.detail"
                    #default="{ row }"
                    width="120"
                    prop="leaderDeepenNumber"
                  >
                    <el-input-number
                      v-model="row.leaderDeepenNumber"
                      style="width: 80%"
                      controls-position="right"
                      v-if="!props.detail && form.deepenType == 1"
                      size="small"
                      laceholder=""
                    ></el-input-number>
                  </el-table-column>
                  <el-table-column
                    show-overflow-tooltip
                    label="深化备注"
                    #default="{ row }"
                    :width="!props.detail ? 210 : 300"
                    prop="deepenRemark"
                  >
                    <el-input
                      v-if="!props.detail"
                      v-model="row.deepenRemark"
                      type="textarea"
                      placeholder=""
                    ></el-input>
                  </el-table-column>
                  <el-table-column
                    label="操作"
                    #default="{ row, $index }"
                    align="center"
                    fixed="right"
                    v-if="!props.detail"
                    width="90"
                    prop="menu"
                  >
                    <el-button
                      type="primary"
                      text
                      icon="delete"
                      v-if="row.uuid && row.splitDTOList"
                      @click="deleteProduct(rowP, $index)"
                      >删除</el-button
                    >
                    <el-button
                      type="primary"
                      text
                      v-if="row.splitDTOList"
                      icon="Operation"
                      @click="handleDempose(row)"
                      >拆解</el-button
                    >
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <wfProductSelect
        @onConfirm="handleProductSelectConfirm"
        ref="productSelectRef"
          check-type="box"
      ></wfProductSelect>
    </div>
    <el-drawer title="拆解产品" v-model="demposeDrawer" size="80%">
      <div style="padding: 0 20px">
        <avue-crud
          ref="crud"
          @row-save="rowSave"
          @row-update="rowUpdate"
          @row-del="rowDel"
          :option="demposeOption"
          :data="currentRow.splitDTOList"
        >
          <template #menu-left>
            <!-- <el-button type="primary" @click="$refs?.crud.rowAdd" icon="plus">添加</el-button> -->
            <el-button type="primary" @click="addProductFromIn" icon="plus">从产品库添加</el-button>
          </template>
        </avue-crud>
      </div>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="handleDemposeConfirm">确 认</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { watchEffect, getCurrentInstance, onMounted } from 'vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { randomLenNum } from '@/utils/util';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  detail: {
    type: Boolean,
    default: false,
  },
  isView: {
    type: Boolean,
    default: false,
  },
  deepenStatus: {
    type: Number,
    default: 0,
  },
  height: Number,
});
const { proxy } = getCurrentInstance();
let treeData = ref([]);
let allData = ref([]);
let tableData = ref([]);
let form = ref({});
let loading = ref(false);
watchEffect(() => {
  if (props.id) {
    getDetail();
  }
});
onMounted(() => {
  if (props.id) {
    getDetail();
  }
});
function getDetail() {
  allData.value = [];
  tableData.value = [];
  loading.value = true;
  axios
    .get('/api/vt-admin/businessOpportunityOptionHistory/detailByOptionId', {
      params: {
        id: props.id,
      },
    })
    .then(res => {
      form.value = res.data.data;
      form.value.businessOpportunityId = props.id;
      formatData(res.data.data.moduleVOList);
      form.value.moduleVOList = null;
      loading.value = false;
    });
}

let treeOption = ref({
  addBtn: false,
  defaultExpandAll: true,
  props: {
    value: 'value',
    label: 'label',
  },
});

function formatData(data) {
  treeData.value = data.map(item => {
    item.label = item.moduleName;
    const isHasClassify = item.detailVOList.every(item => item.classify);
    if (isHasClassify) {
      item.children = item.detailVOList
        .map((i, index) => {
          return {
            value: i.classify,
            label: i.classify || '---',
            parentId: item.moduleId,
            id: i.moduleId + i.classify,
          };
        })
        .reduce((acc, cur) => {
          if (!acc.find(item => item.value === cur.value)) {
            acc.push({
              ...cur,
              // deepenNumber: cur.deepenNumber ? cur.deepenNumber * 1 : cur.number * 1,
              // leaderDeepenNumber: cur.leaderDeepenNumber
              //   ? cur.leaderDeepenNumber * 1
              //   : cur.deepenNumber * 1,
              detailVOList: item.detailVOList
                .filter(i => i.classify == cur.value)
                .map(item => {
                  return {
                    ...item,
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                    splitDTOList:
                      item.splitVOS && item.splitVOS.length > 0
                        ? item.splitVOS.map(item => {
                            item.number = 0;
                            return {
                              ...item,
                              uuid: randomLenNum(10),
                              number: parseFloat(item.number),
                              deepenNumber: item.deepenNumber
                                ? item.deepenNumber * 1
                                : item.number * 1,
                              leaderDeepenNumber:
                                form.value.deepenType == 0
                                  ? null
                                  : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                                  ? item.leaderDeepenNumber * 1
                                  : item.deepenNumber * 1,
                            };
                          })
                        : [],
                  };
                }),
            });
          }
          return acc;
        }, []);
    } else {
      item.detailVOList = item.detailVOList.map(item => {
        return {
          ...item,
          deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
          leaderDeepenNumber:
            form.value.deepenType == 0
              ? null
              : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
              ? item.leaderDeepenNumber * 1
              : item.deepenNumber * 1,
          splitDTOList:
            item.splitVOS && item.splitVOS.length > 0
              ? item.splitVOS.map(item => {
                  item.number = 0;
                  return {
                    ...item,
                    uuid: randomLenNum(10),
                    number: parseFloat(item.number),
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                  };
                })
              : [],
        };
      });
    }
    item.isHasClassify = isHasClassify;

    return item;
  });
  allData.value = data;
}

// function handleNodeClick(node) {
//   if (!node) {
//     allData.value.forEach(item => {
//       item.detailVOList.forEach(element => {
//         element.deepenNumber =
//           element.deepenNumber || element.deepenNumber == 0
//             ? element.deepenNumber * 1
//             : element.number * 1;
//         element.leaderDeepenNumber =
//           element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//             ? element.leaderDeepenNumber * 1
//             : element.deepenNumber * 1;
//         tableData.value.push(element);
//       });
//     });
//   }
//   if (!node.parentId) {
//     ;
//     currModuleId.value = node.parentId;
//     currClassfiy.value = node.value;
//     tableData.value = [];
//     node.detailVOList.forEach(element => {
//       element.deepenNumber =
//         element.deepenNumber || element.deepenNumber == 0
//           ? element.deepenNumber * 1
//           : element.number * 1;
//       element.leaderDeepenNumber =
//         element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//           ? element.leaderDeepenNumber * 1
//           : element.deepenNumber * 1;
//       tableData.value.push(element);
//     });
//   } else {
//     currModuleId.value = node.parentId;
//     currClassfiy.value = node.value;
//     tableData.value = [];
//     if (node.children && node.children.length > 0) return;
//     allData.value
//       .find(item => item.moduleId === node.parentId)
//       .detailVOList.forEach(element => {
//         if (element.classify === node.value) {
//           element.deepenNumber =
//             element.deepenNumber || element.deepenNumber == 0
//               ? element.deepenNumber * 1
//               : element.number * 1;
//           element.leaderDeepenNumber =
//             element.leaderDeepenNumber || element.leaderDeepenNumber == 0
//               ? element.leaderDeepenNumber * 1
//               : element.deepenNumber * 1;
//           tableData.value.push(element);
//         }
//       });
//   }
// }

function handleNodeClick(node) {
  const id = 'box_' + node.moduleId;

  proxy.$nextTick(() => {
    if (node.parentId) {
      const ele = document.querySelector(`.row_${node.parentId}${node.value}`);
      ele.scrollIntoView({ behavior: 'smooth' });
      proxy.$refs[id][0].scrollIntoView({ behavior: 'smooth' });
    } else {
      proxy.$refs[id][0].scrollIntoView({ behavior: 'smooth' });
    }
  });
}
function rowClassName(val) {
  return 'row_' + val.row.parentId + val.row.value;
}
let currModuleId = ref(null);
let currClassfiy = ref(null);
let type = ref(0); // 0  分类添加产品  1 拆解添加产品
function handleAddProduct(row) {
  currModuleId.value = row.parentId;
  currClassfiy.value = row.value;
  type.value = 0;
  proxy.$refs.productSelectRef.visible = true;
}
function handleProductSelectConfirm(ids) {
  // 处理多选情况，ids 可能是字符串 "1545448,152626" 或数组
  const idArray = Array.isArray(ids) ? ids.map(item => item.id || item) : ids.split(',');

  // 为每个选中的产品ID获取详情并添加
  const promises = idArray.map(productId => {
    return axios.get('/api/vt-admin/product/detail', {
      params: {
        id: productId,
      },
    });
  });

  Promise.all(promises).then(responses => {
    responses.forEach(res => {
      const {
        productName,
        productSpecification,
        description,
        productBrand,
        unitName,
        id,
        number = 0,
      } = res.data.data;

      const data = {
        customProductName: productName,
        customProductSpecification: productSpecification,
        customProductDescription: description,
        product: {
          productBrand: productBrand,
          unitName: unitName,
        },
        productBrand: productBrand,
        customUnit: unitName,
        uuid: id,
        productId: id,
        number: number,
        deepenNumber: form.value.deepenType == 0 ? 1 : null,
        leaderDeepenNumber: form.value.deepenType == 0 ? null : 1,
        classify: currClassfiy.value,
        splitDTOList: type.value == 0 ? [] : null,
      };

      if (type.value == 0) {
        treeData.value
          .find(item => item.moduleId === currModuleId.value)
          .children.find(item => item.value == currClassfiy.value)
          .detailVOList.push(data);
      } else {
        currentRow.value.splitDTOList.push(data);
      }
    });
  }).catch(error => {
    console.error('获取产品详情失败:', error);
    proxy.$message.error('获取产品详情失败');
  });
}
function getData() {
  const data = {
    ...form.value,
    moduleHistoryDTOList: treeData.value.map(item => {
      return {
        ...item,
        detailHistoryDTOS: item.isHasClassify
          ? item.children.reduce((pre, cur) => {
              return [...pre, ...cur.detailVOList];
            }, [])
          : item.detailVOList,
        detailVOList: null,
      };
    }),
  };
  ;
  return data;
}
function setId(id) {
  form.value.id = id;
}

function tableRowClassName(row) {
  return row.number == 0 ? 'success-row' : '';
}
defineExpose({ getData, setId, getDetail });
// 删除产品
function deleteProduct(rowp, index) {
  rowp.detailVOList.splice(index, 1);
  // const index = treeData.value
  //   .find(i => i.moduleId === item.moduleId).children(item => item.value === row.classify)
  //   .detailVOList.findIndex(item => item.uuid === row.uuid);
  //   treeData.value.find(i => i.moduleId === item.moduleId).children.find(item => item.value == row.classify).detailVOList.splice(index, 1);
}
// function arraySpanMethod({  row,
//   column,
//   rowIndex,
//   columnIndex,}) {
//   if(column == 1){
//     return
//   }
// }
function cellStyle({ row, column }) {
  if (column.property == 'deepenNumber') {
    return {
      'background-color': row.deepenNumber != row.number ? 'var(--el-color-warning-light-5)' : '',
      color: row.deepenNumber != row.number ? '#fff' : '',
    };
  }
  if (column.property == 'leaderDeepenNumber') {
    return {
      'background-color':
        row.leaderDeepenNumber != row.number ? 'var(--el-color-danger-light-5)' : '',
      color: row.leaderDeepenNumber != row.number ? '#fff' : '',
    };
  }
}
function rowStyle({ row, column }) {
  
  console.log(row);
  if ((row.number == 0 || !row.number) && row.deepenNumber * 1 > 0) {
    return {
      'background-color': 'var(--el-color-warning-light-9)',
    };
  } else if (
    (row.number == 0 || !row.number) &&
    row.leaderDeepenNumber * 1 > 0 &&
    (row.deepenNumber == 0 || !row.deepenNumber)
  ) {
    return {
      'background-color': 'var(--el-color-danger-light-9)',
    };
  }
}
// 拆解产品
let demposeDrawer = ref(false);
let currentRow = ref({});
let demposeOption = ref({
  header: true,
  menu: true,
  editBtn: true,
  viewBtn: false,
  addBtn: false,
  border: true,
  delBtn: true,
  align: 'center',

  dialogType: 'drawer',
  dialogClickModal: true,
  dialogWidth: '60%',
  menuWidth: 160,
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',
      width: 200,
      cell: true,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      cell: true,
      width: 130,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      cell: true,
      overHidden: true,
      // search: true,
      span: 24,
      width: 200,
      type: 'input',
    },
    {
      label: '描述',
      prop: 'customProductDescription',
      cell: true,
      overHidden: true,
      // search: true,
      span: 24,
      width: 250,
      type: 'textarea',
    },
    {
      label: '单位',
      prop: 'customUnit',
      width: 90,
      span: 12,
      cell: true,
    },
    {
      label: '深化数量',
      prop: 'number',
      type: 'number',
      align: 'center',
      span: 12,
      width: 100,
      cell: true,
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      cell: true,
      // width: 160,
      type: 'textarea',
    },
  ],
});
function handleDempose(row) {
  currentRow.value = row;
  currentRow.value.splitDTOList.forEach(item => {
    item.number = item[form.value.deepenType == 0 ? 'deepenNumber' : 'leaderDeepenNumber'];
  });
  demposeDrawer.value = true;
}
function handleDemposeConfirm() {
  demposeDrawer.value = false;
  currentRow.value.splitDTOList.forEach(item => {
    item[form.value.deepenType == 0 ? 'deepenNumber' : 'leaderDeepenNumber'] = item.number;
  });
}
function rowSave(form, done, loading) {
  currentRow.value.splitDTOList.push({ ...form, uuid: randomLenNum(10) });
  done();
}
function rowUpdate(row, index, done, loading) {
  currentRow.value.splitDTOList[index] = row;
  done();
}
function rowDel(form) {
  currentRow.value.splitDTOList = currentRow.value.splitDTOList.filter(
    item => item.uuid != form.uuid
  );
}
function addProductFromIn() {
  type.value = 1;
  proxy.$refs.productSelectRef.visible = true;
}
</script>

<style lang="scss" scoped>
:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 30px !important;
}
</style>
