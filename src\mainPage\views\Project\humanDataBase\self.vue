<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #specialty-form="{ type }">
        <el-autocomplete
          :disabled="type == 'view'"
          style="width: 100%"
          v-model="form.specialty"
          value-key="value"
          :fetch-suggestions="querySearchAsync"
          placeholder="请输入技能标签"
        />
      </template>
      <template #name="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.name }}</el-link>
      </template>
      <template #workType1="{ row }">
        {{ row.salaryVOS && row.salaryVOS[0]?.workTypeName }}
      </template>
      <template #salary1="{ row }">
        {{ row.salaryVOS && row.salaryVOS[0]?.salary }}
      </template>
      <template #files-form>
        <File :fileList="form.attachList"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

function validateName(rule, value, callback) {
  axios
    .get('/api/vt-admin/humanResource/existName?', {
      params: {
        name: value,
        type: 0,
        id: form.value.id || null,
      },
    })
    .then(res => {
      if (res.data.data == 1) {
        callback(new Error('资源库已经存在此人，请重新输入'));
      } else {
        callback();
      }
    });
}
let { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  viewBtn: true,
  calcHeight: 30,
  dialogClickModal: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  labelWidth: 120,
  dialogType: 'drawer',
  dialogWidth: '60%',
  border: true,
  column: [
    // {
    //   label: '姓名',
    //   prop: 'name1',
    //   overHidden: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,

    //   formatter: (row, value) => {
    //     return row.name;
    //   },
    // },
    {
      label: '姓名',
      prop: 'name',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      search: true,
    },
    {
      label: '年龄',
      prop: 'age',
      //   hide: true,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '性别',
      prop: 'sex1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      dicData: [
        {
          label: '男',
          value: 1,
        },
        {
          label: '女',
          value: 0,
        },
      ],
      props: {
        label: 'label',
        value: 'value',
      },
      formatter: (row, value) => {
        return row.sex == 1 ? '男' : '女';
      },
    },

    {
      label: '联系电话',
      prop: 'phone1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.phone;
      },
    },

    {
      label: '技能标签',
      prop: 'specialty',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      overHidden: true,
    },

    {
      label: '态度标签',
      prop: 'attitudeLabel',
      type: 'input',

      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
    },

    {
      label: '居住区域',
      prop: 'address1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.address;
      },
    },
    {
      label: '是否保险',
      prop: 'isInsurance',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '是否开票',
      prop: 'isInvoice1',
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      type: 'radio',
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],

      formatter: (row, value) => {
        return row.$isInsurance;
      },
    },
    {
      label: '工种',
      prop: 'workType1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,

      type: 'select',
    },
    {
      label: '工价(元/天)',
      prop: 'salary1',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      placeholder: '请输入工价',
    },
    // {
    //   label: '开户行',
    //   prop: 'openBank1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.openBank;
    //   },
    // },
    // {
    //   label: '银行账号',
    //   prop: 'bankNumber1',
    //   addDisplay: false,
    //   editDisplay: false,
    //   viewDisplay: false,
    //   formatter: (row, value) => {
    //     return row.bankNumber;
    //   },
    // },
    {
      label: '备注',
      prop: 'remark1',
      addDisplay: false,
      editDisplay: false,
      overHidden: true,
      viewDisplay: false,
      formatter: (row, value) => {
        return row.remark;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '姓名',
          prop: 'name',
          width: 250,
          overHidden: true,
          search: true,
          rules: [
            {
              validator: validateName,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '性别',
          prop: 'sex',
          type: 'radio',
          dicData: [
            {
              label: '男',
              value: 1,
            },
            {
              label: '女',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },
        {
          label: '年龄',
          prop: 'age',
          width: 250,
          overHidden: true,
        },
        {
          label: '联系电话',
          prop: 'phone',
        },

        {
          label: '技能标签',
          prop: 'specialty',
          type: 'input',
        },

        {
          label: '态度标签',
          prop: 'attitudeLabel',
          type: 'input',
        },
        {
          label: '是否开票',
          prop: 'isInvoice',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
          control: val => {
            return {
              invoiceType: {
                display: val == 1,
              },
              taxRate: {
                display: val == 1,
              },
            };
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '税率',
          type: 'select',
          width: 80,
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '是否保险',
          prop: 'isInsurance',
          type: 'select',
          type: 'radio',
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
          props: {
            label: 'label',
            value: 'value',
          },
        },

        {
          label: '居住区域',
          prop: 'address',
        },
        {
          label: '引进人',
          prop: 'createUser',
          component: 'wf-user-select',
          disabled: true,
          value: proxy.$store.getters.userInfo.user_id,
        },
        {
          label: '工种',
          prop: 'workType',
          dicUrl: '/blade-system/dict/dictionary?code=workType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          type: 'select',
        },
        {
          label: '工价(元/天)',
          prop: 'salary',
          placeholder: '请输入工价',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          overHidden: true,
          span: 24,
        },
        {
          label: '附件',
          prop: 'fileIds',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          viewDisplay: false,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '附件',
          prop: 'files',
          span:24,
          addDisplay: false,
          editDisplay: false,
        },
      ],
    },
    // {
    //   label: '工种信息',
    //   column: [
    //     {
    //       type: 'dynamic',
    //       hide: true,
    //       span: 24,
    //       prop: 'salaryDTOList',
    //       children: {
    //         column: [
    //           {
    //             label: '工种',
    //             prop: 'workType',
    //             dicUrl: '/blade-system/dict/dictionary?code=workType',
    //             props: {
    //               label: 'dictValue',
    //               value: 'id',
    //             },
    //             type: 'select',
    //           },
    //           {
    //             label: '工价(元/天)',
    //             prop: 'salary',
    //             placeholder: '请输入工价',
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
    {
      label: '银行信息',
      column: [
        {
          label: '开户行',
          prop: 'openBank',
        },
        {
          label: '银行账号',
          prop: 'bankNumber',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/humanResource/save';
const delUrl = '/api/vt-admin/humanResource/remove?ids=';
const updateUrl = '/api/vt-admin/humanResource/update';
const tableUrl = '/api/vt-admin/humanResource/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,

        type: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    type: 0,
    files: form.fileIds?.map(item => item.value).join(','),
    salaryDTOList: [
      {
        workType: form.workType,
        salary: form.salary,
      },
    ],
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    files: form.value.fileIds?.map(item => item.value).join(','),
    salaryDTOList: [
      {
        workType: row.workType,
        salary: row.salary,
      },
    ],
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function querySearchAsync(value, cb) {
  axios.get('/api/vt-admin/humanResource/getSpecialtyList').then(res => {
    console.log(res.data.data);
    cb(
      res.data.data.map(item => {
        return {
          value: item,
        };
      })
    );
  });
}
function beforeOpen(done, type) {
  if (type == 'edit' || type == 'view') {
    form.value.salaryDTOList = form.value.salaryVOS;
    form.value.workType = form.value.salaryVOS[0]?.workType;
    form.value.salary = form.value.salaryVOS[0]?.salary;
    form.value.fileIds = form.value.attachList?.map(item => {
      return { value: item.id, label: item.originalName };
    });
  }

  done();
}
</script>

<style lang="scss" scoped></style>
