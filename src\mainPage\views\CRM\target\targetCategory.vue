<template>
  <basic-container>
    <el-row :gutter="10">
      <!-- <el-col :span="6">
        <el-card body-style="padding:5px" style="height: 120px; margin-bottom: 10px" shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between">
              <span>{{ params.year }}业务员年度目标</span>
            </div>
          </template>

          <div style="margin-bottom: 10px">
            <div v-if="userId" style="">
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  style="margin-bottom: 5px; width: 65%"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="70"
                />
                <el-text>保底目标</el-text>
              </div>
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="100"
                  status="success"
                  style="margin-bottom: 5px; width: 65%"
                />
                <el-text>标准目标</el-text>
              </div>
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="70"
                  style="margin-bottom: 5px; width: 65%"
                  status="warning"
                />
                <el-text>冲刺目标</el-text>
              </div>
            </div>
            <el-empty v-else :image-size="50" description="请选中业务员" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card body-style="padding:5px" style="height: 120px; margin-bottom: 10px" shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between">
              <span>{{ params.year }}业务员季度目标</span>
            </div>
          </template>

          <div style="margin-bottom: 10px">
            <div v-if="userId" style="">
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  style="margin-bottom: 5px; width: 65%"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="70"
                />
                <el-text>保底目标</el-text>
              </div>
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="100"
                  status="success"
                  style="margin-bottom: 5px; width: 65%"
                />
                <el-text>标准目标</el-text>
              </div>
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="70"
                  style="margin-bottom: 5px; width: 65%"
                  status="warning"
                />
                <el-text>冲刺目标</el-text>
              </div>
            </div>
            <el-empty v-else :image-size="50" description="请选中业务员" />
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card body-style="padding:5px" style="height: 120px; margin-bottom: 10px" shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between">
              <span>{{ params.year }}业务员月度目标</span>
            </div>
          </template>

          <div style="margin-bottom: 10px">
            <div v-if="userId" style="">
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  style="margin-bottom: 5px; width: 65%"
                  :text-inside="true"
                  :stroke-width="26"
                  :percentage="70"
                />
                <el-text>保底目标</el-text>
              </div>
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="100"
                  status="success"
                  style="margin-bottom: 5px; width: 65%"
                />
                <el-text>标准目标</el-text>
              </div>
              <div style="display: flex; align-items: center; gap: 20px">
                <el-progress
                  :text-inside="true"
                  :stroke-width="15"
                  :percentage="70"
                  style="margin-bottom: 5px; width: 65%"
                  status="warning"
                />
                <el-text>冲刺目标</el-text>
              </div>
            </div>
            <el-empty v-else :image-size="50" description="请选中业务员" />
          </div>
        </el-card>
      </el-col> -->
      <el-col :span="12">
        <el-card body-style="padding:5px" style="height: 120px; margin-bottom: 10px" shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between">
              <span
                >{{ params.year }}板块{{
                  currentRow.targetClassify == 1 ? currentRow.$quarter : currentRow.$month
                }}季度销售目标</span
              >
              <el-select size="small" v-model="params.quarter" placeholder="">
                <el-option
                  v-for="item in [
                    {
                      value: 1,
                      label: '第一季度',
                    },
                    {
                      value: 2,
                      label: '第二季度',
                    },
                    {
                      value: 3,
                      label: '第三季度',
                    },
                    {
                      value: 4,
                      label: '第四季度',
                    },
                  ]"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </template>

          <div style="margin-bottom: 10px">
            <div style="margin-bottom: 10px">
              <div style="">
                <div style="display: flex; align-items: center; gap: 20px">
                  <el-progress
                    style="margin-bottom: 5px; width: calc(100% - 300px)"
                    :text-inside="true"
                    :stroke-width="15"
                    :percentage="
                      (
                        ((allData[0]?.list[params.quarter - 1]?.completePrice * 1) /
                          (allData[0]?.list[params.quarter - 1]?.minTarget * 1)) *
                        100
                      ).toFixed(2) > 100? 100:   (
                        ((allData[0]?.list[params.quarter - 1]?.completePrice * 1) /
                          (allData[0]?.list[params.quarter - 1]?.minTarget * 1)) *
                        100
                      ).toFixed(2)
                    "
                  />
                  <el-text style="font-weight: bolder; min-width: 300px" size="default"
                    ><el-text size="default" type="primary">保底目标 </el-text>
                    {{ allData[0]?.list[params.quarter - 1]?.completePrice * 1 }}/
                    <el-text size="default" type="primary">{{
                      allData[0]?.list[params.quarter - 1]?.minTarget
                    }}</el-text>
                  </el-text>
                </div>
                <div style="display: flex; align-items: center; gap: 20px">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="15"
                    :percentage="
                      (
                        ((allData[0]?.list[params.quarter - 1]?.completePrice * 1) /
                          (allData[0]?.list[params.quarter - 1]?.standardTarget * 1)) *
                        100
                      ).toFixed(2) > 100? 100: (
                        ((allData[0]?.list[params.quarter - 1]?.completePrice * 1) /
                          (allData[0]?.list[params.quarter - 1]?.standardTarget * 1)) *
                        100
                      ).toFixed(2)
                    "
                    status="success"
                    style="margin-bottom: 5px; width: calc(100% - 300px)"
                  />
                  <el-text style="font-weight: bolder; min-width: 300px" size="default"
                    ><el-text size="default" type="success">标准目标</el-text>
                    {{ allData[0]?.list[params.quarter - 1]?.completePrice * 1 }}/
                    <el-text size="default" type="success">{{
                      allData[0]?.list[params.quarter - 1]?.standardTarget
                    }}</el-text>
                  </el-text>
                </div>
                <div style="display: flex; align-items: center; gap: 20px">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="15"
                    :percentage="
                      (
                        ((allData[0]?.list[params.quarter - 1]?.completePrice * 1) /
                          (allData[0]?.list[params.quarter - 1]?.sprintTarget * 1)) *
                        100
                      ).toFixed(2) > 100? 100: (
                        ((allData[0]?.list[params.quarter - 1]?.completePrice * 1) /
                          (allData[0]?.list[params.quarter - 1]?.sprintTarget * 1)) *
                        100
                      ).toFixed(2)
                    "
                    style="margin-bottom: 5px; width: calc(100% - 300px)"
                    status="warning"
                    color="#f6ca9d"
                  />
                  <el-text size="default" style="font-weight: bolder; min-width: 300px"
                    ><el-text size="default" type="primary" style="color: #f6ca9d"
                      >冲刺目标</el-text
                    >
                    {{ allData[0]?.list[params.quarter - 1]?.completePrice * 1 }}/
                    <el-text size="default" style="color: #f6ca9d" type="primary">{{
                      allData[0]?.list[params.quarter - 1]?.sprintTarget
                    }}</el-text>
                  </el-text>
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card body-style="padding:5px" style="height: 120px; margin-bottom: 10px" shadow="never">
          <template #header>
            <div v-if="targetForm" style="display: flex; justify-content: space-between">
              <span>{{ params.year }}板块年度销售目标</span>
            </div>
          </template>

          <div style="margin-bottom: 10px">
            <div style="margin-bottom: 10px">
              <div style="">
                <div style="display: flex; align-items: center; gap: 20px">
                  <el-progress
                    style="margin-bottom: 5px; width: calc(100% - 300px)"
                    :text-inside="true"
                    :stroke-width="15"
                    :percentage="
                      (
                        (allData[0]?.list[0]?.completePrice * 1 +
                          allData[0]?.list[1]?.completePrice * 1 +
                          allData[0]?.list[2]?.completePrice * 1 +
                          allData[0]?.list[3]?.completePrice * 1) /
                        (allData[0]?.list[0]?.minTarget * 1 +
                          allData[0]?.list[0]?.minTarget * 1 +
                          allData[0]?.list[0]?.minTarget * 1 +
                          allData[0]?.list[0]?.minTarget * 1)
                      ).toFixed(2) * 100 > 100? 100 : ( (
                        (allData[0]?.list[0]?.completePrice * 1 +
                          allData[0]?.list[1]?.completePrice * 1 +
                          allData[0]?.list[2]?.completePrice * 1 +
                          allData[0]?.list[3]?.completePrice * 1) /
                        (allData[0]?.list[0]?.minTarget * 1 +
                          allData[0]?.list[0]?.minTarget * 1 +
                          allData[0]?.list[0]?.minTarget * 1 +
                          allData[0]?.list[0]?.minTarget * 1)
                      ) * 100).toFixed(2)
                    "
                  />
                  <el-text style="font-weight: bolder; min-width: 300px" size="default"
                    ><el-text size="default" type="primary">保底目标</el-text>
                    {{
                      (
                        allData[0]?.list[0]?.completePrice * 1 +
                        allData[0]?.list[1]?.completePrice * 1 +
                        allData[0]?.list[2]?.completePrice * 1 +
                        allData[0]?.list[3]?.completePrice * 1
                      ).toFixed(2)
                    }}/
                    <el-text size="default" type="primary">{{
                      (
                        allData[0]?.list[0]?.minTarget * 1 +
                        allData[0]?.list[0]?.minTarget * 1 +
                        allData[0]?.list[0]?.minTarget * 1 +
                        allData[0]?.list[0]?.minTarget * 1
                      ).toFixed(2)
                    }}</el-text>
                  </el-text>
                </div>
                <div style="display: flex; align-items: center; gap: 20px">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="15"
                    :percentage="
                      (
                        (allData[0]?.list[0]?.completePrice * 1 +
                          allData[0]?.list[1]?.completePrice * 1 +
                          allData[0]?.list[2]?.completePrice * 1 +
                          allData[0]?.list[3]?.completePrice * 1) /
                        (allData[0]?.list[0]?.standardTarget * 1 +
                          allData[0]?.list[1]?.standardTarget * 1 +
                          allData[0]?.list[2]?.standardTarget * 1 +
                          allData[0]?.list[3]?.standardTarget * 1)
                      ).toFixed(2) * 100 > 100? 100 :( (
                        (allData[0]?.list[0]?.completePrice * 1 +
                          allData[0]?.list[1]?.completePrice * 1 +
                          allData[0]?.list[2]?.completePrice * 1 +
                          allData[0]?.list[3]?.completePrice * 1) /
                        (allData[0]?.list[0]?.standardTarget * 1 +
                          allData[0]?.list[1]?.standardTarget * 1 +
                          allData[0]?.list[2]?.standardTarget * 1 +
                          allData[0]?.list[3]?.standardTarget * 1)
                      ) * 100).toFixed(2)
                    "
                    status="success"
                    style="margin-bottom: 5px; width: calc(100% - 300px)"
                  />
                  <el-text style="font-weight: bolder; min-width: 300px" size="default"
                    ><el-text size="default" type="success">标准目标</el-text>
                    {{
                      (
                        allData[0]?.list[0]?.completePrice * 1 +
                        allData[0]?.list[1]?.completePrice * 1 +
                        allData[0]?.list[2]?.completePrice * 1 +
                        allData[0]?.list[3]?.completePrice * 1
                      ).toFixed(2)
                    }}/
                    <el-text size="default" style="font-weight: bolder" type="success">{{
                      (
                        allData[0]?.list[0]?.standardTarget * 1 +
                        allData[0]?.list[1]?.standardTarget * 1 +
                        allData[0]?.list[2]?.standardTarget * 1 +
                        allData[0]?.list[3]?.standardTarget * 1
                      ).toFixed(2)
                    }}</el-text></el-text
                  >
                </div>
                <div style="display: flex; align-items: center; gap: 20px">
                  <el-progress
                    :text-inside="true"
                    :stroke-width="15"
                    :percentage="
                      (
                        (allData[0]?.list[0]?.completePrice * 1 +
                          allData[0]?.list[1]?.completePrice * 1 +
                          allData[0]?.list[2]?.completePrice * 1 +
                          allData[0]?.list[3]?.completePrice * 1) /
                        (allData[0]?.list[0]?.sprintTarget * 1 +
                          allData[0]?.list[1]?.sprintTarget * 1 +
                          allData[0]?.list[2]?.sprintTarget * 1 +
                          allData[0]?.list[3]?.sprintTarget * 1)
                      ).toFixed(2) * 100 > 100? 100: ((
                        (allData[0]?.list[0]?.completePrice * 1 +
                          allData[0]?.list[1]?.completePrice * 1 +
                          allData[0]?.list[2]?.completePrice * 1 +
                          allData[0]?.list[3]?.completePrice * 1) /
                        (allData[0]?.list[0]?.sprintTarget * 1 +
                          allData[0]?.list[1]?.sprintTarget * 1 +
                          allData[0]?.list[2]?.sprintTarget * 1 +
                          allData[0]?.list[3]?.sprintTarget * 1)
                      ) * 100).toFixed(2)
                    "
                    style="margin-bottom: 5px; width: calc(100% - 300px)"
                    status="warning"
                    color="#f6ca9d"
                  />
                  <el-text size="default" style="font-weight: bolder; min-width: 300px"
                    ><el-text size="default" type="primary" style="color: #f6ca9d"
                      >冲刺目标</el-text
                    >
                    {{
                      (
                        allData[0]?.list[0]?.completePrice * 1 +
                        allData[0]?.list[1]?.completePrice * 1 +
                        allData[0]?.list[2]?.completePrice * 1 +
                        allData[0]?.list[3]?.completePrice * 1
                      ).toFixed(2)
                    }}/
                    <el-text size="default" style="font-weight: bolder; color: #f6ca9d">{{
                      (
                        allData[0]?.list[0]?.sprintTarget * 1 +
                        allData[0]?.list[1]?.sprintTarget * 1 +
                        allData[0]?.list[2]?.sprintTarget * 1 +
                        allData[0]?.list[3]?.sprintTarget * 1
                      ).toFixed(2)
                    }}</el-text></el-text
                  >
                </div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-card body-style="padding:0" shadow="never">
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        style="margin-top: 5px"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="reset"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
        :span-method="spanMethod"
        :cell-style="cellStyle"
        @cell-dblclick="cellClick"
      >
        <!-- <template #search-menu="{ row, size }">
          <el-button
            title="配置比例自动计算下方计划量"
            plain
            type="primary"
            :size="size"
            @click="setRate"
            >比例配置 <el-icon><QuestionFilled /></el-icon
          ></el-button>
        </template> -->
        <template #targetClassify="{ row }">
          <div v-if="row.targetClassify != 3">
            <el-text style="color: #f6ca9d" v-if="row.targetClassify == 2">{{
              row.$targetClassify
            }}</el-text>
            <el-text v-else :type="row.targetClassify == 0 ? 'primary' : 'success'">{{
              row.$targetClassify
            }}</el-text>
          </div>
          <el-text v-if="row.targetClassify == 3"
            >{{ row.$targetClassify
            }}<el-icon @click="setRate(row)" style="color: var(--el-color-primary); cursor: pointer"
              ><Setting /></el-icon
          ></el-text>
        </template>
      </avue-crud>
    </el-card>

    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let crud = ref();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  header: true,
  calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 4,
  menu: false,
  menuWidth: 270,
  size: 'small',
  header: false,
  size: 'default',
  border: true,
  column: [
    // {
    //   label: '业务员',
    //   prop: 'userId',
    //   component: 'wf-user-select',
    //   overHidden: true,
    //   formatter: val => {
    //     return val.userName;
    //   },
    // },
    {
      label: '板块名称',
      prop: 'name',
      width: 100,
      overHidden: true,

      display: false,
    },
    {
      label: '查询日期',
      prop: 'year',
      type: 'year',
      hide: true,
      search: true,
      format: 'YYYY',
      valueFormat: 'YYYY',
    },
    {
      label: '目标类型',
      prop: 'targetClassify',
      type: 'select',
      // width: 80,
      dicData: [
        {
          value: 0,
          label: '保底目标',
          disabled: true,
        },
        {
          value: 1,
          label: '标准目标',
        },
        {
          value: 2,
          label: '冲刺目标',
        },
        {
          value: 3,
          label: '占比',
        },
      ],
      overHidden: true,
      control: val => {
        return {
          quarter: {
            display: val == 1,
          },
          month: {
            display: val == 2,
          },
        };
      },
    },
    {
      label: '目标值',
      prop: 'q5Target',
      width: 95,
      html:true,
      formatter: row => {
        return `<div style='color:var(--el-color-${row.targetClassify == 0?'primary':'success'});cursor:pointer;color:${row.targetClassify == 2?'#f6ca9d':''}'>${
          row.q5Target ? row.q5Target : row.targetClassify != 3 ? 0 : ''
        }</div>`;
      },
    },

    {
      label: 'Q1(1.1-3.31)',
      prop: 'quarter1',
      display: false,
      type: 'select',
      children: [
        {
          label: '计划量',
          prop: 'q1Target',
          type: 'number',
          width: 95,
        },
        // {
        //   label: '累计目标',
        //   prop: 'q1TotalTarget',
        //   type: 'number',
        //   width: 110,
        // },
        {
          label: '实际完成量',
          prop: 'q1CompletePrice',
          width: 95,
        },
        {
          label: '目标差距',
          prop: 'q1Gpa',
          width: 95,
          type: 'number',
          formatter: row => {
            if (!row.q1Gpa && row.targetClassify != 3) return 0;
            return row.q1Gpa * 1 >= 0 ? '+' + row.q1Gpa : row.q1Gpa;
          },
        },
      ],
    },
    {
      label: 'Q2(4.1-6.30)',
      prop: 'quarter2',
      display: false,
      type: 'select',
      children: [
        {
          label: '计划量',
          prop: 'q2Target',
          type: 'number',
          width: 95,
        },
        {
          label: '累计目标',
          prop: 'q2TotalTarget',
          type: 'number',
          width: 95,
        },
        {
          label: '实际完成量',
          prop: 'q2CompletePrice',
          width: 95,
        },
        {
          label: '目标差距',
          prop: 'q2Gpa',
          width: 95,
          type: 'number',
          formatter: row => {
            if (!row.q2Gpa && row.targetClassify != 3) return 0;
            return row.q2Gpa * 1 >= 0 ? '+' + row.q2Gpa : row.q2Gpa;
          },
        },
      ],
    },
    {
      label: 'Q3(7.1-9.30)',
      prop: 'quarter3',
      display: false,
      type: 'select',
      children: [
        {
          label: '计划量',
          prop: 'q3Target',
          type: 'number',
          width: 95,
        },
        {
          label: '累计目标',
          prop: 'q3TotalTarget',
          type: 'number',
          width: 95,
        },
        {
          label: '实际完成量',
          prop: 'q3CompletePrice',
          width: 95,
        },
        {
          label: '目标差距',
          prop: 'q3Gpa',
          width: 95,
          type: 'number',
          formatter: row => {
            if (!row.q3Gpa && row.targetClassify != 3) return 0;
            return row.q3Gpa * 1 >= 0 ? '+' + row.q3Gpa : row.q3Gpa;
          },
        },
      ],
    },
    {
      label: 'Q4(10.1-12.31)',
      prop: 'quarter4',
      display: false,
      type: 'select',
      children: [
        {
          label: '计划量',
          prop: 'q4Target',
          type: 'number',
          width: 95,
        },
        {
          label: '累计目标',
          prop: 'q4TotalTarget',
          type: 'number',
          width: 95,
        },
        {
          label: '实际完成量',
          prop: 'q4CompletePrice',
          width: 95,
        },
        {
          label: '目标差距',
          prop: 'q4Gpa',
          width: 95,
          type: 'number',
          formatter: row => {
            if (!row.q4Gpa && row.targetClassify != 3) return 0;
            return row.q4Gpa * 1 >= 0 ? '+' + row.q4Gpa : row.q4Gpa;
          },
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealTarget/add';
const delUrl = '/api/vt-admin/sealTarget/remove?ids=';
const updateUrl = '/api/vt-admin/sealTarget/edit';
const tableUrl = '/api/vt-admin/sealTarget/pageForCompany';
let params = ref({
  year: '' + new Date().getFullYear(),
  quarter: getCurrentQ(),
  searchType: 1,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
onMounted(() => {
  onLoad();
});
const urlObj = {
  ['']: '/api/vt-admin/sealTarget/pageForCompany',
  0: '',
  1: '/api/vt-admin/sealTarget/pageForBusinessType',
  2: '',
  3: '',
};
let allData = ref([]);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(urlObj[params.value.searchType], {
      params: {
        size,
        current,
        ...params.value,

        selectType: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = [];
      const nameRef = proxy.findObject(option.value.column, 'name');
      console.log(nameRef);
      nameRef.hide = params.value.searchType != 1;
      allData.value = res.data.data;
      if (params.value.searchType === '') {
        tableData.value = [{ targetValue: 0 }, { targetValue: 0 }, { targetValue: 0 }];
        res.data.data.forEach((item, index) => {
          const {
            completeGpa,
            completePrice,
            completeTotalTarget,
            standardTarget,
            minCompleteGpa,
            minCompleteTotalTarget,
            minTarget,
            sprintCompleteGpa,
            sprintCompleteTotalTarget,
            sprintTarget,
            id,
          } = item;
          // tableData.value.push({
          //   [`q${index}completeGpa`]: completeGpa,
          //   [`q${index}completePrice`]: completePrice,
          //   [`q${index}completeTotalTarget`]: completeTotalTarget,
          //   [`q${index}minCompleteGpa`]: minCompleteGpa,
          //   [`q${index}minCompleteTotalTarget`]: minCompleteTotalTarget,
          //   [`q${index}minTarget`]: minTarget,
          //   [`q${index}sprintCompleteGpa`]: sprintCompleteGpa,
          //   [`q${index}sprintCompleteTotalTarget`]: sprintCompleteTotalTarget,
          //   [`q${index}sprintTarget`]: sprintTarget,
          //   [`q${index}id`]: id,
          // });

          tableData.value[0] = {
            ...tableData.value[0],
            [`q${index + 1}Gpa`]: minCompleteGpa,
            [`q${index + 1}CompletePrice`]: completePrice,
            [`q${index + 1}Target`]: minTarget,
            [`q${index + 1}TotalTarget`]: minCompleteTotalTarget,
            [`q${index + 1}Id`]: id,
            targetValue: tableData.value[0]?.targetValue + minTarget * 1,
            targetClassify: 0,
          };
          tableData.value[1] = {
            ...tableData.value[1],
            [`q${index + 1}Gpa`]: completeGpa,
            [`q${index + 1}CompletePrice`]: completePrice,
            [`q${index + 1}Target`]: standardTarget,
            [`q${index + 1}TotalTarget`]: completeTotalTarget,
            [`q${index + 1}Id`]: id,
            targetValue: tableData.value[1]?.targetValue + standardTarget * 1,
            targetClassify: 1,
          };
          tableData.value[2] = {
            ...tableData.value[2],
            [`q${index + 1}Gpa`]: sprintCompleteGpa,
            [`q${index + 1}CompletePrice`]: completePrice,
            [`q${index + 1}Target`]: sprintTarget,
            [`q${index + 1}TotalTarget`]: sprintCompleteTotalTarget,
            [`q${index + 1}Id`]: id,
            targetValue: tableData.value[2]?.targetValue + sprintTarget * 1,
            targetClassify: 2,
          };
          console.log(tableData.value);
          console.log('总计' + ':' + tableData.value[2].targetValue);
        });
      } else {
        res.data.data.forEach((item, index) => {
          item.list.forEach((item1, index1) => {
            const {
              completeGpa,
              completePrice,
              completeTotalTarget,
              standardTarget,
              minCompleteGpa,
              minCompleteTotalTarget,
              minTarget,
              sprintCompleteGpa,
              sprintCompleteTotalTarget,
              sprintTarget,
              id,
              quarterRate,
            } = item1;

            tableData.value[index * 4 + 0] = {
              ...tableData.value[index * 4 + 0],
              [`q${index1 + 1}Gpa`]: minCompleteGpa,
              [`q${index1 + 1}CompletePrice`]: completePrice,
              [`q${index1 + 1}Target`]: minTarget,
              [`q${index1 + 1}TotalTarget`]: minCompleteTotalTarget,
              [`q${index1 + 1}Id`]: id,
              targetValue: res.data.data[index].list.reduce((pre, cur) => {
                pre += isNaN(cur.minTarget * 1) ? 0 : cur.minTarget * 1;
                return pre;
              }, 0),
              targetClassify: 0,
              name: item.name,
              id: id,
            };
            tableData.value[index * 4 + 1] = {
              ...tableData.value[index * 4 + 1],
              [`q${index1 + 1}Gpa`]: completeGpa,
              [`q${index1 + 1}CompletePrice`]: completePrice,
              [`q${index1 + 1}Target`]: standardTarget,
              [`q${index1 + 1}TotalTarget`]: completeTotalTarget,
              [`q${index1 + 1}Id`]: id,
              targetValue: res.data.data[index].list.reduce((pre, cur) => {
                pre += isNaN(cur.standardTarget * 1) ? 0 : cur.standardTarget * 1;
                return pre;
              }, 0),
              targetClassify: 1,
              name: item.name,
              id: id,
            };

            tableData.value[index * 4 + 2] = {
              ...(tableData.value[index * 4 + 2] || {}),
              [`q${index1 + 1}Gpa`]: sprintCompleteGpa,
              [`q${index1 + 1}CompletePrice`]: completePrice,
              [`q${index1 + 1}Target`]: sprintTarget,
              [`q${index1 + 1}TotalTarget`]: sprintCompleteTotalTarget,
              [`q${index1 + 1}Id`]: id,
              targetValue: res.data.data[index].list.reduce((pre, cur) => {
                pre += isNaN(cur.sprintTarget * 1) ? 0 : cur.sprintTarget * 1;
                return pre;
              }, 0),
              targetClassify: 2,
              name: item.name,
              id: id,
            };
            tableData.value[index * 4 + 3] = {
              ...(tableData.value[index * 4 + 3] || {}),
              targetClassify: 3,
              businessTypeId: item1.businessTypeId,
              [`q${index1 + 1}Target`]: quarterRate?quarterRate + '%' : 0,
              id: id,
            };
          });
        });
        console.log(tableData.value);
      }

      nextTick(() => {
        crud.value.setCurrentRow(tableData.value[0]);
        currentRow.value = tableData.value[0];
      });
    });
  getTarget();
  // getRate()
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    type: 1,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(152);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  params.value.year = '' + new Date().getFullYear();
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

let targetForm = ref({});
function getTarget() {
  axios
    .get('/api/vt-admin/sealTarget/detailForCompany', {
      params: {
        year: params.value.year,
      },
    })
    .then(res => {
      targetForm.value = res.data.data || {};
    });
}
function setTarget() {
  proxy.$refs.dialogForm.show({
    title: '设定目标',

    option: {
      column: [
        {
          label: '保底目标',
          prop: 'minTarget',
          type: 'number',
          value: targetForm.value.minTarget,
        },
        {
          label: '标准目标',
          prop: 'standardTarget',
          type: 'number',
          value: targetForm.value.standardTarget,
        },
        {
          label: '冲刺目标',
          prop: 'sprintTarget',
          type: 'number',
          value: targetForm.value.sprintTarget,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealTarget/addCompanyTarget', {
          ...targetForm,
          ...res.data,
          year: params.value.year,
        })
        .then(r => {
          proxy.$message.success('设置成功');
          getTarget();
          res.close();
        });
    },
  });
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  const obj = {
    // fontWeight: 'bolder',
  };
  const number = column.property.match(/\d+/g) && column.property.match(/\d+/g)[0] * 1;
  if (column.property == `q${params.value.quarter}Gpa`) {
    return {
      ...obj,
      backgroundColor: `var(--el-color-${
        row[`q${params.value.quarter}Gpa`] * 1 >= 0 ? 'success' : 'danger'
      }-light-7)`,
    };
  } else if (!(column.property.indexOf('Target') == -1 || column.property.indexOf('Total') > -1)) {
    return {
      ...obj,
      cursor: 'pointer',
    };
  }
  if (
    column.property == 'q4CompletePrice' ||
    column.property == 'q3CompletePrice' ||
    column.property == 'q2CompletePrice' ||
    column.property == 'q1CompletePrice'
  ) {
    return {
      fontWeight: 'bolder',
    };
  }
}
let currentRow = ref({});
function rowClick(row) {
  currentRow.value = row;
}
function getCurrentQ(params) {
  // 获取当前日期
  const currentDate = new Date();

  // 获取当前月份
  const currentMonth = currentDate.getMonth() + 1;

  // 计算当前季度
  const currentQuarter = Math.floor((currentMonth - 1) / 3) + 1;

  console.log(`当前是第${currentQuarter}季度`);
  return currentQuarter;
}
function cellClick(row, column, cell, event) {
  return
  console.log(row, column, cell, event);
  const number = column.property.match(/\d+/g)[0] * 1;
  console.log(row[`q${number}Id`]);
  // if (row[`q${number}Id`]) return;
  if (column.property != 'q5Target') return;
  proxy.$refs.dialogForm.show({
    title: '设定目标',
    option: {
      column: [
        {
          label: '目标',
          prop: 'target',
          type: 'number',
          value: row[`q${number}Target`],
        },
      ],
    },
    callback(res) {
      
      axios
        .post(row[`q${number}Id`] ? updateUrl : addUrl, {
          year: params.value.year,
          targetClassify: 0,
          type: 2,
          [`${['minTarget', 'standardTarget', 'sprintTarget'][row.$index % 3]}`]: res.data.target,
          id: row[`q${number}Id`],
        })
        .then(r => {
          proxy.$message.success('设置成功');
          onLoad();
          res.close();
        });
    },
  });
}
function spanMethod({ row, column, rowIndex, columnIndex }) {
  if (
    column.property == 'q4CompletePrice' ||
    column.property == 'q3CompletePrice' ||
    column.property == 'q2CompletePrice' ||
    column.property == 'q1CompletePrice'
  ) {
    if (rowIndex % 4 == 0) {
      return { rowspan: 3, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 0 };
    }
  }
  if (column.property == 'name') {
    if (rowIndex % 4 == 0) {
      return { rowspan: 4, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }

  if (rowIndex % 4 == 3) {
    if (
      column.property == 'q1Target' ||
      column.property == 'q2Target' ||
      column.property == 'q3Target' ||
      column.property == 'q4Target'
    ) {
      return { rowspan: 1, colspan: column.property == 'q1Target' ? 3 : 4 };
    } else if (
      column.property != 'name' &&
      column.property != 'year' &&
      column.property != 'targetClassify' &&
      column.property != 'q5Target'
    ) {
      return { rowspan: 0, colspan: 0 };
    }
  }
}
let tabs = [
  {
    value: '',
    label: '全部',
  },
  // {
  //   value: 0,
  //   label: '业务员',
  // },
  {
    value: 1,
    label: '业务板块',
  },
  // {
  //   value: 2,
  //   label: '经营体',
  // },
];
function handleTabClick(item, index) {
  if (params.value.searchType === index) {
    return;
  }
  params.value.searchType = index;
  onLoad();
}
let rateForm = ref({});
function getRate() {
  axios
    .get('/api/vt-admin/sealTargetConfig/detail', {
      params: {
        type: 2,
        year: params.value.year,
      },
    })
    .then(res => {
      rateForm.value = res.data.data;
    });
}
function setRate(row  = {}) {
  
  const dicData = allData.value.map(item => {
    return {
      label: item.name,
      value: item.list[0].businessTypeId,
    };
  });
  const { q1Target, q2Target, q3Target, q4Target } = row;
  proxy.$refs.dialogForm.show({
    title: '设置百分比',

    option: {
      column: [
        {
          label: '板块名称',
          type: 'select',
          dicData,
          span: 24,
          prop: 'businessTypeId',
          value: row.businessTypeId || dicData[0].value,
          rules: [
            {
              required: true,
              message: '请选择板块名称',
            },
          ],
        },
        {
          label: '第一季度',
          prop: 'oneQuarter',
          type: 'input',
          value: q1Target ? parseFloat(q1Target) : 0,
          append: '%',
          rules: [
            {
              required: true,
              message: '请输入第一季度百分比',
            },
          ],
        },
        {
          label: '第二季度',
          prop: 'twoQuarter',
          type: 'input',
          append: '%',
          value: q2Target ? parseFloat(q2Target) : 0,
          rules: [
            {
              required: true,
              message: '请输入第二季度百分比',
            },
          ],
        },
        {
          label: '第三季度',
          prop: 'threeQuarter',
          type: 'input',
          append: '%',
          value: q3Target ? parseFloat(q3Target) : 0,
          rules: [
            {
              required: true,
              message: '请输入第三季度百分比',
            },
          ],
        },
        {
          label: '第四季度',
          prop: 'fourQuarter',
          type: 'input',
          append: '%',
          value: q4Target ? parseFloat(q4Target) : 0,
          rules: [
            {
              required: true,
              message: '请输入第四季度百分比',
            },
          ],
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealTargetConfig/businessTypeConfig', {
          ...res.data,
          year: params.value.year,
        })
        .then(r => {
          proxy.$message.success('设置成功');
          res.close();
          onLoad();
        });
    },
  });
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 5px;
}
.project-main {
  // width: calc(100% - 12px);
  margin: 0 auto;
  margin-top: -5px;
  .tabs {
    width: calc(100% - 12px);
    // margin: 0 6px 0 7px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: var(--el-color-info-light-7);
      margin-bottom: -6px;
      border-radius: 5px 5px 0px 0px;

      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
:deep(.cell) {
  padding: 0 0 !important;
}
</style>
