<template>
  <basic-container :shadow="props.supplierId || props.orderId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      @keyup.enter="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex">
          <el-button type="primary" icon="plus" v-if="!route.query.id" @click="handllAdd"
            >新增</el-button
          >
          <div style="display: flex; align-items: center; gap: 20px">
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (contractTotalPrice * 1).toLocaleString() }}</el-text
            >
            <span style="font-weight: bolder">已付款总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (hasPaymentPrice * 1).toLocaleString() }}</el-text
            >
            <span style="font-weight: bolder">已收票总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (hasInvoicePrice * 1).toLocaleString() }}</el-text
            >
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="Edit" v-if="!row.$cellEdit" @click="handleEdit(row)"
          >编辑</el-button
        >
      </template>
      <template #contractCode="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.contractCode }}</el-link>
      </template>
      <template #arriveStatus="{ row }">
        <el-tag effect="plain" size="large" v-if="row.arriveStatus == 0" type="info">未到货</el-tag>
        <el-tag effect="plain" size="large" v-if="row.arriveStatus == 1" type="warning"
          >部分到货</el-tag
        >
        <el-tag effect="plain" size="large" type="success" v-else-if="row.arriveStatus == 2"
          >全部到货</el-tag
        >
      </template>
      <template #invoiceStatus="{ row }">
        <!-- <el-tag effect='plain' size="large" v-if="row.invoiceStatus == 0" type="info">未开票</el-tag> -->
        <el-tag
          effect="plain"
          size="large"
          v-if="row.isNeedInvoice == 1"
          :type="row.invoiceStatus == 0 ? 'success' : row.invoiceStatus == 1 ? 'danger' : 'warning'"
          >{{ row.$invoiceStatus }}</el-tag
        >
        <el-tag size="large" effect="plain" v-else type="info">无需开票</el-tag>
      </template>
      <template #paymentStatus="{ row }">
        <!-- <el-tag effect='plain' size="large" v-if="row.invoiceStatus == 0" type="info">未开票</el-tag> -->
        <el-tag
          effect="plain"
          size="large"
          :type="row.paymentStatus == 1 ? 'success' : row.paymentStatus == 0 ? 'danger' : 'warning'"
          >{{ row.$paymentStatus }}</el-tag
        >
      </template>
      <template #contractFiles="{ row }">
        <File :fileList="row.attachList || []"></File>
      </template>
      <template #productListBtn-form>
        <el-button type="primary" plain icon="plus" @click="visible = true">核对产品</el-button>
      </template>
      <template #contractPrice="{ row }">
        <el-tooltip
          v-if="row.discountPrice && row.discountPrice > 0"
          :content="`优惠金额：¥${Number(row.discountPrice).toLocaleString('zh-CN', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
          })}`"
          placement="top"
        >
          <span class="contract-price-with-discount">{{ row.contractPrice }}</span>
        </el-tooltip>
        <span v-else>{{ row.contractPrice }}</span>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>

    <el-drawer title="确认产品信息" size="90%" v-model="visible">
      <el-row :gutter="10">
        <el-col :span="8">
          <el-card shadow="never">
            <avue-form
              :option="addOption"
              ref="addFormRef"
              @submit="handleSubmit"
              v-model="addForm"
            ></avue-form>
          </el-card>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never">
            <el-table
              class="avue-crud"
              show-summary
              :summary-method="summaryMethod"
              :data="productList"
              border
              align="center"
            >
              <el-table-column
                label="设备名称"
                width="150"
                show-overflow-tooltip
                prop="productVO.productName"
              ></el-table-column>
              <el-table-column
                label="规格型号"
                show-overflow-tooltip
                prop="productVO.productSpecification"
              ></el-table-column>
              <!-- <el-table-column label="产品图片" #default="{ row }">
        <el-image
            style="width: 80px"
            :preview-src-list="[row.coverUrl]"
            :src="row.coverUrl"
          ></el-image>
      </el-table-column> -->
              <el-table-column
                label="产品描述"
                show-overflow-tooltip
                width="200"
                prop="productVO.description"
              ></el-table-column>
              <el-table-column
                label="品牌"
                width="80"
                prop="productVO.productBrand"
              ></el-table-column>
              <el-table-column label="单位" width="80" prop="productVO.unitName"></el-table-column>
              <!-- <el-table-column
                label="供应商"
                show-overflow-tooltip
                prop="supplierName"
              ></el-table-column> -->
              <el-table-column label="数量" width="100" #default="{ row }" prop="number">
              </el-table-column>

              <el-table-column label="单价" #default="{ row, $index }" prop="unitPrice">
                <el-input
                  v-model="productList[$index].unitPrice"
                  @blur="setContractTotalAmount"
                  size="small"
                ></el-input>
              </el-table-column>
              <el-table-column label="金额" width="180" #default="{ row }" prop="totalPrice">
                {{ row.unitPrice ? (row.number * row.unitPrice).toFixed(2) : '---' }}
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRef?.submit()">确 定</el-button>
        </div>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup lang="jsx">
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import moment from 'moment';
let route = useRoute();

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  cellBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  labelWidth: 120,
  column: [
    {
      label: '合同编号',
      prop: 'contractCode',
      width: 160,
      searchSpan: 4,
      overHidden: true,
      addDisplay: false,
      search: !props.orderId,
    },
    {
      label: '产品名称',
      prop: 'productName',
      hide: true,
      addDisplay: false,
      search: !route.query.id,
    },
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
      change: ({ value }) => {
        setUrl(value);
      },
      control: val => {
        return {
          orderId: {
            disabled: !val,
          },
        };
      },
    },
    {
      label: '关联订单',
      prop: 'orderId',
      span: 24,
      component: 'wf-order-select',
      overHidden: true,
      params: {
        checkType: 'checkbox',
        Url: '/api/vt-admin/purchaseOrder/pageForAddContract',
      },
      hide: true,
      change: ({ value }) => {
        console.log(value);
        getProductList(value);
      },
    },
    {
      label: '',
      hide: true,
      // labelWidth:0,
      prop: 'productListBtn',
    },
    {
      label: '供应商',
      prop: 'supplierName',
      overHidden: true,
      component: 'wf-supplier-drop',
      // width: 250,
      searchSpan: 4,
      hide: !!props.supplierId,
      addDisplay: false,
      searchLabelWidth: 100,
      search: !route.query.id,
    },
    {
      label: '对方订单编号',
      prop: 'supplierOrderNo',
      span: 24,
      overHidden: true,
    },
    {
      label: '关联报价',
      prop: 'offerName',
      addDisplay: false,
      overHidden: true,
      // width: 180,
      // search: !route.query.id,
    },
    {
      label: '采购数量',
      prop: 'purchaseNumber',
      addDisplay: false,
      width: 100,
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      search: !route.query.id,
      searchSpan: 3,
      addDisplay: false,
      width: 100,
      dicData: [
        {
          label: '未到货',
          value: '0',
        },
        {
          label: '部分到货',
          value: '1',
        },
        {
          label: '全部到货',
          value: '2',
        },
      ],
    },
    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      width: 130,
    },
    {
      label: '付款状态',
      prop: 'paymentStatus',
      type: 'select',
      width: 100,
      addDisplay: false,
      dicData: [
        {
          label: '未付款',
          value: 0,
        },
        {
          label: '部分付款',
          value: 2,
        },
        {
          label: '已付款',
          value: 1,
        },
      ],
    },
    {
      label: '已付款金额',
      prop: 'paymentPrice',
      addDisplay: false,
      width: 130,
    },
    {
      label: '发票状态',
      prop: 'invoiceStatus',
      addDisplay: false,
      width: 100,
      dicData: [
        {
          label: '未收票',
          value: 1,
        },
        {
          label: '已收票',
          value: 0,
        },
        {
          label: '部分收票',
          value: 2,
        },
      ],
    },

    {
      label: '已收票金额',
      prop: 'hasInvoice',
      addDisplay: false,
      width: 130,
      formatter: row => {
        return (row.hasInvoice * 1).toFixed(2);
      },
    },
    // {
    //   label: '支付时间',
    //   prop: 'paymentTime',
    //   addDisplay: false,
    //   width: 150,

    //   type: 'date',

    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    // },
    // {
    //   label: '已付款金额',
    //   prop: 'totalmoney',
    // },
    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '签订日期',
      type: 'date',
      prop: 'signDate',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      component: 'wf-daterange-search',
      search: true,
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      search: !route.query.id,
      span: 24,
      searchSpan: 5,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      cell: true,
      width: 150,
      hide: true,
    },
    {
      label: '送货地址',
      type: 'input',
      prop: 'deliveryAddress',
      value: '深圳市龙华区民治街道北站社区鸿荣源北站中心B塔2104-2105',
      span: 24,
      hide: true,
    },
    {
      label: '送货方式',
      type: 'radio',
      prop: 'deliveryMethod',
      span: 24,
      dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '付款期限',
      type: 'radio',
      span: 15,
      prop: 'paymentTerm',
      dicUrl: '/blade-system/dict/dictionary?code=payment_term',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '自定义账期',
      span: 9,
      labelTip: '填1-30之间的数字,即每个月这个日期结账',
      prop: 'fixedBillingDate',
      hide: true,
    },
    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

    // {
    //   label: '付款状态',
    //   type: 'payStatus',
    //   prop: 'select',
    //   dicData: [
    //     {
    //       label: '未付款',
    //       value: '0',
    //     },
    //     { label: '已付款', value: '1' },
    //   ],
    // },
    // {
    //   label: '创建日期',
    //   type: 'date',
    //   prop: 'createTime',
    //   width: 150,
    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD HH:mm',
    //   valueFormat: 'YYYY-MM-DD HH:mm',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContract/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseContract/update';
const tableUrl = '/api/vt-admin/purchaseContract/pageForList';
let params = ref({
  signDate:
    props.supplierId || props.orderId
      ? []
      : [`${new Date().getFullYear()}-01-01`, `${new Date().getFullYear()}-12-31`],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

const props = defineProps({
  orderId: {
    type: String,
    default: null,
  },
  supplierId: {
    type: String,
    default: null,
  },
});
onMounted(() => {
  onLoad();
});
let loading = ref(false);
let contractTotalPrice = ref(0);
let hasInvoicePrice = ref(0);
let hasPaymentPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        orderId: props.orderId,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          contractFiles: [],
        };
      });
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        orderId: props.orderId,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      contractTotalPrice.value = res.data.data.contractTotalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      hasPaymentPrice.value = res.data.data.hasPaymentPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    detailDTOList: productList.value.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
      };
    }),
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  debugger;
  const data = {
    ...row,
    contractFiles: row.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function toDetail(row) {
  router.push({
    path: '/procure/contractDetail',
    query: {
      id: row.id,
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleRowDBLClick(row, event) {
  row.$cellEdit = true;
}
function handleEdit(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑合同',
    option: {
      labelWidth: 120,
      column: [
        {
          label: '合同编号',
          prop: 'contractCode',
          width: 170,
          searchSpan: 4,
          value: row.contractCode,
          disabled: true,
          overHidden: true,
          search: !route.query.id,
        },
        {
          label: '关联供应商',
          prop: 'supplierName',
          overHidden: true,
          value: row.supplierName,
          disabled: true,
          width: 180,
          searchSpan: 4,
          searchLabelWidth: 100,
          search: !route.query.id,
        },
        {
          label: '对方订单编号',
          prop: 'supplierOrderNo',
          span: 12,
          overHidden: true,
        },
        // {
        //   label: '合同总额',
        //   prop: 'contractPrice',
        //   value: row.contractPrice,
        //   width: 130,
        // },
        {
          label: '采购日期',
          type: 'date',
          prop: 'purchaseDate',
          searchRange: true,
          value: row.purchaseDate,
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          search: true,
          span: 12,
          searchSpan: 5,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          cell: true,
          width: 150,
          rules: [
            {
              required: true,
              message: '请选择采购日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '是否开票',
          prop: 'isNeedInvoice',
          type: 'radio',
          span: 24,
          width: 100,
          value: row.isNeedInvoice,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
       
        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          dataType: 'object',
          cell: true,
          loadText: '附件上传中，请稍等',
          span: 24,
          width: 150,
          slot: true,
          value: [],
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      debugger;
      const data = {
        ...row,
        contractFiles:
          res.data.contractFiles && res.data.contractFiles.map(item => item.value).join(','),
        purchaseDate: res.data.purchaseDate,
        isNeedInvoice: res.data.isNeedInvoice,
        contractPrice: res.data.contractPrice,
      };
      axios
        .post(updateUrl, data)
        .then(r => {
          if (r.data.code == 200) {
            proxy.$message.success(r.data.msg);
            onLoad();
            res.close();
          }
        })
        .catch(err => {});
    },
  });
}
let addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    {
      label: '关联供应商',
      prop: 'supplierId',
      span: 24,
      component: 'wf-supplier-select',
      overHidden: true,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择供应商',
          trigger: 'change',
        },
      ],
      change: ({ value }) => {
        setUrl(value);
      },
      control: val => {
        return {
          orderId: {
            disabled: !val,
          },
        };
      },
    },
    {
      label: '关联订单',
      prop: 'orderId',
      span: 24,
      component: 'wf-order-select',
      overHidden: true,
      params: {
        checkType: 'checkbox',
        Url: '/api/vt-admin/purchaseOrder/pageForAddContract',
      },
      hide: true,
      change: ({ value }) => {
        console.log(value);
        getProductList(value);
      },
    },
    {
      label: '对方订单编号',
      prop: 'supplierOrderNo',
      span: 24,
      overHidden: true,
    },
    {
      label: '优惠金额',
      prop: 'discountPrice',
      span: 24,
      type: 'number',
      value: 0,
      width: 150,

      blur: ({ value }) => {
        const totalAmount = getContractTotalAmount();
        addForm.value.contractPrice = totalAmount;
      },
    },

    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
      span: 24,
      readonly: true,
      type: 'number',
      width: 150,
      rules: [
        {
          required: true,
          message: '请输入合同总额',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      value: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      width: 150,
      rules: [
        {
          required: true,
          message: '请选择签订日期',
        },
      ],
      // hide: true,
    },
    {
      label: '是否开票',
      prop: 'isNeedInvoice',
      type: 'radio',
      span: 24,
      width: 100,
      value: 1,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
      control:(res) => {
        return {
          billingCompany:{
            display:res == 1
          }
        }
      }
    },
    //  {
    //       label: '收票公司',
    //       type: 'select',
    //       prop: 'billingCompany',
    //       props: {
    //         label: 'companyName',
    //         value: 'id',
    //       },
    //       dicFormatter: res => {
    //         return res.data.records;
    //       },
    //       span:24,
    //       search: true,
    //       hide: true,
    //       cell: false,
    //       dicUrl: '/api/vt-admin/company/page?size=100',
    //     },
    {
      label: '送货地址',
      type: 'input',
      prop: 'deliveryAddress',
      value: '深圳市龙华区民治街道北站社区鸿荣源北站中心B塔2104-2105',
      span: 24,
      hide: true,
    },
    {
      label: '送货方式',
      type: 'radio',
      prop: 'deliveryMethod',
      span: 24,
      dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '付款期限',
      type: 'radio',
      span: 24,
      prop: 'paymentTerm',
      dicUrl: '/blade-system/dict/dictionary?code=payment_term',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },
    {
      label: '自定义账期',
      span: 9,
      span: 24,
      labelTip: '填1-30之间的数字,即每个月这个日期结账',
      prop: 'fixedBillingDate',
      hide: true,
    },
    {
      label: '付款方式',
      type: 'radio',
      prop: 'paymentMethod',
      span: 24,
      dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
      props: {
        value: 'id',
        label: 'dictValue',
      },
      hide: true,
    },

    // {
    //   label: '付款状态',
    //   type: 'payStatus',
    //   prop: 'select',
    //   dicData: [
    //     {
    //       label: '未付款',
    //       value: '0',
    //     },
    //     { label: '已付款', value: '1' },
    //   ],
    // },
    // {
    //   label: '创建日期',
    //   type: 'date',
    //   prop: 'createTime',
    //   width: 150,
    //   overHidden: true,
    //   addDisplay: false,
    //   format: 'YYYY-MM-DD HH:mm',
    //   valueFormat: 'YYYY-MM-DD HH:mm',
    // },
  ],
});
let addForm = ref({
  discountPrice: 0,
});
function setUrl(value) {
  const orderRef = proxy.findObject(addOption.value.column, 'orderId');
  orderRef.params.Url = '/api/vt-admin/purchaseOrder/pageForAddContract?supplierId=' + value;
}
let visible = ref(false);
let productList = ref([]);
function getProductList(value) {
  axios
    .get('/api/vt-admin/purchaseOrder/detailForAddContract', {
      params: {
        ids: value,
        supplierId: addForm.value.supplierId,
      },
    })
    .then(res => {
      productList.value = res.data.data;
      const totalAmount = getContractTotalAmount();

      nextTick(() => {
        addForm.value.contractPrice = totalAmount;
      });
    });
}

function getContractTotalAmount() {
  // 计算产品总额
  const totalAmount = productList.value.reduce((sum, item) => {
    const num = Number(item.number) || 0;
    const price = Number(item.unitPrice) || 0;
    return sum + num * price;
  }, 0);

  return totalAmount - (addForm.value.discountPrice || 0);
}
function beforeOpen(done, type) {
  if (type == 'add') {
    form.value.supplierId = props.supplierId;
    form.value.discountPrice = 0;
  }
  done();
}

const summaryMethod = ({ columns, data }) => {
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'totalPrice') {
      const values = data.map(item => {
        const num = Number(item.number) || 0;
        const price = Number(item.unitPrice) || 0;
        return num * price;
      });
      const subtotal = values.reduce((prev, curr) => prev + curr, 0);
      const discountPrice = Number(addForm.value.discountPrice) || 0;
      const contractPrice = Number(addForm.value.contractPrice) || 0;

      sums[index] = (
        <div class="summary-content">
          <div class="summary-item subtotal">
            <span class="label">小计：</span>
            <span class="value">
              ¥
              {subtotal.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
          {discountPrice > 0 && (
            <div class="summary-item discount">
              <span class="label">优惠金额：</span>
              <span class="value discount-value">
                -¥
                {discountPrice.toLocaleString('zh-CN', {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2,
                })}
              </span>
            </div>
          )}
          <div class="summary-item total">
            <span class="label">合同总额：</span>
            <span class="value total-value">
              ¥
              {contractPrice.toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
              })}
            </span>
          </div>
        </div>
      );
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
let addFormRef = ref();
function handllAdd() {
  if (addFormRef.value) {
    addFormRef.value.resetFields();
    addFormRef.value.resetForm();
  }
  // 清空新增表单数据
  addForm.value = {
    discountPrice: 0,
    isNeedInvoice: 1,
  };
  // 重置关联供应商和订单的URL
  const orderRef = proxy.findObject(addOption.value.column, 'orderId');
  orderRef.params.Url = '/api/vt-admin/purchaseOrder/pageForAddContract';
  // 清空产品列表
  productList.value = [];
  // 打开drawer
  visible.value = true;
}
function handleSubmit(form, done, loading) {
  // 新增合同逻辑
  const data = {
    ...form,
    detailDTOList: productList.value.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
        // 计算总价
        totalPrice: item.number * item.unitPrice,
      };
    }),
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        visible.value = false;
        done();
      }
    })
    .catch(err => {
      done();
    });
}
// 新增一个方法用于设置合同总额
function setContractTotalAmount() {
  const totalAmount = getContractTotalAmount();
  addForm.value.contractPrice = totalAmount.toFixed(2);
}
</script>

<style lang="scss" scoped>
// 合计行样式优化
:deep(.el-table__footer-wrapper) {
  .el-table__footer {
    .summary-content {
      display: flex;
      flex-direction: column;
      gap: 4px;
      padding: 8px 0;
      font-size: 13px;
      line-height: 1.4;

      .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .label {
          font-weight: 500;
          color: #606266;
        }

        .value {
          font-weight: 600;
          color: #303133;
        }

        &.subtotal {
          .value {
            color: #409eff;
          }
        }

        &.discount {
          .discount-value {
            color: #67c23a;
          }
        }

        &.total {
          border-top: 1px solid #ebeef5;
          padding-top: 4px;
          margin-top: 2px;

          .label {
            font-weight: 600;
            color: #303133;
          }

          .total-value {
            color: #e6a23c;
            font-size: 14px;
            font-weight: 700;
          }
        }
      }
    }
  }
}

// 表格合计行整体样式
:deep(.el-table__footer) {
  .el-table__cell {
    background-color: #fafafa;
    border-top: 2px solid #409eff;

    &:first-child {
      font-weight: 600;
      color: #303133;
    }
  }
}

// 合同价格悬浮样式
.contract-price-with-discount {
  cursor: help;
  color: #409eff;
  border-bottom: 1px dashed #409eff;

  &:hover {
    color: #66b1ff;
    border-bottom-color: #66b1ff;
  }
}
</style>
