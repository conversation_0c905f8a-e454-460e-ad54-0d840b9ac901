<template>
  <div class="basic-container"  :style="styleName" :class="{ 'basic-container--block': block }">
    <el-card style="min-height: 100%;"   body-style="height:100%" :shadow="shadow" class="basic-container__card">
      <slot></slot>
    </el-card>
  </div>
</template>

<script>
export default {
  name: 'basicContainer',
  props: {
    radius: {
      type: [String, Number],
      default: 10,
    },
    background: {
      type: String,
    },
    block: {
      type: Boolean,
      default: false,
    },
    shadow: {
      type: String,
      default: 'always',
    }
  },
  computed: {
    styleName() {
      return {
        borderRadius: `${this.radius}px`,
        background: this.background,
      };
    },
  },
};
</script>

<style lang="scss">
.basic-container {
  padding: 10px 6px;
  box-sizing: border-box;

  &--block {
    height: 100%;

    .basic-container__card {
      height: 100%;
      overflow-y: auto;
    }
  }

  &__card {
    width: 100%;
  }

  &:first-child {
    padding-top: 0;
  }
}
</style>
