<template>
  <div style="padding-bottom: 100px">
    <!-- <h3 style="text-align: center">需求调查表</h3> -->

    <Collapse v-model="activeNames">
      <CollapseItem :name="item1.id" v-for="item1 in allData">
        <template #title>
          <div style="position: relative">
            <div
              style="
                height: auto;
                width: 3px;
                background-color: var(--van-primary-color);
                position: absolute;
                top: 0;
                bottom: 0;
              "
            ></div>
            <span
              style="
                padding: 0 0 0 10px;
                font-weight: bolder;
                color: var(--van-primary-color);
                font-size: 20px;
              "
              >{{ item1.clueName }}</span
            >
          </div>
        </template>
        <Form :ref="`formRef-${item1.id}`">
          <CellGroup v-for="item in item1.propertyVOList">
            <Divider
              dashed
              :style="{ color: '#1989fa', borderColor: '#1989fa', padding: '0 16px', margin: '0' }"
              >{{ item.categoryName }}</Divider
            >
            <Divider
              v-if="item.categoryRemark"
              dashed
              :style="{
                color: '#1989fa',
                borderColor: '#1989fa',
                fontSize: '12px',
                padding: '0 16px',
                margin: '0',
              }"
              >建设思路</Divider
            >

            <TextEllipsis
              expand-text="展开"
              collapse-text="收起"
              rows="4"
              v-if="item.categoryRemark"
              style="padding: 0 20px; font-size: 12px; text-indent: 24px"
              :content="item.categoryRemark"
            ></TextEllipsis>
            <Divider
              v-if="item.categoryRemark"
              dashed
              :style="{ margin: '0', borderColor: '#1989fa' }"
            ></Divider>
            <Cell v-for="(item, index) in item.tableData">
              <Field
                :label="`${index + 1}.${item.collectionContent}${
                  item.type == 0 ? '(单选)' : item.type == 1 ? '(多选)' : ''
                }`"
                :rules="[{ required: item.isRequired == 1, message: '请填写此项' }]"
                label-align="top"
                rows="2"
                autosize
                :readonly="detailForm.requirementStatus == 1"
                v-model="item.value"
                type="textarea"
                :required="item.isRequired == 1"
                placeholder="请输入内容"
              >
                <template v-if="item.type == 0" #input>
                  <RadioGroup v-model="item.value" :disabled="detailForm.requirementStatus == 1">
                    <Radio
                      :name="i.value"
                      style="margin-bottom: 5px"
                      v-for="i in item.requirementPropertyValuesVOS"
                      >{{ i.value }}</Radio
                    >
                  </RadioGroup>
                </template>
                <template v-if="item.type == 1" #input>
                  <CheckboxGroup
                    v-model="item.values"
                    :disabled="detailForm.requirementStatus == 1"
                  >
                    <Checkbox
                      :name="i.value"
                      style="margin-bottom: 5px"
                      v-for="i in item.requirementPropertyValuesVOS"
                      shape="square"
                      >{{ i.value }}</Checkbox
                    >
                  </CheckboxGroup>
                </template>
              </Field>
              <Field
                v-if="
                  JSON.stringify(item.values).indexOf('其他') > -1 ||
                  JSON.stringify(item.value).indexOf('其他') > -1
                "
                v-model="item.otherInput"
                placeholder="请输入内容"
                label-align="top"
              />
            </Cell>
          </CellGroup>
        </Form>
      </CollapseItem>
    </Collapse>
    <div
      style="
        position: fixed;
        left: 0;
        bottom: 0px;
        right: 0;
        background-color: #fff;
        display: flex;
        justify-content: center;
        height: 60px;
        display: flex;
        align-items: center;
      "
    >
      <Button
        size="large"
        style="width: 80%"
        v-if="allData[0]?.requirementStatus == 0"
        @click="onSubmit"
        type="primary"
        >提交</Button
      >
    </div>
    <div></div>

    <Dialog
      v-model="detailForm.dialogVisible"
      title="提交需求调查表"
      @ok="handleConfirm"
      desc="是否确认需求调查表？提交后将不可修改"
    ></Dialog>
  </div>
</template>

<script setup>
import axios from 'axios';
import {
  Field,
  CellGroup,
  Cell,
  RadioGroup,
  Radio,
  CheckboxGroup,
  Checkbox,
  Button,
  Divider,
  Form,
  TextEllipsis,
  Collapse,
  CollapseItem,
} from 'vant';
import { Dialog, Toast } from 'vue-weui-next';
import { onMounted, onBeforeMount, ref, getCurrentInstance } from 'vue';
import wx from 'weixin-js-sdk';
let text = ref([]);
let { proxy } = getCurrentInstance();
let detailForm = ref({});
const getQueryString = name => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(decodeURI(r[2]));
  return null;
};
onBeforeMount(() => {
  axios
    .get('/api/vt-admin/purchaseInquiry/getWxShareSian/' + getQueryString('id') + '?type=1')
    .then(res => {
      console.log(res);
      const { appId, nonceStr, signature, timestamp } = res.data.data;

      wx.config({
        debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
        appId, // 必填，公众号的唯一标识
        timestamp, // 必填，生成签名的时间戳
        nonceStr, // 必填，生成签名的随机串
        signature, // 必填，签名
        jsApiList: ['updateAppMessageShareData'], // 必填，需要使用的JS接口列表
      });
    });

  wx.ready(() => {
    const title = '非常聚成采集表';
    const origin = window.location.origin;
    const imgUrl = `${origin}/img/logo2.png`;
    const link = window.location.href;
    wx.updateAppMessageShareData({
      title: title, // 分享标题
      desc: '非常聚成邀请你填写采集表',
      link, // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
      imgUrl, // 分享图标
      success: function () {},
    });
  });
});
onMounted(() => {
  getData();
});

let allData = ref([]);
let activeNames = ref([]);
function getData() {
  axios
    .get('/api/vt-admin/clue/detailByIds', {
      params: {
        id: getQueryString('id'),
      },
    })
    .then(res => {
      allData.value = res.data.data.map(item => {
        return {
          ...item,
          propertyVOList: item.propertyVOList.reduce((pre, cur) => {
            const data = pre.find(iten => iten.id == cur.categoryId);
            if (data) {
              data.tableData.push({
                ...cur,
                value: cur.type == 1 ? cur.value && cur.value.split(',') : cur.value,
                values: cur.type == 1 && !!cur.value ? cur.value.split(',') : [],
              });
            } else {
              pre.push({
                id: cur.categoryId,
                categoryName: cur.categoryName,
                categoryRemark: cur.categoryRemark,
                tableData: [
                  {
                    ...cur,
                    value: cur.type == 1 ? cur.value && cur.value.split(',') : cur.value,
                    values: cur.type == 1 && !!cur.value ? cur.value.split(',') : [],
                  },
                ],
              });
            }
            return pre;
          }, []),
        };
      });
      activeNames.value = allData.value.map(item => item.id);
      detailForm.value = {
        propertyVOList: allData,
      };
    });
}

function handleConfirm(params) {
  axios
    .post('/api/vt-admin/clue/batchComplete', {
      discoverVOList: allData.value.map(item => {
        return {
          id:item.id,
          propertyDTOList: item.propertyVOList.reduce((pre, cur) => {
            return pre.concat(
              cur.tableData.map(item => {
                return {
                  ...item,
                  value: item.type == 1 ? item.values.join(',') : item.value,
                };
              })
            );
          }, []),
        };
      }),
    })
    .then(res => {
      Toast.toast({
        text: '操作成功',
        type: 'success',
        duration: 1000,
      });
      getData();
    });
}

function onSubmit() {
  console.log(proxy.$refs);
  // 用于存储所有表单验证的 Promise
  const validationPromises = [];

  allData.value.forEach(item => {
    // 将每个表单的验证 Promise 添加到数组中
    validationPromises.push(proxy.$refs[`formRef-${item.id}`][0].validate());
  });

  // 使用 Promise.all 来等待所有表单验证完成
  Promise.all(validationPromises)
    .then(() => {
      // 所有表单都验证通过，设置 dialogVisible 为 true
      detailForm.value.dialogVisible = true;
    })
    .catch(() => {
      // 只要有一个表单验证失败，提示用户并阻止对话框显示
      Toast.toast({
        text: '数据未完全填写',
        type: 'warn',
        duration: 1000,
      });
    });
}
</script>

<style lang="scss" scoped></style>
