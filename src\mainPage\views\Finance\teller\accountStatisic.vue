<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex; justify-content: flex-start; align-items: center">
          <el-text size="large" style="font-weight: bolder; margin-left: 5px">总收入：</el-text>
          <el-text type="danger" size="large">￥{{ (inCome * 1).toLocaleString() }}</el-text>
          <el-text size="large" style="font-weight: bolder; margin-left: 5px">总支出：</el-text>
          <el-text type="success" size="large">￥{{ (outCome * 1).toLocaleString() }}</el-text>
          <!-- <el-text size="large" style="font-weight: bolder; margin-left: 5px">结余：</el-text>
          <el-text type="primary" size="large">￥{{ (earnings * 1).toLocaleString() }}</el-text> -->
          <div style="display: flex; align-items: center; margin-left: 10px">
            <span style="font-weight: bolder">账户总余额：</span>
            <el-text type="primary" size="large">￥{{ balance.toLocaleString() || 0 }}</el-text>
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="handleView(row)">明细</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" :title="`账户明细[${currentRow.abbreviation}]`" size="90%">
      <accountOutAndIn :accountId="currentRow.id"></accountOutAndIn>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import accountOutAndIn from './accountOutAndIn.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  dialogWidth:'30%',
  editBtnText:'修改初期余额',
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 220,
  menu: true,
  labelWidth: 120,
  border: true,
  column: [
    {
      label: '账户简称',
      prop: 'abbreviation',
      display:false,
      rules: [{ required: true, message: '请输入账户简称', trigger: 'blur' }],
    },
    {
      label: '期初余额',
      prop: 'beginPeriodBalance',
      type:'number',
      span:24
    },
    
    {
      label: '账户收入',
      prop: 'inCome',  display:false,
    },
    {
      label: '账户支出',  display:false,
      prop: 'outCome',
    },
    // {
    //   label: '账户结余',
    //   prop: 'earnings',
    // },
    {
      label: '账户余额',  display:false,
      prop: 'balance',
      html: true,
      formatter: row => {
        return `<div style='color:var(--el-color-${row.balance * 1 > 0 ? 'danger' : 'success'})'>${
          row.balance * 1 > 0 ? row.balance || 0 : row.balance || 0
        }</div>`;
      },
    },
    {
      label: '交易时间',
      prop: 'transactionTime',
      type: 'date',  display:false,
      width: 110,
      hide: true,
      format: 'YYYY-MM-DD HH:mm',
      searchSpan: 6,
      overHidden: true,
      component: 'wf-daterange-search',
      search: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '/api/vt-admin/companyAccount/update';
const tableUrl = '/api/vt-admin/companyAccount/pageForPrice';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
// let earnings = ref(0);
let balance = ref(0);
let inCome = ref(0);
let outCome = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.transactionTime && params.value.transactionTime[0],
        endTime: params.value.transactionTime && params.value.transactionTime[1],
        transactionTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/companyAccount/pageForPriceStatistics', {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.transactionTime && params.value.transactionTime[0],
        endTime: params.value.transactionTime && params.value.transactionTime[1],
        transactionTime: null,
      },
    })
    .then(res => {
      //   earnings.value = res.data.data.earnings;
      balance.value = res.data.data.balance;
      inCome.value = res.data.data.inCome;
      outCome.value = res.data.data.outCome;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let currentRow = ref('');
let drawer = ref(false);
function handleView(row) {
  currentRow.value = row;
  drawer.value = true;
}
</script>

<style lang="scss" scoped></style>
