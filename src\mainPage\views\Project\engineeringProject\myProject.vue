<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      :before-open="beforeOpen"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex; gap: 10px">
          <el-button type="primary" @click="handleAdd" icon="Plus">新增</el-button>
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">项目总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (totalPrice * 1).toLocaleString() || 0 }}</el-text
            >
          </div>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" @click="handleView(row)" text icon="view">详情</el-button>
        <el-button
          type="primary"
          @click="addContract(row)"
          v-if="row.isHasContract == 0 && row.offerId"
          text
          icon="CircleCheckFilled"
          >生成合同</el-button
        >
      </template>
      <template #projectCode="{ row, index }">
       <div style="display: flex;align-items: center;">
           <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag> <el-link @click="handleView(row)" type="primary">{{ row.projectCode }}</el-link>
        </div>
      </template>
      <template #projectSchedule="{ row, index }">
        <div style="display: flex; align-items: center; justify-content: center">
          <span style="font-weight: bolder">{{ row.projectSchedule }}</span>
          <el-icon size="20" v-if="row.isAddProjectPlan == 0" title="项目计划未填写"
            ><WarningFilled style="color: var(--el-color-danger); cursor: pointer"
          /></el-icon>
          <el-icon size="20" v-if="row.isAddProjectHours == 0" title="项目工时未填写"
            ><WarningFilled style="color: var(--el-color-warning); cursor: pointer"
          /></el-icon>
        </div>
      </template>
      <template #projectStatus="{ row }">
        <div style="display: flex; align-items: center;justify-content: center;gap: 3px;">
          <el-tag size="small" style="font-weight: bolder;"
          :type="
            row.projectStatus == 0
              ? 'info'
              : row.projectStatus == 1
              ? 'warning'
              : row.projectStatus == 2
              ? 'success'
              : 'success'
          "
          effect="dark"
          >{{ row.$projectStatus }}</el-tag
        >
        <el-button v-if="row.projectStatus == 2" title="验收完成" size="small"  type="primary" @click="valiDateComplete(row)" circle icon="check"></el-button>
        </div>
        
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="深化设计" size="90%" v-model="deepDrawer">
      <deepSign></deepSign>
    </el-drawer>
    <el-dialog
      v-model="addDialogVisible"
      width="300"
      :title="'选择类型'"
      style="border-radius: 10px"
      :show-close="false"
      align-center
    >
      <div style="display: flex; align-items: center; justify-content: center">
        <el-button type="primary" @click="handleToAdd(0)" size="large" round>有报价</el-button>
        <el-button type="primary" size="large" @click="handleToAdd(1)" round>无报价</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import deepSign from '../compoents/deepSign.vue';
import { ElMessageBox } from 'element-plus';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  searchIcon:true,
  searchIndex:5,
  labelWidth: 150,
  border: true,
 
  column: [
    {
      label: '项目编号',
      prop: 'projectCode',
      width: 180,
      overHidden: true,

      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '项目名称',
      prop: 'projectName',
      width: 250,
      overHidden: true,
      search: true,
      addDisplay: false,
      editDisplay: false,
      component: 'wf-project-drop',
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      search: true,
      component: 'wf-customer-drop',
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '项目总额',
      prop: 'projectPrice',
      addDisplay: false,
      editDisplay: false,
    },

    {
      label: '业务员',
      prop: 'businessUserName',
      addDisplay: false,
      search: false,

      width: 100,
      editDisplay: false,
    },
    {
      label: '售前技术',
      prop: 'preSaleTechnology',
      addDisplay: false,
      component: 'wf-user-drop',
      params: {
        roleKeys: 'technology_leader,technology_person',
      },
      searchSpan: 3,
      search: true,
      editDisplay: false,
    },
    {
      label: '协助人员',
      prop: 'technologyAssistUserName',
      addDisplay: false,
    },
    {
      label: '项目经理',
      prop: 'projectLeader',
      width: 250,
      component: 'wf-user-select',
      overHidden: true,

      display: false,
      hide: true,
    },
    {
      label: '项目属性',
      prop: 'projectAttribute',
      overHidden: true,
      dicData: [
        {
          label: '总包',
          value: 0,
        },
        {
          label: '分包',
          value: 1,
        },
      ],
      type: 'radio',
      hide: true,
    },

    {
      label: '项目地址',
      prop: 'projectAddress',
      overHidden: true,
      type: 'input',
      span: 24,
      hide: true,
    },

    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      hide: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },

    {
      label: '项目经理',
      prop: 'technologyUserName',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      search: true,
      searchSpan: 3,
      component: 'wf-user-drop',
      params: {
        roleKeys: 'project_leader',
      },
    },
    {
      label: '项目进度',
      prop: 'projectSchedule',
      overHidden: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      width: 110,
      type: 'textarea',
     
    },
    // {
    //   label: '签订时间',
    //   prop: 'signDate',
    //   addDisplay: false,
    //   component: 'wf-daterange-search',
    //   searchSpan: 6,
    //   search: true,
    //   editDisplay: false,
    // },

    // {
    //   label: '客户联系人',
    //   prop: 'customerContact',
    //   addDisplay: false,
    // },

    // {
    //   label: '联系电话',
    //   prop: 'customerPhone',
    //   addDisplay: false,
    // },
    {
      label: '开工时间',
      prop: 'startDate',
      type: 'date',
      span: 24,
      width: 100,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '要求交付时间',
      prop: 'deliveryDate',
      type: 'date',
      span: 24,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '签订时间',
      prop: 'signDate',
      type: 'date',
      span: 24,
      width: 120,
      editDisplay: false,

      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '项目状态',
      prop: 'projectStatus',
      overHidden: true,
      span: 24,
     
      type: 'select',
      dicData: [
        {
          label: '未施工',
          value: 0,
        },
        {
          label: '施工中',
          value: 1,
        },
        {
          label: '施工完成',
          value: 2,
        },
        {
          label: '验收完成',
          value: 3,
        },
      ],
      search: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/project/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/project/update';
const tableUrl = '/api/vt-admin/project/page';
let route = useRoute();
let params = ref({
  ids: route.query.ids,
});
watch(
  () => route.query.ids,
  val => {
    params.value = {
      ids: route.query.ids,
    };
    onLoad();
  }
);
let tableData = ref([]);
let { proxy } = getCurrentInstance();

let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
        businessName: params.value.businessUserName,
        startTime: params.value.signDate ? params.value.signDate[0] : null,
        endTime: params.value.signDate ? params.value.signDate[1] : null,
        signDate: null,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  axios
    .get('/api/vt-admin/project/pageStatistics', {
      params: {
        ...params.value,
        selectType: 0,
        businessName: params.value.businessUserName,
        startTime: params.value.signDate ? params.value.signDate[0] : null,
        endTime: params.value.signDate ? params.value.signDate[1] : null,
        signDate: null,
        createTime: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();
function reset() {
  params.value.ids = null;
  onLoad();
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
    projectFiles: form.fileIds && form.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    projectFiles: row.fileIds && row.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
let addDialogVisible = ref(false);
function handleAdd() {
  addDialogVisible.value = true;
  // router.push({
  //   path: '/Order/salesOrder/compoents/addOrder',
  // });
}
function handleToAdd(value) {
  const menu = {
    0: () => {
      router.push({
        path: '/Project/add',
      });
    },
    1: () => {
      router.push({
        path: '/Project/addNoOffer',
      });
    },
  };
  menu[value]();
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.fileIds = (form.value.attachList || []).map(item => {
      return {
        value: item.id,
        label: item.originalName,
      };
    });
  }
  done();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleView(row) {
  router.push({
    path: '/Project/detail/detail',
    query: {
      id: row.id,
      name: row.projectName + '-详情',
      type: 0,
      isMy:
        proxy.$store.getters.userInfo.user_name == row.businessUserName ||
        proxy.$store.getters.userInfo.user_name == row.preSaleTechnology
          ? 1
          : 0,
    },
  });
}
let deepDrawer = ref(false);
let isPaymentPeriodData = ref([]);
function addContract(row) {
  proxy.$refs.dialogForm.show({
    title: '生成合同',
    option: {
      labelWidth: 120,
      column: [
        {
          type: 'input',
          label: '合同编号',
          span: 12,
          display: true,
          readonly: true,
          placeholder: '自动生成',
          prop: 'contractCode',
        },
        {
          type: 'input',
          label: '合同名称',
          span: 12,
          display: true,
          prop: 'contractName',
          value: row.projectName,
          required: true,
          rules: [
            {
              required: true,
              message: '项目名称必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '对方订单编号',
          span: 12,
          display: true,
          prop: 'customerOrderNumber',
        },
        {
          type: 'input',
          label: '合同金额',
          span: 12,
          display: true,

          value: row.projectPrice,
          prop: 'contractTotalPrice',
          required: true,
          rules: [
            {
              required: true,
              message: '合同金额必须填写',
            },
          ],
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 12,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            isPaymentPeriodData.value = res.data;
            return res.data;
          },
          control: (val, form, b, c) => {
            return {
              fixedBillingDate: {
                display:
                  isPaymentPeriodData.value.find(item => item.id == val)?.dictValue == '固定账期',
              },
            };
          },
          dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'number',
          label: '固定账期时间',
          span: 12,
          display: true,
          min: 1,
          max: 31,
          tip: '输入1到31之间的数字,账期则为每月这个时间',
          prop: 'fixedBillingDate',
          rules: [
            {
              required: true,
              message: '请输入固定账期时间',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '结算方式',
          type: 'radio',
          prop: 'settlementMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
          value: row.deliveryDate,
          valueFormat: 'YYYY-MM-DD',
          // rules: [
          //   {
          //     required: true,
          //     message: '签订日期必须填写',
          //   },
          // ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        offerId: row.offerId,
        businessOpportunityId: row.businessOpportunityId,
        contractFiles:
          res.data.contractFiles && res.data.contractFiles.map(item => item.value).join(','),
      };
      axios
        .post('/api/vt-admin/vt-admin/project/replenishOffer', {
          id: row.id,
        })
        .then(r => {
          axios.post('/api/vt-admin/sealContract/save', data).then(r => {
            proxy.$message.success(r.data.msg);
            onLoad();
            res.close();
          });
        });
    },
  });

  // router.push({
  //   path: '/Order/salesOrder/compoents/addOrder',
  //   query: {
  //     id: row.id,
  //   },
  // });
}
function valiDateComplete(row) {
  ElMessageBox.confirm('是否确认验收完成？', '提示', {
    type: 'warning',
  }).then(() => {
    axios.post('/api/vt-admin/project/updateProjectStatus', { id: row.id,projectStatus:3 }).then(r => {
      proxy.$message.success(r.data.msg);
      onLoad();
    });
  });
}
</script>

<style lang="scss" scoped>
::v-deep .el-dialog__header {
  display: none !important;
}
</style>
