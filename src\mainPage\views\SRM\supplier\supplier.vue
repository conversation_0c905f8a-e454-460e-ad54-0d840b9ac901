<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @keyup.enter="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-button type="primary" @click="handleAdd" icon="Plus">新增</el-button>
      </template>
      <template #menu="{ row, index }">
        <el-button type="primary" text @click="handleEdit(row)" icon="Edit">编辑</el-button>
        <el-button type="primary" text @click="handleView(row)" icon="View">详情</el-button>
        <el-button type="primary" text @click="handleAddProduct(row)" icon="link">关联产品</el-button>
        <el-button type="primary" text @click="$refs.crud.rowDel(row, index)" icon="delete"
          >删除</el-button
        >
      </template>
      <template #supplierName="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.supplierName }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <detail_drawer :id="currentId" ref="detailRef"></detail_drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import detail_drawer from './compoents/detail_drawer.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  menuWidth: 300,
  border: true,
  column: [
    {
      label: '供应商名称',
      prop: 'supplierName',
      width: 250,
      component:'wf-supplier-drop',
      overHidden: true,
      search: true,
    },
    // {
    //   label: '联系人',
    //   prop: 'contact',
    // },
    // {
    //   label: '联系电话',
    //   prop: 'contactPhone',
    // },
    {
      label: '供应商地址',
      prop: 'address',
      overHidden: true,
    },
    {
          label: '主要供应品牌',
          prop: 'brand',
          search:true,
     },
     {
          label: '主要供应产品',
          prop: 'mainProduct',
        },
    {
      type: 'select',
      label: '供应商分类',
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择供应商分类',
        },
      ],
      display: true,
      prop: 'supplierClassify',
      dicUrl: '/blade-system/dict/dictionary?code=supplierClassify',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    
    {
      type: 'input',
      label: '供应商特色',
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择供应商级别',
        },
      ],
      display: true,
      prop: 'supplierFeature',
    },
    {
      label: '供应商网址',
      prop: 'webUrl',
    },
    {
      type: 'radio',
      label: '账期',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择账期',
        },
      ],
      span: 24,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'paymentMethod',
      dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
      remote: false,
    },
  
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/supplier/remove?ids=';

const tableUrl = '/api/vt-admin/supplier/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push({
    path: '/SRM/supplier/compoents/update',
  });
}
let currentId = ref(null);
let detailRef = ref(null)
function handleView(row) {
  currentId.value = row.id;

  detailRef.value.open();
}
function handleAddProduct(row) {
  router.push({
    path: '/SRM/supplierDetail',
    query: {
      id: row.id,
      activeName: 'product',
    },
  });
}
function handleEdit(row) {
  router.push({
    path: '/SRM/supplier/compoents/update',
    query: {
      type: 'edite',
      name:'编辑' + row.name,
      id: row.id,
    },
  });
}
</script>

<style lang="scss" scoped></style>
