<template>
  <div class="box">
    <el-row :gutter="20">
      <el-col :span="24">
        <div>
          <el-row :gutter="20">
            <el-col :span="12">
            <yearStatisis></yearStatisis>
            </el-col>
            <el-col :span="12">
                <allStaticsis></allStaticsis>
            </el-col>
          </el-row>
        </div>
      </el-col>
      <!-- <el-col :span="8"> <el-card></el-card></el-col> -->
    </el-row>
  </div>
</template>

<script setup>
import { provide, computed } from 'vue';
import { useStore } from 'vuex';
import yearStatisis from './components/yearStatisis.vue'
import allStaticsis from './components/allStaticsis.vue'
let form = ref({
  selectType: '0',
});
const store = useStore();
const permission = computed(() => {
  return store.getters.permission;
});

provide('form', form);
</script>

<style lang="scss" scoped>
.box {
  padding: 15px;
  box-sizing: border-box;
}
</style>
