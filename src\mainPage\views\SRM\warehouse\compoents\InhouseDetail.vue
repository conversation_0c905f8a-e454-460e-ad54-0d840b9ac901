<template>
  <basic-container>
    <Title
      >入库详情
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
      <template #files>
        <File :fileList="form.attachList || []"></File>
      </template>
      <template #product>
        <el-table
          @cell-dblclick="handleDbclick"
          class="avue-crud"
          :data="form.detailVOS"
          border
          align="center"
        >
          <el-table-column
            label="设备名称"
            show-overflow-tooltip
            prop="productName"
          ></el-table-column>
          <el-table-column
            label="供应商"
            show-overflow-tooltip
            prop="supplierName"
          ></el-table-column>
          <el-table-column
            label="规格型号"
            show-overflow-tooltip
            prop="productSpecification"
          ></el-table-column>
          <el-table-column label="产品图片" #default="{ row }">
            <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
          </el-table-column>
          <el-table-column label="品牌" width="90" prop="productBrand"></el-table-column>
          <el-table-column label="单位" prop="unitName"></el-table-column>
          <el-table-column show-overflow-tooltip label="序列号" prop="productSerialNumber">
            <template #header>
              <div style="display: flex; align-items: center">序列号</div>
            </template>
            <template #default="{ row }">
              <el-button type="primary" @click="editSerialNumber(row)" size="small">查看</el-button>
            </template>
          </el-table-column>
          <!-- <el-table-column label="采购价" width="80" #default="{ row }" prop="unitPrice">
            <span>{{ row.unitPrice }}</span>
          </el-table-column>
          <el-table-column label="采购总价" width="90" #default="{ row }" prop="totalPrice">
            {{ row.unitPrice ? row.number * row.unitPrice : '---' }}
          </el-table-column> -->
          <el-table-column
            label="入库数量"
            align="center"
            width="180"
            #default="{ row }"
            prop="number"
          >
          </el-table-column>
          <el-table-column label="入库地点" align="center" width="200" #default="{ row }">
            <el-tag effect="plain" type="primary" v-if="row.inStorageAddress == 0">公司</el-tag>
            <el-tag effect="plain" type="primary" v-if="row.inStorageAddress == 1">客户现场</el-tag>
          </el-table-column>
        </el-table>
      </template>
    </avue-form>

    <dialogForm ref="dialogForm"></dialogForm>
   
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let route = useRoute();
// let router = useRouter();
let form = ref({
  detailVOS: [],
});

let { proxy } = getCurrentInstance();
let isEdit = ref(true);
let option = ref({
  submitBtn: false,
  labelWidth: 140,
  detail: true,
  emptyBtn: false,
  column: [
    {
      label: '入库人',
      prop: 'createName',
      disabled: true,
    },
    {
      label: '入库类型',
      prop: 'inStorageType',
      type: 'radio',
      width:100,
      dicData: [
        {
          value: 0,
          label: '订单入库',
        },
       
        {
          value: 2,
          label: '客户退货',
        },
        {
          value: 3,
          label: '期初入库',
        },
        {
          value: 4,
          label: '其他',
        },
       
      ],
      rules: [{ required: true, message: '请选择入库类型' }],
      
    },
    {
      label: '关联订单',
      prop: 'orderNo',

      span: 12,
      // component: 'wf-order-select',
      // format: 'YYYY-MM-DD',
      // valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '关联客户',
      prop: 'customerName',
     
    },
    {
      label: '入库日期',
      prop: 'inStorageDate',
      type: 'date',
    },
    {
      label: '入库数量',
      prop: 'inStorageNumber',
    },

    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: false,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    {
      label: '入库明细',
      prop: 'product',
      slot: true,
      span: 24,
    },
  ],
});
watch(
  () => route.query.id,
  () => {
    if (route.query.id) {
      getDetail(route.query.id);
    }
  }
);
onMounted(() => {
  getDetail(route.query.id);
});
function getDetail(id) {
  axios
    .get('/api/vt-admin/purchaseInStorage/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      form.value = {
        ...res.data.data,
        detailVOS: res.data.data.detailVOS
          ? res.data.data.detailVOS.map(item => {
              return {
                ...item.productVO,
                ...item,
              };
            })
          : [],
      };
    });
}
function handleDbclick(row, column) {
  if (column.property == 'productSerialNumber') {
    editSerialNumber(row);
  }
}
function editSerialNumber(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑序列号',
    option: {
      column: [
        {
          label: '序列号',
          prop: 'productSerialNumber',
          type: 'textarea',
          value: row.productSerialNumber,
          span: 24,
          rows: 8,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/purchaseInStorageDetail/update', {
          ...row,
          ...res.data,
        })
        .then(r => {
          row.productSerialNumber = res.data.productSerialNumber;
          proxy.$message.success('修改成功');
          res.close();
        });
    },
  });
}
</script>

<style lang="scss" scoped></style>
