<template>
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @keyup.enter="onLoad"
        @current-change="onLoad"
        @refresh-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
      <template #menu="{row}">
        <el-button type="primary" text icon="view" @click="toDetail(row)">详情</el-button>
      </template>
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    menuWidth: 270,
    menu:true,
    border: true,
    column: [
    {
      label: '产品编号',
      prop: 'productCode',
      overHidden: true,

      search: true,
    },
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      search: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },

    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      span: 24,
      type: 'input',
    },

    {
      label: '产品图片',
      prop: 'coverUrl',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        form.value.coverId = res.id;
        console.log(form.value);
        done();
      },
    },
    {
        label:'入库数量',
        prop:'inStorageNumber',
    },
    {
        label:'库存数量',
        prop:'surplusNumber',
    }
  ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  
  const addUrl = ''
  const delUrl = ''
  const updateUrl = ''
  const tableUrl = '/api/vt-admin/inventory/page'
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  onMounted(() => {
    onLoad();
  });
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
         
          size,
          current,
          ...params.value,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records.map(item => {
          return {
            ...item.productVO,
            ...item
          }
        } );
        page.value.total = res.data.data.total;
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  function toDetail(row) {
    router.push({
      path:'/SRM/warehouse/detail/houseDetail',
      query:{
        id:row.id,
        productName : row.productName
      }
    })
  }
  </script>
  
  <style lang="scss" scoped></style>
  