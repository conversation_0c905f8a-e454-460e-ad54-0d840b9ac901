<template>
  <div style="">
    <el-button
      @click="handleClick(item.value)"
      v-for="item in businessOpportunityData"
      :style="{ backgroundColor: item.color, color: '#fff' }"
      >{{ item.label }}</el-button
    >
    <el-button @click="handleClick('')" style="background-color: #ddd;color: #fff;">全部</el-button>
  
  </div>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { businessOpportunityData } from '@/const/const';
const emit = defineEmits(['click']);

function handleClick(value) {
  emit('click', value);
}
</script>

<style lang="scss" scoped></style>
