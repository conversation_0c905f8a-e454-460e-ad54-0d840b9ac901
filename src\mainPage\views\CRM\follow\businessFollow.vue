<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
    
      @row-update="rowUpdate"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-button @click="handleAdd" type="primary" icon="plus" v-if="$store.getters.permission.follow_add"> 新增 </el-button>
      </template>
      <template #menu="{ row, index }">
        <div style="display: flex; justify-content: center">
          <el-button text type="primary" icon="View" @click="toDetail(row)">详情</el-button>
          <div >
            <el-button text type="primary" icon="edit" @click="update(row)" v-if="$store.getters.permission.follow_edit">编辑</el-button>
            <el-button
              text
              type="primary"
              icon="notification"
              @click="approve(row)"
              v-if="
                $store.getters.permission.follow_check &&
                row.createUser !== $store.getters.userInfo.user_id
              "
              >批示</el-button
            >
            <el-button text type="primary" v-if="$store.getters.permission.follow_del" icon="delete" @click="$refs.crud.rowDel(row, index)"
              >删除</el-button
            >
          </div>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="批示" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <!-- <slot ></slot> -->
      <el-timeline>
        <el-timeline-item
          v-for="(item, index) in [{}, ...customerFollowCommandVoS]"
          
          :timestamp="item.createTime"
          placement="top"
        >
          <el-input
            type="textarea"
            v-model="remark"
            :rows="5"
            placeholder="请输入此次批示内容"
            v-if="index == 0"
          ></el-input>
          <el-card v-else>
            <h4>{{ item.createName }}</h4>
            <p>{{ item.remark }}</p>
          </el-card>
        </el-timeline-item>
      </el-timeline>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false;remark = ''">取 消</el-button>
        <el-button @click="approveSubmit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '客户名称',
      prop: 'customerName',
      width: 250,
      overHidden: true, component: 'wf-customer-drop',
      search: true,
    },
    {
      label: '跟进记录',
      prop: 'followContent',
      overHidden: true,
    },

    // {
    //   label: '跟进成效',
    //   prop: 'jobPerformance',
    //   overHidden: true,
    // },

    {
      label: '批示',
      prop: 'followCommandContent',
      overHidden: true,
      html:true,
      formatter(row, column, cellValue, index) {
        if (row.isSee == 0) {
          return `<div style='color:var(--el-color-danger)'>${row.followCommandContent || ''}</div>`;
         
        } else {
          return row.followCommandContent || '';
        }
      },
    },
    {
      label: '跟进类型',
      prop: 'followType',
      type: 'select',
      search: true,
      dicData: followType,
    },
    {
      label: '填写时间',
      prop: 'createTime',
      type: 'dateTime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm',
      searchRange: true,
      search: true,
      searchSpan: 6,
      width: 135,
      searchRange: true,
      type: 'date',
    },
    {
      label: '实际跟进时间',
      prop: 'followTime',
      type: 'date',
      width: 135,
      format: 'YYYY-MM-DD HH:mm',
    },
    {
      label: '跟进人',
      prop: 'followName',
      component: 'wf-user-drop',
      search: true,
    },
    {
      label: '提醒人',
      prop: 'remindName',
    },
    {
      label: '跟进方式',
      prop: 'followWayStr',
      type: 'select',
    },
    {
      label: '跟进联系人',
      prop: 'contactName',
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/customerFollow/page', {
      params: {
        size,
        current,
        ...params.value,
        followType: 1,
        startDate: params.value.createTime && params.value.createTime[0],
        endDate: params.value.createTime && params.value.createTime[1],
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function handleAdd() {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      type: 1,
    },
  });
}
function update(row) {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      id: row.id,
    },
  });
}
function toDetail(row) {
  router.push({
    path: '/CRM/follow/compoents/detail',
    query: {
      id: row.id,
    },
  });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/customerItInfo/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/customerItInfo/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
// function approve(row) {
//   proxy.$refs.dialogForm.show({
//     title: '批示',
//     option: {
//       column: [
//         {
//           label: '批示内容',
//           type: 'textarea',
//           prop: 'remark',
//           span: 24,
//         },
//       ],
//     },
//     callback(res) {
//       const data = {
//         followId: row.id,
//         ...res.data,
//       };
//       axios.post('/api/vt-admin/customerFollowCommand/save', data).then(r => {
//         proxy.$message.success(r.data.msg);
//         res.close();
//         onLoad();
//       });
//     },
//   });
// }
function searchChange(params, done) {
  onLoad();
  done();
}
let dialogVisible = ref(false);
let customerFollowCommandVoS = ref([]);
let remark = ref('');
let currentId = ref('');
function approve(row) {
  currentId.value = row.id;
  axios.get('/api/vt-admin/customerFollow/detail?id=' + row.id).then(res => {
    customerFollowCommandVoS.value = res.data.data.customerFollowCommandVoS;
  });
  dialogVisible.value = true;
}
function approveSubmit() {
  const data = {
    remark: remark.value,
    followId: currentId.value,
  };
  axios.post('/api/vt-admin/customerFollowCommand/save', data).then(r => {
    proxy.$message.success(r.data.msg);
    onLoad();
    remark.value = ''
    dialogVisible.value = false;
  });
}
</script>

<style lang="scss" scoped></style>
