<template>
  <el-drawer title="编辑询价单" v-model="drawer" size="60%">
    <avue-form ref="formRef" :option="option" v-model="form" @submit="submit"></avue-form>
    <template #footer>
      <el-button type="primary" @click="$refs.formRef.submit()">提交</el-button>
    </template>
  </el-drawer>
  <productSelect
    ref="productSelectRef"
    :id="form.purchaseId"
    :type="0"
    @onConfirm="handleProductSelectConfirm"
  ></productSelect>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import { watch ,getCurrentInstance} from 'vue';
import productSelect from '../../inquiry/compoents/productSelect.vue';
let drawer = ref(false);
let form = ref({});
const {proxy} = getCurrentInstance();
function open() {
  drawer.value = true;
}
function submit(form, done, loading) {
  const data = {
    ...form,
    detailDTOList: form.detailDTOList.map(item => {
      return {
        ...item,
        id: null,
        productId: item.productId,
        orderDetailId: item.orderDetailId,
      };
    }),
  };
  axios
    .post('/api/vt-admin/purchaseInquiry/update', data)
    .then(res => {
      if (res.data.code == 200) {
        ElMessage.success(res.data.msg);
        done();
        drawer.value = false;
      }
    })
    .catch(err => {
      done();
    });
}
let productSelectRef = ref();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  delBtn: false,
  editBtn: true,
  delBtn: false,
  menu: true,
  search: false,
  rowKey: 'id',
  expand: true,
  updateBtn: false,
  saveBtn: false,
  // cancelBtn: false,
  emptyBtn: false,
  submitBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,

  searchLabelWidth: 120,
  labelWidth: 150,
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
          trigger: 'blur',
        },
      ],
      hide: true,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
         
          if (!form.value.purchaseId) {
            return proxy.$message.error('请先选择关联采购单');
          }

          if (form.value.inquirySource == 0) {
            productSelectRef.value.open();
          } else {
            wfproductSelect.value.show;
          }
          // wfproductSelect.value.visible = true;
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            width: 260,
            editDisabled: true,
            overHidden: true,

            cell: false,
          },

          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,

            editDisabled: true,
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            span: 24,
            overHidden: true,
            editDisabled: true,

            type: 'input',
            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
          },
        ],
      },
      formatter: row => {
        return row.inquiryNumber;
      },
    },
    {
      label: '关联供应商',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
          trigger: 'blur',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '关联供应商',
            prop: 'supplierId',
            // search: true,
            rules: [
              {
                required: true,
                message: '请选择供应商',
                trigger: 'blur',
              },
            ],
            component: 'wf-supplier-select',
            cascader: ['supplierContactId'],
            change: (value, a, b) => {
              // setBaseInfo(value);
            },
            overHidden: true,
          },
        ],
      },
      hide: true,
    },
  ],
});
const props = defineProps(['id','purchaseId']);
watch(
  () => props.id,
  () => {
    getDetail();
    
    form.value.purchaseId = props.purchaseId
  },
  {
    immediate: true,
  }
);
function getDetail(params) {
  if (!props.id) return;
  axios
    .get('/api/vt-admin/purchaseInquiry/detail', {
      params: {
        id: props.id,
      },
    })
    .then(res => {
      form.value = {
        ...res.data.data,
        detailDTOList: res.data.data.detailVOS,
        supplierList: res.data.data.supplierVOList,
      };
    });
}
function handleProductSelectConfirm(value) {
  console.log(value);
  form.value.detailDTOList.push(
    ...value.map(item => {
      return {
        ...item,
        number: item.number * 1,
      };
    })
  );
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
