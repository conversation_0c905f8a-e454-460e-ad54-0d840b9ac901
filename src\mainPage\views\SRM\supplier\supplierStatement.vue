<template>
  <basic-container class="container" block :shadow="supplierId ? 'never' : 'always'">
    <div style="height: 100%; padding-bottom: 60px" class="box">
      <el-affix target=".box" :offset="159">
        <div style="background-color: #fff">
          <el-form :size="size" ref="form" inline label-width="100">
            <el-row :gutter="10">
              <el-col v-if="!supplierId" :span="4" :lg="4" :md="6">
                <el-form-item label="关联供应商">
                  <wfSupplierDrop v-model="query.supplierName" />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="是否收票">
                  <el-select v-model="query.isInvoice" placeholder="请选择是否收票" clearable>
                    <el-option
                      v-for="item in [
                        {
                          value: 0,
                          label: '未收票',
                        },
                        {
                          value: 1,
                          label: '已收票',
                        },
                        {
                          value: 2,
                          label: '部分收票',
                        },
                        {
                          value: 3,
                          label: '无需收票',
                        },
                      ]"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6" :lg="6" :md="9">
                <el-form-item style="width: 100%" label="签订时间">
                  <wfDaterangeSearch v-model="query.signDate"></wfDaterangeSearch>
                </el-form-item>
              </el-col>
              <el-col :lg="3" :md="5" :span="3">
                <el-form-item>
                  <div style="margin-left: 10px">
                    <el-button type="primary" @click="onLoad" icon="search">搜索</el-button>
                    <el-button icon="delete" @click="searchReset">清空</el-button>
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="" label-width="50px">
                  <el-pagination
                    background
                    :page-sizes="[10, 20, 30, 40, 50, 100]"
                    layout="sizes,prev, pager, next"
                    :total="page.total"
                   size="small"
                    v-model:page-size="page.pageSize"
                    v-model:current-page="page.currentPage"
                    @size-change="onLoad"
                    @current-change="onLoad"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-affix>
      <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 5px">
        <span style="font-weight: bolder; font-size: 20px">{{ query.supplierName || '---' }}</span>
        <span style="font-weight: bolder">合同总额：</span>
        <el-text type="primary" size="large"
          >￥{{ (contractTotalPrice * 1).toLocaleString() }}</el-text
        >
        <span style="font-weight: bolder">已付款总额：</span>
        <el-text type="primary" size="large"
          >￥{{ (hasPaymentPrice * 1).toLocaleString() }}</el-text
        >
        <span style="font-weight: bolder">已收票总额：</span>
        <el-text type="primary" size="large"
          >￥{{ (hasInvoicePrice * 1).toLocaleString() }}</el-text
        >
        <span style="font-weight: bolder">未付款金额：</span>
        <el-text type="primary" size="large"
          >￥{{ ((contractTotalPrice - hasPaymentPrice) * 1).toLocaleString() }}</el-text
        >
        <span style="font-weight: bolder">未收票金额：</span>
        <el-text type="primary" size="large"
          >￥{{ ((contractTotalPrice - hasInvoicePrice) * 1).toLocaleString() }}</el-text
        >
        <el-button type="primary" style="margin-left: -5px" @click="receview" icon="plus"
          >收票</el-button
        >
        <div style="display: flex; gap: 20px">
          <el-text
            size="large"
            :type="query.signDate[0] ? 'success' : 'warning'"
            style="white-space: noWrap"
            >{{ query.signDate[0] || '未选中时间' }}</el-text
          >
          ~
          <el-text
            size="large"
            :type="query.signDate[1] ? 'success' : 'warning'"
            style="white-space: noWrap"
            >{{ query.signDate[1] || new Date().toISOString().split('T')[0] }}</el-text
          >
        </div>
      </div>
      <div style="display: flex; flex-direction: column; gap: 20px" v-loading="loading">
        <el-card shadow="never" body-style="padding:3px" v-for="(item, index) in tableData">
          <!-- <el-divider
            content-position="left"
            style="margin: 5px 0; border-top-color: var(--el-color-primary)"
            ><el-text :size="size" type="primary"
              >{{ item.supplierName }} - {{ item.contractCode }}</el-text
            ></el-divider
          > -->

          <el-row :gutter="10">
            <el-col :span="6">
              <el-descriptions
                border
                :title="item.contractCode + `${supplierId ? '' : '-' + item.supplierName}`"
                style="margin: 5px 0; margin-top: 20px"
                :size="size"
                :column="2"
              >
                <template #title>
                  {{
                    item.contractCode +
                    `${supplierId || query.supplierName ? '' : '-' + item.supplierName}`
                  }}
                </template>
                <el-descriptions-item label-class-name="my-label" span="2" label="关联采购单"
                  ><el-link
                    v-for="(i, index) in item.orderCode && item.orderCode.split(',')"
                    @click="toDetail(item.orderId.split(',')[index])"
                    type="primary"
                    >{{ i }}</el-link
                  >
                </el-descriptions-item>
                <el-descriptions-item label="优惠金额">{{
                  (item.discountPrice * 1).toFixed(2) || '0.00'
                }}</el-descriptions-item>
                <el-descriptions-item label="优惠后合同总额">{{
                  (item.contractPrice * 1).toFixed(2) || '0.00'
                }}</el-descriptions-item>
                <el-descriptions-item label="已收票金额">{{
                  (item.hasInvoice * 1).toFixed(2) || '0.00'
                }}</el-descriptions-item>
                <el-descriptions-item label="已付款金额">{{
                  (item.paymentPrice * 1).toFixed(2) || '0.00'
                }}</el-descriptions-item>
                <el-descriptions-item label-class-name="my-label" span="2" label="签订日期">
                  {{ item.createTime.split(' ')[0] }}</el-descriptions-item
                >
              </el-descriptions></el-col
            >
            <el-col :span="18">
              <el-row :gutter="0">
                <el-col :span="24">
                  <avue-crud
                    style="border-right: none"
                    :option="{ ...option, showHeader: index == 0 }"
                    @selection-change="handleSelectChange($event, item)"
                    :data="item.detailVOList"
                    @row-click="
                      row => {
                        row.checked = !row.checked;
                        handleSelectChange(row.checked, row, item);
                      }
                    "
                  >
                    <template #checked="{ row }">
                      <el-checkbox
                        @click.stop
                        :disabled="row.isInvoice == 1 || row.isInvoice == 3"
                        @change="handleSelectChange($event, row, item)"
                        v-model="row.checked"
                      ></el-checkbox>
                    </template>
                    <template #isInvoice="{ row }">
                      <el-tag
                        effect="plain"
                      v-if="item.isNeedInvoice == 1"
                        :type="
                          row.isInvoice == 0
                            ? 'danger'
                            : row.isInvoice == 1 || row.isInvoice == 2
                            ? 'success'
                            : 'info'
                        "
                        >{{ row.$isInvoice }}</el-tag
                      >
                         <el-tag size="large"   effect="plain" v-else type="info">无需开票</el-tag>
                    </template>
                  </avue-crud>
                </el-col>
                <el-col :span="0">
                  <avue-crud :option="payOption" :data="item.payData"></avue-crud>
                </el-col> </el-row
            ></el-col>
          </el-row>
        </el-card>
        <el-empty v-if="tableData.length == 0" description="暂无数据"></el-empty>
      </div>
      <div style="display: flex; justify-content: flex-end; margin-top: 10px"></div>
    </div>
    <el-drawer v-model="drawer" size="90%" @closed='handleClose' title="新增收票">
      <el-row :gutter="10">
        <el-col style="height: 100%" :span="24">
          <el-divider content-position="left"
            ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
              >关联产品</span
            ></el-divider
          >
          <el-card class="box-card" shadow="never" style="height: 100%">
            <avue-crud
              :option="selectProductOption"
              @row-del="productRowDel"
              :summary-method="summaryMethod"
              :data="selectProductList"
            >
              <template #menu-left>
                <!-- <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button> -->
              </template>
              <template #number="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  :size="size"
                  style="width: 80%"
                  v-model="row.number"
                ></el-input-number>
              </template>
              <template #realCostPrice="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  controls-position="right"
                  :size="size"
                  style="width: 80%"
                  v-model="row.realCostPrice"
                ></el-input-number>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-divider content-position="left"
            ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
              >发票信息</span
            ></el-divider
          >
          <el-card shadow="never" class="box-card" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
            </avue-form>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRef.submit()">确认 收票</el-button>
        </div>
      </template>
    </el-drawer>
    <el-drawer title="" v-model="selectListDrawer" size="50%">
      <avue-crud
        :option="selectProductOption"
        :summary-method="summaryMethod"
        @row-del="productRowDelSelect"
        :data="selectList"
      >
        <template #menu-left>
          <!-- <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button> -->
        </template>
        <!-- <template #number="{ row }">
          <el-input-number
           
            :min="0"
            size="small"
            style="width: 80%"
            v-model="row.number"
          ></el-input-number>
        </template> -->
      </avue-crud>
    </el-drawer>
    <el-dialog
      v-model="headerDialog"
      width="200"
      top="20vh"
      :title="'选择导出的表头'"
      style="border-radius: 10px"
      :show-close="false"
      align-center
    >
      <el-table
        border
        rowKey="id"
        ref="headerTableRef"
        @selection-change="handleSelectionChange"
        :data="headerTableData"
      >
        <el-table-column type="selection"></el-table-column>
        <el-table-column label="表头" prop="label"></el-table-column>
      </el-table>
      <template #footer>
        <el-button type="primary" @click="exportConfirm">导出</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { onMounted, getCurrentInstance, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
import wfDaterangeSearch from '@/views/plugin/workflow/components/custom-fields/wf-daterange-search/index.vue';
import wfSupplierDrop from '@/views/plugin/workflow/components/custom-fields/wf-supplier-drop/index.vue';
import { ElMessage } from 'element-plus';
onMounted(() => {
  onLoad();
});
let router = useRouter();
const { proxy } = getCurrentInstance();
let size = ref('small');
let page = ref({
  total: 10,
  pageSize: 10,
  currentPage: 1,
});
let option = ref({
  size: size.value,
  header: false,
  border: true,
  showHeader: false,
  //   selection: true,
  menu: false,
  align: 'center',
  tip: false,
  column: [
    {
      label: '',
      prop: 'checked',
      width: 50,
    },
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      // search: true,
      editDisabled: true,
      bind: 'productVO.productName',
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      editDisplay: false,
      // search: true,
      bind: 'productVO.productBrand',
    },
    {
      label: '规格型号',
      editDisplay: false,
      prop: 'productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      bind: 'productVO.productSpecification',
      type: 'input',
    },
    {
      label: '单位',
      type: 'select',
      editDisplay: false,
      width: 80,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      bind: 'productVO.unit',
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
    },
    {
      label: '已收票数量',
      prop: 'invoiceNumber',
    },
    {
      label: '已退货数量',
      prop: 'returnNumber',
      formatter: row => parseFloat(row.returnNumber),
    },
    {
      label: '单价',
      prop: 'unitPrice',
    },
    {
      label: '金额',
      prop: 'totalPrice',
    },
    {
      label: '是否收票',
      prop: 'isInvoice',
      width: 110,

      type: 'select',

      dicData: [
        {
          value: 0,
          label: '未收票',
        },
        {
          value: 1,
          label: '已收票',
        },
        {
          value: 2,
          label: '部分收票',
        },
        {
          value: 3,
          label: '无需收票',
        },
      ],
    },
  ],
});
let payOption = ref({
  //   height: 'auto',
  align: 'center',
  addBtn: false,
  header: false,
  menu: false,
  editBtn: true,
  delBtn: true,
  //   calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,

  border: true,
  searchLabelWidth: 120,
  column: [
    {
      type: 'number',
      label: '付款金额',
      span: 12,
      display: true,
      prop: 'actualPaymentPrice',
    },
    {
      type: 'date',
      label: '付款日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',

      prop: 'actualPaymentDate',
    },

    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let tableData = ref([
  {
    productName: '测试产品',
    productBrand: '测试品牌',
    productSpecification: '测试规格型号',
    unit: '个',
    number: 100,
    price: 100,
    amount: 10000,
    checked: true,
  },
  {
    productName: '测试产品',
    productBrand: '测试品牌',
    productSpecification: '测试规格型号',
    unit: '个',
    number: 100,
    price: 100,
    amount: 10000,
  },
  {
    productName: '测试产品',
    productBrand: '测试品牌',
    productSpecification: '测试规格型号',
    unit: '个',
    number: 100,
    price: 100,
    amount: 10000,
  },
]);
let query = ref({
  isInvoice: 0,
  signDate: [],
});
const props = defineProps(['supplierId']);
let loading = ref(false);
let contractTotalPrice = ref(0);
let hasInvoicePrice = ref(0);
let hasPaymentPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/purchaseContract/supplierStatementAccount', {
      params: {
        ...query.value,
        size,
        current,
        supplierId: props.supplierId,
        startTime: query.value.signDate ? query.value.signDate[0] : null,
        endTime: query.value.signDate ? query.value.signDate[1] : null,
        signDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
      setChecked();
    })
    .catch(() => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContract/supplierStatementAccountStatistics', {
      params: {
        ...query.value,
        size,
        current,
        supplierId: props.supplierId,
        startTime: query.value.signDate ? query.value.signDate[0] : null,
        endTime: query.value.signDate ? query.value.signDate[1] : null,
        signDate: null,
      },
    })
    .then(res => {
      contractTotalPrice.value = res.data.data.contractTotalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      hasPaymentPrice.value = res.data.data.hasPaymentPrice;
    });
}
function setChecked() {
  tableData.value.forEach(item => {
    item.detailVOList.forEach(it => {
      it.checked = selectList.value.findIndex(i => i.id == it.id) > -1;
    });
  });
}
function searchReset() {
  page.value.currentPage = 1;
  query.value = {
    signDate: [],
  };
  onLoad();
}
let selectList = ref([]);
let form = ref({});
function handleSelectChange(e, row, item) {
  if (e) {
    selectList.value.push({
      ...row,
      supplierName: item.supplierName,
      supplierId: item.supplierId,
    });
  } else {
    selectList.value = selectList.value.filter(item => item.id !== row.id);
  }
}
let selectListDrawer = ref(false);
function productRowDelSelect(row, done) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectList.value = selectList.value.filter(item => item.id !== row.id);
      setChecked();
    })
    .catch(() => {});
}
function toDetail(id, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: id,
      activeName,
    },
  });
}
//   收票申请
let drawer = ref(false);

let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '上传发票',
      prop: 'invoiceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      width: 120,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },

      tip: '先上传发票可以自动填充下面部分类容',
      uploadAfter: (res, done) => {
        console.log(res);
        done();
        const { id } = res;
        axios
          .get('/api/vt-admin/purchaseContractInvoiceDetail/analysisInvoice', {
            params: {
              id,
            },
          })
          .then(res => {
            const {
              date: invoiceDate,
              buyerName: supplierName,
              totalAmount: invoicePrice,
              number: invoiceNumber,
            } = res.data.data;
            const list = res.data.data.detailList.map(item => {
              const {
                totalPrice,
                amount: productTotalPrice,
                taxAmount: taxPrice,
                taxRate,
                count: number,
                name: productName,
                model: specification,
                unit: unitName,
                price: price,
              } = item;
              return {
                totalPrice,
                productTotalPrice,
                taxPrice,
                taxRate,
                number,
                productName,
                specification,
                unitName,
                price,
                disabled: true,
              };
            });

            if (form.value.detailEntityList) {
              form.value.detailEntityList.push(...list);
            } else {
              form.value.detailEntityList = list;
            }
            form.value.invoiceDate = invoiceDate;

            form.value.invoicePrice = invoicePrice;
            form.value.invoiceNumber = invoiceNumber;
          });
      },
      uploadPreview: file => {
        return;
      },

      action: '/blade-resource/attach/upload',
    },
    {
      type: 'input',
      label: '供应商名称',
      span: 12,
      width: 120,

      placeholder: '',
      prop: 'supplierName',
      search: true,
      disabled: true,
      overHidden: true,
    },

    {
      type: 'date',
      label: '登记日期',
      span: 12,
      width: 120,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',

      prop: 'createTimeBf',
      formatter(row, column, cellValue, index) {
        return dateFormat(new Date(row.createTime), 'yyyy-MM-dd');
      },
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      display: true,
      width: 120,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },
    {
      type: 'input',
      label: '发票号码',
      span: 12,
      display: true,
      width: 180,
      overHidden: true,
      prop: 'invoiceNumber',
    },
    // {
    //   type: 'textarea',
    //   label: '发票内容',
    //   span: 24,
    //   overHidden: true,
    //   display: true,
    //   prop: 'invoiceContent',
    // },

    {
      type: 'input',
      label: '发票总额',
      span: 12,
      overHidden: true,
      display: true,
      width: 100,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '发票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      search: true,
      width: 120,
      display: true,
      overHidden: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [
        {
          required: true,
          message: '请选择发票类型',
        },
      ],
    },
  {
          label: '收票公司',
          type: 'select',
          prop: 'billingCompany',
          props: {
            label: 'companyName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span:24,
          search: true,
          hide: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
    {
      type: 'input',
      labelPosition: 'left',
      label: '明细信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'detailEntityList',
      type: 'dynamic',
      rules: [
        {
          required: true,
          message: '请输入明细信息',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width: 80,
          },
          {
            label: '数量',
            prop: 'number',
            width: 80,
            change: (a, b, c) => {
              const { row } = a;
              if (row.productTotalPrice && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '单价',
            prop: 'price',
            width: 100,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.number) {
              //   row.productTotalPrice = (row.number * 1 * row.price * 1).toFixed(2);
              // }
            },
          },
          {
            label: '金额',
            prop: 'productTotalPrice',
            width: 120,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.taxRate) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.number && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width: 100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.productTotalPrice) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.totalPrice && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width: 120,
          },
          {
            label: '小计',
            prop: 'totalPrice',
            width: 120,
            change: a => {
              form.value.invoicePrice = form.value.detailEntityList
                .reduce((pre, cur) => {
                  return (pre += cur.totalPrice * 1);
                }, 0)
                .toFixed(2);
              const { row } = a;
              if (row.taxRate && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
        ],
      },
    },

    {
      type: 'textarea',
      label: '备注',
      overHidden: true,
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});

let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  header: false,
  showSummary: true,
  menuWidth: 100,
  border: true,
  selection: false,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      bind: 'product.productName',
      cell: false,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      bind: 'product.productBrand',
      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      bind: 'product.productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 150,
      span: 12,
      cell: true,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
      width: 80,
    },
     {
      label: '发票单价',
      prop: 'realCostPrice',
      type: 'number',
      span: 12,
      cell: true,
      width: 150,
    },
    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',
      span: 12,
      cell: false,
      width: 80,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      width: 100,
      cell: false,
      formatter: row => {
        return (row.number * row.realCostPrice).toFixed(2);
      },
    },
  ],
});

function receview() {
  if (selectList.value.length == 0) return ElMessage.warning('请选择要收票的产品');
  const bol = [...new Set(selectList.value.map(item => item.supplierId))].length > 1;

  if (bol) {
    proxy.$message.warning('请选择同一个供应商');
    return;
  }

  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();

    selectProductList.value.push(
      ...selectList.value.map(i => {
        return {
          ...i,
          number: i.number * 1 - i.invoiceNumber * 1,
          realCostPrice : i.unitPrice,
          product: {
            ...i.productVO,
          },
        };
      })
    );

    nextTick(() => {
      setTotalAmount();
    });
    form.value.supplierId = selectProductList.value[0].supplierId;
    form.value.supplierName = selectProductList.value[0].supplierName;
    // form.value.supplierName = dateFormat(new Date(), 'YYYY-MM-DD');
  });
}

function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      setTotalAmount();
      setChecked();
    })
    .catch(() => {});
}
function setTotalAmount() {
  const totalPrice = selectProductList.value.reduce(
    (total, item) => (total += item.number * 1 * (item.unitPrice * 1)),
    0
  );
  form.value.invoicePrice = totalPrice.toFixed(2);
}
function submit(form, done, loading) {
  if (selectProductList.value.length == 0) {
    proxy.$message.error('请选择产品');
    done();
    return;
  }
  form.productDTOList = selectProductList.value.map(item => {
    return {
      purchaseContractDetailId: item.id,
      ...item,

      id: null,
    };
  });
  form.purchaseContractId = selectProductList.value
    .reduce((pre, cur) => {
      if (pre.includes(cur.contractId)) return pre;
      return [...pre, cur.contractId];
    }, [])
    .join(',');
  form.createTime = null;
  form.invoiceFiles = form.invoiceFiles.map(item => item.value).join(',');

  axios.post('/api/vt-admin/purchaseContractInvoice/save', form).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      onLoad();
      drawer.value = false;
      clearAll();
    }
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();
  selectProductList.value = [];
  form.value.createName = proxy.$store.getters.userInfo.nick_name;
  form.value.createTimeBf = dateFormat(new Date(), 'yyyy-MM-dd');
}

const summaryMethod = ({ columns, data }) => {
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'totalPrice') {
      const values = data.map(item => {
        const num = Number(item.number) || 0;
        const price = Number(item.realCostPrice) || 0;
        return num * price;
      });
      sums[index] = values.reduce((a, b) => a + b, 0).toFixed(2);
    } else {
      sums[index] = '';
    }
  });
  return sums;
};

// 导出
let headerDialog = ref(false);
let headerTableData = ref([
  {
    id: 'contractCode',
    label: '合同编号',
  },
  {
    id: 'productName',
    label: '产品名称',
  },
  {
    id: 'productSpecification',
    label: '规格型号',
  },
  {
    id: 'productBrand',
    label: '品牌',
  },
  {
    id: 'unitName',
    label: '单位',
  },
  {
    id: 'number',
    label: '数量',
  },
  {
    id: 'unitPrice',
    label: '单价',
  },
  {
    id: 'totalPrice',
    label: '金额',
  },
  {
    id: 'isInvoice',
    label: '开票状态',
  },
  {
    id: 'supplierName',
    label: '供应商名称',
  },

  {
    id: 'purchaseDate',
    label: '签订日期',
  },
]);
function exportData() {
  headerDialog.value = true;
  proxy.$nextTick(() => {
    headerTableData.value.forEach(item => {
      proxy.$refs.headerTableRef.toggleRowSelection(item, true);
    });
  });
}
function exportConfirm() {
  const columnList = proxy.$refs.headerTableRef.getSelectionRows().map(item => item.id);
  downloadXls({
    url: '/api/vt-admin/businessOpportunity/downTransactionProducts',
    method: 'post',
    data: {
      columnList,
      ...params.value,
      selectType: 0,
      startTime: params.value.signDate ? params.value.signDate[0] : null,
      endTime: params.value.signDate ? params.value.signDate[1] : null,
    },
  });
}



function handleClose() {
  debugger
  selectList.value = []
}
</script>

<style lang="scss" scoped>
.bottom_box {
  height: 0px;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  position: fixed;
  z-index: 2000;
  bottom: 0;
  left: 0;
  transition: height 0.2s linear;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom_box.active {
  height: 60px;
}
.box {
  background-color: #fff;
}
.el-form-item {
  margin-bottom: 0;
  margin: 5px 0;
}
.el-descriptions {
  margin-top: 5px !important;
}

:deep(.my-label) {
  min-width: 60px;
}
</style>
