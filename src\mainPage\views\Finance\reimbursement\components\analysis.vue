<template>
  <avue-crud
    :option="option"
    :data="tableData"
   
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="reset"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
    :row-style="
      ({ row, index }) => {
        return {
          'background-color': row.isUpdate == 1 ? 'var(--el-color-success-light-9)' : '',
        };
      }
    "
    :summary-method="getSummaries"
  >
    <template #accountingAmount="{ row }">
      <span>{{ row.accountingAmount || '---' }} </span>
    </template>
    <template #manageFee="{ row }">
      <span>{{ row.manageFee || '---' }}</span>
    </template>
    <template #number="{ row }">
      <span>{{ parseFloat(row.number || 0) || 1 }}</span>
    </template>
    <template #feeCost="{ row }">
      <span type="primary"> {{ row.feeCost || '---' }}</span>
    </template>

    <template #purchaseCost="{ row }">
      <span>
        {{ row.purchaseCost || '---' }}
      </span>
    </template>
    <template #labourCost="{ row }">
      <span type="info">
        {{ row.labourCost || '---' }}
      </span>
    </template>
  </avue-crud>
</template>

<script setup lang="jsx">
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import userSelect from '@/views/desk/components/userSelect.vue';

import { ElMessage } from 'element-plus';
const props = defineProps(['sealContractName','sealContractId']);
let option = ref({
  // height: 'auto',
  maxHeight: '650',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  size: 'small',
  searchLabelWidth: 120,
  menuWidth: 120,
  menu: false,
  border: true,
  header: false,
  showSummary: true,
  // sumColumnList: [
  //   { name: 'actualPaymentPrice', type: 'sum' },
  //   { name: 'actualPaymentPrice', type: 'sum' },
  // ],
  column: [
  
    {
      label: '产品名称',
      prop: 'productNames',
      overHidden: true,
      width: 130,
    },
    {
      label: '数量',
      prop: 'number',
      width: 60,
      formatter: row => {
        return parseFloat(row.number || 0) || 1;
      },
    },
    {
      label: '单价',
      prop: 'purchasePrice',
      width: 80,
    },
    {
      label: '含税成本总额',
      prop: 'hsprice',
      width: 80,
      children: [
        {
          label: '采购成本',
          prop: 'purchaseCost',
        },
        {
          label: '人工成本',
          prop: 'labourCost',
        },
      ],
    },
    // {
    //   label: '含税核算成本',
    //   prop: 'accountingAmount',
    //   width: 100,
    // },
    // {
    //   label: '财务核算成本',
    //   prop: 'financeAccountingAmount',
    //   width: 100,
    // },
    {
      label: '销售单价',
      prop: 'sealPrice',
      width: 80,
    },
    {
      label: '销售总额/合同额',
      prop: 'contractTotalPrice',
      width: 100,
    },
    {
      label: '业务费用',
      prop: 'businessFee',
      width: 80,
    },
    {
      label: '交付费用',
      prop: 'feeCost',
      width: 80,
    },
    // {
    //   label: '管理费用',
    //   prop: 'manageFee',
    //   width: 80,
    // },
    // {
    //   label: '质保尾款',
    //   prop: 'balancePayment',
    //   width: 80,
    // },
    // {
    //   label: '逾期利息',
    //   prop: 'overdueInterest',
    //   width: 80,
    // },

    // {
    //   label: '年份',
    //   prop: 'year',
    //   type: 'year',
    //   format: 'YYYY',
    //   valueFormat: 'YYYY',
    //
    //   hide: true,
    //   width: 100,
    // },
    // {
    //   label: '月份',
    //   prop: 'date',
    //   type: 'month',
    //   format: 'YYYY-MM',
    //   valueFormat: 'YYYY-MM',
    //
    //   hide: true,
    //   width: 100,
    // },

    // {
    //   label: '采购成本',
    //   prop: 'purchaseCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.purchaseCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.purchaseCost).toFixed(2);
    //   },
    // },

    // {
    //   label: '人工成本',
    //   prop: 'labourCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.labourCost).toFixed(2))
    //       ? ''
    //       : parseFloat(row.labourCost).toFixed(2);
    //   },
    // },
    // {
    //   label: '费用',
    //   prop: 'feeCost',
    //   width: 120,
    //   formatter: row => {
    //     return isNaN(parseFloat(row.feeCost).toFixed(2)) ? '' : parseFloat(row.feeCost).toFixed(2);
    //   },
    // },
    {
      label: '税金',
      prop: 'taxCost',
      width: 80,
      formatter: row => {
        return isNaN(parseFloat(row.taxCost).toFixed(2)) ? '' : parseFloat(row.taxCost).toFixed(2);
      },
    },
    {
      label: '利润',
      prop: 'grossProfit',
      width: 90,
      fixed: 'right',
      formatter: row => {
        return isNaN(parseFloat(row.grossProfit).toFixed(2))
          ? ''
          : parseFloat(row.grossProfit).toFixed(2);
      },
    },
    {
      label: '利润率',
      prop: 'grossProfitRate',
      width: 70,
      fixed: 'right',
    },
    
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const tableUrl = '/api/vt-admin/profitAnalysis/page';
let params = ref({
  // year: moment(new Date()).format('YYYY'),
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
watch(()  => props.sealContractName, async () => {
    onLoad()  
},{
    immediate:true
})
function onLoad() {
    if(!props.sealContractName) return 
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      
         sealContractId:props.sealContractId,
         year:props.year
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          newAccountingAmount: item.accountingAmount,
          newManageFee: item.manageFee,
        };
      });
      // tableData.value = [
      //   {
      //     contractName: '项目1',
      //     contractTotalPrice: 10000,
      //     customerName: '客户1',
      //     purchaseCost: 1000,
      //     labourCost: 500,
      //     otherCost: 1000,
      //     taxCost: 1000,
      //     feeCost: 1000,
      //     profit: 1000,
      //     profitRate: 1000,
      //     businessName: '业务员1',
      //   },
      // ];
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
  axios
    .get('/api/vt-admin/profitAnalysis/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.signDate && params.value.signDate[0],
        endTime: params.value.signDate && params.value.signDate[1],
        signDate: null,
      },
    })
    .then(res => {
      console.log(res.data.data);
      statisticData.value = res.data.data;
    });
}

function searchChange(params, done) {
  onLoad();
  done();
}
onMounted(() => {});
let statisticData = ref({});

function getSummaries({ columns, data }) {
  let arr = [];
  let valueArr = [
    
    'purchaseCost',
    'labourCost',
    'feeCost',
    'taxCost',
    'grossProfit',
    'businessFee',
    'feeCost',
  ];
  columns.forEach((item, index) => {
    if (index === 0) {
      arr.push(
        <div>
          本页小计：
          {/* <br />
          合计： */}
        </div>
      );
    } else {
      if (valueArr.includes(item.property)) {
        const value = data.reduce((sum, i) => {
          sum = sum + i[item.property] * 1;
          return sum;
        }, 0);
        arr.push(
          <div>
            {isNaN(value) ? '' : value.toFixed(2)}
            {/* <br />
            {
              statisticData.value[
                item.property == 'purchaseCost'
                  ? 'purchasePrice'
                  : item.property == 'profit'
                  ? 'profitPrice'
                  : item.property
              ]
            } */}
          </div>
        );
      } else {
        arr.push('');
      }
    }
  });
  return arr;
}

function reset() {
  params.value.businessUser = '';
  onLoad();
}
</script>

<style lang="scss" scoped>
:deep(.el-table--small .cell) {
  padding: 0;
}
</style>
