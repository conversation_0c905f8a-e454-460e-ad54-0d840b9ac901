import { downloadOwn } from './util';
import { ElNotification,ElMessage } from 'element-plus';
import { h } from 'vue';
import progress from '@/components/progress/index.vue';
export const loadFile = (url, name) => {

  let notice = ElNotification({
    title: '下载中',
    position: 'bottom-right',
    duration: 0,
    message: h(progress, {
     
      // 事件要以onXxx的形式书写
      onFinish: status => {
        if (status.value == 'ok') {
          notice.close(); // 关闭ElNotification
        }
      },
    }),
  });
    // 获取当前网页的域名，包含协议部分
    const currentOrigin = window.location.origin;
    // 正则表达式匹配协议和域名部分
    const domainRegex = /https?:\/\/[^\/]+/;
    // 替换 res.data.data 中的域名
    url = url.replace(domainRegex, currentOrigin);
  axios
    .get(url, {
      responseType: 'blob',
      withCredentials: false,
      onDownloadProgress(e) {
        console.log(e);
        // if (!e.total) return a.close();
        // a.$children[0].percentage = ((e.loaded / e.total) * 100).toFixed(2);
        // if ((e.loaded / e.total) * 100 == 100) {
        //   setTimeout(() => {
        //     a.close();
        //   }, 2000);
        // }
      },
    })
    .then(res => {
     
      let name1 = decodeURI(res.headers["content-disposition"]).split("=")[1];
      downloadOwn(res.data, name || name1);
      notice.close();
    })
    .catch(err => {
      notice.close();
      ElMessage.error(err.message);
    });
};
