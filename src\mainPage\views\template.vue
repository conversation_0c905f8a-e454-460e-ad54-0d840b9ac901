<template>
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="reset"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: true,
    editBtn: true,
    delBtn: true,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    menuWidth: 270,
    border: true,
    column: [
      {
        label: '客户名称',
        prop: 'customerName',
        width: 250,
        overHidden: true,
        search: true,
      },
      {
        label: '跟进记录',
        prop: 'followContent',
      },
  
      // {
      //   label: '跟进成效',
      //   prop: 'jobPerformance',
      // },
  
      {
        label: '批示',
        prop: 'remark',
      },
      {
        label: '跟进类型',
        prop: 'followType',
        type: 'select',
        search: true,
        dicData: followType,
      },
      {
        label: '填写时间',
        prop: 'createTime',
        search: true,
        searchSpan: 6,
        searchRange: true,
        type: 'date',
      },
      {
        label: '实际跟进时间',
        prop: 'followTime',
      },
      {
        label: '跟进人',
        prop: 'followName',
        search: true,
      },
      {
        label: '提醒人',
        prop: 'messageRemind',
      },
      {
        label: '跟进方式',
        prop: 'followWayStr',
        type: 'select',
      },
      {
        label: '跟进联系人',
        prop: 'followPerson',
      },
      // {
      //   label: '联系电话',
      //   prop: 'followPerson',
      // },
    ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  
  const addUrl = ''
  const delUrl = ''
  const updateUrl = ''
  const tableUrl = ''
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
         
          size,
          current,
          ...params.value,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        page.value.total = res.data.data.total;
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...form,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  function reset(){
    onLoad()
  }
  function searchChange(params, done) {
    onLoad();
    done();
  }
  </script>
  
  <style lang="scss" scoped></style>
  