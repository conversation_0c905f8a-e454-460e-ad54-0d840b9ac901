<template>
  <div style="width: 100%">
    <el-autocomplete
      v-model="value"
      style="width: 100%"
      :fetch-suggestions="querySearch"
      :trigger-on-focus="false"
      clearable
      v-if="dataType === 'name'"
      value-key="projectName"
      class="inline-input w-50"
      placeholder="请输入项目名称"
      @select="handleUserSelectConfirm"
      @blur="handleBlur"
    />
    <el-select v-else style="width: 100%" filterable remote placeholder="请输入项目" :remote-method="querySearch" v-model="value" clearable @change="handleBlur">
      <el-option :label="item.projectName" :value="item.id" v-for="item in projectData"></el-option>
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'project-drop',
};
</script>
<script setup>
import axios from 'axios';
import { watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
  },
  url: {
    type: String,
    default: '/api/vt-admin/project/page',
  },
  dataType:{
    type: String,
    default: 'name'
  }
});
watch(
  () => props.modelValue,
  val => {
    value.value = val;
  }
);
const emits = defineEmits(['update:modelValue']);
let value = ref('');
let projectData = ref([]);
function querySearch(value, cb) {
  if (!value) {
    projectData.value = [];
    return [];
  }
  axios.get(props.url, { params: { projectName: value, size: 100,current:1,selectType:2 } }).then(res => {
    if(cb){
      cb(res.data.data.records);
    }
    projectData.value = res.data.data.records;
  });
}
function handleUserSelectConfirm(value) {
  emits('update:modelValue', value.projectName);
}
function handleBlur() {
  emits('update:modelValue', value.value);
}
</script>

<style lang="scss" scoped></style>
