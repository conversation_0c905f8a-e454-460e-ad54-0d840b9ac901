<template>
  <div class="container">
    <div
      class="item"
      :class="{ is_actice: activeName == i[website.menu.path] }"
      @click="open(i)"
      v-for="i in $store.getters.topMenu.filter(i => !i.isHide)"
    >
    
        
        <el-badge :hidden="$store.getters.getDataNumberByUrl(i[website.menu.path]).number == 0" :value="$store.getters.getDataNumberByUrl(i[website.menu.path]).number" :title="$store.getters.getDataNumberByUrl(i[website.menu.path]).message" class="item">
          {{ i.name }}
        </el-badge>
    
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useStore } from 'vuex';
import { useRouter, useRoute } from 'vue-router';
import website from '@/config/website';

const router = useRouter();
const route = useRoute();
const store = useStore();
const open = item => {
  router.push({
    path: item[website.menu.path],
    query: item[website.menu.query],
  });
};

const activeName = computed(() => {
  const { meta, path } = route;

  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.container {
  // height: 40px;
  background-color: #fff;
  margin-bottom: 5px;
  padding: 5px;
  display: flex;
  font-size: 14px;
  color: #303133;
  // box-sizing: border-box;
  .item {
    font-size: 14px;
    height: 40px;
    line-height: 40px;
    margin: 0 10px;
    cursor: pointer;
  }
  .is_actice {
    color: $color-primary;
    border-bottom: 3px solid $color-primary;
  }
}
.el-tag {
  margin-right: 10px;
}
</style>
