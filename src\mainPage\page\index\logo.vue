<template>
  <div class="avue-logo">
    <transition name="fade">
      <!-- <span  class="avue-logo_subtitle" key="0">
        {{ website.logo }}
      </span> -->
      <div
        v-if="getScreen(isCollapse)"
        style="display: flex; justify-content: center; align-items: center; height: 100%"
      >
        <img style="width: 30px;color:#fff" :src="$store.getters.companyInfo?.logoUrl" alt="未配置logo" />
      </div>
    </transition>
    <transition-group name="fade">
      <template v-if="getScreen(!isCollapse)">
        <span class="avue-logo_title" key="1"
          ><img class="logo1" style="color:#fff"  :src="$store.getters.companyInfo?.logoUrl" alt="未配置logo"
        /></span>
      </template>
    </transition-group>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

export default {
  name: 'logo',
  data() {
    return {};
  },
  created() {},
  computed: {
    ...mapGetters(['isCollapse']),
  },
  methods: {},
};
</script>
<style scoped>
.logo1 {
  margin-top: 5px;
  width: 100px;
  height: 44px;
}
</style>
