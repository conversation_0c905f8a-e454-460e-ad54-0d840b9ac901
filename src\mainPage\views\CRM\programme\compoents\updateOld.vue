<template>
  <basic-container style="height: 100%" v-loading="loading">
    <Title
      >{{ isEdit ? '编辑方案' : '方案详情' }}
      <template #foot>
        <div v-if="isDeep">
          <el-button
            type="primary"
            @click="isFullScreen = true"
            icon="FullScreen"
            plain
            circle
          ></el-button>
          <el-button
            type="primary"
            @click="draftDeep"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >保存草稿</el-button
          >
          <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
          >保存草稿并生成报价单</el-button
        > -->

          <el-button
            type="primary"
            @click="draftDeep('confirm')"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            >保存并提交</el-button
          >
          <el-button
            type="primary"
            @click="handleEdit"
            v-if="
              !isEdit &&
              form.deepenStatus != 2 &&
              form.auditStatus != 1 &&
              form.auditStatus != 2 &&
              !props.businessPerson &&
              route.query.isAdmin != 'admin'
            "
            >编辑</el-button
          >
          <el-button
            type="primary"
            @click="deepAdminSubmit"
            v-if="isEdit && route.query.isAdmin == 'admin'"
            >保存</el-button
          >
          <el-button type="primary" icon="check" v-if="form.auditStatus == 1" @click="deepConfirm"
            >确认</el-button
          >
          <el-button
            @click="
              $router.$avueRouter.closeTag();
              $router.back();
            "
            v-if="!props.businessPerson"
            >关闭</el-button
          >
        </div>
        <div v-else>
          <el-button type="primary" @click="fullScreen" icon="FullScreen" plain circle></el-button>
          <el-button
            type="primary"
            @click="submit()"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >保存草稿</el-button
          >
          <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
          >保存草稿并生成报价单</el-button
        > -->
          <el-button
            type="primary"
            @click="draft('confirm')"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            >保存并提交</el-button
          >
          <el-button
            type="primary"
            @click="handleEdit"
            v-if="
              !isEdit &&
              form.optionStatus != 2 &&
              !props.businessPerson &&
              route.query.isAdmin != 'admin'
            "
            >编辑</el-button
          >
          <el-button
            type="primary"
            @click="adminSubmit"
            v-if="isEdit && route.query.isAdmin == 'admin'"
            >保存</el-button
          >
          <el-button type="primary" icon="check" v-if="form.auditStatus == 1" @click="normalConfirm"
            >确认</el-button
          >
          <el-button
            @click="
              $router.$avueRouter.closeTag();
              $router.back();
            "
            v-if="!props.businessPerson"
            >关闭</el-button
          >
        </div>
      </template></Title
    >
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
      <template #baseInfo-header="column">
        <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
        <el-button @click.stop="viewBusiness" type="primary" size="small">商机详情</el-button>
      </template>
    </avue-form>
    <div :class="isFullScreen ? 'fullScreen' : ''" style="transition: all 0.3s">
      <div v-if="isFullScreen" style="display: flex; justify-content: flex-end; height: 20px">
        <el-affix :offset="10">
          <el-button
            type="primary"
            size="small"
            @click="
              isFullScreen = false;
              $refs['affix-2'][0].updateRoot();
              $refs['affix-2'][0].update();
            "
            icon="House"
            circle
            plain
          ></el-button>
        </el-affix>
      </div>
      <el-tabs
        v-model="editableTabsValue"
        type="card"
        @tab-change="$nextTick(() => setSort())"
        :before-leave="handleTabsLeave"
        class="sort-tabs"
        ref="sort-tabs"
        style="margin-top: 10px"
        @edit="handleTabsEdit"
      >
        <el-tab-pane
          v-for="(item, index) in form.moduleDTOList"
          :key="item.uuid"
          :label="item.moduleName"
          :name="index"
          class="allowDrag"
        >
          <template #label>
            <span class="tab_box" style="display: flex; align-items: center">
              <i
                v-if="isEdit && !isDeep"
                style="font-size: 20px; cursor: move"
                class="element-icons el-icon-yidong_huaban move1"
                title="拖拽排序"
              ></i>
              <el-icon
                @click="editModuleName(item)"
                v-if="index !== 0 && isEdit && !isDeep"
                style="margin-right: 5px"
                ><edit
              /></el-icon>
              <span>{{ item.moduleName }}</span>
              <el-icon
                @click="handleTabsEdit(index, 'edit')"
                v-if="index !== 0 && isEdit && !isDeep"
                style="margin-left: 5px"
                ><close
              /></el-icon>
            </span>
          </template>
          <el-affix
            v-if="index != 0"
            :ref="`affix-${index}`"
            style="min-height: 50px"
            :offset="isFullScreen ? 0 : 155"
            class="affix"
          >
            <el-table
              show-header
              :row-class-name="tableRowClassName"
              :data="[]"
              border
              class="avue-crud"
            >
              <el-table-column label="基本信息" align="left">
                <template #header>
                  <div style="display: flex; align-items: center; flex-wrap: wrap">
                    基本信息：
                    <el-checkbox
                      v-model="baseShowArr[index].value"
                      v-for="(item, index) in baseShowArr"
                      :label="item.label"
                    />
                  </div>
                </template>

                <el-table-column v-if="isEdit && !isDeep" align="center" width="40">
                  <div>
                    <i
                      style="font-size: 20px; cursor: move"
                      class="element-icons el-icon-yidong_huaban move3"
                      title="拖拽排序"
                    ></i>
                  </div>
                </el-table-column>
                <el-table-column type="index" :index="val => customIndex(val, index1)" width="40" />
                <el-table-column
                  label="设备名称"
                  class-name="allowDrag"
                  show-overflow-tooltip
                  min-width="200"
                  v-if="findValueByLabel('名称', baseShowArr)"
                  prop="productName"
                >
                </el-table-column>
                <el-table-column
                  label="规格型号"
                  prop="productSpecification"
                  show-overflow-tooltip
                  min-width="200"
                  class-name="allowDrag"
                  v-if="findValueByLabel('型号', baseShowArr)"
                >
                </el-table-column>

                <el-table-column
                  label="商品描述"
                  class-name="allowDrag"
                  show-overflow-tooltip
                  min-width="200"
                  v-if="findValueByLabel('描述', baseShowArr)"
                  prop="description"
                ></el-table-column>
                <el-table-column
                  label="产品图片"
                  width="110"
                  v-if="findValueByLabel('图片', baseShowArr)"
                  class-name="allowDrag"
                  #default="{ row }"
                >
                </el-table-column>
                <el-table-column
                  label="品牌"
                  class-name="allowDrag"
                  prop="productBrand"
                  width="80"
                  show-overflow-tooltip
                  v-if="findValueByLabel('品牌', baseShowArr)"
                ></el-table-column>
                <el-table-column
                  label="单位"
                  width="60"
                  class-name="allowDrag"
                  v-if="findValueByLabel('单位', baseShowArr)"
                  prop="unitName"
                ></el-table-column>
              </el-table-column>
              <el-table-column label="报价信息" align="center">
                <el-table-column width="80" label="数量" #default="{ row }" prop="number">
                </el-table-column>
                <el-table-column
                  width="130"
                  label="单价"
                  #default="{ row, $index }"
                  prop="sealPrice"
                >
                </el-table-column>
                <el-table-column
                  label="人工费"
                  #default="{ row }"
                  v-if="form.isNeedLabor"
                  width="110"
                  prop="laborCost"
                >
                </el-table-column>
                <el-table-column
                  label="金额"
                  width="110"
                  class-name="allowDrag"
                  #default="{ row }"
                  prop="totalPrice"
                >
                </el-table-column>
                <el-table-column
                  label="专项成本"
                  #default="{ row, $index }"
                  width="110"
                  v-if="form.isHasSpecialPrice == 1"
                  prop="specialCostPrice"
                >
                </el-table-column>
                <el-table-column
                  label="专项供应商"
                  #default="{ row, $index }"
                  width="120"
                  v-if="form.isHasSpecialPrice == 1"
                  show-overflow-tooltip
                  prop="specialSupplierId"
                >
                </el-table-column>
                <el-table-column label="备注" width="130"></el-table-column>
              </el-table-column>
              <el-table-column label="参考信息" align="center">
                <template #header>
                  <div style="display: flex; align-items: center; flex-wrap: wrap">
                    <el-checkbox
                      v-model="referShowArr[index].value"
                      v-for="(item, index) in referShowArr"
                      :label="item.label"
                    />
                  </div>
                </template>
                <el-table-column
                  label="最近销售价"
                  class-name="allowDrag"
                  #default="{ row }"
                  width="100"
                  v-if="findValueByLabel('最近', referShowArr)"
                  prop="preSealPrice"
                >
                </el-table-column>
                <el-table-column
                  label="成本价"
                  class-name="allowDrag"
                  #default="{ row }"
                  width="110"
                  v-if="findValueByLabel('成本', referShowArr)"
                  prop="costPrice"
                >
                </el-table-column>
                <el-table-column
                  label="最低销售价"
                  class-name="allowDrag"
                  #default="{ row }"
                  width="100"
                  v-if="findValueByLabel('最低', referShowArr)"
                  prop="minSealPrice"
                >
                </el-table-column>
                <el-table-column
                  label="参考销售价"
                  class-name="allowDrag"
                  #default="{ row }"
                  width="100"
                  v-if="findValueByLabel('参考', referShowArr)"
                  prop="referSealPrice"
                >
                </el-table-column>
                <el-table-column
                  label="市场价"
                  class-name="allowDrag"
                  #default="{ row }"
                  width="80"
                  v-if="findValueByLabel('市场', referShowArr)"
                  prop="marketPrice"
                >
                </el-table-column>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                prop=""
                class-name="allowDrag"
                width="100"
                #default="{ row, index }"
                v-if="isEdit && !isDeep"
              >
                <el-button
                  type="primary"
                  text
                  icon="delete"
                  @click="deleteProduct(i.productList, row)"
                  >删除</el-button
                >
              </el-table-column>
            </el-table>
          </el-affix>
          <div v-show="index !== 0 && editableTabsValue == index">
            <el-collapse v-model="item.currentCollapse" :ref="`collapse-${index}`">
              <el-collapse-item
                :name="index1"
                v-for="(i, index1) in item.detailDTOList"
                :key="i.uuid"
              >
                <template #title>
                  <div
                    style="
                      display: flex;
                      justify-content: space-between;
                      flex: 1;
                      align-items: center;
                    "
                  >
                    <div
                      style="
                        display: flex;
                        justify-content: flex-start;
                        flex: 1;
                        align-items: center;
                      "
                    >
                      <i
                        v-if="isEdit && !isDeep"
                        style="font-size: 20px; cursor: move"
                        class="element-icons el-icon-yidong_huaban move2"
                        title="拖拽排序"
                      ></i>
                      <el-text size="large" style="font-weight: bolder" type="primary">{{
                        i.classify
                      }}</el-text>
                      <el-icon
                        @click.stop="editCategoryName(i)"
                        v-if="isEdit && !isDeep"
                        class="header-icon"
                      >
                        <edit />
                      </el-icon>

                      <el-button
                        type="primary"
                        style="margin-left: 10px"
                        icon="plus"
                        v-if="isEdit && !isDeep"
                        size="small"
                        @click.stop="addProduct(i.productList, i.classify)"
                        plain
                        >添加产品</el-button
                      >

                      <productSelectDrop
                        v-if="isEdit && !isDeep"
                        @focus="focus($event, i.productList, i.classify)"
                        @select="handleUserSelectConfirm"
                        style="margin-left: 5px"
                      ></productSelectDrop>
                    </div>
                    <el-button
                      style="margin-right: 5px"
                      @click.stop="deleteCategory(i.classify, item.detailDTOList)"
                      type="danger"
                      icon="delete"
                      size="small"
                      circle
                      plain
                    ></el-button>
                  </div>
                </template>

                <div>
                  <el-table
                    :show-header="!!form.isNeedLabor || !!form.isHasSpecialPrice"
                    :row-class-name="tableRowClassName"
                    :data="i.productList"
                    border
                    class="avue-crud"
                    :ref="`productTable-${index}-${index1}`"
                    show-summary
                    row-key="uuid"
                    :summary-method="
                      p => {
                        return productSum(p, item, item.detailDTOList);
                      }
                    "
                  >
                    <el-table-column label="基本信息" align="center">
                      <el-table-column v-if="isEdit && !isDeep" label="拖拽" width="40">
                        <div>
                          <i
                            style="font-size: 20px; cursor: move"
                            class="element-icons el-icon-yidong_huaban move3"
                            title="拖拽排序"
                          ></i>
                        </div>
                      </el-table-column>
                      <el-table-column
                        type="index"
                        :index="val => customIndex(val, index1, item.detailDTOList)"
                        width="40"
                      />
                      <el-table-column
                        label="设备名称"
                        class-name="allowDrag"
                        min-width="200"
                        v-if="findValueByLabel('名称', baseShowArr)"
                        prop="customProductName"
                      >
                        <template #default="{ row }" v-if="form.isHasCustom">
                          <el-popover
                            placement="top"
                            title="原名称:"
                            width="400"
                            :disabled="row.productName == row.customProductName"
                            effect="dark"
                            v-if="isEdit && !isDeep"
                            trigger="hover"
                            :content="row.productName"
                          >
                            <template #reference>
                              <span
                                v-if="!row.editName"
                                style="cursor: pointer"
                                @click="handleFocus(row, 'editName')"
                                >{{ row.customProductName || '---' }}</span
                              >
                              <el-input
                                type="textarea"
                                v-model="row.customProductName"
                                placeholder=""
                                v-else
                                :ref="`editName${row.uuid}`"
                                style="margin: 5px 0"
                                @blur="row.editName = false"
                                :rows="5"
                              ></el-input>
                            </template>
                          </el-popover>
                          <el-popover
                            placement="top"
                            width="600"
                            effect="light"
                            v-else
                            trigger="click"
                            :content="row.productName"
                          >
                            <template #default>
                              <Title>原信息:</Title>
                              <el-form>
                                <el-form-item label="产品名称:">
                                  <span>{{ row.productName }}</span>
                                </el-form-item>
                                <el-form-item label="规格型号:">
                                  <span>{{ row.productSpecification }}</span>
                                </el-form-item>
                                <el-form-item label="产品描述:">
                                  <span>{{ row.description }}</span>
                                </el-form-item>
                              </el-form>
                            </template>
                            <template #reference>
                              <el-text style="cursor: pointer" type="primary">{{
                                row.customProductName
                              }}</el-text>
                            </template>
                          </el-popover>
                        </template>
                      </el-table-column>

                      <el-table-column
                        label="规格型号"
                        prop="customProductSpecification"
                        show-overflow-tooltip
                        min-width="200"
                        v-if="findValueByLabel('型号', baseShowArr)"
                        class-name="allowDrag"
                      >
                        <template #default="{ row }" v-if="form.isHasCustom">
                          <el-popover
                            placement="top"
                            title="原规格型号:"
                            effect="dark"
                            width="400"
                            :disabled="row.productSpecification == row.customProductSpecification"
                            v-if="isEdit && !isDeep"
                            trigger="hover"
                            :content="row.productSpecification"
                          >
                            <template #reference>
                              <span
                                v-if="!row.editSpecification"
                                style="cursor: pointer"
                                @click="handleFocus(row, 'editSpecification')"
                                >{{ row.customProductSpecification || '---' }}</span
                              >
                              <el-input
                                type="textarea"
                                v-model="row.customProductSpecification"
                                placeholder=""
                                :ref="`editSpecification${row.uuid}`"
                                style="margin: 5px 0"
                                @blur="row.editSpecification = false"
                                :rows="5"
                              ></el-input>
                            </template>
                          </el-popover>
                        </template>
                      </el-table-column>

                      <el-table-column
                        label="商品描述"
                        class-name="allowDrag"
                        show-overflow-tooltip
                        min-width="200"
                        v-if="findValueByLabel('描述', baseShowArr)"
                        prop="customProductDescription"
                      >
                        <template #default="{ row }">
                          <el-popover
                            placement="top"
                            title="原描述:"
                            effect="dark"
                            :disabled="row.customProductDescription == row.description"
                            width="400"
                            v-if="isEdit && !isDeep && form.isHasCustom"
                            trigger="hover"
                            :content="row.description"
                          >
                            <template #reference>
                              <div
                                v-if="!row.editDescription"
                                style="cursor: pointer; width: 100%"
                                @click="handleFocus(row, 'editDescription')"
                              >
                                {{ row.customProductDescription || '---' }}
                              </div>
                              <el-input
                                type="textarea"
                                v-model="row.customProductDescription"
                                placeholder=""
                                :ref="`editDescription${row.uuid}`"
                                style="margin: 5px 0"
                                @blur="row.editDescription = false"
                                :rows="5"
                              ></el-input>
                            </template>
                          </el-popover>
                        </template>
                      </el-table-column>
                      <el-table-column
                        label="产品图片"
                        v-if="findValueByLabel('图片', baseShowArr)"
                        class-name="allowDrag"
                        width="110"
                        #default="{ row }"
                      >
                        <el-image
                          style="width: 80px"
                          :z-index="1000000"
                          :preview-src-list="[row.coverUrl]"
                          :src="row.coverUrl"
                        ></el-image>
                      </el-table-column>
                      <el-table-column
                        label="品牌"
                        class-name="allowDrag"
                        prop="productBrand"
                        width="80"
                        show-overflow-tooltip
                        v-if="findValueByLabel('品牌', baseShowArr)"
                      ></el-table-column>
                      <el-table-column
                        label="单位"
                        width="60"
                        class-name="allowDrag"
                        v-if="findValueByLabel('单位', baseShowArr)"
                        prop="unitName"
                      ></el-table-column>
                    </el-table-column>
                    <el-table-column label="报价信息" align="center">
                      <el-table-column width="80" label="数量" #default="{ row }" prop="number">
                        <el-input
                          v-if="isEdit"
                          v-model="row.number"
                          size="small"
                          style="width: 80%"
                        ></el-input>
                        <span v-else>{{ row.number }}</span>
                      </el-table-column>
                      <el-table-column
                        width="130"
                        label="单价"
                        #default="{ row, $index }"
                        prop="sealPrice"
                      >
                        <el-form
                          @submit.prevent
                          v-if="isEdit && !isDeep"
                          :ref="`sealPrice-${index}-${index1}-${$index}`"
                          :model="row"
                        >
                          <el-form-item
                            prop="sealPrice"
                            :rules="{
                              required: true,
                              message: '请输入单价',
                              trigger: 'blur',
                            }"
                          >
                            <div
                              style="
                                display: flex;
                                align-items: center;
                                justify-content: space-between;
                              "
                            >
                              <el-input
                                v-model="row.sealPrice"
                                placeholder="请输入单价"
                                size="small"
                                :title="
                                  row.sealPrice * 1 <= row.minSealPrice * 1
                                    ? '该价格小于或等于最低销售价'
                                    : ''
                                "
                                :class="{ warningInput: row.sealPrice * 1 <= row.minSealPrice * 1 }"
                                style="width: 58%"
                              ></el-input>
                              {{
                                ((row.sealPrice - row.costPrice) / row.costPrice).toFixed(2) ==
                                'Infinity'
                                  ? '---'
                                  : (
                                      ((row.sealPrice - row.costPrice) / row.costPrice) *
                                      100
                                    ).toFixed(1)
                              }}%
                            </div>
                          </el-form-item>
                        </el-form>
                        <span
                          :class="{ warningInput: row.sealPrice * 1 <= row.minSealPrice * 1 }"
                          :title="
                            row.sealPrice * 1 <= row.minSealPrice * 1
                              ? '该价格小于或等于最低销售价'
                              : ''
                          "
                          v-else
                          >{{ parseFloat(row.sealPrice).toLocaleString() }}</span
                        >
                      </el-table-column>
                      <el-table-column
                        label="人工费"
                        #default="{ row }"
                        v-if="form.isNeedLabor"
                        width="110"
                        prop="laborCost"
                      >
                        <el-input
                          v-if="isEdit && !isDeep"
                          v-model="row.laborCost"
                          placeholder="请输入人工费"
                          size="small"
                          style="width: 80%"
                        ></el-input>
                        <span v-else>{{ parseFloat(row.laborCost).toLocaleString() }}</span>
                      </el-table-column>
                      <el-table-column
                        label="金额"
                        class-name="allowDrag"
                        #default="{ row }"
                        width="110"
                        prop="totalPrice"
                      >
                        {{
                          parseFloat(
                            row.number *
                              (row.sealPrice * 1 + (form.isNeedLabor == 1 ? row.laborCost * 1 : 0))
                          ).toLocaleString() || '---'
                        }}
                      </el-table-column>
                      <el-table-column
                        label="专项成本"
                        #default="{ row, $index }"
                        width="110"
                        v-if="form.isHasSpecialPrice == 1"
                        prop="specialCostPrice"
                      >
                        <el-input
                          v-if="isEdit && !isDeep"
                          v-model="row.specialCostPrice"
                          placeholder="请输入专项成本"
                          size="small"
                          style="width: 100%"
                        ></el-input>
                        <span v-else>{{ row.specialCostPrice }}</span>
                      </el-table-column>
                      <el-table-column
                        label="专项供应商"
                        #default="{ row, $index }"
                        width="120"
                        v-if="form.isHasSpecialPrice == 1"
                        show-overflow-tooltip
                        prop="specialSupplierId"
                      >
                        <WfSupplierSelect
                          v-if="isEdit && !isDeep"
                          v-model="row.specialSupplierId"
                          placeholder="请选择专项供应商"
                          size="small"
                          style="width: 100%"
                        ></WfSupplierSelect>
                        <span v-else>{{ row.specialSupplierName }}</span>
                      </el-table-column>
                      <el-table-column width="130" label="备注" #default="{ row }" prop="number">
                        <el-input
                          v-if="isEdit"
                          v-model="row.remark"
                          size="small"
                          type="textarea"
                          autosize
                          style="width: 100%"
                        ></el-input>
                        <span v-else>{{ row.remark }}</span>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column label="参考信息" align="center">
                      <el-table-column
                        label="最近销售价"
                        class-name="allowDrag"
                        #default="{ row }"
                        width="100"
                        v-if="findValueByLabel('最近', referShowArr)"
                        prop="preSealPrice"
                      >
                        <span>{{
                          parseFloat(row.preSealPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.preSealPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        label="成本价"
                        class-name="allowDrag"
                        #default="{ row }"
                        width="110"
                        v-if="findValueByLabel('成本', referShowArr)"
                        prop="costPrice"
                      >
                        <div v-if="row.priceWarnType == 0 || !row.priceWarnType">
                          <div>
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                        </div>
                        <div v-else>
                          <div v-if="row.priceWarnType == 1" style="color: var(--el-color-success)">
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                          <div
                            v-else-if="row.priceWarnType == 2"
                            style="color: var(--el-color-warning)"
                          >
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                          <div v-else style="color: var(--el-color-danger)">
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                        </div>
                      </el-table-column>
                      <el-table-column
                        label="最低销售价"
                        class-name="allowDrag"
                        #default="{ row }"
                        width="100"
                        v-if="findValueByLabel('最低', referShowArr)"
                        prop="minSealPrice"
                      >
                        <span>{{
                          parseFloat(row.minSealPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.minSealPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        label="参考销售价"
                        class-name="allowDrag"
                        #default="{ row }"
                        width="100"
                        v-if="findValueByLabel('参考', referShowArr)"
                        prop="referSealPrice"
                      >
                        <span>{{
                          parseFloat(row.referSealPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.referSealPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        label="市场价"
                        class-name="allowDrag"
                        #default="{ row }"
                        width="80"
                        v-if="findValueByLabel('市场', referShowArr)"
                        prop="marketPrice"
                      >
                        <span>{{
                          parseFloat(row.marketPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.marketPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column
                      label="操作"
                      align="center"
                      prop=""
                      class-name="allowDrag"
                      width="100"
                      fixed="right"
                      #default="{ row, index }"
                      v-if="isEdit && !isDeep"
                    >
                      <el-button
                        type="primary"
                        text
                        icon="delete"
                        @click="deleteProduct(i.productList, row)"
                        >删除</el-button
                      >
                    </el-table-column>
                  </el-table>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-form v-if="index !== 0 && isEdit && !isDeep">
              <el-form-item label="">
                <el-button
                  type="primary"
                  style="margin-top: 5px; width: 100%"
                  icon="plus"
                  size="small"
                  @click="addCategory(item)"
                  >新增分类</el-button
                >
              </el-form-item>
            </el-form>
            <el-form inline style="margin-top: 5px">
              <el-form-item label="服务费、人工费:">
                <el-input
                  v-if="isEdit && !isDeep"
                  v-model="item.servicePrice"
                  size="small"
                ></el-input>
                <span v-else>￥{{ parseFloat(item.servicePrice).toLocaleString() }}</span>
              </el-form-item>
              <!-- <el-form-item label="人工费">
                <el-input
                  v-if="isEdit && !isDeep"
                  v-model="item.constructionPrice"
                  size="small"
                ></el-input>
                <span v-else>￥{{ parseFloat(item.constructionPrice).toLocaleString() }}</span>
              </el-form-item> -->
              <el-form-item label="税费:">
                <el-input v-if="isEdit && !isDeep" v-model="item.taxPrice" size="small"></el-input>
                <!-- <span v-else>￥{{ parseFloat(item.taxPrice).toLocaleString() }}</span> -->
                <span v-else>{{ item.taxPrice }}</span>
              </el-form-item>
              <el-form-item label="总计:">
                <span style="color: var(--el-color-danger)">
                  ￥{{ parseFloat(moduleTotal(item)).toLocaleString() }}</span
                ><span v-if="form.isNeedLabor == 1"
                  >(含人工<span style="color: var(--el-color-danger)"
                    >￥{{ parseFloat(totalLaborCost(item)).toLocaleString() }}</span
                  >
                  )</span
                >
              </el-form-item>
              <el-form-item label="成本总计">
                <span style="color: var(--el-color-danger)">
                  ￥{{ parseFloat(moduleCostTotal(item)).toLocaleString() }}</span
                >
              </el-form-item>
            </el-form>
          </div>
          <el-empty
            v-if="index == 0 && form.moduleDTOList.length <= 1"
            description="请先添加子项"
          ></el-empty>
          <el-table
            v-if="index == 0"
            :data="form.moduleDTOList.filter((item, index) => index !== 0)"
            show-summary
            class="avue-crud"
            border
            :summary-method="totalSum"
          >
            <el-table-column label="子项名称" prop="moduleName" #default="{ row }">
              <el-button
                type="primary"
                @click="
                  editableTabsValue = form.moduleDTOList.findIndex(
                    item => item.moduleName == row.moduleName
                  )
                "
                text
                >{{ row.moduleName }}</el-button
              >
            </el-table-column>
            <el-table-column label="服务费，人工费" #default="{ row }" prop="servicePrice">
              <span>{{ parseFloat(row.servicePrice).toLocaleString() }}</span>
            </el-table-column>
            <!-- <el-table-column label="施工费" #default="{ row }" prop="constructionPrice">
              <span>{{ parseFloat(row.constructionPrice).toLocaleString() }}</span>
            </el-table-column> -->
            <el-table-column label="税费" #default="{ row }" prop="taxPrice">
              <span>{{ parseFloat(row.taxPrice).toLocaleString() }}</span>
            </el-table-column>
            <el-table-column label="数量" #default="{ row }" prop="number">
              {{ totalNumber(row) }}
            </el-table-column>
            <el-table-column label="总计" #default="{ row }" prop="totalPrice">
              {{ parseFloat(totalPrice(row)).toLocaleString()
              }}<span v-if="form.isNeedLabor == 1"
                >(含人工：{{ parseFloat(totalLaborCost(row)).toLocaleString() }})</span
              >
            </el-table-column>
            <el-table-column label="成本总计" #default="{ row }" prop="totalCostPrice">
              {{ parseFloat(totalCostPrice(row)).toLocaleString() }}
            </el-table-column>
            <el-table-column label="备注" show-overflow-tooltip prop="remark"></el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="CustoBtn" v-if="isEdit && !isDeep" :closable="false">
          <template #label>
            <el-button
              icon="plus"
              circle
              plain
              type="primary"
              @click="handleTabsEdit(undefined, 'add')"
            ></el-button>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select ref="product-select" check-type="box" @onConfirm="handleUserSelectConfirm">
    </wf-product-select>

    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" size="60%">
      <Title>{{ businessForm.name }}</Title>
      <BusinessDetail :form="businessForm" :isEdit="false"></BusinessDetail>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import Sortable from 'sortablejs';
import { ref, getCurrentInstance, onBeforeUnmount, onMounted, onUnmounted } from 'vue';
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import BusinessDetail from '@/views/CRM/businessOpportunity/detail/baseInfo.vue';
import productSelectDrop from '../../quotation/compoents/productSelectDrop.vue';
import { randomLenNum } from '@/utils/util';
const route = useRoute();
const router = useRouter();
let isEdit = ref(route.query.type == 'edit' || route.query.type == 'add');
let form = ref({
  moduleDTOList: [
    {
      moduleName: '汇总',
    },
  ],
});
const props = defineProps({
  stageStatus: {
    type: Number,
    default: -1,
  },
  businessPerson: {
    type: String,
    default: '',
  },
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
onMounted(() => {
  console.log(111);
  if (route.query.id) {
    getDetail();
  }
});

const isDeep = ref(route.query.deep == '1');
let option = ref({
  submitBtn: false,
  labelWidth: 100,
  detail: !isEdit.value || isDeep.value,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      column: [
        {
          label: '方案名称',
          prop: 'optionName',
          rules: [
            {
              required: true,
              message: '请填写方案名称',
            },
            {
              validator: (rule, value, callback) => {
                const reg = /^[^/\\?？\[\]]*$/;
                console.log(value, rule, reg);
                if (!reg.test(value)) {
                  callback(new Error('不能包含特殊字符"/\?？[]"'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        },
        {
          label: '客户名称',
          prop: 'customerName',
          readonly: true,
        },
        {
          label: '关联联系人',
          prop: 'concatName',
          readonly: true,
        },
        {
          label: '人工费',
          prop: 'isNeedLabor',
          readonly: true,
          type: 'switch',
          span: 4,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '自定义',
          prop: 'isHasCustom',
          readonly: true,
          type: 'switch',
          labelTip: '打开后点击文字即可编辑',
          span: 4,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '专项报价',
          prop: 'isHasSpecialPrice',
          type: 'radio',
          span: 4,
          value: 0,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,

          // format: 'YYYY-MM-DD',
          // valueFormat: 'YYYY-MM-DD',
        },
      ],
    },
  ],
});
let { proxy } = getCurrentInstance();
let tabIndex = 2;
const editableTabsValue = ref(0);
// 是否是深化设计

let tableOption = ref({
  header: false,
  menuLeft: false,
  border: true,
  column: [
    {
      label: '产品名称',
    },
  ],
});
function handleTabsEdit(tabName, action) {
  if (action == 'add') {
    proxy.$refs.dialogForm.show({
      title: '添加子项',
      option: {
        column: [
          {
            label: '子项名称',

            prop: 'moduleName',
            rules: [
              { required: true, message: '请输入子项名称', trigger: 'blur' },
              {
                validator: (rule, value, callback) => {
                  const reg = /^[^/\\?？\[\]]*$/;
                  console.log(value, rule, reg);
                  if (!reg.test(value)) {
                    callback(new Error('不能包含特殊字符"/\?？[]"'));
                  } else {
                    callback();
                  }
                },
                trigger: 'change',
              },
            ],
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        form.value.moduleDTOList.push({
          moduleName: res.data.moduleName,
          remark: res.data.remark,
          currentCollapse: [0],
          taxPrice: 0,
          constructionPrice: 0,
          sortNumber: form.value.moduleDTOList.length + 1,
          uuid: randomLenNum(10, true),
          servicePrice: 0,
          detailDTOList: [
            {
              classify: '默认分类',
              uuid: randomLenNum(10, true),
              classifySort: 0,
              productList: [],
            },
          ],
        });
        editableTabsValue.value = form.value.moduleDTOList.length - 1;
        res.close();
        proxy.$nextTick(() => {
          setSort();
        });
      },
    });
  } else {
    if (tabName == 0) return;
    proxy
      .$confirm('删除该子项将删除下面所有产品，确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        form.value.moduleDTOList.splice(tabName, 1);
        // form.value.moduleDTOList = form.value.moduleDTOList.filter(
        //   (item, index) => index !== tabName
        // );
      });
  }
}
let classify = ref('');
let productList = ref('');
function addProduct(productListBf, classifyName) {
  classify.value = classifyName;
  productList.value = productListBf;
  proxy.$refs['product-select'].visible = true;
}

function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      productList.value.push({
        ...r.data.data,
        productId: r.data.data.id,
        preSealPrice: r.data.data.sealPrice,
        sealPrice: '',
        number: 1,
        laborCost: r.data.data.laborCost,
        id: null,
        classify: classify.value,
        uuid: randomLenNum(10, true),
        customProductName: r.data.data.productName,
        customProductSpecification: r.data.data.productSpecification,
        customProductDescription: r.data.data.description,
      });
      proxy.$nextTick(() => {
        setSort();
      });
    });
  });
}
function editProduct(productList, row, i) {
  proxy.$refs.dialogForm.show({
    title: '编辑产品',
    option: {
      column: [
        {
          label: '选择产品',
          component: 'wf-product-select',
          prop: 'productId',
          value: row.productId,
          disabled: true,
          span: 24,
        },
        {
          label: '数量',
          type: 'number',
          prop: 'number',
          span: 24,
          value: row.number,
        },
      ],
    },
    callback(res) {
      row.number = res.data.number;
      res.close();
    },
  });
}
// function summary(params) {
//   const { columns, data } = param
//   columns.map((item,index) => {
//     if(index == 0){
//       return '合计'
//     }else if(index == )
//   })
// }
let loading = ref(false);
function getDetail() {
  let url = '/api/vt-admin/businessOpportunityOption/detailByOptionId';
  if (isDeep.value) {
    url = '/vt-admin/businessOpportunityOptionHistory/detailByOptionId';
  }
  loading.value = true;
  axios
    .get(url, {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      const data = parseData(res.data.data);
      form.value = data;
      if (isDeep.value) return;
      proxy.$nextTick(() => {
        setSort();
      });
    });
}

const setSort = () => {
  // 设置 子系统拖拽
  console.log(proxy.$refs);
  const el = proxy.$refs['sort-tabs'].$el.querySelector('.el-tabs__nav');
  new Sortable(el, {
    handle: '.move1',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      console.log(evt);

      const targetRow = form.value.moduleDTOList.splice(evt.oldIndex, 1);
      // if (!targetRow[0]) return;
      form.value.moduleDTOList.splice(evt.newIndex, 0, targetRow[0]);
      form.value.moduleDTOList = form.value.moduleDTOList.map((item, index) => {
        return {
          ...item,
          sortNumber: index,
        };
      });
    },
  });
  form.value.moduleDTOList.forEach((item, index) => {
    console.log(item, editableTabsValue.value, index);
    if (!item.detailDTOList) return;
    // 当前页签和tabs页签重复则设置拖拽,此时页面才有dom元素
    if (editableTabsValue.value == index) {
      const el = proxy.$refs[`collapse-${index}`][0].$el;
      // 设置分类拖拽
      new Sortable(el, {
        handle: '.move2',
        animation: 180,
        delay: 0,
        put: true,
        onStart: function (/**Event*/ evt) {
          item.currentCollapse = new Array(item.detailDTOList.length);
        },
        onEnd: evt => {
          const targetRow = item.detailDTOList.splice(evt.oldIndex, 1);
          // if (!targetRow[0]) return;
          item.detailDTOList.splice(evt.newIndex, 0, targetRow[0]);
          item.detailDTOList = item.detailDTOList.map((item, index) => {
            return {
              ...item,
              classifySort: index,
            };
          });
          console.log(evt, item.detailDTOList);
        },
      });
      item.detailDTOList.forEach((item1, index1) => {
        const el = proxy.$refs[`productTable-${index}-${index1}`][0].$el.querySelector('tbody');
        new Sortable(el, {
          // group: 'shared',
          handle: '.move3',
          animation: 180,
          delay: 0,
          put: true,
          onEnd: evt => {
            console.log(evt);

            const targetRow = item1.productList.splice(evt.oldIndex, 1);
            // if (!targetRow[0]) return;
            item1.productList.splice(evt.newIndex, 0, targetRow[0]);
            item1.productList = item1.productList.map((item, index) => {
              return {
                ...item,
                sortNumber: index,
              };
            });

            // console.log( proxy.$refs[`productTable-${index}-${index1}`][0]);
            // proxy.$refs[`productTable-${index}-${index1}`][0].doLayout()
            // console.log(item1.productList, form.value);
          },
        });
      });
    }
  });
};
function deleteProduct(list, row) {
  proxy
    .$confirm('确定删除该产品?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const indexToRemove = list.findIndex(item => row.uuid === item.uuid);
      list.splice(indexToRemove, 1);
    });
}
function editCategoryName(i) {
  proxy.$refs.dialogForm.show({
    title: '修改分类名称',
    option: {
      column: [
        {
          label: '分类名称',
          value: i.classify,
          prop: 'classify',
          span: 24,
        },
      ],
    },
    callback(res) {
      i.classify = res.data.classify;
      res.close();
    },
  });
}
function editModuleName(i) {
  proxy.$refs.dialogForm.show({
    title: '修改子项名称',
    option: {
      column: [
        {
          label: '子项名称',
          value: i.moduleName,
          prop: 'moduleName',
          span: 24,
        },
        {
          label: '备注',
          value: i.remark,
          prop: 'remark',
          span: 24,
        },
      ],
    },
    callback(res) {
      i.moduleName = res.data.moduleName;
      i.remark = res.data.remark;
      res.close();
    },
  });
}
function addCategory(item) {
  proxy.$refs.dialogForm.show({
    title: '添加分类',
    option: {
      column: [
        {
          label: '分类名称',
          prop: 'categoryName',
          span: 24,
        },
      ],
    },
    callback(res) {
      item.detailDTOList.push({
        classify: res.data.categoryName,
        uuid: randomLenNum(10, true),
        classifySort: item.detailDTOList.length,
        productList: [],
      });
      item.currentCollapse.push(item.detailDTOList.length - 1);
      res.close();
    },
  });
}
function deleteCategory(classifyName, list) {
  proxy
    .$confirm(`是否删除分类【${classifyName}】`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      list.splice(
        list.findIndex(item => item.classify == classifyName),
        1
      );
      proxy.$message({
        type: 'success',
        message: '删除成功!',
      });
    });
}
function formatData(type = '') {
  let moduleKey = type == '' ? 'moduleDTOList' : 'moduleHistoryDTOList';
  let detailKey = type == '' ? 'detailDTOList' : 'detailHistoryDTOS';
  let data = {
    ...form.value,
    [moduleKey]: form.value.moduleDTOList
      .filter((item, index) => index !== 0)
      .map((i, index) => {
        return {
          ...i,
          sortNumber: index,
          [detailKey]: i.detailDTOList.reduce((pre, cur, i) => {
            return pre.concat(
              cur.productList.map((item, index) => {
                return {
                  ...item,
                  classify: cur.classify,
                  classifySort: i,
                  unitPrice: item.purchasePrice,
                  sortNumber: index,
                  laborCost: form.value.isNeedLabor == 1 ? item.laborCost : 0,
                  uuid: randomLenNum(10, true),
                };
              })
            );
          }, []),
        };
      }),
  };
  return data;
}
let isSubmit = ref(false);
// 非深化设计提交
function draft(type) {
  let res = [];
  let logList = [];
  form.value.moduleDTOList.forEach((item, index) => {
    console.log(item);
    if (!item.detailDTOList) return;
    item.detailDTOList.forEach((item1, index1) => {
      item1.productList.forEach((item2, index2) => {
        if (
          proxy.$refs[`sealPrice-${index}-${index1}-${index2}`] &&
          proxy.$refs[`sealPrice-${index}-${index1}-${index2}`][0]
        ) {
          proxy.$refs[`sealPrice-${index}-${index1}-${index2}`][0].validate(valid => {
            if (valid) {
              res.push(true);
            } else {
              logList.push(`${item.moduleName}-${item1.classify}-${item2.productName}单价未填`);
              res.push(false);
            }
          });
        } else {
          res.push(true);
        }
      });
    });
  });

  setTimeout(() => {
    if (!res.some(i => !i)) {
      proxy.$refs.addForm.validate((valid, done) => {
        if (valid) {
          proxy.$confirm('确认此次操作吗？', '提示').then(() => {
            submit(type, done);
          });
        }
      });
    } else {
      proxy.$message.warning(logList.join('\n'));
      done();
    }
  }, 0);
}
function submit(type, done = () => {}) {
  let data = formatData();
  if (data.id) {
    axios
      .post('/api/vt-admin/businessOpportunityOption/update', {
        ...data,
        optionStatus: type == 'confirm' ? 2 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();
        done();

        if (type == 'confirm') {
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }

        // option.value.detail = true;
      });
  } else {
    data = {
      ...data,
      businessOpportunityId: route.query.id,
      optionStatus: type == 'confirm' ? 2 : 0,
    };
    axios.post('/api/vt-admin/businessOpportunityOption/save', data).then(res => {
      proxy.$message.success(res.data.msg);
      getDetail();
      done();

      if (type == 'confirm') {
        isEdit.value = false;
        proxy.$router.$avueRouter.closeTag();
        proxy.$router.back();
      }

      // option.value.detail = true;
    });
  }
}
function adminSubmit(params) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        let data = formatData();

        axios
          .post('/api/vt-admin/businessOpportunityOption/updateOption', {
            ...data,
          })
          .then(res => {
            proxy.$message.success(res.data.msg);
            getDetail();
            done();
            // isEdit.value = false;
            // option.value.detail = true;
          });
      });
    }
  });
}

// 深化设计提交
function draftDeep(type) {
  // let res = [];
  // let logList = [];
  // form.value.moduleDTOList.forEach((item, index) => {
  //   console.log(item);
  //   if (!item.detailDTOList) return;
  //   item.detailDTOList.forEach((item1, index1) => {
  //     item1.productList.forEach((item2, index2) => {
  //       proxy.$refs[`sealPrice-${index}-${index1}-${index2}`][0].validate(valid => {
  //         if (valid) {
  //           res.push(true);
  //         } else {
  //           logList.push(`${item.moduleName}-${item1.classify}-${item2.productName}单价未填`);
  //           res.push(false);
  //         }
  //         console.log(res);
  //       });
  //     });
  //   });
  // });

  // setTimeout(() => {
  //   if (!res.some(i => !i)) {
  //     proxy.$refs.addForm.validate((valid, done) => {
  //       if (valid) {
  //         proxy.$confirm('确认此次操作吗？', '提示').then(() => {
  //           deepSubmit(type, done);
  //         });
  //       }
  //     });
  //   } else {
  //     proxy.$message.warning(logList.join('\n'));
  //   }
  // }, 0);
  deepSubmit(type);
}
function deepSubmit(type) {
  let data = formatData('deep');
  if (data.id) {
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/editDeepenDesign', {
        ...data,
        optionStatus: type == 'confirm' ? 1 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();

        if (type == 'confirm') {
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }
        // isEdit.value = false;
        // option.value.detail = true;
      });
  } else {
    data = {
      ...data,
      businessOpportunityId: route.query.id,
      optionStatus: type == 'confirm' ? 1 : 0,
    };
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/saveDeepenDesign', data)
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();

        if (type == 'confirm') {
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }
        // isEdit.value = false;
        // option.value.detail = true;
      });
  }
}
function deepAdminSubmit(params) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        let data = formatData();

        axios
          .post('/api/vt-admin/businessOpportunityOption/updateOption', {
            ...data,
          })
          .then(res => {
            proxy.$message.success(res.data.msg);
            getDetail();
            done();
            // isEdit.value = false;
            // option.value.detail = true;
          });
      });
    }
  });
}
// 深化提交over
function totalNumber(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p += c.number * 1;
      return p;
    }, 0);
    return pre;
  }, 0);
}
function totalPrice(row) {
  return (
    row.detailDTOList.reduce((pre, cur) => {
      pre += cur.productList.reduce((p, c) => {
        p += c.number * (c.sealPrice * 1 + (form.value.isNeedLabor == 1 ? c.laborCost * 1 : 0));
        // p += c.number * (c.sealPrice * 1);
        return p;
      }, 0);
      return pre;
    }, 0) +
    // row.taxPrice * 1 +
    row.servicePrice * 1
    // row.constructionPrice * 1
  );
}
function totalLaborCost(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p += c.number * c.laborCost * 1;
      // p += c.number * (c.sealPrice * 1);
      return p;
    }, 0);
    return pre;
  }, 0);
  // row.taxPrice * 1 +
  // row.servicePrice * 1
  // row.constructionPrice * 1
}
function totalCostPrice(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p +=
        c.number *
        (form.value.isHasSpecialPrice == 1 && c.specialCostPrice
          ? c.specialCostPrice * 1
          : c.costPrice * 1);
      // p += c.number * (c.sealPrice * 1);
      return p;
    }, 0);
    return pre;
  }, 0);
  // row.taxPrice * 1 +
  // row.servicePrice * 1
  // row.constructionPrice * 1
}
function parseData(resData) {
  const data = {
    ...resData,
    // optionDate: resData.optionDate || new Date(),
    moduleDTOList: [
      {
        moduleName: '汇总',
      },
      ...((resData.moduleVOList &&
        resData.moduleVOList.map(item => {
          return {
            ...item,
            uuid: randomLenNum(10, true),
            currentCollapse: item.detailVOList.map((item, index) => index),
            detailDTOList: item.detailVOList.reduce((result, item) => {
              const existingEntry = result.find(entry => entry.classify === item.classify);
              if (existingEntry) {
                existingEntry.productList.push({
                  ...item.product,
                  ...item,
                  costPrice: resData.auditStatus == 2 ? item.costPrice : item.product.costPrice,
                  preSealPrice:
                    resData.auditStatus == 2
                      ? item.sealPrice
                      : item.product && item.product.sealPrice,
                  minSealPrice:
                    resData.auditStatus == 2
                      ? item.minSealPrice
                      : item.product && item.product.minSealPrice,
                  referSealPrice:
                    resData.auditStatus == 2
                      ? item.referSealPrice
                      : item.product && item.product.referSealPrice,
                  uuid: randomLenNum(10, true),
                });
              } else {
                result.push({
                  classify: item.classify,
                  uuid: randomLenNum(10, true),
                  productList: [
                    {
                      ...item.product,
                      ...item,
                      costPrice: resData.auditStatus == 2 ? item.costPrice : item.product.costPrice,
                      preSealPrice:
                        resData.auditStatus == 2
                          ? item.sealPrice
                          : item.product && item.product.sealPrice,
                      minSealPrice:
                        resData.auditStatus == 2
                          ? item.minSealPrice
                          : item.product && item.product.minSealPrice,
                      referSealPrice:
                        resData.auditStatus == 2
                          ? item.referSealPrice
                          : item.product && item.product.referSealPrice,
                      sealPrice: item.sealPrice,
                      uuid: randomLenNum(10, true),
                    },
                  ],
                });
              }
              result.sort((a, b) => a - b);
              return result;
            }, []),
          };
        })) ||
        []),
    ],
    optionName: route.query.type == 'edit' ? route.query.name + '方案' : resData.optionName,
  };

  return data;
}
function productSum(param, i, allData) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => Number(item[column.property]));

      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'prePrice') {
      // const values = data.map(item => Number(item[column.property]));
      // sums[index] = `￥ ${values.reduce((prev, curr) => {
      //   const value = Number(curr);
      //   if (!Number.isNaN(value)) {
      //     return prev + curr;
      //   } else {
      //     return prev;
      //   }
      // }, 0)}`
    } else if (column.property == 'totalPrice') {
      const values = data.map(item => {
        return Number(
          item.number *
            (item.sealPrice * 1 + (form.value.isNeedLabor == 1 ? item.laborCost * 1 : 0))
        );
      });

      const total = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      setConstructionPrice(allData, i);
      sums[index] = `￥ ${parseFloat(total).toLocaleString()}`;
    } else if (column.property == 'costPrice') {
      const values = data.map(item => {
        return Number(
          item.number *
            (form.value.isHasSpecialPrice == 1 && item.specialCostPrice
              ? item.specialCostPrice * 1
              : item.costPrice * 1)
        );
      });

      const total = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);

      sums[index] = `￥ ${parseFloat(total).toLocaleString()}`;
    }
  });

  return sums;
}
function setConstructionPrice(data, i) {
  const allValueArr = data.map(item => {
    const arr = item.productList.map(item =>
      Number(item.number * (form.value.isNeedLabor == 1 ? item.laborCost * 1 : 0))
    );
    const value = arr.reduce((prev, curr) => {
      const value = Number(curr);
      if (!Number.isNaN(value)) {
        return prev + curr;
      } else {
        return prev;
      }
    }, 0);
    return value;
  });

  i.constructionPrice = allValueArr.reduce((prev, curr) => {
    const value = Number(curr);
    if (!Number.isNaN(value)) {
      return prev + curr;
    } else {
      return prev;
    }
  }, 0);
}
function totalSum(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => totalNumber(item));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'taxPrice') {
      const values = data.map(item => Number(item[column.property]));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'servicePrice') {
      const values = data.map(item => Number(item[column.property]));
      sums[index] = `￥ ${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'constructionPrice') {
      const values = data.map(item => Number(item[column.property]));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'totalPrice') {
      const values = data.map(item => totalPrice(item));
      const allPrice = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      form.value.allPrice = allPrice;
      sums[index] = `￥ ${parseFloat(allPrice).toLocaleString()}  `;
    } else if (column.property == 'totalCostPrice') {
      const values = data.map(item => totalCostPrice(item));
      const allPrice = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      // form.value.allPrice = allPrice;
      sums[index] = `￥ ${parseFloat(allPrice).toLocaleString()}  `;
    }
  });

  return sums;
}
function tableRowClassName({ row }) {
  if (row.isNew === 1) {
    return 'warning-row';
  } else {
    return '';
  }
}
function moduleTotal(item) {
  return totalPrice(item);
}
function moduleCostTotal(item) {
  return totalCostPrice(item);
}
function handleEdit() {
  isEdit.value = true;
  option.value.detail = false;
}
function handleTabsLeave(activeName) {
  if (activeName == 'CustoBtn') {
    return false;
  }
}

let businessOpportunityId = ref(route.query.id);

let drawer = ref(false);
let businessForm = ref({});
function viewBusiness(params) {
  drawer.value = true;
  getBusinessDetail();
}
function getBusinessDetail() {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id: route.query.businessOpportunityId || form.value.businessOpportunityId,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode } = res.data.data;
      businessForm.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        isProduct: res.data.data.productVOList && res.data.data.productVOList.length == 0 ? 0 : 1,
        productVOList:
          res.data.data.productVOList &&
          res.data.data.productVOList.map(item => {
            return {
              ...item,
              ...item.productVO,
            };
          }),
        // registeredCapital: Number(res.data.data.registeredCapital),
      };
    });
}
function focus(a, productListBf, classifyName) {
  classify.value = classifyName;
  productList.value = productListBf;
}
function normalConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type: 'radio',
          value: 2,
          dicData: [
            {
              value: 2,
              label: '通过',
            },
            {
              value: 3,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control: val => {
            return {
              auditReason: {
                display: val == 3,
              },
            };
          },
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      console.log(form.value);
      axios
        .post('/api/vt-admin/businessOpportunityOption/audit', {
          id: form.value.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        });
    },
  });
}
function deepConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type: 'radio',
          value: 2,
          dicData: [
            {
              value: 2,
              label: '通过',
            },
            {
              value: 3,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control: val => {
            return {
              auditReason: {
                display: val == 3,
              },
            };
          },
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunityOptionHistory/auditDeepenDesign', {
          id: form.value.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        });
    },
  });
}
let isFullScreen = ref(false);
function fullScreen() {
  isFullScreen.value = true;
  // console.log(proxy.$refs);
  proxy.$refs['affix-2'][0].updateRoot();
  proxy.$refs['affix-2'][0].update();
}
// 控制基本列显隐
let baseShowArr = ref([
  {
    value: true,
    label: '名称',
  },
  {
    value: true,
    label: '型号',
  },

  {
    value: false,
    label: '图片',
  },
  {
    value: true,
    label: '描述',
  },
  {
    value: true,
    label: '品牌',
  },
  {
    value: true,
    label: '单位',
  },
]);
// 控制报价列显隐
let quaShowArr = ref([
  {
    value: true,
    label: '数量',
  },
  {
    value: true,
    label: '单价',
  },

  {
    value: true,
    label: '金额',
  },
]);
// 控制参考信息列显隐
let referShowArr = ref([
  {
    value: true,
    label: '最近',
  },
  {
    value: true,
    label: '成本',
  },

  {
    value: true,
    label: '最低',
  },
  {
    value: true,
    label: '参考',
  },
  {
    value: true,
    label: '市场',
  },
]);
function findValueByLabel(lable, arr) {
  return Boolean(arr.find(item => item.label === lable).value);
}
function handleFocus(row, value) {
  row[value] = true;
  console.log(proxy.$refs);
  proxy.$nextTick(() => {
    proxy.$refs[`${value}${row.uuid}`][0].focus();
  });
}
// 自定义序号
function customIndex(index, parentIndex, detailDTOList) {
  // 计算前面的所有数组总长度
  let sum = 0;
  for (let i = 0; i < parentIndex; i++) {
    sum += detailDTOList[i].productList.length;
  }
  // 返回序号
  index = sum + index + 1;

  return index;
}
onBeforeRouteLeave((to, from) => {
  if (isEdit.value) {
    const answer = window.confirm('确定离开当前页面吗？');
    // 取消导航并停留在同一页面上
    if (!answer) {
      proxy.$store.commit('ADD_TAG', {
        name: from.name || from.name,
        path: from.path,
        fullPath: from.fullPath,
        params: from.params,
        query: from.query,
        meta: from.meta,
      });
      return false;
    }
  }
});
</script>

<style scoped>
::v-deep .el-table .el-table__cell {
  padding: 0;
}
::v-deep .el-collapse-item__header {
  line-height: 33px;
}
::v-deep .el-form-item {
  /* margin-bottom: 5px; */
}
::v-deep .el-tabs__header {
  margin-bottom: 5px;
}
.el-collapse-item__content {
  padding-bottom: 8px;
}
.el-table .el-form-item {
  margin-bottom: 0;
}
.fullScreen {
  position: fixed;

  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  left: 0;
  top: 0;
  z-index: 999;
  background-color: #fff;
  padding: 15px;
}
::v-deep .affix .el-table .el-table__empty-block {
  display: none !important;
}
::v-deep .affix .el-table__body-wrapper {
  display: none !important;
}
.tab_box .move1 {
  display: none;
}
.tab_box:hover .move1 {
  display: inline-block;
}
.warningInput {
  /* border: 1px solid var(--el-color-warning) !important; */
}
::v-deep .warningInput .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}
.warningInput {
  color: var(--el-color-danger);
}
</style>
