<template>
  <div class="m-calendar">
    <div class="m-calendar-title">
      <el-icon @click="handlePrevYear" class="calendaricon"><DArrowLeft /></el-icon>
      <el-icon @click="handlePrevMonth" class="calendaricon"><ArrowLeft /></el-icon>
      <!-- <i @click="handlePrevYear" class="calendaricon el-icon-d-arrow-left"></i>
      <i @click="handlePrevMonth" class="calendaricon el-icon-arrow-left"></i> -->
      <span>{{ year }}年{{ month }}月</span>
      <el-icon @click="handleNextMonth" class="calendaricon"><ArrowRight /></el-icon>
      <el-icon @click="handleNextYear" class="calendaricon"><DArrowRight /></el-icon>
      <!-- <i @click="handleNextMonth" class="calendaricon el-icon-arrow-right"></i>
      <i @click="handleNextYear" class="calendaricon el-icon-d-arrow-right"></i> -->
    </div>
    <div class="m-calendar-con">
      <div class="m-calendar-con-week">
        <div class="m-calendar-con-week-item">日</div>
        <div class="m-calendar-con-week-item">一</div>
        <div class="m-calendar-con-week-item">二</div>
        <div class="m-calendar-con-week-item">三</div>
        <div class="m-calendar-con-week-item">四</div>
        <div class="m-calendar-con-week-item">五</div>
        <div class="m-calendar-con-week-item">六</div>
      </div>
      <div class="m-calendar-con-days">
        <div
          class="m-calendar-con-day"
          v-for="(item, index) in dayArr"
          :class="index == selectIndex ? 'active' : ''"
          :key="index"
          @click="handleDayClick(index)"
        >
          <span>{{ item['name'] }}</span>
          <el-icon
            v-if="
              logDetailForMonth &&
              logDetailForMonth[
                `${year}-${('' + month).toString().padStart(2, '0')}-${item['name']
                  ?.toString()
                  .padStart(2, '0')}`
              ] == 1 &&
              new Date(
                `${year}-${month.toString().padStart(2, '0')}-${item['name']
                  ?.toString()
                  .padStart(2, '0')}`
              ).getTime() <= new Date().getTime()
            "
            style="color: var(--el-color-success)"
          >
            <SuccessFilled></SuccessFilled>
          </el-icon>
          <el-icon
            v-if="
              logDetailForMonth &&
              logDetailForMonth[
                `${year}-${('' + month)?.toString().padStart(2, '0')}-${item['name']
                  ?.toString()
                  .padStart(2, '0')}`
              ] == 0 &&
              new Date(
                `${year}-${month.toString().padStart(2, '0')}-${item['name']
                  ?.toString()
                  .padStart(2, '0')}`
              ).getTime() <= new Date().getTime()
            "
            style="color: var(--el-color-danger)"
          >
            <CircleCloseFilled />
          </el-icon>
        </div>
      </div>
    </div>
    
  </div>
  
</template>

<script>
// import axios from "axios";
import moment from 'moment';
const fullMonth = [1, 3, 5, 7, 8, 10, 12];
import { SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue';
export default {
  name: 'MCalendar',
  props: ['logDetailForMonth'],
  data() {
    return {
      // 时间戳
      time: 0,
      // 当前时间戳的年份
      year: 0,
      // 当前时间戳的月份
      month: 0,
      // 日历天的数组
      dayArr: [],
      // 时间戳的日子
      day: 0,
      // 任务日程状态数组
      todoStatusList: [],
      // 选中的日期
      selectIndex: 0,
    };
  },
  created() {
    let time = new Date();
    this.time = time.getTime();

    this.initCalendar();
    this.handleOutYearMonthDay();
  },
  methods: {
    // 获取当前日期星期几
    getTodayWeek() {
      let time = `${this.year}-${this.month.toString().padStart(2, 0)}-01`;
      let day = new Date(time).getDay();
      return day;
    },
    // 获取当前月份有多少天
    getToMonDays() {
      if (fullMonth.includes(this.month)) {
        return 31;
      }
      if (this.month != 2) {
        return 30;
      }
      // 闰年2月判断
      if ((this.year % 4 == 0 && this.year % 100 != 0) || this.year % 400 == 0) {
        return 29;
      }
      return 28;
    },
    // 天数选择
    handleDayClick(index) {
      if (index < this.getTodayWeek()) {
        return;
      }
      this.selectIndex = index;
      this.day = this.dayArr[index]['name'];
      this.time = new Date(
        `${this.year}-${this.month.toString().padStart(2, '0')}-${this.day
          .toString()
          .padStart(2, '0')}`
      ).getTime();
      // 抛出日期
      this.handleOutYearMonthDay();
    },
    // 初始化/更新日历
    async initCalendar() {
      this.year = new Date(this.time).getFullYear();
      this.month = new Date(this.time).getMonth() + 1;
      this.day = new Date(this.time).getDate();
      // 拿到所有任务状态
      const todoStatusList = await this.queryTodoStatusList();

      let arr = [];
      for (let i = 1; i <= this.getToMonDays(); i++) {
        let date = `${this.year}-${this.month.toString().padStart(2, '0')}-${i
          .toString()
          .padStart(2, '0')}`;
        for (let j = 0; j < todoStatusList.length; j++) {
          if (date == todoStatusList[j]['scheduleDate']) {
            // 有状态推送状态
            arr.push({
              name: i,
              status: todoStatusList[j]['status'],
            });
            break;
          }
        }
        if (!arr[i - 1]) {
          arr.push({
            name: i,
          });
        }
      }
      if (this.getTodayWeek() == 0) {
        this.dayArr = arr;
      } else {
        this.dayArr = [...Array(this.getTodayWeek()).fill(''), ...arr];
      }
      console.log(this.getTodayWeek(), this.day - 1, '数据');
      // 设置选中
      this.selectIndex = this.getTodayWeek() + this.day - 1;
      // 刷新当前任务列表
      this.handleOutYearMonthDay();
    },
    // 查询日程任务状态数组
    queryTodoStatusList() {
      return new Promise(resolve => {
        let dateArr = moment(this.time).format('YYYY-MM').split('-');
        // 通过年/月拿到当前月份的任务状态数组
        axios
          .get('/api/vt-admin/schedule/statisticsByYearAndDate', {
            params: {
              year: dateArr[0],
              month: dateArr[1],
            },
          })
          .then(e => {
            resolve(e.data.data);
          });
      });
    },
    // 上一年
    handlePrevYear() {
      if (this.time && this.time > 31536000000) {
        this.time -= 31536000000;
        // 更新日历
        this.initCalendar();
      }
    },
    // 下一页
    handleNextYear() {
      if (this.time) {
        this.time += 31536000000;
        // 更新日历
        this.initCalendar();
      }
    },
    // 上一月
    handlePrevMonth() {
      if (this.time && this.time > 2592000000) {
        this.time -= 2592000000;
        // 更新日历
        this.initCalendar();
      }
    },
    // 下一月
    handleNextMonth() {
      if (this.time) {
        console.log(this.time);
        this.time += 2592000000;
        console.log(this.time);
        // 更新日历
        this.initCalendar();
      }
    },
    // 对外抛出日期变化
    handleOutYearMonthDay() {
      this.$emit('calendarChange', moment(this.time).format('YYYY-MM-DD'));
    },
  },
};
</script>

<style lang="scss" scoped>
.m-calendar {
  width: 100%;
  height: 100%;
  padding-right: 7px;
  box-sizing: border-box;

  .m-calendar-title {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #303133;

    .calendaricon {
      margin: 0 10px;
      cursor: pointer;
    }
  }
  .m-calendar-con {
    width: 100%;
    height: calc(100% - 40px);

    .m-calendar-con-week {
      width: 100%;
      height: 24px;
      display: flex;

      .m-calendar-con-week-item {
        flex: 1;
        width: 100%;
        color: #606266;
        line-height: 24px;
        height: 24px;
        text-align: center;
      }
    }
    .m-calendar-con-days {
      width: 100%;
      height: calc(100% - 24px);
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-x: hidden;
      overflow-y: auto;

      .m-calendar-con-day {
        width: calc(100% / 7);
        height: 16.5%;
        // border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;

        font-size: 14px;

        cursor: pointer;

        .complete {
          display: inline-block;
          width: 5px;
          height: 5px;
          background-color: green;
          border-radius: 100%;

          position: absolute;
          bottom: 2px;
          left: 50%;
          transform: translateX(-50%);
        }

        .nocomplete {
          width: 5px;
          height: 5px;
          border-radius: 100%;
          position: absolute;
          bottom: 2px;
          left: 50%;
          transform: translateX(-50%);
          background-color: #fa3534;
          font-size: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
        }

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          color: #fff;

          background-color: var(--el-color-primary);
        }
      }
    }
  }
}
</style>
