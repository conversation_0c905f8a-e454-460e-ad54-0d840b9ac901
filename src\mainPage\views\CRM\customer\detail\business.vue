<template>
  <div>
    <myBusinessOpportunity
      v-if="props.info.type == 0"
      
      :customerId="props.info.customerId"
    ></myBusinessOpportunity>
    <subordinateBusinessOpportunity
      v-if="props.info.type == 1"
      
      :customerId="props.info.customerId"
    ></subordinateBusinessOpportunity>
    <assistBusinessOpportunity
      v-if="props.info.type == 2"
      
      :customerId="props.info.customerId"
    ></assistBusinessOpportunity>
    <allBusinessOpportunity
      v-if="props.info.type == 3 || props.info.type == 4 || props.info.type == 5"
      
      :customerId="props.info.customerId"
    ></allBusinessOpportunity>
  </div>
</template>

<script setup>
import myBusinessOpportunity from '../../businessOpportunity/myBusinessOpportunity.vue';
import subordinateBusinessOpportunity from '../../businessOpportunity/subordinateBusinessOpportunity.vue';
import assistBusinessOpportunity from '../../businessOpportunity/assistBusinessOpportunity.vue';
import allBusinessOpportunity from '../../businessOpportunity/allBusinessOpportunity.vue';
const props = defineProps(['info']);

</script>

<style lang="scss" scoped></style>
