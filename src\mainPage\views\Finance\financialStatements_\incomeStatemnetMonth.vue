<template>
  <div>
    <el-row :gutter="20" style="height: 100%">
      <el-col :span="16" style="height: 100%">
        <el-card body-style="padding:5px" style="height: 100%" shadow="never">
          <avue-crud
            :option="option"
            :data="tableData"
            v-model:page="page"
            v-model:search="params"
            @on-load="onLoad"
            :table-loading="loading"
            ref="crud"
            @keyup.enter="onLoad"
            @row-del="rowDel"
            @search-reset="reset"
            @search-change="searchChange"
            @refresh-change="onLoad"
            @current-change="onLoad"
            @size-change="onLoad"
            v-model="form"
            @row-click="handleRowClick"
            :row-style="rowStyle"
            :cell-style="cellStyle"
          >
          </avue-crud>
        </el-card>
      </el-col>
      <el-col style="height: 100%" :span="8">
        <el-card style="height: 100%" body-style="padding:5px" shadow="never">
          <template #header>
            <div style="display: flex; gap: 20px">
              <div>{{ tableData[currentIndex]?.businessUserName }}统计</div>
              <div style="font-size: 14px">
                <div>
                  环比增长：
                  <span v-html="lastMonthPrice"></span>
                </div>
                <div>环比增长率：<span v-html="lastMonthPriceRate"></span></div>
              </div>
              <div style="font-size: 14px">
                <div>
                  同比增长：
                  <span v-html="lastYearPrice"></span>
                </div>
                <div>同比增长率：<span v-html="lastYearPriceRate"></span></div>
              </div>
            </div>
          </template>
          <div>
            <div ref="chartsRef" style="width: 100%; height: 300px"></div>
          </div>
          <div></div>
        </el-card>
      </el-col>
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';
const props = defineProps(['date']);
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  height: '100%',
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  size: 'small',
  menu: false,
  header: false,
  highlightCurrentRow: true,
  border: true,
  searchSpan: 6,
  searchMenuSpan: 6,
  column: {
    businessUserName: {
      label: '类目',

      //   component: 'wf-user-drop',

      //   search: true,
      searchSpan: 4,
    },
    totalPrice: {
      label: '本月销售额',
    },
    pre: {
      label: '与上月对比',
      children: [
        {
          label: '上月销售额',
          prop: 'lastMonthTotalPrice',
        },
        {
          label: '上月增长额',
          prop: 'lastMonthGrowthPrice',
          html: true,
          formatter: row => {
            return `<div style='color:${row.lastMonthGrowthPrice * 1 <= 0 ? 'green' : 'red'}'>${
              row.lastMonthGrowthPrice ? row.lastMonthGrowthPrice : '0'
            }</div>`;
          },
        },
        {
          label: '上月增长率',
          prop: 'lastMonthGrowthRate',
          html: true,
          formatter: row => {
            return `<div style='color:${row.lastMonthGrowthRate * 1 <= 0 ? 'green' : 'red'}'>${
              row.lastMonthGrowthRate ? row.lastMonthGrowthRate : '0'
            }%</div>`;
          },
        },
      ],
    },
    preYear: {
      label: '与上年同期对比',
      children: [
        {
          label: '上年同期销售额',
          prop: 'lastYearTotalPrice',
        },
        {
          label: '上年同期增长额',
          prop: 'growthPrice',
          html: true,
          formatter: row => {
            return `<span style='color:${row.growthPrice * 1 <= 0 ? 'green' : 'red'}'>${
              row.growthPrice ? row.growthPrice : '0'
            }</span>`;
          },
        },
        {
          label: '上年同期增长率',
          prop: 'growthRate',
          html: true,
          formatter: row => {
            return `<span style='color:${row.growthRate * 1 <= 0 ? 'green' : 'red'}'>${
              row.growthRate ? row.growthRate : '0'
            }%</span>`;
          },
        },
      ],
    },
    preYearNext: {
      label: '上年下期数据',
      children: [
        {
          label: '上年下期销售额',
          prop: 'lastYearNextMonthTotalPrice',
        },
      ],
    },
    //   businessName: {
    //     label: '业务员',
    //     components: 'wf-user-drop',
    //     hide: true,
    //     search: true,
    //     searchSpan: 4,
    //   },
    //   businessType: {
    //     label: '业务板块',
    //     type: 'select',
    //     dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
    //     props: {
    //       label: 'dictValue',
    //       value: 'id',
    //     },
    //     label: '业务类型',
    //     // multiple: true,
    //     span: 12,
    //     width: 250,
    //     overHidden: true,
    //     parent: true,
    //     hide: true,
    //     search: true,
    //   },
    //   name: {
    //     label: '经营体名字',
    //     hide: true,
    //     search: true,
    //     searchLabelWidth: 120,
    //   },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/statistics/monthlyBusinessPersonList';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
const lastMonthPrice = computed(() => {
  return `<span style='color:${
    tableData.value[currentIndex.value]?.lastMonthGrowthPrice * 1 <= 0 ? 'green' : 'red'
  }'>${
    tableData.value[currentIndex.value]?.lastMonthGrowthPrice
      ? tableData.value[currentIndex.value]?.lastMonthGrowthPrice
      : '0'
  }</span>`;
});
const lastMonthPriceRate = computed(() => {
  return `<span style='color:${
    tableData.value[currentIndex.value]?.lastMonthGrowthRate * 1 <= 0 ? 'green' : 'red'
  }'>${
    tableData.value[currentIndex.value]?.lastMonthGrowthRate
      ? tableData.value[currentIndex.value]?.lastMonthGrowthRate
      : '0'
  }%</span>`;
});
const lastYearPrice = computed(() => {
  return `<span style='color:${
    tableData.value[currentIndex.value]?.growthPrice * 1 <= 0 ? 'green' : 'red'
  }'>${
    tableData.value[currentIndex.value]?.growthPrice
      ? tableData.value[currentIndex.value]?.growthPrice
      : '0'
  }</span>`;
});
const lastYearPriceRate = computed(() => {
  return `<span style='color:${
    tableData.value[currentIndex.value]?.growthRate * 1 <= 0 ? 'green' : 'red'
  }'>${
    tableData.value[currentIndex.value]?.growthRate
      ? tableData.value[currentIndex.value]?.growthRate
      : '0'
  }%</span>`;
});
let crud = ref();
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        yearMonth: props.date,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data;
      initChart();
      crud.value.setCurrentRow(tableData.value[0]);
    });
}
let router = useRouter();

function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

let chartsRef = ref();
function initChart(params) {
  var chartDom = chartsRef.value;
  var myChart = echarts.init(chartDom);
  var option;

  option = {
    tooltip: {
      trigger: 'item',
    },
    legend: {
      show: false,
      left: 'center',
      top: 'bottom',
      data: ['本月销售额', '上月销售额', '上年同期销售额'],
    },
    // toolbox: {
    //   show: true,
    //   feature: {
    //     mark: { show: true },
    //     dataView: { show: true, readOnly: false },
    //     restore: { show: true },
    //     saveAsImage: { show: true },
    //   },
    // },
    series: [
      {
        name: '销售额',
        type: 'pie',
        radius: [20, 140],

        itemStyle: {
          borderRadius: 5,
        },
        data: [
          { value: tableData.value[currentIndex.value].totalPrice, name: '本月销售额' },
          { value: tableData.value[currentIndex.value].lastMonthTotalPrice, name: '上月销售额' },
          { value: tableData.value[currentIndex.value].lastYearTotalPrice, name: '上年同期销售额' },
        ],
      },
    ],
  };

  option && myChart.setOption(option);
}
let currentIndex = ref(0);
function handleRowClick(row, index) {
  currentIndex.value = row.$index;
  initChart();
}
function rowStyle({ row, index }) {
  if (row.$index == 0) {
    return {
      'font-size': '16px',
      fontWeight: 'bolder',
    };
  }
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  if (column.property == 'businessUserName') {
    return {
      fontSize: '14px',
      fontWeight: 'bolder',
    };
  }
}
</script>

<style lang="scss" scoped></style>
