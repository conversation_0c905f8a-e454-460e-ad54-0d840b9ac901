<template>
  <basic-container block>
    <el-breadcrumb style="margin-bottom: 10px" separator="/">
      <el-breadcrumb-item
        ><a style="cursor: pointer" href="#" @click="activeName = 'annualIncomeExpenseYear'"
          >{{ currentDate.split('-')[0] }}年度收入支出统计</a
        >
      </el-breadcrumb-item>
      <el-breadcrumb-item v-if="activeName == 'annualIncomeExpenseMonth'"
        ><a href="#" @click="activeName = 'annualIncomeExpenseMonth'"
          >{{ currentDate }}收入支出统计</a
        ></el-breadcrumb-item
      >
    </el-breadcrumb>
    <!-- <incomeStatemnetMonth></incomeStatemnetMonth>
      <annualIncomeExpenseYear></annualIncomeExpenseYear> -->
    <transition name="fade" mode="out-in">
      <!-- <component :is="activeName"></component> -->
      <annualIncomeExpenseMonth
        :date="currentDate"
        v-if="activeName == 'annualIncomeExpenseMonth'"
      ></annualIncomeExpenseMonth>
      <annualIncomeExpenseYear
        @updateDate="updateDate"
        @priceClick="handleClick"
        v-else
      ></annualIncomeExpenseYear>
    </transition>
  </basic-container>
</template>

<script setup>
import { shallowRef } from 'vue';
import annualIncomeExpenseMonth from './annualIncomeExpenseMonth.vue';
import annualIncomeExpenseYear from './annualIncomeExpenseYear.vue';

let activeName = ref('annualIncomeExpenseYear');

let currentDate = ref(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`);
function handleClick(val) {
  currentDate.value = val.date;
  activeName.value = 'annualIncomeExpenseMonth';
}

function updateDate(date) {
  currentDate.value = date;
}
</script>

<style lang="scss" scoped>
/* 下面我们会解释这些 class 是做什么的 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
  transform: translateX(0px);
}

.fade-enter-from,
.fade-leave-to {
  //   opacity: 0;
  transform: translateX(100px);
}
</style>
