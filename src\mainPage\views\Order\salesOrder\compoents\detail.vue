<template>
  <basic-container style="height: 100%">
    <Title
      >订单详情
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="订单名称">
            <el-tag effect="plain" size="default">{{ form.contractName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="订单总额">
            <el-tag effect="plain" size="default">{{ form.contractTotalPrice || '---' }}</el-tag>
          </el-form-item>

          <!-- <el-form-item label="订单状态">
            <el-tag effect='plain' size="default">{{ '---' }}</el-tag>
          </el-form-item> -->

          <!-- <el-form-item label="合同状态">
            <el-tag effect='plain' size="default" v-if="form.contractStatus == 0" type="info">待执行</el-tag>
            <el-tag effect='plain' size="default" v-if="form.contractStatus == 1" type="info">执行中</el-tag>
            <el-tag effect='plain' size="default" type="info" v-if="form.contractStatus == 2"  >暂停</el-tag>
            <el-tag effect='plain' size="default" type="info" v-if="form.contractStatus == 3"  >终止</el-tag>
            <el-tag effect='plain' size="default" type="success" v-if="form.contractStatus == 4"  >结束</el-tag>
          </el-form-item> -->
          <el-form-item label="业务">
            <el-tag effect="plain">{{ form.businessUserName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="商务">
            <el-tag effect="plain">{{ form.assistUserName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="交付负责人">
            <el-tag effect="plain">{{ form.deliveryUserName || '---' }}</el-tag>
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <!-- <div style="display: flex">
          <div class="left_content">
            <div class="main_box">
              <div
                class="item"
                v-for="(item, index) in tabArr"
                :class="{ active: currentIndex == index }"
                @click="handleClick(index)"
              >
                <div class="arrow"></div>
                {{ item }}
              </div>
            </div>
          </div>
          <div style="width: calc(100% - 100px)">
            <component
              :is="currentCompoent"
              :form="form"
              :isEdit="isEdit"
              @getDetail="getDetail"
            ></component>
          </div>
        </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <ContractBaseInfo
            :form="form"
            v-loading="loading"
            :isEdit="isEdit"
            @getDetail="getDetail"
          ></ContractBaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :userId="form.businessPerson"
      :is="tabArray.find(item => item.name == activeName).component"
      :offerId="form.offerId"
      :sealContractId="form.id"
      :customerId="form.customerId"
      :sealContractInvoiceId="form.customerInvoiceId"
      :delBtn="delBtn"
      :form="form"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import ContractBaseInfo from '../detail/orderBaseInfo.vue';

// 产品信息
import productInfo from '../detail/orderProduct.vue';
// 标的
import mission from '../detail/mission.vue';

// 费用
import cost from '../detail/cost.vue';
// // 退货
// import returnProduct from '../detail/returnProduct.vue';
// 变更
import orderChange from '../detail/orderChange.vue';

import axios from 'axios';

let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
let delBtn = ref(false);
// onMounted(() => {
//   getDetail();
// });
watchEffect(() => {
  if (route.query.id) {
    getDetail();

    console.log(route.query.delBtn);
    delBtn.value = route.query.delBtn == 1;
  }
});
const conpletArr = [
  'follow',
  'product',
  'contract',
  'mission',
  'cost',
  'returnProduct'
  // 'orderChange'
];
const tabArray = [
  {
    label: '产品信息',
    name: 'product',
    component: productInfo,
  },
  {
    label: '任务',
    name: 'mission',
    component: mission,
  },
  // {
  //   label: '退货',
  //   name: 'returnProduct',
  //   component: returnProduct,
  // },
  {
    label: '费用',
    name: 'cost',
    component: cost,
  },
//  {
//   label:'订单变更',
//   name:'orderChange',
//   component:orderChange
//  },

];

function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: route.query.id.split(',')[0],
      },
    })
    .then(res => {
      loading.value = false;
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
    });
}
let moreInfoDetail = ref(false);

let currentIndex = ref(0);
// let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}
let { proxy } = getCurrentInstance();

function edit() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '商务',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistUser,
        },
        {
          label: '技术',
          prop: 'technicalPersonnel',
          component: 'wf-user-select',
          value: form.value.technologyUser,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealContract/update', {
          ...res.data,
          id: form.value.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
const activeName = ref('baseInfo');
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
..../detail/orderProduct.vue/detail/orderBaseInfo.vue
