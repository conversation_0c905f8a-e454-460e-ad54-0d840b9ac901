<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #companyName="scope">
        <el-link type="primary" @click="$refs.crud.rowView(scope.row)">{{
          scope.row.companyName
        }}</el-link>
      </template>
      <template #isDefault="{ row }">
        <el-switch
          v-model="row.isDefault"
          :active-value="1"
          :disabled="row.isDefault === 1"
          :inactive-value="0"
          @click="handleIsDefaultChange(row)"
        >
        </el-switch>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  border: true,
  labelWidth: 120,
  // tabs: true,
  dialogType: 'drawer',
  column: [
    {
      display: false,
      label: '公司名称',
      prop: 'companyName',
      search: true,
      width: 280,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入公司名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '法人代表',
      prop: 'legalRepresentative',
      display: false,
      width: 120,
      rules: [
        {
          required: true,
          message: '请输入法人代表',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '联系人',
      prop: 'contactPerson',
      width: 120,
      display: false,

      rules: [
        {
          required: true,
          message: '请输入联系人',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '联系电话',
      display: false,
      prop: 'contactPhone',
      width: 150,
    },
    {
      label: '联系地址',
      prop: 'address',
      display: false,
      span: 24,

      minRows: 2,
      type: 'textarea',
    },
    {
      label: '是否默认',
      prop: 'isDefault',
      display: false,
      width:120,
    },
  ],
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      column: [
        {
          label: '公司名称',
          prop: 'companyName',
          search: true,
          width: 180,
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入公司名称',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '税号',
          prop: 'taxNumber',
          rules: [
            {
              required: true,
              message: '请输入税号',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '法人代表',
          prop: 'legalRepresentative',
          width: 100,
          rules: [
            {
              required: true,
              message: '请输入法人代表',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '联系人',
          prop: 'contactPerson',
          width: 100,

          rules: [
            {
              required: true,
              message: '请输入联系人',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          width: 160,
        },
        {
          label: '联系地址',
          prop: 'address',
          span: 24,
          minRows: 2,
          type: 'textarea',
          hide: true,
        },
        {
          label: '公司简介',
          prop: 'remark',
          value: '',
        
          component: 'AvueUeditor',
           action: '/api/blade-resource/attach/upload',
           options: {
             action: '/api/blade-resource/attach/upload',
             accept: 'image/png, image/jpeg, image/jpg,.mp4',
          },
           propsHttp: {
             res: 'data',
           url: 'link',
          },

          hide: true,
          minRows: 6,
          span: 24,
        },
      ],
    },
    {
      label: '报价方案导出设置',
      prop: 'exportSetting',
      column: [
        {
          label: '导出口号',
          prop: 'exportSlogan',
          search: true,
          width: 180,
          span: 24,
        },
        {
          label: '企业邮箱',
          prop: 'email',
          search: true,
          width: 180,
          span: 24,
        },

        {
          label: '企业电子章',
          prop: 'electronicSealUrl',
          type: 'upload',
          listType: 'picture-card',
          dataType: 'string',
        
          action: '/blade-resource/attach/upload',
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          accept: '.jpeg,.png,.jpg',
          limit: 1,
          uploadExceed: () => {
            ElMessage.warning('限制1张');
          },
          hide: true,
          span: 24,
          tip: '尺寸 150px  * 150px',
        },
        {
          label: '报价导出logo',
          prop: 'exportLogoUrl',
          type: 'upload',
         
          listType: 'picture-card',
          dataType: 'string',
          accept: '.jpeg,.png,.jpg',
          limit: 1,
          uploadExceed: () => {
            ElMessage.warning('限制1张');
          },
          action: '/blade-resource/attach/upload',
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          hide: true,
          span: 24,
          tip: '尺寸230px * 90px',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/company/save';
const delUrl = '/vt-admin/company/remove?ids=';
const updateUrl = '/api/vt-admin/company/update';
const tableUrl = '/api/vt-admin/company/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function handleIsDefaultChange(item) {
 
  
  axios
    .post('/api/vt-admin/company/setDefault', {
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
      onLoad();
    });
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
