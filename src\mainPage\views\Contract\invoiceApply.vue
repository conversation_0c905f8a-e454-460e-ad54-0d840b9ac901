<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
    >
      <template #menu-left>
        <el-button
          type="primary"
          icon="plus"
          @click="invoiceApply"
          v-if="!route.query.type || route.query.type == 0"
          plain
          >开票申请</el-button
        >
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">发票总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">不含税总额：</el-text>
        <el-text type="primary" size="large">￥{{ (noTaxPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">税额总额：</el-text>
        <el-text type="primary" size="large">￥{{ (taxPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">已开票总额：</el-text>
        <el-text type="primary" size="large"
          >￥{{ (hasInvoicePrice * 1).toLocaleString() }}</el-text
        >
      </template>
      <template #menu="{ row, $index }">
        <el-button type="primary" icon="view" text @click="viewDetail(row, $index)">详情</el-button>
        <el-button
          type="primary"
          v-if="row.invoiceStatus == 0 && (!route.query.type || route.query.type == 0)"
          icon="delete"
          text
          @click="$refs.crud.rowDel(row, $index)"
          >删除</el-button
        >
        <el-button
          type="primary"
          icon="Compass"
          v-if="row.invoiceStatus == 1"
          text
          @click="applyInvoice(row, $index)"
          >申请作废</el-button
        >
      </template>
      <template #customerInvoiceInfoId-form>
        <wfInvoiceDrop v-model="form.customerInvoiceInfoId" :id="props.customerId"></wfInvoiceDrop>
        <productSelect
          ref="productSelectRef"
          @select="handleConfirm"
          :sealContractId="props.sealContractId"
          :offerId="props.offerId"
        ></productSelect>
      </template>
      <template #invoiceStatus="{ row }">
        <el-tooltip
          :content="row.invoiceStatus == 3 ? row.invalidDescript : row.applyVoidReason"
          :disabled="row.invoiceStatus != 3 && row.invoiceStatus != 4"
        >
          <el-tag
            effect="plain"
            :type="
              row.invoiceStatus == 3
                ? 'danger'
                : row.invoiceStatus == 0
                ? ''
                : row.invoiceStatus == 4
                ? 'info'
                : 'success'
            "
            >{{ row.$invoiceStatus }}</el-tag
          >
        </el-tooltip>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog
      title="开票详情"
      v-model="dialogVisible"
      width="80%"
      class="avue-dialog avue-dialog--top"
    >
      <avue-form ref="dialogForm" :option="detailOption" v-model="detailForm">
        <template #contractBaseInfo>
          <!-- 公共信息区域 -->
          <div v-if="detailForm.sealContractVOList && detailForm.sealContractVOList.length > 0" class="common-info-section">
            <div class="common-info-content">
              <div class="info-header">
                <el-tag type="warning" effect="plain" size="small">{{
                  !detailForm.invoicePriceType ? '按产品开票' : '按合同额开票'
                }}</el-tag>
              </div>
              <el-row :gutter="16">
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">客户名称：</span>
                    <span class="info-value">{{ getCommonInfo('customerName') || detailForm.customerName }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">客户地址：</span>
                    <span class="info-value">{{ getCommonInfo('customerAddress') }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">联系人：</span>
                    <span class="info-value">{{ getCommonInfo('customerContact') }}</span>
                  </div>
                </el-col>
                <el-col :span="6">
                  <div class="info-item">
                    <span class="info-label">联系电话：</span>
                    <span class="info-value">{{ getCommonInfo('customerPhone') }}</span>
                  </div>
                </el-col>
              </el-row>
            </div>
          </div>

          <!-- 合同详细信息区域 -->
          <div class="contracts-section">
            <div v-for="(item, index) in detailForm.sealContractVOList" :key="item.id" class="contract-item">
              <el-card class="contract-card" shadow="hover">
                <template #header>
                  <div class="card-header">
                    <div class="header-left">
                      <span class="header-title">
                        <template v-if="detailForm.sealContractVOList.length > 1">合同 {{ index + 1 }}：</template>
                        <el-link type="primary" @click="toDetail({ id: item.id })" class="contract-link">
                          {{ item.contractName }}
                        </el-link>
                      </span>
                      <span class="collection-amount">本次开票金额：<span class="amount-value">￥{{ calculateContractCollectionAmount(item).toLocaleString() }}</span></span>
                    </div>
                    <el-tag type="primary" size="small">{{ item.contractCode || '编号待生成' }}</el-tag>
                  </div>
                </template>
                <ContractInfo
                  :detailForm="item"
                  :invoicePriceType="detailForm.invoicePriceType"
                  :key="item.id"
                  :hideCommonInfo="true"
                ></ContractInfo>
              </el-card>
            </div>
          </div>
        </template>
        <template #invoiceFiles> <File :fileList="detailForm.attachList || []"></File></template>
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
    <el-drawer v-model="drawer" size="90%" title="开票申请">
      <el-row style="height: 100%" :gutter="8">
        <el-col style="height: 100%" :span="10">
          <el-card class="box-card" shadow="never" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
              <template #customerInvoiceInfoId>
                <wfInvoiceDrop
                  v-model="form.customerInvoiceInfoId"
                  :id="form.customerId"
                ></wfInvoiceDrop>
              </template>
            </avue-form>
          </el-card>
        </el-col>

        <el-col style="height: 100%" :span="14">
          <el-card
            shadow="never"
            class="product-table-card"
            :body-style="{ padding: 0 + 'px' }"
            style="height: 100%"
          >
            <template #header>
              <div class="product-table-header">
                <div class="header-title">
                  <el-icon class="header-icon"><Grid /></el-icon>
                  <span>已选产品</span>
                </div>
                <div class="header-actions">
                  <el-button type="primary" icon="Plus" @click="addProduct" size="small">
                    添加产品
                  </el-button>
                </div>
              </div>
            </template>

            <avue-crud
              :option="selectProductOption"
              @row-del="productRowDel"
              :summary-method="getSummaries"
              :data="selectProductList"
              :key="tableKey"
            >
              <template #number="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  size="small"
                  style="width: 80%"
                  v-model="row.number"
                ></el-input-number>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary"  :loading="submitLoading" @click="$refs.addFormRef.submit()">确认 申请</el-button>
        </div>
      </template>
      <el-drawer title="选择产品" v-model="innerDrawer" size="80%" append-to-body>
        <el-row style="height: 100%" :gutter="20">
          <el-col style="height: 100%" :span="8">
            <el-card
              class="contract-selection-card"
              style="height: 100%; overflow-y: auto"
              v-infinite-scroll="loadMore"
              shadow="never"
              :infinite-scroll-disabled="contractLoading || !hasMoreContracts"
              :infinite-scroll-delay="500"
              :infinite-scroll-distance="50"
              :infinite-scroll-immediate="false"
            >
              <template #header>
                <div class="contract-selection-header">
                  <div class="header-title">
                    <el-icon class="header-icon"><FolderOpened /></el-icon>
                    <span>选择合同</span>
                  </div>
                  <div class="header-actions">
                    <div class="header-count" v-if="contractList.length > 0">
                      共 {{ contractList.length }} 个合同
                    </div>
                   
                  </div>
                </div>
              </template>

              <div class="search-container">
                <el-input
                  placeholder="请输入合同名称、订单号模糊搜索"
                  @input="handleInput"
                  v-model="applyQuery.contractName"
                  prefix-icon="Search"
                  clearable
                  class="search-input"
                ></el-input>
              </div>
              <div v-if="contractList.length === 0" class="contract-empty-state">
                <el-icon class="empty-icon"><FolderOpened /></el-icon>
                <div class="empty-text">
                  <p>暂无可用合同</p>
                  <p>请先选择客户或调整搜索条件</p>
                </div>
              </div>

              <el-row v-for="item in contractList" style="width: 100%" :key="item.id">
                <el-col :span="24">
                  <div
                    class="contract-card"
                    :class="{ 'contract-card--selected': currentContractId == item.id }"
                    @click="
                      currentContractId = item.id;
                      queryProductByContractId();
                    "
                  >
                    <div class="contract-content">
                      <div class="contract-header">
                        <div class="contract-title">
                          <el-icon class="contract-icon"><Document /></el-icon>
                          <span class="contract-name">{{ item.contractName }}</span>
                        </div>
                        <div class="contract-status" v-if="currentContractId == item.id">
                          <el-tag type="success" size="small">已选择</el-tag>
                        </div>
                      </div>
                    </div>

                    <div class="contract-details" style="padding: 10px; padding-top: 0">
                      <!-- 第一行：合同编号和业务员 -->
                      <div class="detail-row">
                        <div class="detail-item">
                          <span class="detail-label">合同编号</span>
                          <span class="detail-value">{{ item.contractCode }}</span>
                        </div>
                        <div
                          class="detail-item"
                          v-if="proxy.$store.getters.userInfo.user_id != item.businessUser"
                        >
                          <span class="detail-label">业务员</span>
                          <span class="detail-value">{{ item.businessUserName || '--' }}</span>
                        </div>
                      </div>

                      <!-- 第二行：订单号 -->
                      <div class="detail-row">
                        <div class="detail-item">
                          <span class="detail-label">订单号</span>
                          <span class="detail-value">{{ item.customerOrderNumber || '--' }}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>

              <!-- <el-empty v-if="contractList.length == 0" description="该客户没有合同"></el-empty> -->

              <!-- 加载状态指示器 -->
              <div v-if="contractLoading" class="loading-indicator">
                <el-icon class="is-loading"><Loading /></el-icon>
                <span>加载中...</span>
               
              </div>

              <!-- 没有更多数据提示 -->
              <div
                v-else-if="contractList.length > 0 && !hasMoreContracts"
                class="no-more-indicator"
              >
                <span>没有更多合同了 ({{ contractList.length }}/{{ pageContract.total }})</span>
              </div>

            </el-card>
          </el-col>
          <el-col style="height: 100%" :span="16">
            <el-card class="box-card" shadow="never" style="height: 100%">
              <avue-crud
                ref="addAvue"
                :option="productOption"
                v-model:page="pageOption"
                :table-loading="selectLoading"
                @current-change="queryProductByContractId"
                @size-change="queryProductByContractId"
                @refresh-change="queryProductByContractId"
                @selection-change="handleChange"
                :data="productList"
              >
                <template #isInvoice="{ row }">
                  <el-tag effect="plain" v-if="row.isInvoice === 1" type="success">是</el-tag>
                  <el-tag effect="plain" v-else-if="row.isInvoice === 2" type="success"
                    >部分开票</el-tag
                  >
                  <el-tag effect="plain" v-else type="danger">否</el-tag>
                </template>
              </avue-crud>
            </el-card>
          </el-col>
        </el-row>
        <template #footer>
          <div style="flex: auto">
            <el-button type="primary" @click="confirmAddProduct">确 认</el-button>
          </div>
        </template>
      </el-drawer>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfInvoiceDrop from '../Contract/customer/compoents/wf-invoice-drop.vue';
import productSelect from '../Contract/customer/compoents/productSelect.vue';
import customerSelect from '@/views/plugin/workflow/components/custom-fields/wf-customer-select/index.vue';
import { dateFormat } from '@/utils/date';
import contractBaseInfo from '@/views/Contract/customer/detail/contractBaseInfo.vue';
import { ElForm, ElFormItem, ElMessageBox, ElInput } from 'element-plus';
import { Document, FolderOpened, Grid, Plus, Loading } from '@element-plus/icons-vue';
import { debounce } from '@/utils/util';
import { planNameData } from '@/const/const';
import ContractInfo from '../Finance/invoice/contractInfo.vue';
let { proxy } = getCurrentInstance();

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  viewBtn: false,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIndex: 4,
  searchIcon:true,
  addTitle: '开票申请',
  addBtnText: '开票申请',
  menuWidth: 200,
  border: true,
  column: [
    {
      label: '关联合同',
      prop: 'contractName',
      overHidden: true,
      width: 200,
      search: true,
      //   component: 'wf-contract-select',
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      width: 200,
      search: true,
      component: 'wf-customer-drop',
    },
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'customerInvoiceInfoId',
     
    },

    {
      type: 'date',
      label: '开票日期',
      span: 12,

      display: false,
      component: 'wf-daterange-search',
      search: true,
      overHidden: true,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      span: 12,
      display: true,
      prop: 'invoicePrice',
      
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      width: 130,
      // search: true,
      display: true,

      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      label: '开票公司',
      type: 'select',
      props: {
        label: 'companyName',
        value: 'id',
      },
      width: 150,
      search: true,
      overHidden: true,
      cell: false,
      prop: 'billingCompany',
      dicUrl: '/api/vt-admin/company/page?size=100',
      dicFormatter:(res) => {
        return res.data.records
      },
      formatter: (res) => {
        return res.billingCompanyName
      },
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      readonly: true,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
      overHidden: true,
    },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          proxy.$refs.productSelectRef.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品',
            prop: 'productId',
            bind: 'product.productName',
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            bind: 'product.productSpecification',
            overHidden: true,
            // search: true,
            span: 24,
            cell: false,
            type: 'input',
          },
          {
            label: '单位',
            prop: 'unitName',
            bind: 'product.unitName',
            type: 'input',
            span: 12,
            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            span: 12,
            cell: false,
          },
          {
            label: '单价',
            prop: 'sealPrice',
            type: 'number',
            span: 12,
            cell: false,
          },
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            cell: false,
          },
        ],
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'select',
      label: '状态',
      search: true,
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票 ',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '申请作废',
          value: 4,
        },
      ],
      cascader: [],
      span: 12,
      addDisplay: false,
      editDisplay: false,
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
      prop: 'invoiceStatus',
    },
    {
      type: 'date',
      label: '计划收款时间',
      span: 12,
      addDisplay: false,
      width: 130,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
    },
  ],
});
let route = useRoute();
let form = ref({
  customerId: route.query.id || null,
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractInvoice/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractInvoice/page';
let params = ref({});
let tableData = ref([]);

onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
let hasInvoicePrice = ref(0);
let taxPrice = ref(0);
let noTaxPrice = ref(0);

let isMy = ref(route.query.type == 0 || !route.query.type);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: route.query.id || null,
        invoiceStartDate: params.value.invoiceDate && params.value.invoiceDate[0],
        invoiceEndDate: params.value.invoiceDate && params.value.invoiceDate[1],
        invoiceDate: null,
        selectType: isMy.value ? 0 : null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContractInvoice/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        customerId: route.query.id || null,
        invoiceStartDate: params.value.invoiceDate && params.value.invoiceDate[0],
        invoiceEndDate: params.value.invoiceDate && params.value.invoiceDate[1],
        invoiceDate: null,
        selectType: isMy.value ? 0 : null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      noTaxPrice.value = res.data.data.noTaxPrice;
      taxPrice.value = res.data.data.taxPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    createUser: null,
    createTime: null,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/sealContractInvoice/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm(list) {
  console.log(list);
  form.value.detailDTOList = list.map(item => {
    return {
      detailId: item.id,
      ...item,
      id: null,
    };
  });
  form.value.invoicePrice = totalAmount();
}
function totalAmount() {
  return form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0).toFixed(2);
}

let dialogVisible = ref(false);
let detailForm = ref({});
let detailOption = ref({
  detail: true,
  tabs: true,
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      label: '申请信息',
      prop: 'applyInfo',
      labelWidth: 140,
      column: [
        {
          type: 'input',
          label: '申请开票金额',
          span: 12,
          display: true,
          prop: 'invoicePrice',
        },
        {
          type: 'select',
          label: '申请开票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          label: '开票方式',
          type: 'radio',
          prop: 'invoicePriceType',
          span: 12,
          dicData: [
            {
              label: '按产品开票',
              value: 0,
            },
            {
              label: '按合同额开票',
              value: 1,
            },
          ],
        },
        {
          label: '税率',
          type: 'select',
          props: {
            label: 'dictValue',
            value: 'dictKey',
          },
          cell: false,
          prop: 'taxRate',
          dicUrl: '/blade-system/dict/dictionary?code=tax',
        },
        {
          label: '申请开票公司',
          type: 'select',
          span: 24,
          cell: false,
          prop: 'billingCompanyName',
        },
        {
          label: '申请人',
          type: 'input',
          component: 'wf-user-select',
          prop: 'createUser',
          readonly: true,
        },
        {
          type: 'date',
          label: '申请时间',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          readonly: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd HH:mm:ss'),
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'createTime',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    {
      label: '抬头信息',
      prop: 'titleInfo',
      labelWidth: 160,
      column: [
        {
          label: '客户公司名称',
          prop: 'invoiceCompanyName',
          bind: 'sealContractMakeInvoiceInfoVO.invoiceCompanyName',
          rules: [{ required: true, message: '请输入开票公司名称', trigger: 'blur' }],
        },
        {
          label: '纳税人识别号',
          prop: 'ratepayerIdentifyNumber',
          bind: 'sealContractMakeInvoiceInfoVO.ratepayerIdentifyNumber',
          rules: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
        },
        {
          label: '开户银行',
          prop: 'bankName',
          bind: 'sealContractMakeInvoiceInfoVO.bankName',
          rules: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
        },
        {
          label: '银行账号',
          prop: 'bankAccount',
          bind: 'sealContractMakeInvoiceInfoVO.bankAccount',
          rules: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
        },
        {
          label: '注册地址',
          prop: 'bankAddress',
          bind: 'sealContractMakeInvoiceInfoVO.bankAddress',
        },
        {
          label: '电话',
          prop: 'phone',
          bind: 'sealContractMakeInvoiceInfoVO.phone',
        },
        {
          label: '期初末开票余额(元)',
          prop: 'endTermNoInvoiceAmount',
          type: 'number',
          bind: 'sealContractMakeInvoiceInfoVO.endTermNoInvoiceAmount',
        },
      ],
    },
    {
      label: '发票信息',
      prop: 'invoiceInfo',
      column: [
        {
          type: 'date',
          label: '发票日期',
          span: 12,

          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'invoiceDate',
        },

        {
          type: 'input',
          label: '发票金额',
          span: 12,
          display: true,
          prop: 'invoicePrice',
        },
        {
          type: 'select',
          label: '发票类型',
          dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'invoiceType',
        },
        {
          type: 'input',
          label: '发票代码',
          span: 12,
          display: true,
          prop: 'invoiceCode',
        },
        {
          type: 'textarea',
          label: '发票号码',
          span: 24,
          display: true,
          prop: 'invoiceNumber',
        },
        {
          label: '发票附件',
          prop: 'invoiceFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    // {
    //   label: '关联产品',
    //   prop: 'productInfo',

    //   column: [
    //     {
    //       label: '',
    //       prop: 'detailVOList',
    //       type: 'dynamic',
    //       labelWidth: 0,
    //       rules: [
    //         {
    //           required: true,
    //           message: '请选择关联产品',
    //         },
    //       ],
    //       span: 24,
    //       children: {
    //         align: 'center',
    //         headerAlign: 'center',
    //         rowAdd: done => {
    //           proxy.$refs.productSelectRef.open();
    //           // done();
    //         },
    //         rowDel: (row, done) => {
    //           done();
    //         },

    //         column: [
    //           {
    //             label: '合同名称',
    //             prop: 'contractName',
    //             overHidden: true,
    //             cell: false,
    //           },
    //           {
    //             label: '产品',
    //             prop: 'productId',

    //             formatter: row => {
    //               return row.customProductName || row.productVO?.productName;
    //             },
    //             cell: false,
    //           },
    //           {
    //             label: '规格型号',
    //             prop: 'productSpecification',

    //             formatter: row => {
    //               return row.customProductSpecification || row.productVO?.productSpecification;
    //             },
    //             overHidden: true,
    //             // search: true,
    //             span: 24,
    //             cell: false,
    //             type: 'input',
    //           },
    //           {
    //             label: '数量',
    //             prop: 'number',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '单位',
    //             prop: 'unitName',
    //             bind: 'productVO.unitName',
    //             type: 'input',
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '单价',
    //             prop: 'zhhsdj',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //           },
    //           {
    //             label: '金额',
    //             prop: 'totalPrice',
    //             type: 'number',
    //             span: 12,
    //             cell: false,
    //           },
    //         ],
    //       },
    //     },
    //   ],
    // },
    {
      label: '快递信息',
      prop: 'courieInfo',
      column: [
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict/dictionary?code=courierCompany',
          cascader: [],
          span: 24,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择快递公司',
              trigger: 'blur',
            },
          ],
          prop: 'courierCompany',
        },
        {
          label: '快递单号',
          prop: 'courierNumber',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入快递单号',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    {
      label: '合同信息',
      prop: 'contract',
      column: [
        {
          type: 'select',
          label: '',
          span: 24,
          labelWidth: 0,
          prop: 'contractBaseInfo',
        },
      ],
    },
  ],
});
function viewDetail(row) {
  axios.get('/api/vt-admin/sealContractInvoice/detail?id=' + row.id).then(res => {
    dialogVisible.value = true;
    detailForm.value = res.data.data;
    getDetail();
  });
}

// 获取公共信息的方法
function getCommonInfo(field) {
  if (!detailForm.value.sealContractVOList || detailForm.value.sealContractVOList.length === 0) {
    return '';
  }

  // 获取第一个合同的信息作为基准
  const firstContract = detailForm.value.sealContractVOList[0];
  const value = firstContract[field];

  // 检查是否所有合同都有相同的值
  const allSame = detailForm.value.sealContractVOList.every(contract => contract[field] === value);

 return value
}

// 计算合同本次开票金额（产品列表金额相加）
function calculateContractCollectionAmount(contract) {
  if (!contract.invoiceDetailVOList || contract.invoiceDetailVOList.length === 0) {
    return 0;
  }

  return contract.invoiceDetailVOList.reduce((total, product) => {
    const amount = parseFloat(product.totalPrice) || 0;
    return total + amount;
  }, 0);
}

// 跳转到合同详情页面
function toDetail(row) {
  if (row.id.split(',').length > 1) {
    return;
  }

  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      delBtn: 1,
    },
  });
}
function invoice(row, type) {
  proxy.$refs.dialogForm.show({
    title: '开票',
    option: {
      column: [
        {
          type: 'date',
          label: '开票日期',
          span: 24,
          value: row.invoiceDate || dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'invoiceDate',
        },
        {
          type: 'input',
          label: '发票代码',
          span: 24,
          display: true,
          value: row.invoiceCode,
          prop: 'invoiceCode',
          // rules: [
          //   {
          //     required: true,
          //     message: '请输入发票代码',
          //     trigger: 'blur',
          //   },
          // ],
        },
        {
          type: 'textarea',
          label: '发票号码',
          placeholder: '多个号码以逗号隔开',
          span: 24,
          display: true,
          value: row.invoiceNumber,
          rules: [
            {
              required: true,
              message: '请输入发票号码',
              trigger: 'blur',
            },
          ],
          prop: 'invoiceNumber',
        },
        {
          label: '上传发票',
          prop: 'invoiceFiles',
          type: 'upload',
          overHidden: true,
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          value:
            row.attachList &&
            row.attachList.map(item => {
              return {
                value: item.id,
                label: item.originalName,
              };
            }),
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      let url = '';
      if (type) {
        url = '/api/vt-admin/sealContractInvoice/update';
      } else {
        url = '/api/vt-admin/sealContractInvoice/invoice';
      }
      const data = {
        id: row.id,
        ...res.data,
        invoiceFiles: res.data.invoiceFiles.map(item => item.value).join(','),
      };
      axios.post(url, data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function send(row) {
  proxy.$refs.dialogForm.show({
    title: '邮寄',
    option: {
      column: [
        {
          type: 'select',
          label: '快递公司',
          dicUrl: '/blade-system/dict/dictionary?code=courierCompany',
          cascader: [],
          span: 24,
          // search: true,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择快递公司',
              trigger: 'blur',
            },
          ],
          prop: 'courierCompany',
        },
        {
          label: '快递单号',
          prop: 'courierNumber',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入快递单号',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractInvoice/mail', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let contractForm = ref({});
let result = ref([]);
function getDetail() {
  result.value = [];
  detailForm.value.sealContractId.split(',').map((item, index) => {
    axios
      .get('/api/vt-admin/sealContract/detail', {
        params: {
          id: item,
        },
      })
      .then(res => {
        // form.value = formatData(res.data.data)
        result.value[index] = {
          ...res.data.data,
          distributionMethod: '' + res.data.data.distributionMethod,
        };
      });
  });
}
function voidInvoice(row) {
  const invalidDescript = ref('');
  ElMessageBox({
    title: '提示',
    cancelButtonText: 'Cancel',
    message: () => [
      h(
        ElForm,
        {
          labelPosition: 'top',
        },

        h(
          ElFormItem,
          { label: '作废原因：' },
          h(ElInput, {
            modelValue: invalidDescript.value,
            type: 'textarea',
            placeholder: '请输入作废原因',
            'onUpdate:modelValue': val => {
              invalidDescript.value = val;
            },
          })
        )
      ),
    ],
  }).then(res => {
    axios
      .post('/api/vt-admin/sealContractInvoice/voidInvoice', {
        id: row.id,
        invalidDescript: invalidDescript.value,
      })
      .then(res => {
        proxy.$message.success('操作成功');

        onLoad();
      });
  });
}
//   开票申请
let drawer = ref(false);
let innerDrawer = ref(false);
let applyQuery = ref({});
let pageOption = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0,
});
let selectProductList = ref([]);
let tableKey = ref(0); // 用于强制表格重新渲染
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '客户名称',
      prop: 'customerId',
      overHidden: true,
      span: 24,
      change: val => {
        contractList.value = [];
        pageContract.value.current = 1;
      },
      component: 'wf-customer-select',
      params: {
        Url: '/vt-admin/customer/page?type=6',
      },
    },
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'customerInvoiceInfoId',
      
    },
    {
      label: '计划名称',
      prop: 'planName',
      span: 24,
      type: 'radio',
      dicData: planNameData,

      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
     // 收款时间
    {
      label: '收款时间',
      type: 'date',
      label: '收款时间',
      span: 24,
     
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      type: 'date',
      label: '开票日期',
      span: 24,
      width: 100,
      display: false,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      span: 24,
      display: true,
      prop: 'invoicePrice',
      readonly: true,
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 24,
      width: 130,
      // search: true,
      value: '',
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [{ required: true, message: '请选择开票类型', trigger: 'blur' }],
    },
    {
      label: '税率',
      type: 'select',
      span: 24,
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      // change:({value}) => {
      //   totalAmount(value);
      // },
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      rules: [
        {
          required: true,
          message: '请选择税率',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '开票公司',
      type: 'select',
      span: 24,
      cell: false,
      overHidden: true,
      prop: 'billingCompany',
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },

      overHidden: true,
      cell: false,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      width: 80,
      span: 24,
      readonly: true,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 24,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      width: 100,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
    },
     {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
  ],
});
let productOption = ref({
  header: true,
  menu: false,
  addBtn: false,
  height: 'auto',
  calcHeight: '20',
  selection: true,
  reserveSelection: true,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '已开票数量',
      prop: 'invoiceNumber',
      type: 'number',
      span: 110,
      span: 12,
      cell: false,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
    },
    {
      label: '单价',
      prop: 'zhhsdj',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'zhhsze',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '是否开票',
      prop: 'isInvoice',
    },
  ],
});
let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  delBtn: true,
  addBtn: false,
  header: false,
  height: 'calc(100vh - 300px)', // 动态计算卡片body的高度，减去头部、边距等
  selection: false,
  showSummary: true, // 显示统计行
  menuWidth: 120,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 150,
      span: 12,
      cell: true,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
    },
    {
      label: '单价',
      prop: 'zhhsdj',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'zhhsze',
      type: 'number',
      span: 12,
      cell: false,
    },
  ],
});
let contractList = ref([]);
let currentContractId = ref('');
// 查询客户合同
function queryContract(id) {
  console.log('queryContract 执行了, current:', pageContract.value.current, 'customerId:', form.value.customerId);

  // 如果没有客户ID，直接返回
  if (!form.value.customerId) {
    console.log('没有客户ID，跳过查询');
    contractLoading.value = false;
    hasMoreContracts.value = false;
    contractList.value = [];
    return;
  }

  // 如果正在加载，直接返回
  if (contractLoading.value) {
    return;
  }

  contractLoading.value = true;

  axios
    .get('/api/vt-admin/sealContract/pageForInvoice', {
      params: {
        customerId: form.value.customerId,
        ...applyQuery.value,
        ...pageContract.value,
        selectType: 5,
      },
    })
    .then(res => {
      const { records, total, current, size } = res.data.data;

      // 如果是第一页，重置列表
      if (pageContract.value.current === 1) {
        contractList.value = records;
      } else {
        // 追加数据
        contractList.value = [...contractList.value, ...records];
      }

      // 更新总数和分页信息
      pageContract.value.total = total;

      // 检查是否还有更多数据
      hasMoreContracts.value = contractList.value.length < total;

      console.log('数据加载完成:', {
        current: pageContract.value.current,
        total: total,
        loaded: contractList.value.length,
        hasMore: hasMoreContracts.value
      });
    })
    .catch(error => {
      console.error('加载合同数据失败:', error);
    })
    .finally(() => {
      contractLoading.value = false;
    });
}
const handleInput = debounce(() => {
  console.log('搜索输入变化，重置数据');
  contractList.value = [];
  pageContract.value.current = 1;
  pageContract.value.total = 0;
  hasMoreContracts.value = true;
  contractLoading.value = false;
  queryContract();
}, 500);

// 重置加载状态的函数（调试用）
function resetLoadingState() {
  console.log('手动重置加载状态');
  contractLoading.value = false;
  hasMoreContracts.value = true;
}

function invoiceApply() {
  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();
  });
}
function addProduct() {
  if (!form.value.customerId) {
    proxy.$message.error('请先选择客户');
    return;
  }

  // 重置所有状态
  pageContract.value.current = 1;
  pageContract.value.total = 0;
  contractList.value = [];
  contractLoading.value = false;
  hasMoreContracts.value = true;

  // 打开抽屉
  innerDrawer.value = true;

  // 只加载第一页数据，不自动加载所有数据
  proxy.$nextTick(() => {
    queryContract();
  });
}
function handleCheckChange(value) {
  currentContractId.value = value;
  queryProductByContractId();
}
let productList = ref([]);
let selectLoading = ref(false);
function queryProductByContractId() {
  selectLoading.value = true;
  axios
    .get('/api/vt-admin/sealContract/productPageForMergeInvoice', {
      params: {
        id: currentContractId.value,
        customerId: form.value.customerId,
        current: pageOption.value.currentPage,
        size: pageOption.value.pageSize,
      },
    })
    .then(res => {
      productList.value = res.data.data.records;
      pageOption.value.total = res.data.data.total;
      selectLoading.value = false;
    });
}
let selectList = ref([]);
function handleChange(list) {
  selectList.value = list.map(i => i);
}
let sealContractId = ref([]);
function confirmAddProduct() {
  selectProductList.value.push(
    ...selectList.value.map(i => {
      return {
        ...i,
        number: i.number * 1 - i.invoiceNumber * 1,
        product: {
          ...i.product,
        },
      };
    })
  );
  //   selectList.value = [];
  proxy.$refs.addAvue.toggleSelection();
  innerDrawer.value = false;
  setTotalAmount();
  // 强制表格重新渲染
  tableKey.value++;

  // 获取第一个产品所属合同的开票信息
  const sealContractId =  selectProductList.value[0]?.sealContractId;
  debugger
  axios.get('/api/vt-admin/sealContract/detail',{
    params:{
      id:sealContractId
    }
  }).then(res => {
    const {billingCompany,taxRate,invoiceType} = res.data.data;
    form.value.billingCompany = billingCompany;
    form.value.taxRate = taxRate? '' + taxRate : '';
    form.value.invoiceType = invoiceType;
  })
}
function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      setTotalAmount();
      // 强制表格重新渲染
      tableKey.value++;
    })
    .catch(() => {});
}
function setTotalAmount() {
  const totalPrice = selectProductList.value.reduce(
    (total, item) => (total += item.number * 1 * (item.zhhsdj * 1)),
    0
  );
  form.value.invoicePrice = totalPrice;

  // 强制表格重新渲染以更新统计行
  tableKey.value++;
}

// 自定义统计方法
function getSummaries(param) {
  const { columns, data } = param;
  const sums = [];

  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }

    if (column.property === 'number') {
      // 统计数量总和
      const totalNumber = data.reduce((sum, item) => {
        const value = Number(item.number) || 0;
        return sum + value;
      }, 0);
      sums[index] = totalNumber.toFixed(2);
    } else if (column.property === 'zhhsze') {
      // 统计金额总和（数量 × 单价）
      const totalAmount = data.reduce((sum, item) => {
        const number = Number(item.number) || 0;
        const price = Number(item.zhhsdj) || 0;
        return sum + number * price;
      }, 0);
      sums[index] = `￥${totalAmount.toFixed(2)}`;
    } else if (column.property === 'customProductName') {
      // 产品名称列显示总计数量
      sums[index] = `共 ${data.length} 项`;
    } else {
      sums[index] = '--';
    }
  });

  return sums;
}
let submitLoading = ref(false)
function submit(form, done, loading) {
  if (selectProductList.value.length == 0) {
    proxy.$message.error('请选择产品');
    done();
    return;
  }
  submitLoading.value = true;
  form.detailDTOList = selectProductList.value.map(item => {
    return {
      detailId: item.id,
      ...item,
      id: null,
    };
  });
  form.sealContractId = selectProductList.value
    .reduce((pre, cur) => {
      if (pre.includes(cur.sealContractId)) return pre;
      return [...pre, cur.sealContractId];
    }, [])
    .join(',');
  form.createTime = null;
  axios.post('/api/vt-admin/sealContractInvoice/save', form).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      onLoad();
      drawer.value = false;
      clearAll();
      submitLoading.value = false;
    }
  }).catch(err => {
    submitLoading.value = false
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();
  contractList.value = [];
  selectProductList.value = [];
  form.value.createName = proxy.$store.getters.userInfo.nick_name;
  form.value.createTime = dateFormat(new Date(), 'yyyy-MM-dd');
  pageOption.value.currentPage = 1;
  pageContract.value.current = 1;
  pageContract.value.total = 0;
  contractLoading.value = false;
  hasMoreContracts.value = true;
}
let pageContract = ref({
  current: 1,
  size: 10,
  total: 0, // 添加总数量
});

// 添加加载状态
let contractLoading = ref(false);
let hasMoreContracts = ref(true); // 是否还有更多数据

function loadMore() {
  console.log(
    'loadMore triggered, loading:',
    contractLoading.value,
    'hasMore:',
    hasMoreContracts.value,
    'current:',
    pageContract.value.current,
    'total:',
    pageContract.value.total
  );

  // 防止重复加载
  if (contractLoading.value || !hasMoreContracts.value) {
    console.log('跳过加载 - loading:', contractLoading.value, 'hasMore:', hasMoreContracts.value);
    return;
  }

  console.log('开始加载下一页');
  // 不在这里设置 loading 状态，让 queryContract 函数自己管理
  pageContract.value.current += 1;
  queryContract();
}
// 申请作废
function applyInvoice(row) {
  proxy.$refs.dialogForm.show({
    title: '作废申请',
    option: {
      column: [
        {
          label: '作废理由',
          prop: 'applyVoidReason',
          span: 24,
          type: 'textarea',
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        invoiceStatus: 4,
        applyVoidReason: res.data.applyVoidReason,
      };
      axios.post('/api/vt-admin/sealContractInvoice/applyInvoice', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
</script>

<style lang="scss" scoped>
// 统计行样式
:deep(.el-table__footer-wrapper) {
  flex-shrink: 0; // 确保统计行不会被压缩

  .el-table__footer {
    .el-table__cell {
      background-color: #f5f7fa !important;
      font-weight: bold;
      color: #303133;
      border-top: 2px solid #409eff;
    }

    // 金额列特殊样式
    .el-table__cell:last-child {
      color: #e6a23c;
      font-size: 16px;
    }
  }
}

// 产品表格卡片样式
.product-table-card {
  display: flex;
  flex-direction: column;
  height: 100%;

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    flex-shrink: 0;
  }

  :deep(.el-card__body) {
    padding: 5px;
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
}

.product-table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

// // 表格整体样式优化
// :deep(.avue-crud) {
//   height: 100%;
//   display: flex;
//   flex-direction: column;

//   .el-table {
//     border: 1px solid #ebeef5;
//     border-radius: 8px;
//     overflow: hidden;
//     box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
//     flex: 1;

//     .el-table__header {
//       th {
//         background: linear-gradient(135deg, #fafbfc 0%, #f5f7fa 100%);
//         color: #606266;
//         font-weight: 600;
//         border-bottom: 2px solid #e4e7ed;

//         &:first-child {
//           border-top-left-radius: 8px;
//         }

//         &:last-child {
//           border-top-right-radius: 8px;
//         }
//       }
//     }

//     .el-table__body-wrapper {
//       flex: 1;
//       overflow-y: auto;
//     }

//     .el-table__body {
//       tr {
//         transition: all 0.3s ease;

//         &:hover {
//           background-color: #f8fafc;
//         }
//       }

//       td {
//         border-bottom: 1px solid #f0f0f0;

//         &:first-child {
//           border-left: none;
//         }

//         &:last-child {
//           border-right: none;
//         }
//       }
//     }
//   }

//   // 菜单列样式优化
//   .avue-crud__menu {
//     text-align: center;

//     .el-button {
//       margin: 0 2px;

//       &.el-button--text {
//         padding: 4px 8px;
//         font-size: 12px;
//         border-radius: 4px;
//         transition: all 0.3s ease;

//         &:hover {
//           background-color: #f5f7fa;
//         }

//         &.el-button--danger {
//           color: #f56c6c;

//           &:hover {
//             background-color: #fef0f0;
//             color: #f56c6c;
//           }
//         }
//       }
//     }
//   }
// }

// 合同卡片样式
.contract-card {
  position: relative;
  margin-bottom: 12px;
  border: 2px solid #e4e7ed;
  border-radius: 12px;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: visible; // 改为 visible 确保内容不被裁剪
  min-height: 120px; // 设置最小高度确保内容显示完整

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #409eff 0%, #67c23a 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    border-color: #409eff;
    box-shadow: 0 8px 25px rgba(64, 158, 255, 0.15);
    transform: translateY(-2px);

    &::before {
      opacity: 1;
    }
  }

  &--selected {
    border-color: #409eff;
    background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
    box-shadow: 0 4px 20px rgba(64, 158, 255, 0.2);

    &::before {
      opacity: 1;
    }
  }
}

.contract-radio {
  width: 100%;
  margin: 0;

  :deep(.el-radio__input) {
    display: none;
  }

  :deep(.el-radio__label) {
    padding: 0;
    width: 100%;
  }
}

.contract-content {
  padding: 10px;
  // height: 200px;
  width: 100%;
}

.contract-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // margin-bottom: 16px;
}

.contract-title {
  display: flex;
  align-items: center;
  gap: 12px;
}

.contract-icon {
  font-size: 20px;
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  padding: 8px;
  border-radius: 8px;
}

.contract-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  line-height: 1.4;
}

.contract-status {
  flex-shrink: 0;
}

.contract-details {
  display: flex;
  flex-direction: column;
  gap: 6px;
  width: 100%;
  justify-content: flex-start;
}

.detail-row {
  display: flex;
  gap: 16px;
  align-items: center;
  min-height: 28px;
  width: 100%;
}

.detail-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
  flex: 1;
  min-width: 0; // 防止内容溢出
  padding: 2px 0;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  min-width: 70px;
  flex-shrink: 0;
  position: relative;

  &::after {
    content: ':';
    margin-left: 2px;
    color: #c0c4cc;
  }
}

.detail-value {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
  word-break: break-all;
  flex: 1;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 合同选择区域样式
.contract-selection-card {
  display: flex;
  flex-direction: column;
  height: 100%;

  :deep(.el-card__header) {
    padding: 16px 20px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);
    flex-shrink: 0;
  }

  :deep(.el-card__body) {
    padding: 20px;
    flex: 1;
    // overflow-y: auto;
    // max-height: calc(100vh - 200px); // 确保不会超出视口高度
  }
}

.contract-selection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.header-icon {
  font-size: 18px;
  color: #409eff;
}

.header-count {
  font-size: 12px;
  color: #909399;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.search-container {
  margin-bottom: 20px;
}

.search-input {
  :deep(.el-input__wrapper) {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.is-focus {
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }
  }
}

// 空状态样式
.contract-empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;

  .empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
  }

  .empty-text {
    font-size: 14px;
    line-height: 1.6;
  }
}

// 加载指示器样式
.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  color: #909399;
  font-size: 14px;
  gap: 8px;

  .el-icon {
    font-size: 16px;
  }
}

// 没有更多数据提示样式
.no-more-indicator {
  text-align: center;
  padding: 16px;
  color: #c0c4cc;
  font-size: 12px;
  border-top: 1px solid #f0f0f0;
  margin-top: 8px;
}

// 响应式设计
@media (max-width: 768px) {
  .contract-content {
    padding: 16px;
  }

  .contract-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }

  .contract-selection-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .search-container {
    margin-bottom: 16px;
  }

  .detail-row {
    flex-direction: column;
    gap: 8px;
  }

  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .detail-label {
    min-width: auto;
  }

  .detail-value {
    white-space: normal;
  }
}

// 抽屉内容样式优化
:deep(.el-drawer) {
  .el-drawer__header {
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    background: linear-gradient(135deg, #f8fafc 0%, #ffffff 100%);

    .el-drawer__title {
      font-size: 18px;
      font-weight: 600;
      color: #303133;
    }
  }

  .el-drawer__body {
    padding: 20px;
    background-color: #fafbfc;
  }
}

// 整体页面样式
.main-content-row {
  .el-col {
    &:first-child {
      padding-right: 10px;
    }

    &:last-child {
      padding-left: 10px;
    }
  }
}

// 卡片通用样式
.el-card {
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

// 明细对话框样式
.common-info-section {
  margin-bottom: 16px;
}

.common-info-content {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 12px 16px;

  .info-header {
    margin-bottom: 8px;
    text-align: right;
  }

  .info-item {
    display: flex;
    align-items: center;
    margin-bottom: 0;
    min-height: 28px;

    .info-label {
      font-weight: 500;
      color: #495057;
      font-size: 13px;
      min-width: 70px;
      flex-shrink: 0;
    }

    .info-value {
      color: #212529;
      flex: 1;
      word-break: break-all;
      font-size: 13px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.contracts-section {
  .contract-item {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .contract-card {
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;
      }

      .header-title {
        font-size: 15px;
        font-weight: 600;
        color: #303133;

        .contract-link {
          font-size: 15px;
          font-weight: 600;
          text-decoration: none;

          &:hover {
            text-decoration: underline;
          }
        }
      }

      .collection-amount {
        font-size: 13px;
        color: #606266;

        .amount-value {
          font-weight: 600;
          color: #e6a23c;
          font-size: 14px;
        }
      }
    }
  }
}
</style>
