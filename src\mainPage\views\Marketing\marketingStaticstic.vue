<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      @expand-change="expandChanges"
    >
      <template #menu="{ row }">
        <el-button type="primary" text icon="user" @click="addUser(row)">添加人员</el-button>
      </template>
      <template #expand="{ row }">
        <div style="padding: 5px">
          <avue-crud
            style="width: 100%; box-sizing: border-box"
            :data="row.userVOList || []"
            :option="userOption"
            @rowDel="deleteUser"
          >
            <template #menu="{ row }">
              <el-button
                type="primary"
                text
                icon="user"
                v-if="row.isLeader == 0"
                @click="setLeader(row)"
                >设为负责人</el-button
              >
            </template>
          </avue-crud>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wfUserSelect
      ref="wfUserSelectRef"
      :checkType="addType == 0 ? 'checkbox' : 'radio'"
      @onConfirm="handleConfirm"
    ></wfUserSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
let wfUserSelectRef = ref(null);
let addType = ref(0);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  expand: true,
  menu: true,
  delBtn: true,
  viewBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  labelWidth: 150,
  tip: true,
  searchLabelWidth: 150,
  column: [
    {
      label: '营销体名字',
      prop: 'name',
      search: true,
      span: 24,
    },
    {
      label: '人员配置',
      prop: 'userEntityList',
      type: 'dynamic',
      editDisplay: false,
      span: 24,
      formatter: (row, column, cellValue) => {
        return row.userName;
      },
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
            addType.value = 0
          wfUserSelectRef.value.visible = true;
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '姓名',
            prop: 'userId',
            component: 'wf-user-select',
          },
          {
            label: '是否负责人',
            prop: 'isLeader',
            type: 'switch',
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
            change: val => {
              console.log(val);

              if (val.value == 1) {
                form.value.userEntityList.forEach(item => {
                  if (item.isLeader == 1 && val.row.userId != item.userId) {
                    item.isLeader = 0;
                  }
                });
              }
            },
          },
        ],
      },
    },
    {
      label: '经营收入',
      prop: 'contractTotalPrice',
      addDisplay: false,
      editDisplay: false,
      width:150,
      formatter: val => {
        return (val.contractTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '其他收入',
      prop: 'otherTotalPrice',
      addDisplay: false,
      editDisplay: false,
      width:150,
      formatter: val => {
        return isNaN((val.otherTotalPrice * 1).toLocaleString())
          ? 0
          : (val.otherTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '总成本',
      prop: 'costTotalPrice',
      addDisplay: false,
      editDisplay: false,  width:150,
      formatter: val => {
        return (val.costTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '总费用',
      prop: 'expenseTotalPrice',
      addDisplay: false,  width:150,
      editDisplay: false,
      formatter: val => {
        return (val.expenseTotalPrice * 1).toLocaleString();
      },
    },

    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
  ],
});
let userOption = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  expandRowKeys: [],
  delBtn: true,
  viewBtn: false,

  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  border: true,
  labelWidth: 150,
  tip: true,
  searchLabelWidth: 150,
  menu: true,
  header: false,
  column: [
    {
      label: '名字',
      prop: 'userName',
      span: 24,
    },

    {
      label: '经营收入',
      prop: 'contractTotalPrice',
      formatter: val => {
        return (val.contractTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '其他收入',
      prop: 'otherTotalPrice',
      formatter: val => {
        return isNaN((val.otherTotalPrice * 1).toLocaleString())
          ? 0
          : (val.otherTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '总成本',
      prop: 'costTotalPrice',
      formatter: val => {
        return (val.costTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '总费用',
      prop: 'expenseTotalPrice',
      formatter: val => {
        return (val.expenseTotalPrice * 1).toLocaleString();
      },
    },
    {
      label: '是否负责人',
      prop: 'isLeader',
      type: 'switch',
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      change: val => {
        console.log(val);

        if (val.value == 1) {
          form.value.userEntityList.forEach(item => {
            if (item.isLeader == 1 && val.row.userId != item.userId) {
              item.isLeader = 0;
            }
          });
        }
      },
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/marketPoint/save';
const delUrl = '/api/vt-admin/marketPoint/remove?ids=';
const updateUrl = '/api/vt-admin/marketPoint/update';
const tableUrl = '/api/vt-admin/marketPoint/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}

function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'edit' || type == 'view') {
    form.value.userEntityList = form.value.userVOList.map(item => {
      return {
        ...item,
      };
    });
  }
  done();
}
function handleConfirm(ids) {
  if (addType.value == 0) {
    form.value.userEntityList = ids.split(',').map(item => {
      return { userId: item };
    });
  } else {
    axios
      .post('/api/vt-admin/marketPointUser/save', {
        marketPointId: currentId.value,
        userId: ids,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        refreshUserTableData();
      });
  }
}
function expandChanges(row, expandList) {
    currentId.value = row.id;
    currentRow.value = row
  axios
    .get('/api/vt-admin/marketPoint/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      row.userVOList = res.data.data.userVOList;
      if (expandList.length) {
        option.value.expandRowKeys = [];
        if (row) {
          option.value.expandRowKeys.push(row.id);
        }
      } else {
        option.value.expandRowKeys = [];
      }
    });
}
let userTableData = ref([]);
let currentId = ref('');
let currentRow = ref()
let drawer = ref(false);

function addUser(row) {
  addType.value = 1;
  currentId.value = row.id;
  currentRow.value = row
  wfUserSelectRef.value.visible = true;
}
function refreshUserTableData() {
  axios
    .get('/api/vt-admin/marketPoint/detail', {
      params: {
        id: currentId.value,
      },
    })
    .then(res => {
        
        currentRow.value.userVOList = res.data.data.userVOList;
    });
}
function deleteUser(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/marketPointUser/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        refreshUserTableData();
      });
    })
    .catch(() => {});
}
function setLeader(row) {
  
  proxy
    .$confirm('确定将该用户设置为负责人吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/marketPointUser/settingLeader', {
          id: row.id,
        })
        .then(res => {
          proxy.$message({
            type: 'success',
            message: '操作成功',
          });
          refreshUserTableData();
        });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>
