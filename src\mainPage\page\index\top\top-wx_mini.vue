<template>
  <el-popover placement="bottom" width="250" trigger="click" @show="getWxMiniUrl">
    <template #reference>
      <div>
        <el-badge is-dot>
          <i class="element-icons el-icon-xiaochengxu"></i>
        </el-badge>
      </div>
    </template>
   <div v-loading="loading"> <el-alert type="success" :closable="false">
      扫码自动登录小程序({{ type == 0 ? '内部' : '对外' }})
    </el-alert>
    <div style="display: flex; justify-content: center">
      <img style="height: 250px; width: 250px" :src="wxMiniUrl" alt="" />
    </div></div>
  </el-popover>
</template>
<script>
export default {
  name: 'top-wxMini',
  components: {},
  mixins: [],
  props: {
    
    type: String, // 0 对内 1 对外
  },
  data() {
    return {
      wxMiniUrl: '',
      loading:false,
    };
  },
  computed: {},
  watch: {},
  mounted() {
    console.log(111);
  },
  methods: {
    getWxMiniUrl() {
      this.loading = true
      axios
        .get(
          `/api/blade-system/wechat_mini/${this.type == 0 ? 'getMiniQrUrl' : 'getPortalMiniQrUrl'}`,
          {
            responseType: 'blob',
          }
        )
        .then(res => {
          // 假设你已经有了一个Blob对象，例如通过Ajax请求获取的二进制数据
          var blob = new Blob([res.data], { type: 'image/png' }); // 这里的data是你的Blob数据

          // 创建一个FileReader实例
          var reader = new FileReader();

          // 当FileReader加载完成之后，转换的Base64版图片将在result属性中
          reader.onload = event => {
            var base64Image = event.target.result;
            console.log(base64Image); // 这里是图片的Base64编码
            this.wxMiniUrl = base64Image;
            this.loading = false
          };

          // 使用FileReader的readAsDataURL方法读取Blob并转换成Base64
          reader.readAsDataURL(blob);

        });
    },
  },
};
</script>
<style lang="scss" scoped></style>
