@use "element-plus/theme-chalk/src/common/var.scss" as *;
.login-container {
  display: flex;
  align-items: center;
  justify-content: right;
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0 auto;
  background: -webkit-linear-gradient(white, $color-primary); /* Safari 5.1 - 6.0 */ 

  background: -o-linear-gradient(white, $color-primary); /* Opera 11.1 - 12.0 */ 

 background: -moz-linear-gradient(white, $color-primary); /* Firefox 3.6 - 15 */

 background: linear-gradient(white, $color-primary) ; 
 background: rgba(0, 0, 0, 0.3);
  
  // background-repeat: repeat;
  background-size: 100vw 100vh;
  // background-position: 1% 1%;
}
.logo{
  position: absolute;
  left: 20px;
  top: 20px;
  height: 50px;
  z-index: 10;
}
.login-weaper {
  text-align: right;
  // margin: 0 auto;
  width: 500px;
  margin-right: 50px;
  box-shadow: -4px 5px 10px rgba(0, 0, 0, 0.1);
  z-index: 11;
  .el-input-group__append {
    border: none;
  }
}

.login-left,
.login-border {
  position: relative;
  min-height: 47vh;
  background: rgba(22,46,136,0.6);
  align-items: center;
  display: flex;
}

.login-left {
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  justify-content: center;
  flex-direction: column;
  background-color: $color-primary;
  color: #fff;
  float: left;
  width: 100%;
  position: relative;
}

.login-left .img {
  width: 140px;
}

.login-time {
  position: absolute;
  left: 25px;
  top: 25px;
  width: 100%;
  color: #fff;
  font-weight: 200;
  opacity: 0.9;
  font-size: 18px;
  overflow: hidden;
}

.login-left .title {
  text-align: center;
  color: #fff;
  font-weight: bold;
  font-size: 30px;
  letter-spacing: 2px;
}

.login-border {
  border-left: none;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;
  color: #fff;
//   background: -webkit-linear-gradient(white, $color-primary); /* Safari 5.1 - 6.0 */ 

//   background: -o-linear-gradient(white, $color-primary); /* Opera 11.1 - 12.0 */ 

//  background: -moz-linear-gradient(white, $color-primary); /* Firefox 3.6 - 15 */

//  background: linear-gradient(white, $color-primary); 


  width: 100%;
  float: left;
  box-sizing: border-box;
}
.avue-logo_title{
  margin-left: 20px;
  text-align: left;
}
.login-main {
  margin: 0 auto;
  width: 65%;
  box-sizing: border-box;
}

.login-main > h3 {
  margin-bottom: 20px;
}

.login-main > p {
  color: #76838f;
}

.login-title {
  color: #fff;
  margin-bottom: 40px;
  font-weight: 500;
  font-size: 22px;
  text-align: center;
  letter-spacing: 4px;
}

.login-menu {
  margin-top: 40px;
  width: 100%;
  text-align: center;

  a {
    color: #fff;
    font-size: 12px;
    margin: 0px 8px;
  }
}

.login-submit {
  width: 100%;
  height: 45px;
  border: 1px solid #fff;
  background: #fff;
  font-size: 18px;
  letter-spacing: 2px;
  font-weight: 300;
  color: $color-primary;
  cursor: pointer;
  margin-top: 30px;
  font-family: "neo";
  transition: 0.25s;
}

// .login-form {
//   margin: 10px 0;

//   i {
//     color: #333;
//   }

//   .el-form-item__content {
//     width: 100%;
//   }

//   .el-form-item {
//     margin-bottom: 12px;
//   }

//   .el-input {
//     .el-input__wrapper {
//       padding-bottom: 10px;
//       text-indent: 5px;
//       background: transparent;
//       box-shadow: none;
//       border-radius: 0;
//       color: #fff;
//       border-bottom: 1px solid rgb(235, 237, 242);
//     }

//     .el-input__prefix {
//       i {
//         padding: 0 5px;
//         font-size: 16px !important;
//       }
//     }
//   }
// }

.login-code {
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 0 0 0 10px;
}

.login-code-img {
  margin-top: 2px;
  width: 100px;
  height: 38px;
  background-color: #fdfdfd;
  border: 1px solid #f0f0f0;
  color: #333;
  font-size: 14px;
  font-weight: bold;
  letter-spacing: 5px;
  line-height: 38px;
  text-indent: 5px;
  text-align: center;
  cursor: pointer !important;
}
