<template>
  <div style="margin-bottom: 10px">
    <el-card :body-style="{paddingTop:'5px'}">
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>采购总额统计</div>
          <div style="display: flex; gap: 20px">
            <el-date-picker
              v-model="query.year"
              :clearable="false"
              value-format="YYYY"
              @change="getDetail"
              clearable
              type="year"
              placeholder=""
            ></el-date-picker>
            <el-radio-group v-model="echartsType" @change="handleTypeChange">
              <el-radio-button value="按时间" label="按时间" />
              <el-radio-button value="按排名" label="按排名" />
            </el-radio-group>
          </div>
        </div>
      </template>
      <!-- 图表 -->
      <div v-if="echartsType == '按时间'" style="height: 400px">
        <div ref="chartRef" style="height: 100%"></div>
      </div>
      <div v-else v-loading="loading">
        <div v-if="form.selectType == 1" style="display: flex; justify-content: right; margin-bottom: 5px">
          <el-radio-group size="mini" v-model="searchType" @change="handlesearchTypeChange">
            <el-radio-button :label="0">按供应商</el-radio-button>
            <el-radio-button :label="1">按产品</el-radio-button>
            <el-radio-button :label="2">按品牌</el-radio-button>
          </el-radio-group>
        </div>
        <div style="height: 350px">
          <div ref="chartRefForCustomer" style="height: 100%"></div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import wfUserSelectDrop from './userSelect.vue';
import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';
import moment from 'moment';
const chartRef = ref(null);
let searchType = ref(0);
onMounted(() => {
  getDetail();
});

let echartsType = ref('按时间');
const form = inject('form');
let query = ref({
  year: moment(new Date()).format('YYYY'),
  type1: 0,
});
watch(form.value, () => {
  query.value.userId = form.value.userId;
  query.value.year = form.value.year;
  getDetail();
});
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#303133'],

    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: false,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: detailForm.value.x, //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '总额', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: [
      {
        name: '采购总额', //名称
        type: 'line', //类型
        barWidth: 18, //宽度
        smooth: true,
        data: detailForm.value.y, //数值
        z: 1,
        label: {
          show: true,
          position: 'top',
        },
        itemStyle: {
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            type: 'linear',
            global: false,
            colorStops: [
              {
                offset: 0,
                color: '#E6A23C',
              },
              {
                offset: 1,
                color: '#fff',
              },
            ],
          },
        },
        // 添加 markLine 配置（平均值）
      },
    ],
  };
  chart.setOption(option);
}
let chartRefForCustomer = ref(null);
async function initRangeEcharts() {
  await getdetailForRange({});
  loading.value = false;
  const chart = echarts.init(chartRefForCustomer.value);
  let option = {
    color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#303133'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '0%',
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 100 - (10 / echartsForRange.value.length) * 100,
        end: 100,
        minValueSpan: 15,
        maxValueSpan: 15,
        zoomLock: true,
        handleSize: 20,
        yAxisIndex: 0,
      },
      {
        type: 'inside',
        start: 100 - (10 / echartsForRange.value.length) * 100,
        end: 100,
        yAxisIndex: 0,
      },
    ],
    yAxis: {
      type: 'category',
      data: echartsForRange.value.map(item => item.name).reverse(),
      axisLabel:{
        formatter: val => {
            return val.slice(0,18);
          },
      }
    },
    series: [
      {
        name: '采购总额',
        type: 'bar',
        itemStyle: {
          barWidth: 20,
        },
        data: echartsForRange.value.map(item => item.totalPrice).reverse(),
      },
    ],
  };
  chart.setOption(option);
}

let echartsForRange = ref({});
let loading = ref(false);
function getdetailForRange({ size = 10000, current = 1 }) {
  loading.value = true;
  return axios
    .get('/api/vt-admin/statistics/supplierRankingList', {
      params: {
        size,
        current,
        ...query.value,
        ...form.value,
        selectType: null,
        type: searchType.value,
      },
    })
    .then(res => {
      console.log(res);
      echartsForRange.value = res.data.data;
    });
}

function handlesearchTypeChange() {
  initRangeEcharts({});
}
let detailForm = ref({ y: [] });
function getDetail() {
  if (echartsType.value == '按时间') {
    getDataByTime();
  } else {
    initRangeEcharts();
  }
}
function getDataByTime() {
  axios
    .get('/api/vt-admin/statistics/purchaseAmountStatistics', {
      params: {
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      initChart();
    });
}
function handleTypeChange(val) {
  if (val == '按时间') {
    initChart();
  } else {
    initRangeEcharts();
  }
}
</script>

<style lang="scss" scoped>
.el-card {
  height: 500px;
}
</style>
