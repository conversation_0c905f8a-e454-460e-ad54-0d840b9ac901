<template>
  <basic-container>
    <Title
      >订单详情

      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        >
      </template>
    </Title>
    <avue-form style="margin-top: 20px" :option="option" v-model="form">
      <template #product>
        <el-collapse v-model="form.supplierName">
          <el-collapse-item :name="index" v-for="(i, index) in form.supplier">
            <template #title>
              <div style="display: flex; justify-content: center; align-items: center">
                <span style="font-weight: bolder">{{ i.supplierName }}</span>
                <el-button
                  type="primary"
                  style="margin-left: 10px"
                  icon="plus"
                  v-if="isEdit"
                  size="small"
                  @click.stop="addProduct(i.productList, i.classify)"
                  plain
                  >新增</el-button
                >
              </div>
            </template>

            <div>
              <el-table class="avue-crud" :data="i.products" border>
                <el-table-column label="设备名称" prop="productName"></el-table-column>
                <el-table-column label="规格型号" prop="productSpecification"></el-table-column>
                <el-table-column label="产品图片" #default="{ row }">
                  <el-image
                    style="width: 80px"
                    :preview-src-list="[row.coverUrl]"
                    :src="row.coverUrl"
                  ></el-image>
                </el-table-column>
                <el-table-column
                  label="产品描述"
                  show-overflow-tooltip
                  width="200"
                  prop="description"
                ></el-table-column>
                <el-table-column label="品牌" prop="productBrand"></el-table-column>
                <el-table-column label="单位" prop="unitName"></el-table-column>
                <el-table-column label="数量" #default="{ row }" prop="number"> </el-table-column>
                <!-- <el-table-column label="预估价" #default="{ row }" prop="prePrice"> </el-table-column>
        <el-table-column label="预估总价" #default="{ row }" prop="preTotalPrice">
          {{ row.number * row.prePrice }}
        </el-table-column> -->
                <el-table-column label="报价(成本价)" #default="{ row }" prop="unitPrice">
                  <span>{{ row.unitPrice }}</span>
                </el-table-column>
                <el-table-column label="成本总价" #default="{ row }" prop="totalPrice">
                  {{ row.unitPrice ? row.number * row.unitPrice : '---' }}
                </el-table-column>
                <el-table-column label="供应商" #default="{ row }" width="300" prop="supplier">
                  <span>{{ row.supplierName }}</span>
                </el-table-column>
                <el-table-column label="实际采购价" #default="{ row }" prop="prePrice">
                  <el-input v-model="row.prePrice" size="small" placeholder="请输入报价"></el-input>
                </el-table-column>
                <el-table-column label="实际供应商" #default="{ row }" prop="perSupplierId">
                  <suppliyerSelect
                    size="small"
                    v-model="row.perSupplierId"
                    :Url="`/api/vt-admin/supplier/page?productId=${row.productId}`"
                  ></suppliyerSelect>
                </el-table-column>
              </el-table>
            </div>
          </el-collapse-item>
        </el-collapse>
      </template>
    </avue-form>
  </basic-container>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import suppliyerSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
let route = useRoute();
let router = useRouter();
let { proxy } = getCurrentInstance();
let form = ref({});
let getDetail = async () => {
  let res = await proxy.$axios.get(`/api/vt-admin/purchaseOrder/detail?id=${route.query.id}`);
  form.value = formatData(res.data.data);
};
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
let option = ref({
  labelWidth: 110,
  emptyBtn: false,
  submitBtn: false,
  detail: true,
  group: [
    {
      label: '订单信息',
      prop: 'order',
      span: 12,
      row: true,
      column: [
        {
          label: '订单编号',
          prop: 'orderNo',
          width: 250,
          overHidden: true,
          search: true,
        },
        {
          label: '报价名称',
          prop: 'offerName',
          search: true,
        },

        {
          label: '采购数量',
          prop: 'number',
        },

        {
          label: '下单时间',
          prop: 'orderDate',
          type: 'date',
          searchRange: true,
          searchSpan: 5,
          search: true,
        },
        {
          label: '采购人',
          prop: 'purchasePeople',
          component: 'wf-user-select',
          searchSpan: 3,
          search: true,
        },
        {
          label: '订单状态',
          prop: 'orderStatus',
          type: 'select',
          dicData: [
            {
              value: 0,
              label: '未支付',
            },
            {
              value: 1,
              label: '已支付',
            },
          ],
          slot: true,
          search: true,
        },
        {
          label: '附件',
          prop: 'files',
          span: 24,
        },
        {
          label: '备注',
          prop: 'remark',
          span: 24,
        },
      ],
    },
    {
      label: '商品信息',
      prop: 'order',
      span: 12,
      row: true,
      column: [
        {
          label: '',
          prop: 'product',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
    // {
    //   label: '付款信息',
    //   prop: 'order',
    //   span: 12,
    //   row: true,
    //   column: [],
    // },
  ],
});
function formatData(data) {
  const keys = Object.keys(data.detailMap);
  const form = {
    ...data,
  };
  form.supplier = keys.map(item => {
    return {
      supplierName: item,
      products: data.detailMap[item].map(i => {
        console.log(i);
        return {
          ...i.productVO,
          ...i,
        };
      }),
    };
  });
  return form;
}
</script>

<style></style>
