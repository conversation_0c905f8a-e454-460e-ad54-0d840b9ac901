<template>
  <div style="height: calc(100vh - 160px)" :class="{ isFullSreen: isFullSreen }">
    <Title style="margin: 0 15px; background-color: #fff" 
      >{{
        $route.query.type == 'add'
          ? '新增报价'
          : $route.query.type == 'edit'
          ? `编辑${$route.query.name}`
          : `查看${$route.query.name}`
      }}
      <template #foot>
        <el-button icon="FullScreen" @click="handleScreen"></el-button>
        <el-button type="primary" @click="drawer = true">基本信息</el-button>
        <el-button type="warning" @click="savehistory" v-if="form.isOnline == 0 && $route.query.type != 'detail'" 
          >保存为历史版本</el-button
        >
        <el-button type="primary" @click="submit" v-if="form.isOnline == 0&& $route.query.type != 'detail'" plain >保存</el-button>
        <el-button type="primary" @click="submit('confirm')" v-if="$route.query.type != 'detail'">保存并提交</el-button>

        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >

    <!-- <input type="file" @change="handleChange" ref="file" /> -->

    <div style="height: calc(100% - 90px)">
      <Sheet :option="sheetOption" ref="sheet"></Sheet>
    </div>
  </div>
  <el-drawer title="基本信息" v-model="drawer">
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form"></avue-form>
    <template #footer>
      <div style="flex: auto">
        <el-button type="primary" @click="drawer = false">确认</el-button>
      </div>
    </template>
  </el-drawer>
  <div style="position: fixed"></div>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import { getCurrentInstance, onBeforeUnmount, onMounted } from 'vue';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import Sortable from 'sortablejs';
import { randomLenNum } from '@/utils/util';
// import sheet from '@/views/CRM/compoents/sheet.vue';
let { proxy } = getCurrentInstance();
// 设置拖拽排序

onMounted(() => {
  if (route.query.roleType == 'assist') {
    console.log(111);
    setBusinessUrl();
    setCustomerUrl();
  }
});
let route = useRoute();
let router = useRouter();
let form = ref({
  detailList: [],
});
let isEdit = ref(true);
let option = ref({
  submitBtn: false,
  labelWidth: 140,
  detail: false,
  emptyBtn: false,
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      span: 24,
      rules: [
        {
          required: true,
          message: '请填写方案名称',
        },
        {
          validator: (rule, value, callback) => {
            const reg = /^[^/\\?？\[\]]*$/;
            console.log(value, rule, reg);
            if (!reg.test(value)) {
              callback(new Error('不能包含特殊字符"/\?？[]"'));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联商机',
      prop: 'businessOpportunityId',
      component: 'wf-business-select',
      span: 24,
      params: {
        isNew: true,
      },
      change: val => {
        if (val.value) {
          getBusinessDetail(val.value);
        }
      },
      control: val => {
        return {
          businessTypeId: {
            display: !val,
          },
        };
      },
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 24,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'businessTypeId',
      checkStrictly: true,
    },
    {
      label: '对应客户',
      prop: 'customerId',
      span: 24,
      component: 'wf-customer-select',
      change: val => {
        const contactPerson = proxy.findObject(option.value.column, 'contactPerson');

        if (!val.value) {
          contactPerson.disabled = true;
          return;
        }
        contactPerson.disabled = false;
        contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val.value;
        setBaseInfo(val.value);
      },
      params: {},
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      label: '协作业务员',
      placeholder: '请选择协作业务员',
      type: 'select',
      span: 24,
      dicData: [],
      display: false,
      prop: 'businessPerson',
      rules: [
        {
          required: true,
          message: '请选择协作业务员',
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      span: 24,
      component: 'wf-contact-select',
      disabled: true,
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        // checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },

    // {
    //   label: '报价人员',
    //   component: 'wf-user-select',
    //   prop: 'offerPeople',
    // },
    {
      label: '报价日期',
      prop: 'offerDate',
      type: 'date',
     span:24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      
    },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
      type: 'number',
      span: 24,
    },
    {
      label: '线下报价',
      prop: 'isOnline',
      type: 'radio',
      span: 24,
      // display: !route.query.id,
      value: 0,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    // {
    //   label: '专项报价',
    //   prop: 'isHasSpecialPrice',
    //   type: 'radio',
    //   span: 24,
    //   value: 0,
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
  if (route.query.businessId) {
    getBusinessDetail(route.query.businessId);
  }
  if (route.query.type == 'add') {
    sheetOption.value = {};
    proxy.$nextTick(() => {
      proxy.$refs.sheet.renderSheet();
    });
  }
});
let drawer = ref(route.query.type != 'detail');
function submit(type) {
  console.log(proxy.$refs.sheet.getSheetData());

  // if (form.value.detailList.length == 0) {
  //   return proxy.$message.warning('请至少添加一个产品');
  // }

  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy
        .$confirm('确认此次操作吗？', '提示')
        .then(() => {
          sumitForm(type, done);
        })
        .catch(err => {
          done();
        });
    } else {
      drawer.value = true;
    }
  });
}

function sumitForm(type, done) {
  console.log(type, form.value);
  return new Promise((resolve, reject) => {
    const { configuredJson } = proxy.$refs.sheet.getSheetData();
    form.value = {
      ...form.value,
      ...proxy.$refs.sheet.getSheetData(),
      configuredJson: JSON.stringify({
        ...configuredJson,
        isFullSreen: isFullSreen.value,
      }),
      detailList: proxy.$refs.sheet.getSheetData().modelData[0].productList.map(item => {
        return {
          ...item,
          customProductName: item.productName,
          customProductSpecification: item.productSpecification,
          customProductDescription: item.description,
        };
      }),
      modelData: null,
    };
    if (form.value.id) {
      axios
        .post('/api/vt-admin/offer/businessUpdateOffer', {
          ...form.value,
          detailList: form.value.detailList.map((i, index) => {
            return {
              ...i,
              sortNumber: index,
            };
          }),
          id: form.value.id,
          offerPrice: offerPrice(),
          offerStatus: type == 'confirm' ? 1 : 0,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          resolve(form.value.id);
          if (type == 'confirm') {
            isEdit.value = false;
            router.$avueRouter.closeTag();
            router.go(-1);
          }
          done();
        });
    } else {
      let url;
      if (form.value.businessOpportunityId) {
        url = '/api/vt-admin/businessOpportunity/noOptionAddOffer';
      } else {
        url = '/api/vt-admin/offer/noBusinessAddOffer';
      }
      axios
        .post(url, {
          ...form.value,
          detailList: form.value.detailList.map((i, index) => {
            return {
              ...i,
              sortNumber: index,
            };
          }),
          offerPeople: proxy.$store.getters.userInfo.user_id,
          offerStatus: type == 'confirm' ? 1 : 0,
          offerPrice: offerPrice(),
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          if (type == 'confirm') {
            isEdit.value = false;
            router.$avueRouter.closeTag();
            router.go(-1);
          } else {
            getDetail(res.data.data);
            resolve(res.data.data);
          }
          done();
        });
    }
  });
}
function offerPrice() {
  return form.value.detailList.reduce((prev, curr) => {
    const value = Number(curr.sealPrice) * Number(curr.number);
    if (!Number.isNaN(value)) {
      return prev + value;
    } else {
      return prev;
    }
  }, 0);
}
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const mainPerson = res.data.data.customerContactVO?.id;
      form.value.contactPerson = mainPerson;
      const businessPersonRef = proxy.findObject(option.value.column, 'businessPerson');
      if (res.data.data.businessPerson.split(',').length > 1 && route.query.roleType == 'assist') {
        businessPersonRef.display = true;
        const dicData = res.data.data.businessPerson.split(',').map((item, index) => {
          return {
            value: item,
            label: res.data.data.businessPersonName.split(',')[index],
          };
        });
        businessPersonRef.dicData = dicData;
      } else if (
        res.data.data.businessPerson.split(',').length == 1 &&
        route.query.roleType == 'assist'
      ) {
        form.value.businessPerson = res.data.data.businessPerson;
        businessPersonRef.display = false;
      } else {
        businessPersonRef.display = false;
      }
    });
}
let sheetOption = ref({
  option: '', //JSON数据
  detailList: [], //详情数据
  isRefresh: false, //是否刷新表格中的数据
  type: 1, //1 报价 2 方案
  form: {
    productRate: '0.13',
    labourRate: '0.06',
    otherRate: '0.06',
    warrantyRate: '0.06',
  },
});
function getDetail(id) {
  let url = '';
  if (route.query.isHistory == '1') {
    url = '/api/vt-admin/offerHistory/detail';
  } else if (route.query.isTranslate) {
    url = '/api/vt-admin/offer/detailByHistoryId';
  } else {
    url = '/api/vt-admin/offer/detail';
  }
  axios
    .get(url, {
      params: {
        id: id,
      },
    })
    .then(res => {
      let {
        detailVOList,
        id,
        customerId,
        businessOpportunityId,
        contactPerson,
        offerName,
        isHasOption,
        offerValidity,
        offerDate,
        businessTypeId,
        isHasSpecialPrice,
        isHasCustom,
        isOnline,
        auditStatus,
        dataJson,
        configuredJson,
        productRate,
        labourRate,
        otherRate,
        remark,
        warrantyRate,
      } = res.data.data;
      form.value.detailList = detailVOList;
      form.value.businessOpportunityId = businessOpportunityId;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = offerName;
      form.value.isHasOption = isHasOption;
      form.value.offerValidity = offerValidity;
      form.value.offerDate = offerDate;
      form.value.businessTypeId = businessTypeId;
      form.value.isHasSpecialPrice = isHasSpecialPrice;
      form.value.isHasCustom = isHasCustom;
      form.value.id = id;
      form.value.isOnline = isOnline;
      form.value.remark = remark;
      sheetOption.value.option = dataJson;
      sheetOption.value.detailList = form.value.detailList;
      sheetOption.value.isRefresh = auditStatus != 1;

      if (!configuredJson) {
        configuredJson = '{}';
      }

      const {
        isFullSreen: newIsFullSreen = false,
        isFold = false,
        utilCheckBox = [],
      } = JSON.parse(configuredJson);
      isFullSreen.value = newIsFullSreen;
      sheetOption.value.form = {
        productRate,
        labourRate,
        otherRate,
        warrantyRate,
        isLock: false,
        isFold,
        utilCheckBox,
      };
      proxy.$refs.sheet.renderSheet();
    });
}
function getBusinessDetail(id) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        id: businessOpportunityId,
        contactPerson,
        name,
      } = res.data.data;

      if (productVOList && productVOList.length > 0) {
        console.log(productVOList);
        const detailList = productVOList.map(item => {
          return {
            ...item.productVO,
            number: item.number,
            productId: item.productId,
            preSealPrice: item.productVO.sealPrice,
            sealPrice: '',
          };
        });
        proxy.$refs.sheet.handleBusiness(detailList);
      }
      form.value.businessOpportunityId = businessOpportunityId;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = name;
    });
}

function setBusinessUrl(params) {
  const businessOpportunity = proxy.findObject(option.value.column, 'businessOpportunityId');
  businessOpportunity.params.Url = '/vt-admin/businessOpportunity/page?selectType=3';
}
function setCustomerUrl() {
  const customer = proxy.findObject(option.value.column, 'customerId');
  customer.params.Url = '/vt-admin/customer/page?type=2';
}
onBeforeUnmount(() => {});
function handleFocus(row, value) {
  row[value] = true;
  console.log(proxy.$refs);
  proxy.$nextTick(() => {
    proxy.$refs[`${value}${row.uuid}`].focus();
  });
}

let isFullSreen = ref(false);
function handleScreen() {
  isFullSreen.value = !isFullSreen.value;
  proxy.$nextTick(() => {
    luckysheet.resize();
  });
}
function savehistory() {
  if (!form.value.id) {
    proxy.$refs.addForm.validate((valid, done) => {
      if (valid) {
        savehistorySubmit();
      } else {
        drawer.value = true;
      }
    });
  } else {
    proxy.$refs.dialogForm.show({
      title: '保存历史版本',
      option: {
        column: [
          {
            label: '报价名称',
            type: 'input',
            prop: 'offerName',
            value: form.value.offerName,
            rules: [
              {
                required: true,
                message: '请输入报价名称',
                trigger: 'blur',
              },
            ],
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin//vt-admin/offer/saveHistoryOffer', {
            ...res.data,
            id: form.value.id,
          })
          .then(r => {
            proxy.$message.success(r.data.msg);
            res.close();
          });
      },
    });
  }
}
function savehistorySubmit(params) {
  sumitForm().then(r => {
    proxy.$refs.dialogForm.show({
      title: '保存历史版本',
      option: {
        column: [
          {
            label: '报价名称',
            type: 'input',
            prop: 'offerName',
            value: form.value.offerName,
            rules: [
              {
                required: true,
                message: '请输入报价名称',
                trigger: 'blur',
              },
            ],
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin//vt-admin/offer/saveHistoryOffer', {
            ...res.data,
            id: r,
          })
          .then(r => {
            proxy.$message.success(r.data.msg);
            res.close();
          });
      },
    });
  });
}
onBeforeRouteLeave((to, from) => {
  if (route.query.type != 'detail') {
    const answer = window.confirm('确定离开当前页面吗？');
    // 取消导航并停留在同一页面上
    if (!answer) {
      proxy.$store.commit('ADD_TAG', {
        name: from.name || from.name,
        path: from.path,
        fullPath: from.fullPath,
        params: from.params,
        query: from.query,
        meta: from.meta,
      });
      return false;
    }
  }
});
</script>

<style scoped>
.el-table .el-form-item {
  margin-bottom: 0;
}
.isFullSreen {
  position: fixed;
  height: 100vh !important;
  width: 100vw;
  background-color: #fff;
  top: 0;
  left: 0;
  z-index: 1000;
}
</style>
