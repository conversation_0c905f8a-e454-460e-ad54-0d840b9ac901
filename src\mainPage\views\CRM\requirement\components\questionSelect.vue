<template>
  <el-dialog title="需求项选择" v-model="dialogVisible" width="80%">
    <el-row style="height: 100%" :gutter="20">
      <el-col :span="5" style="height: 100%">
        <avue-tree
          :option="treeOption"
          ref="tree"
          v-model="form"
          style="max-height: 660px; overflow-y: auto"
          :data="treeData"
          @node-click="nodeClick"
        >
        </avue-tree>
      </el-col>

      <el-col :span="19" style="height: 100%">
        <Title>{{ categoryName || '----' }}</Title>
        <div style="margin: 20px 0">
          <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
            <el-tab-pane label="采集配置" name="first">
              <el-form>
                <el-table class="avue-crud" :row-key="row => row.id" :data="tableData" border>
                  <el-table-column type="index" width="50" />
                  <el-table-column label="#" width="50" type="expand">
                    <template #default="scope">
                      <div style="margin-left: 100px; display: flex">
                        <el-tag effect='plain'
                          style="margin-right: 5px; margin-bottom: 5px"
                          v-for="element in scope.row.valuesVOList"
                          :key="element.id"
                        >
                          {{ element.value }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="采集内容" prop="collectionContent" width="500" />
                  <el-table-column label="备注" prop="remark" width=""> </el-table-column>
                  <!-- <el-table-column label="选择项" >
                    <template #default="scope">
                      <div >
                        <draggable
                          v-model="scope.row.valuesVOList"
                          :animation="100"
                          @sort="
                            a => {
                              onMoveCallback(a, scope.row);
                            }
                          "
                        >
                          <transition-group>
                           <div   v-for="element in scope.row.valuesVOList">
                            <el-tag effect='plain'
                              style="margin-right: 5px;margin-bottom: 5px"
                             
                              :key="element.id"
                              closable
                              @close="handleClose(element)"
                            >
                              {{ element.value }}
                            </el-tag>
                           </div>
                          </transition-group>
                        </draggable>
  
                        <el-input
                          class="input-new-tag"
                          v-if="scope.row.inputVisible"
                          style="width: 100%"
                          v-model="scope.row.inputValue"
                          :ref="'saveTagInput' + scope.$index"
                          size="small"
                          @keyup.enter.native="addTag(scope.row)"
                          @blur="addTag(scope.row)"
                        >
                        </el-input>
  
                        <el-button
                          v-else-if="scope.row.type != 2"
                          class="button-new-tag"
                          size="small"
                          @click="showInput(scope.row, scope.$index)"
                          >+ 添加可选项</el-button
                        >
                      </div>
                    </template>
                  </el-table-column> -->
                  <el-table-column label="回答方式" prop="type" width="100">
                    <template #default="scope">
                      <el-tag effect='plain' type="success" v-if="scope.row.type == 0">单选</el-tag>
                      <el-tag effect='plain' type="success" v-else-if="scope.row.type == 1">多选</el-tag>
                      <el-tag effect='plain' type="success" v-else>文本输入</el-tag>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="是否使用" prop="isUse" width="100">
                    <template #default="scope">
                      <el-switch
                        :active-value="1"
                        :inactive-value="0"
                        disabled
                        v-model="scope.row.isUse"
                      ></el-switch
                    ></template>
                  </el-table-column> -->
                </el-table>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="话术参考" name="second">
              <avue-crud :option="{}"></avue-crud>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <span>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="">确认</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let form = ref({
  hasChildren: true,
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  defaultExpandAll: true,
  menu: false,
  filter: true,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    id: 'id',
    children: 'children',
  },
  formOption: {
    column: {
      categoryName: {
        label: '分类名称',
        rules: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur',
          },
        ],
      },
    },
  },
});

const updateUrl = '/api/vt-admin/product/update';

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  getTreeData();
});
let loading = ref(false);

let router = useRouter();
let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/requirementCategory/tree', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren,
        };
      });
      setShowCategory();
    });
}

let categoryId = ref('');
let categoryName = ref('');
let hasChildren = ref(true);
function nodeClick(val, accountName) {
  categoryId.value = val.id;
  categoryName.value = val.categoryName;
  hasChildren.value = val.hasChildren;
  getTableData();
}
function getTableData() {
  const { current, size } = page.value;
  axios
    .get('/api/vt-admin/requirementProperty/page', {
      params: {
        categoryId: categoryId.value,
        current,
        size,
      },
    })
    .then(res => {
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}

function setShowCategory() {
  proxy.$refs.tree.setCurrentKey(categoryId.value || treeData.value[0].id);
}

function handleClick() {}
let dialogVisible = ref(false);
function open() {
  dialogVisible.value = true;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
