export default {
  tabs: true,
  tabsActive: 1,
  group: [
    {
      label: '个人信息',
      prop: 'info',
      column: [
        {
          label: '头像',
          type: 'upload',
          listType: 'picture-img',
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          canvasOption: {
            text: ' ',
            ratio: 0.1,
          },
          action: '/blade-resource/attach/upload',
        
          span: 12,
          row: true,
          prop: 'avatar',
        },
        {
          label: '姓名',
          span: 12,
          row: true,
          prop: 'realName',
        },
        {
          label: '用户名',
          span: 12,
          row: true,
          prop: 'name',
        },
        {
          label: '手机号',
          span: 12,
          row: true,
          prop: 'phone',
        },
        {
          label: '邮箱',
          prop: 'email',
          span: 12,
          row: true,
        },
      ],
    },
    {
      label: '修改密码',
      prop: 'password',
      column: [
        {
          label: '原密码',
          span: 12,
          row: true,
          type: 'password',
          prop: 'oldPassword',
        },
        {
          label: '新密码',
          span: 12,
          row: true,
          type: 'password',
          prop: 'newPassword',
        },
        {
          label: '确认密码',
          span: 12,
          row: true,
          type: 'password',
          prop: 'newPassword1',
        },
      ],
    },
    {
      label: '个人名片',
      prop: 'card',
      labelWidth: 150,
      column: [
        {
          label: '个人头像',
          type: 'upload',
          listType: 'picture-img',
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          canvasOption: {
            text: ' ',
            ratio: 0.1,
          },
          action: '/blade-resource/attach/upload',
          tip: '推荐比例（宽：高）1：1.26',
          span: 12,
          row: true,
          prop: 'wxQrCode',
        },
        {
          label: '职位',
          prop: 'position',
          span: 12,
          row: true,
        },
        {
          label: '姓名',
          span: 12,
          row: true,
          prop: 'name',
        },
        {
          label: '手机号',
          span: 12,
          row: true,
          prop: 'phone',
        },
        {
          label: '邮箱',
          prop: 'email',
          span: 12,
          // row: true,
        },
        // {
        //   label: '使用个人简介',
        //   type: 'switch',
        //   dicData: [
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //   ],
        //   control:val => {
        //     return {
        //       content:{
        //         label:val == 1?'个人简介':'公司简介'
        //       }
        //     }
        //   }
        // },
       
        {
          label: '公司简介',
          prop: 'companyProfile',
          value: '',
          component: 'AvueUeditor',
          action: '/api/blade-resource/attach/upload',
          options: {
            action: '/api/blade-resource/attach/upload',
            accept: 'image/png, image/jpeg, image/jpg,.mp4',
          },
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          disabled:true,
          hide: true,
          minRows: 6,
          span: 24,
        },
      ],
    },
  ],
};
