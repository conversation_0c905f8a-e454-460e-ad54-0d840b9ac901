<template>
  <basic-container :shadow="shadow">
    <avue-crud
      class="crud"
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      :before-open="beforeOpen"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #menu-left>
       
        <!-- <el-button type="primary" @click="addInquiryOpen" icon="plus">生成询价单</el-button> -->
      </template>
      <template #inquiryCode="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.inquiryCode }}</el-link>
      </template>
      <template #detailDTOList="{ row }">
        <el-link type="primary" @click="handleClick(row)">{{
          parseFloat(row.inquiryNumber)
        }}</el-link>
      </template>
      <template #menu="{ row, index }">
        <el-button
          type="primary"
          v-if="row.inquiryStatus == 0"
          icon="edit"
          @click="edit(row)"
          text
          >编辑</el-button
        >
        <!-- <el-popover placement="bottom" trigger="click">
          <div
            style="
              display: flex;
              justify-content: center;
              flex-direction: column;
              align-items: center;
            "
          >
            <el-text size="small" type="primary">微信扫一扫分享给对方</el-text>
            <el-image style="width: 100px; height: 100px" :src="url"></el-image>
          </div>
          <template #reference>
            <el-button
              type="primary"
              v-if="row.inquiryStatus == 1"
              icon="Share"
              @click="share(row)"
              text
              >询价</el-button
            >
          </template>
        </el-popover> -->
        <!-- <el-button
          type="primary"
          v-if="row.inquiryStatus == 0"
          icon="Check"
          @click="submit(row)"
          text
          >提交</el-button
        > -->
        <el-button
          type="primary"
          v-if="row.inquiryStatus == 0"
          icon="Submit"
          @click="submit(row)"
          text
          >编辑</el-button
        >
      </template>
      <template #menu-form-before="{ row, index, type }">
        <el-tag effect='plain' type="primary" :size="size">提示</el-tag>
      </template>
      <template #menu-form="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-if="type === 'edit'"
          @click="handleUpdate(0)"
          >修改</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          plain
          v-if="type === 'edit'"
          @click="handleUpdate(1)"
          >修改并提交</el-button
        >
        <el-button type="primary" icon="el-icon-check" v-if="type === 'add'" @click="handleAdd(0)"
          >保存</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          plain
          v-if="type === 'add'"
          @click="handleAdd(1)"
          >保存并提交</el-button
        >
      </template>
      <template #expand="{ row }">
        <div style="margin-left: 50px">
          <avue-crud :option="detailOption" :data="row.supplierVOList">
            <template #menu="{ row }">
              <el-popover placement="bottom" trigger="click">
                <div
                  style="
                    display: flex;
                    justify-content: center;
                    flex-direction: column;
                    align-items: center;
                  "
                >
                  <el-text size="small" type="primary">微信扫一扫分享给对方</el-text>
                  <el-image style="width: 100px; height: 100px" :src="url"></el-image>
                </div>
                <template #reference>
                  <el-button
                    type="primary"
                    v-if="row.inquiryStatus == 1"
                    icon="Share"
                    @click="share(row)"
                    text
                    >询价</el-button
                  >
                </template>
              </el-popover>
            </template>
            <template #inquiryStatus="{ row }">
              <el-tag effect='plain'
                :type="
                  row.inquiryStatus == 0
                    ? 'info'
                    : row.inquiryStatus == 1 || row.inquiryStatus == 2
                    ? 'success'
                    : 'danger'
                "
                >{{ row.$inquiryStatus }}</el-tag
              >
            </template>
          </avue-crud>
        </div>
      </template>
      <template #inquiryStatus="{ row }">
        <el-tag effect='plain'
          :type="row.inquiryStatus == 0 ? 'info' : row.inquiryStatus == 1 ? 'success' : 'danger'"
          >{{ row.$inquiryStatus }}</el-tag
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="wfproductSelect"
      data-type="object"
      check-type="box"
      @onConfirm="handleProductSelectConfirm"
    >
    </wf-product-select>
    <productSelect
      ref="productSelectRef"
      :type="form.inquirySource"
      :id="form.purchaseId"
      @onConfirm="handleProductSelectConfirm"
    ></productSelect>
    <!-- <inquiryDetail ref="inquiryDetailRef" :id="currentRow.id"></inquiryDetail> -->
    <el-drawer
      title="询价单详情"
      v-model="drawer"
      size="80%"
      :destroy-on-close="true"
      :show-close="true"
      :wrapperClosable="true"
    >
      <div v-for="item in detailForm.supplierVOList">
        <el-divider content-position="left"
          ><el-text type="primary">{{ item.supplierName }}</el-text>
          <el-tag effect='plain'
            :type="
              item.inquiryStatus == 0 ? 'info' : item.inquiryStatus == 1 || item.inquiryStatus == 2 ? 'success' : 'danger'
            "
            >{{
              item.inquiryStatus == 0
                ? '待提交'
                : item.inquiryStatus == 1
                ? '待报价'
                : item.inquiryStatus == 2
                ? '已报价'
                : '已过期'
            }}</el-tag
          ></el-divider
        >
        <inquiryDetail ref="inquiryDetailRef" :data="item.detailVOS"></inquiryDetail>
      </div>
    </el-drawer>
    <addInquiry ref="addInquiryRef" :data="detailData" ></addInquiry>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import productSelect from './compoents/productSelect.vue';
import inquiryDetail from './compoents/inquiryDetail.vue';
import addInquiry from '../procure/compoents/addInquiry.vue';
const store = useStore();
let userInfo = computed(() => store.getters.userInfo);
let crud = ref();
let wfproductSelect = ref(null);
let productSelectRef = ref(null);
let inquiryDetailRef = ref(null);
let form = ref({});
const props = defineProps(['shadow', 'orderId', 'search']);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  delBtn: false,
  editBtn: true,
  delBtn: false,
  menu: true,
  search: false,
  rowKey: 'id',
  expand: true,
  updateBtn: false,
  saveBtn: false,
  // cancelBtn: false,
  header: !props.orderId,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  defaultExpandAll: props.orderId,
  searchLabelWidth: 120,
  labelWidth: 150,
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '询价单编号',
      prop: 'inquiryCode',

      overHidden: true,
      search: !props.orderId,
      display: false,
    },
    {
      label: '询价类型',
      prop: 'inquirySource',
      overHidden: true,
      search: !props.orderId,
      type: 'radio',

      span: 12,
      value: 0,
      dicData: [
        {
          label: '采购询价',
          value: 0,
        },
      ],
    },
    {
      label: '合同名称',
      prop: 'contractNames',
     
      display: false,
    },
    // {
    //   label: '产品类别',
    //   prop: 'contractNames',
     
    //   display: false,
    // },
    {
      label: '产品分类',
      prop: 'productCategory',
      overHidden:true,
      display: false,
    },
    {
      label: '品牌',
      prop: 'productBrands',
     
      display: false,
    },
    {
      label: '关联采购单',
      prop: 'purchaseId',
      component: 'wf-order-select',
      hide: true,
      display:false
    },
    {
      label: '关联供应商',
      prop: 'supplierId',
      search: !props.orderId,
      component: 'wf-supplier-select',
      hide: true,
      display: false,
    },
    {
      label: '询价有效期（天）',
      prop: 'overDays',
      type: 'number',
      width: 150,
      hide:true,
      span: 12,
    },
    {
      label: '询价人',
      prop: 'contactPerson',
      value: userInfo.value.user_id,
      component: 'wf-user-select',
      hide:true,
      formatter: row => {
        return row.contactName;
      },
    },
    {
      label: '联系电话',
      prop: 'contactPhone',
      value: '',
      hide:true,
      display:false,
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
      overHidden: true,
    },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
          trigger: 'blur',
        },
      ],
      hide: true,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          console.log(form.value.inquirySource);

          if (form.value.inquirySource === '') {
            return proxy.$message.error('请先选择询价类型');
          }
          if (!form.value.purchaseId) {
            return proxy.$message.error('请先选择关联采购单');
          }

          if (form.value.inquirySource == 0) {
            productSelectRef.value.open();
          } else {
            wfproductSelect.value.show;
          }
          // wfproductSelect.value.visible = true;
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            width: 260,
            editDisabled: true,
            overHidden: true,

            cell: false,
          },

          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,

            editDisabled: true,
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            span: 24,
            overHidden: true,
            editDisabled: true,

            type: 'input',
            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
          },
        ],
      },
      formatter: row => {
        return row.inquiryNumber;
      },
    },
    {
      label: '关联供应商',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
          trigger: 'blur',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '关联供应商',
            prop: 'supplierId',
            // search: true,
            rules: [
              {
                required: true,
                message: '请选择供应商',
                trigger: 'blur',
              },
            ],
            component: 'wf-supplier-select',
            cascader: ['supplierContactId'],
            change: (value, a, b) => {
             
              // setBaseInfo(value);
            },
            overHidden: true,
          },
          {
            label: '供应商联系人',
            prop: 'supplierContactId',
            type: 'select',
            props: {
              value: 'id',
              label: 'concatName',
              desc: 'concatPhone',
            },
            dicFormatter:(res)=>{
              return res.data.supplierConcatVOList
            },
            cell:true,
            dicUrl:`/api/vt-admin/supplier/detail?id={{key}}`
            // dicData: [],
            // formatter: row => {
            //   return row.supplierContactName;
            // },
          },
        ],
      },
      hide: true,
    },

    {
      label: '公司名称',
      prop: 'companyName',
      value: '深圳市非常聚成科技有限公司',
      overHidden: true,
      hide: true,
      display: false,
    },
    

    {
      label: '创建时间',
      prop: 'createTime',
      display: false,

      overHidden: true,
    },

   
    {
      label: '状态',
      prop: 'inquiryStatus',
      type: 'select',
      display: false,
      hide: true,
      dicData: [
        {
          label: '待提交',
          value: 0,
        },
        {
          label: '待报价',
          value: 1,
        },
        {
          label: '已报价',
          value: 2,
        },
        {
          label: '已过期',
          value: 3,
        },
      ],
    },
   
  ],
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let contactPhone = ref('')
onMounted(() => {
  axios.get('/api/blade-system/user/info').then(res => {
    const contactPhoneRef = proxy.findObject(option.value.column, 'contactPhone');
    contactPhoneRef.value = res.data.data.phone;
    contactPhone.value = res.data.data.phone;
  });
});
const addUrl = '/api/vt-admin/purchaseInquiry/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseInquiry/update';
const tableUrl = '/api/vt-admin/purchaseInquiry/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        orderId: props.orderId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,

    detailDTOList: form.detailDTOList.map(item => {
      return {
        ...item,
        id: null,
        deliveryCycle: '',
        isHasTax: '',
        guaranteePeriod: '',
        contactPhone:contactPhone.value,
        productId: item.productId,
        orderDetailId: item.id,
      };
    }),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,

    detailDTOList: row.detailDTOList.map(item => {
      return {
        ...item,
        id: null,
        productId: item.productId,
        orderDetailId: item.orderDetailId,
      };
    }),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function handleProductSelectConfirm(value) {
  console.log(value);
  form.value.detailDTOList.push(
    ...value.map(item => {
      return {
        ...item,
        number: item.number * 1,
      };
    })
  );
}
function setBaseInfo(value) {
  
  axios.get(`/api/vt-admin/supplier/detail?id=${value.value}`).then(res => {
    const supplierContactId = proxy.findObject(option.value.column, 'supplierContactId');
    supplierContactId.dicData = res.data.data.supplierConcatVOList;
  });
}
let currentRow = ref({});
function handleClick(row) {
  currentRow.value = row;
  console.log(currentRow.value);

  inquiryDetailRef.value.open();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function submit(row) {
  proxy.$confirm('确认提交吗？', '提示').then(() => {
    axios
      .post('/api/vt-admin/purchaseInquiry/update', {
        id: row.id,
        inquiryStatus: 1,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        onLoad();
      });
  });
}
let detailData = ref({})
function edit(row) {
  axios
      .get('/api/vt-admin/purchaseInquiry/detail', {
        params: {
          id: row.id,
        },
      })
      .then(res => {
        detailData.value = {
          ...res.data.data,
          contactPhone:contactPhone.value,
          detailDTOList: res.data.data.detailVOS,
          supplierList: res.data.data.supplierVOList,
        };
        addInquiryRef.value.open()
      });
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    axios
      .get('/api/vt-admin/purchaseInquiry/detail', {
        params: {
          id: form.value.id,
        },
      })
      .then(res => {
        form.value = {
          ...res.data.data,
          contactPhone:contactPhone.value,
          detailDTOList: res.data.data.detailVOS,
          supplierList: res.data.data.supplierVOList,
        };

        setBaseInfo({
          value: form.value.supplierList[0].supplierId,
        });
        done();
      });
  } else {
    done();
  }
}
function handleUpdate(value) {
  form.value.inquiryStatus = value;
  crud.value.rowUpdate();
}
function handleAdd(value) {
  form.value.inquiryStatus = value;
  crud.value.rowSave();
}
let url = ref(null);
function share(row) {
  axios
    .get('/api/vt-admin/purchaseInquiry/getQrCode', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      url.value = `data:image/jpg;base64,${res.data.data}`;
    });
}
let drawer = ref(false);
let detailOption = ref({
  header: false,
  border: true,
  editBtn: false,
  align: 'center',
  delBtn: false,
  column: {
    supplierName: {
      label: '供应商名称',
    },
    supplierContactName: {
      label: '联系人',
    },
    supplierContactPhone: {
      label: '联系人电话',
    },
    supplierFeature:{
      label: '供应商特色',
    },
    inquiryStatus: {
      label: '状态',

      type: 'select',

      dicData: [
        {
          label: '待提交',
          value: 0,
        },
        {
          label: '待报价',
          value: 1,
        },
        {
          label: '已报价',
          value: 2,
        },
        {
          label: '已过期',
          value: 3,
        },
      ],
    },
  },
});
let detailForm = ref({});
function viewDetail(row) {
  
  axios.get('/api/vt-admin/purchaseInquiry/detail?id=' + row.id, {}).then(res => {
    detailForm.value = res.data.data;
    drawer.value = true;
  });
}
let addInquiryRef = ref(null);
function addInquiryOpen() {
  detailData.value = {}
  addInquiryRef.value.open();
}
defineExpose({
  onLoad,
});
</script>

<style lang="scss" scoped>
:deep(.crud .el-empty) {
  padding: 0 !important;
}
:deep(.crud .avue-crud__empty) {
  padding: 0 !important;
}
</style>
