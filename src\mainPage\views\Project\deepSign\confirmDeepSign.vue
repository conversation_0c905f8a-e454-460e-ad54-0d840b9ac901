<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #stage="{ row, a, b, c }">
        <el-tag effect='plain'>{{ (a, b, c) }}{{ row.$stage }}</el-tag>
      </template>
      <template #menu="{ row, index }">
        <!-- <el-button
            type="primary"
            text
            @click="handleEdit(row)"
            v-if="row.optionStatus == 1 && $store.getters.userInfo.user_id == row.technicalPersonnel"
            icon="Edit"
            >编辑方案</el-button
          > -->
        <!-- <el-button type="primary" text @click="handleAdd(row)" v-else icon="plus"
            >新增方案</el-button
          > -->
        <!-- <el-button type="primary" text @click="submit(row)" icon="check" v-if="row.optionStatus != 2 && $store.getters.userInfo.user_id == row.technicalPersonnel">提交</el-button> -->
        <!-- <el-button type="primary" text @click="handleEdit(row)" icon="Edit">修改</el-button> -->
        <el-button text type="primary" icon="el-icon-check" @click="confirm(row)">确认</el-button>
        <el-button type="primary" text @click="handleView(row)" icon="View">详情</el-button>
        <!-- <el-button type="primary" text @click="handleHistory(row)" icon="Clock">历史记录</el-button> -->
      </template>
      <template #auditStatus="{ row }">
        <span v-if="!row.auditStatus">---</span>
        <el-tag effect='plain' v-if="row.auditStatus == 1" size="small" type="info">待主管审核</el-tag>
        <el-tag effect='plain' v-if="row.auditStatus == 2" size="small" type="success">审核成功</el-tag>
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="row.auditReason"
          placement="top-start"
          v-if="row.auditStatus == 3"
        >
          <el-tag effect='plain' size="small" type="danger">审核失败</el-tag>
        </el-tooltip>
      </template>
    </avue-crud>
    <el-drawer title="深化设计" size="90%" :with-header="false" v-model="deepDrawer">
      <deepSign ref="deepSignRef" :detail="detail" :id="currentId"></deepSign>
      <template #footer v-if="!detail">
        <el-button @click="deepDrawer = false">取 消</el-button>
        <el-button type="primary" @click="deepSubmit" plain>保存草稿</el-button>
        <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
          >保存草稿并生成报价单</el-button
        > -->
        <el-button type="primary" @click="deepSubmit('confirm')">保存并提交</el-button>
      </template>
    </el-drawer>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { programmeStatus, businessOpportunityData, auditStatus } from '@/const/const.js';
import deepSign from '../compoents/deepSign.vue';
let store = useStore();
let permission = computed(() => store.getters.permission);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  menuWidth: 200,
  menu: true,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      overHidden: true,
      search: true,
    },
  
    {
      type: 'select',
      label: '业务板块',
      span: 12,
      // search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择商机关联',
        },
      ],
      display: true,
      prop: 'type',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机描述',
      prop: 'description',
      overHidden: true,
    },

    
   
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
    },
    // {
    //   label: '介绍人',
    //   prop: 'introducer',
    //   type: 'input',
    // },
  
    {
      label: '方案状态',
      type: 'select',
      dicData: programmeStatus,
      prop: 'optionStatus',
      search: true,
    },
    // {
    //   label: '审核状态',
    //   type: 'select',
    //   width: 100,
    //   dicData: auditStatus,
    //   prop: 'auditStatus',
    //   //   search: true,
    // },
    {
      label: '技术员',
      prop: 'technicalPersonnelName',
      component: 'wf-user-drop',
      search: true,
      
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/auditDeepenPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 1,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}


function handleEdit(row) {
  router.push({
    path: '/CRM/programme/compoents/update',
    query: {
      id: row.id,
      type: 'edit',
      name: row.name,
      isAdmin: 'admin',
      deep: '1',
    },
  });
}
function handleAdd(row) {
  router.push({
    path: '/CRM/programme/compoents/update',
    query: {
      id: row.id,
      type: 'add',
    },
  });
}
let currentId = ref(0);
function handleHistory(row) {
  currentId.value = row.id;
  proxy.$refs.history.open();
}
function submit(row) {
  proxy
    .$confirm('提交之后将会不可修改，你可以在历史记录查看?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/businessOpportunityOption/confirm', {
          id: row.optionId,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
        });
    });
}
function confirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type: 'radio',
          value: 2,
          dicData: [
            {
              value: 2,
              label: '通过',
            },
            {
              value: 3,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control: val => {
            return {
              auditReason: {
                display: val == 3,
              },
            };
          },
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunityOptionHistory/auditDeepenDesign', {
          id: row.deepenOptionId,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
let detail = ref(false);
let deepDrawer = ref(false);
function handleView(row) {
  currentId.value = row.id;
  detail.value = true
  deepDrawer.value = true
}
</script>

<style lang="scss" scoped></style>
