<template>
  <basic-container>
    <el-container>
      <el-aside width="350px" style="margin-right: 20px; height: 700px; overflow: scroll">
        <el-collapse v-model="activeName" @change="collapseChange">
          <el-collapse-item
            v-for="(item, index) in treeData"
            :key="item.id"
            icon="none"
            :name="index"
          >
            <template #title>
              <div
                style="white-space: nowrap; text-overflow: ellipsis; width: 370px; overflow: hidden"
              >
                {{ item.dictValue }}
              </div>
            </template>
            <div style="display: flex; gap: 10px; width: 100%; flex-wrap: wrap; margin-top: 5px">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  cursor: pointer;
                  justify-content: space-between;
                  width: 20%;
                "
                @click="handleNodeClick(item2)"
                v-for="(item2, index2) in item.children"
                :key="item2.id"
                class="item"
              >
                <div>
                  <el-image
                    style="width: 60px; border-radius: 5px; height: 60px"
                    :src="item2.icon"
                    :fit="fit"
                    :class="{active :currentNode.id == item2.id}"
                  ></el-image>
                </div>
                <div>{{ item2.title }}</div>
                <div class="edit_box" style="display: flex; justify-content: space-between">
                  <el-icon @click="addOrUpdateCategoty(item2, 1)" style="cursor: pointer" :size="16"
                    ><Edit /></el-icon
                  ><el-icon
                    style="cursor: pointer"
                    @click="delCategry(item2)"
                    color="var(--el-color-danger)"
                    :size="16"
                    ><Delete
                  /></el-icon>
                </div>
              </div>
              <div style="width: 20%; display: flex; justify-content: center" class="item">
                <el-button
                  type="info"
                  @click="addOrUpdateCategoty(item, 0)"
                  style="width: 60px; height: 60px"
                  icon="plus"
                ></el-button>
              </div>
            </div>
          </el-collapse-item>
        </el-collapse>
      </el-aside>
      <el-main>
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @on-load="onLoad"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          :before-open="beforeOpen"
          @keyup.enter="onLoad"
          @row-del="rowDel"
          @search-reset="searchReset"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          v-model="form"
        >
          <template #header> </template>
          <template #menu="{ row, index }">
            <el-button type="danger" text icon="delete" @click.stop="$refs.crud.rowDel(row, index)"
              >删除</el-button
            >
          </template>
          <template #templateName="{ row }">
            <el-link @click.stop="toDetail(row)" type="primary">{{ row.templateName }}</el-link>
          </template>
          <!-- <template #templateCategory-form>
        <templateTagSelect v-model="form.templateCategory"></templateTagSelect>
      </template>
      <template #templateCategory-search>
        <templateTagSelect  v-model="params.templateCategory"></templateTagSelect>
      </template> -->
          <template #templateCategory="{ row }">
            <div v-if="row.templateCategory" style="display: flex; flex-wrap: wrap">
              <el-tag
                effect="plain"
                style="margin: 2px"
                size="small"
                v-for="item in row.$templateCategory?.split('|') || []"
                :key="item"
                >{{ item }}</el-tag
              >
            </div>
          </template>
          <template #menu-form="{ row, index, type }">
            <el-button
              type="primary"
              icon="el-icon-check"
              v-if="type === 'edit'"
              @click="handleUpdate(0)"
              >修改</el-button
            >
            <el-button
              type="primary"
              icon="el-icon-check"
              plain
              v-if="type === 'edit'"
              @click="handleUpdate(1)"
              >修改并发布</el-button
            >
            <el-button
              type="primary"
              icon="el-icon-check"
              v-if="type === 'add'"
              @click="handleAdd(0)"
              >保存</el-button
            >
            <el-button
              type="primary"
              icon="el-icon-check"
              plain
              v-if="type === 'add'"
              @click="handleAdd(1)"
              >保存并发布</el-button
            >
          </template>
          <template #isRecommend="{ row }">
            <el-switch
              v-model="row.isRecommend"
              :active-value="1"
              :inactive-value="0"
              @click="handleIsRecommendChange(row)"
            >
            </el-switch>
          </template>
          <template #discoverStatus="{ row }">
            <el-switch
              v-model="row.discoverStatus"
              :active-value="1"
              :inactive-value="0"
              @click="handleDiscoverStatusChange(row)"
            >
            </el-switch>
          </template>
          <template #discoverStatus-form>{{ form.content }}</template>
        </avue-crud>
      </el-main>
    </el-container>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="分类模板" v-model="addVisible" size="50%"> </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let activeName = ref([0, 1, 2, 3, 4, 5, 6, 7]);
let treeData = ref([]);
let { proxy } = getCurrentInstance();
function addOrUpdateCategoty(item = {}, type) {
  console.log(item);

  proxy.$refs.dialogForm.show({
    title: '添加业务类型',
    width:'30%',
    option: {
      column: [
        {
          label: '标题',
          prop: 'title',
          search: true,
          value: item.title,
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入标题',
              trigger: 'blur',
            },
          ],
        },

        {
          label: '图标',
          prop: 'icon',
          type: 'upload',
          value: item.icon,
          dataType: 'string',
          listType: 'picture-card',
          rules: [
            {
              required: 'true',
              message: '请上传图标',
            },
          ],
          limit: 1,
          loadText: '附件上传中，请稍等',
          span: 24,
          accept: 'image/png, image/jpeg,image/jpg',
          // align: 'center',
          uploadExceed: err => {
            console.log(err);
            ElMessage.error('只能上传一张图片');
          },
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '排序',
          prop: 'sort',
          span: 24,
          value: item.sort,
        },
      ],
    },
    callback(res) {
      if (type == 0) {
        const data = {
          ...res.data,
          type: item.id,
        };

        axios
          .post('/api/vt-admin/businessServer/save', data)
          .then(r => {
            if (r.data.code == 200) {
              proxy.$message.success(r.data.msg);
              getTreeData();
              res.close();
            }
          })
          .catch(err => {});
      } else {
        axios
          .post('/api/vt-admin/businessServer/update', {
            ...item,
            ...res.data,
          })
          .then(r => {
            if (r.data.code == 200) {
              proxy.$message.success(r.data.msg);
              getTreeData();
              res.close();
            }
          })
          .catch(err => {});
      }
    },
  });
}
function getCateGoryList(params) {
  axios
    .get('/api/vt-admin/businessServer/page', {
      params: {
        size: 10000,
      },
    })
    .then(res => {
      res.data.data.records.forEach(i => {
        treeData.value.forEach(item => {
          if (!item.children) {
            item.children = [];
          }
          if (i.type == item.id) {
            item.children.push(i);
          }
        });
      });
      proxy.$nextTick(() => {
        ;
        if (treeData.value[0].children && treeData.value[0].children[0]) {
          handleNodeClick(treeData.value[0].children[0]);
        }
      });
    });
}
function delCategry(item) {
  proxy
    .$confirm('提示', '确认删除此业务吗', {
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/businessServer/remove?ids=' + item.id).then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          getTreeData();
        }
      });
    });
}
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  viewBtn: true,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  dialogType: 'drawer',
  searchMenuSpan: 4,
  updateBtn: false,
  saveBtn: false,
  searchSpan: 4,
  menuWidth: 250,
  border: true,
  column: [
    {
      label: '标题',
      span: 24,
      prop: 'title',
      rules: [
        {
          required: true,
          message: '请输入标题',
          trigger: 'change',
        },
      ],
    },
    {
      label: '封面图',
      prop: 'homePictureIds',
      type: 'upload',
      value: [],
      dataType: 'string',
      listType: 'picture-card',

      limit: 1,
      loadText: '附件上传中，请稍等',
      span: 24,
      accept: 'image/png, image/jpeg,image/jpg',
      // align: 'center',
      uploadExceed: err => {
        console.log(err);
        ElMessage.error('只能上传一张图片');
      },
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      rules: [
        {
          required: true,
          message: '请上传封面图',
          trigger: 'blur',
        },
      ],
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'select',
      label: '类型',
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择类型',
        },
      ],
      dataType: 'string',
      display: true,
      prop: 'type',
      dicUrl: '/blade-system/dict/dictionary?code=businessSeverType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      width: 110,
    },
    {
      type: 'tree',
      label: '关联服务',
      span: 12,
      rules: [
        {
          required: true,
          message: '关联服务',
        },
      ],
        disabled:true,
      display: true,
      parent: false,
      multiple: true,
      checkStrictly: true,
      prop: 'labels',
      dataType: 'string',
      dicUrl: '/vt-admin/businessServer/tree',
      dicFormatter: res => {
        console.log(res);
        return res.data.map(item => {
          return {
            ...item,
            disabled: true,
          };
        });
      },
      props: {
        label: 'title',
        value: 'id',
        children: 'list',
      },
    },
    {
      label: '内容',
      prop: 'content',
      component: 'AvueUeditor',
      action: '/api/blade-resource/attach/upload',
      options: {
        action: '/api/blade-resource/attach/upload',
        accept: 'image/png, image/jpeg, image/jpg,.mp4',
      },
      propsHttp: {
        res: 'data',
        url: 'link',
      },

      rules: [
        {
          required: true,
          message: '请输入内容',
          trigger: 'blur',
        },
      ],
      hide: true,
      minRows: 6,
      span: 24,
    },
    {
      label: '是否发布',
      prop: 'discoverStatus',
      type: 'switch',
      width: 110,
      // display: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      label: '是否推荐',
      prop: 'isRecommend',
      type: 'switch',
      width: 110,
      display: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      label: '浏览量',
      prop: 'viewNumber',
      width: 110,
      display: false,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/discover/save';
const delUrl = '/api/vt-admin/discover/remove?ids=';
const updateUrl = '/api/vt-admin/discover/update';
const tableUrl = '/api/vt-admin/discover/pageForManager';
let params = ref({
  templateCategory: '',
});
let tableData = ref([{}]);

let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        labels:currentNode.value.id
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    
  };
  console.log(form);

  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}

function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function toDetail(row) {
  router.push({
    path: '/CRM/programme/compoents/addOrUpdateTemplate',
    query: {
      id: row.id,
      name: row.templateName,
    },
  });
}
function searchReset() {
  params.value = {};
  onLoad();
}
let addVisible = ref(false);

onMounted(() => {
  getTreeData();
});
// onActivated(() => {
//   onLoad();
//   getTreeData();
// });

function getTreeData() {
  axios.get('/api/blade-system/dict/dictionary?code=businessOpportunityType').then(res => {
    treeData.value = res.data.data;
    getCateGoryList();
  });
}
let currentNode = ref({});
function handleNodeClick(node) {
  currentNode.value = node;
  onLoad()
}

let crud = ref(null);
function handleUpdate(value) {
  form.value.discoverStatus = value;
  crud.value.rowUpdate();
}
function handleAdd(value) {
  form.value.discoverStatus = value;
  crud.value.rowSave();
}
function handleIsRecommendChange(item) {
  axios
    .post('/api/vt-admin/discover/recommend', {
      isRecommend: item.isRecommend,
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
function handleDiscoverStatusChange(item) {
  axios
    .post('/api/vt-admin/discover/publish', {
      discoverStatus: item.discoverStatus,
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
function beforeOpen(done,type) {
  if(type == 'add'){
    form.value.labels = currentNode.value.id;
  }
  done();
}
</script>

<style lang="scss" scoped>

.active{
  border: 1px solid var(--el-color-primary);
}
</style>
