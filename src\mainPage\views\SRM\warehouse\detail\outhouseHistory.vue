<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #orderNo="{ row }">
      <el-link type="primary" @click="toOrderDetail(row)">{{ row.orderNo }}</el-link>
    </template>
    <template #serialNumber="{ row }">
      <el-button type="primary" @click="editSerialNumber(row)" size="small">查看</el-button>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const route = useRoute();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  menu: false,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '出库单号',
      prop: 'storageCode',
      width: 250,
      overHidden: true,
    },

    {
      label: '关联客户',
      prop: 'customerName',
      search: true,
      overHidden: true,
      width: 300,
    },
    {
      label: '关联订单',
      prop: 'orderNo',
    },
    {
      label: '出库数量',
      prop: 'number',
      width:100,
    },
    {
      label: '出库单价',
      prop: 'costPrice',
    },
    {
      label: '序列号',
      prop: 'serialNumber',
      search: true,
      searchSpan: 4, width:100,
    },
    {
      label: '出库类型',
      prop: 'outStorageType',
      type: 'radio',

      dicData: [
        {
          value: 0,
          label: '订单出库',
        },
        {
          value: 1,
          label: '借测出库',
        },
        {
          value: 2,
          label: '其他',
        },
        {
          value: 3,
          label: '采购退货',
        },
        // {
        //   value: 4,
        //   label: '其他',
        // },
      ],
      rules: [{ required: true, message: '请选择入库类型' }],
    },
    {
      label: '出库时间',
      prop: 'createTime',
      type: 'date',
      component: 'wf-daterange-search',
      search: true,
      startPlaceholder: '开始日期',
      endPlaceholder: '结束日期',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      searchSpan: 5,
      // search: true,
    },

    {
      label: '出库人',
      prop: 'createName',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/inventoryRecord/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

// onMounted(() => {
//   onLoad();
// });
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sourceType: 1,
        inventoryId: route.query.id,
        startDate: params.value.createTime ? params.value.createTime[0] : '',
        endDate: params.value.createTime ? params.value.createTime[1] : '',
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function toOrderDetail(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: row.orderId,
      activeName,
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function editSerialNumber(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑序列号',
    option: {
      column: [
        {
          label: '序列号',
          prop: 'productSerialNumber',
          type: 'textarea',
          value: row.serialNumber,
          span: 24,
          readonly: true,
          rows: 8,
        },
      ],
    },
    callback(res) {
      res.close()
    },
  });
}
</script>

<style lang="scss" scoped></style>
