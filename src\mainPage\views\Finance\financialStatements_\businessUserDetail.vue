<template>
  <avue-crud
    :option="option"
    :data="tableData[0].children"
    v-model:page="page"
    v-model:search="params"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @search-reset="reset"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
    :span-method="spanMethod"
    :cell-style="cellStyle"
    :header-row-style="
      () => {
        return { 'font-weight': 'bolder' };
      }
    "
  >
  </avue-crud>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { randomLenNum } from '@/utils/util';
import moment from 'moment';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 12,
  searchSpan: 12,
  menuWidth: 270,
  border: true,
  menu: false,
  header: false,
  rowKey: 'userId',
  size: 'small',
  showSummary: true,
  sumColumnList: [
    { name: 'totalPrice', type: 'sum', decimals: 2 },
    { name: 'otherOutCome', type: 'sum', decimals: 2 },
    { name: 'sealsPrice', type: 'sum', decimals: 2 },
    { name: 'expensePrice', type: 'sum', decimals: 2 },
  ],
  //   rowParentKey: 'userId',
  column: [
    {
      label: '业务员',
      prop: 'businessUserName',
      width: 100,
      component: 'wf-user-drop',
      overHidden: true,
    },
    {
      label: '年份',
      prop: 'year',
      width: 100,
      type: 'year',
      format: 'YYYY',
      valueFormat: 'YYYY',
    },
    {
      label: '季度',
      prop: 'quarter',
      formatter: row => {
        return setQuarter(row.month);
      },
      width: 110,
    },
    {
      label: '月份',
      prop: 'month',
      width: 50,
    },
    {
      label: '合同金额',
      prop: 'totalPrice',
    },  {
      label: '报销费用',
      prop: 'expensePrice',
    },
    {
      label: '费销比',
      prop: 'otherOutCome',
    },
    {
      label: '初纯利',
      prop: 'sealsPrice',
    },
  
  ],
});
const props = defineProps(['businessUserName','userId','year'])
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
onMounted(() => {
    onLoad()
})
watch(() => props.businessUserName,() => {
    if(props.businessUserName) {
        onLoad()
    }
},{
    immediate:true
})
watch(() => props.userId,() => {
    if(props.userId) {
        onLoad()
    }
},{
    immediate:true
})
watch(() => props.year,() => {
    if(props.year) {
        onLoad()
    }
},{
    immediate:true
})
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/statistics/businessPersonComprehensiveStatistics';
let params = ref({
  year: '' + new Date().getFullYear(),
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
       businessUserName:props.businessUserName,
       userId:props.userId,
       year:props.year,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.map(item => {
        return {
          businessUser: item.userId,
          businessUserName: item.userName,
          ...item,
          totalPrice: item.children
            ?.reduce((total, child) => {
              total += child.totalPrice * 1;
              return total;
            }, 0)
            .toFixed(2),
          otherOutCome: item.children
            ?.reduce((total, child) => {
              total += child.otherOutCome * 1;
              return total;
            }, 0)
            .toFixed(2),
          expensePrice: item.children
            ?.reduce((total, child) => {
              total += child.expensePrice * 1;
              return total;
            }, 0)
            .toFixed(2),
          uuid: randomLenNum(10, true),
          children: item.children?.map(child => {
            return {
              ...child,
              businessUserName: item.userName,
              year: '' + item.year,
              uuid: randomLenNum(10, true),
            };
          }),
        };
      });
      console.log(tableData.value);

      // page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function setQuarter(month) {
  if (month >= 1 && month <= 3) {
    return '第一季度';
  } else if (month >= 4 && month <= 6) {
    return '第二季度';
  } else if (month >= 7 && month <= 9) {
    return '第三季度';
  } else if (month >= 10 && month <= 12) {
    return '第四季度';
  } else {
    return '';
  }
}

function reset() {
  params.value.year = '' + new Date().getFullYear();
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function spanMethod({ row, column, rowIndex, columnIndex }) {
  if (column.property == 'businessUserName') {
    if (rowIndex == 0) {
      return { rowspan: 12, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }
  if (column.property == 'year') {
    if (rowIndex == 0) {
      return { rowspan: 12, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }
  if (column.property == 'quarter') {
    if (rowIndex % 3 == 0 && rowIndex != 12) {
      return { rowspan: 3, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  // if(column.property == 'totalPrice' || column.property == 'otherOutCome' || column.property == 'expensePrice' || column.property == 'businessUserName'){
  return {
    // color: 'red',
    fontSize: '15px',
    // fontWeight: 'bold',
    // }
  };
}
let activeName = ref('all');
const currentTable = computed(() => {
  if (activeName.value == 'all') {
    return tableData.value;
  } else {
    return tableData.value.filter(item => item.uuid == activeName.value);
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-table__footer) {
  font-weight: bolder !important;
  font-size: 16px;

  .cell {
    color: var(--el-color-primary) !important;
  }
}
</style>
