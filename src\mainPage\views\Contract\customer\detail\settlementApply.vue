<template>
  <div class="settlement-apply-container">
    <el-form :model="form" label-width="150px" :rules="rules" ref="formRef">
      <!-- 合同信息 -->
      <div class="section-title">合同信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同名称:"  prop="contractName">
            <wfContractSelect v-if="!$route.query.id" v-model="form.sealContractId" Url="/api/vt-admin/sealContract/page?selectType=8" style="width: 100%;"></wfContractSelect>
            <span class="form-text" v-else>{{ props.detailForm.contractName }}</span>
          </el-form-item>
        </el-col>
          <!-- 合同总额 -->
        <el-col :span="12">
          <el-form-item label="合同总额:" prop="contractTotalPrice">
            <span class="form-text">{{ props.detailForm.contractTotalPrice }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管理费点数:" prop="managementFeePoints">
                <el-input v-model="form.managementFeePoints" placeholder="请输入管理费点数">
                  <template #append>
                    <span>%</span>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理费额:" prop="managementFeeAmount">
                <span class="form-text">{{ form.managementFeeAmount }}</span>
              </el-form-item>
            </el-col>
             <el-col :span="12">
              <el-form-item label="本合同已付管理费额:" prop="hasManageFee">
                <span class="form-text">{{ props.detailForm.hasManageFee }}</span>
              </el-form-item>
            </el-col>
             <el-col :span="12" v-if="(form.managementFeeAmount - (props.detailForm.hasManageFee || 0)) > 0">
              <el-form-item label="本合同可付管理费额:" prop="hasManageFee">
                <span class="form-text">{{ form.managementFeeAmount -( props.detailForm.hasManageFee || 0) }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <!-- 基本信息 -->
      <div class="section-title">基本信息</div>

      <el-row :gutter="20">
      
        <el-col :span="12">
          <el-form-item label="计划名称:" prop="planIds">
            <el-select
              @change="handlePlanChange($event)"
              multiple
              v-model="form.planIds"
              placeholder="请选择结算计划"
              clearable
              style="width: 100%"
            >
              <el-option
                :label="item.planName"
                v-for="item in planList"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划收款金额:" prop="planAmount">
            <span class="form-text">{{ form.planAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="税率:" prop="taxPoints">
            <el-select
              v-model="form.taxPoints"
              placeholder="请选择税点"
              clearable
              style="width: 100%"
            >
              <el-option
                :label="item.dictValue"
                v-for="item in taxRateData"
                :value="item.dictKey"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="税额:" prop="taxAmount">
            <span class="form-text">{{ form.taxAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20" v-if="(form.managementFeeAmount - (props.detailForm.hasManageFee || 0)) > 0">
        <el-col :span="24">
          <el-form-item label="本次付管理费金额:" prop="currentManagementFee">
            <div style="display: flex; flex: 1 1">
              <el-input
                v-model="form.currentManagementFee"
                @input="handleCurrentFeeInput"
                placeholder="本次付管理费金额"
              ></el-input>
              <el-input
                v-model="form.managementFeeRatio"
                @input="handleRatioInput"
                style="width: 200px"
                placeholder="比例"
              >
                <template #append>
                  <span>%</span>
                </template>
              </el-input>
            </div>
          </el-form-item>
        </el-col>
      
      </el-row>
      <el-row :gutter="20">
           <!-- 选择收款账号 -->
        <el-col :span="24">
          <el-form-item label="收款账号:" prop="collectionAccount">
            <el-select
              v-model="form.collectionAccount"
              placeholder="请选择收款账号"
              clearable
              style="width: 100%"
            >
              <el-option
                :label="item.abbreviation"
                v-for="item in accountList"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <!-- 结算信息 -->
      <div class="section-title">结算信息（非最终结算金额）</div>
      <el-row :gutter="20">
        <el-col :span="24">
          <div class="settlement-info">
            <div class="settlement-section">
              <div class="settlement-title">本次开票应包含税额：</div>
              <div class="settlement-content">
                <div class="settlement-item">
                  <span class="item-label">税额:</span>
                  <span class="item-value">+{{ form.taxAmount || 0 }}</span>
                </div>

                <div class="settlement-item total">
                  <span class="item-label">总计:</span>
                  <span class="item-value">{{ calculateInvoiceAmount() }}</span>
                </div>
              </div>
            </div>
            <div class="settlement-section">
              <div class="settlement-title">本次应收款：</div>
              <div class="settlement-content">
                <div class="settlement-item">
                  <span class="item-label">收款额:</span>
                  <span class="item-value">+{{ form.planAmount || 0 }}</span>
                </div>
                <div class="settlement-item" v-if="(form.managementFeeAmount - (props.detailForm.hasManageFee || 0)) > 0">
                  <span class="item-label">本次付管理费:</span>
                  <span class="item-value">-{{ form.currentManagementFee || 0 }}</span>
                </div>
                <div class="settlement-item total">
                  <span class="item-label">总计</span>
                  <span class="item-value">{{ calculateReceivableAmount() }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <!-- 按钮已移至抽屉footer插槽 -->
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, defineProps, defineEmits, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import wfContractSelect from '@/views/plugin/workflow/components/custom-fields/wf-contract-select/index.vue';
const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  },
  detailForm: Object,
  isApply: Boolean,
  isAudit: Boolean,
});

const emit = defineEmits(['submit-success','getDetailForm']);

// 表单数据
const form = reactive({
  contractTotalPrice: '',
  planIds: [],
  planAmount: '',
  taxPoints: '',
  taxAmount: '',
  managementFeePoints: '',
  managementFeeAmount: '',
  currentManagementFee: '',
  sealContractId:'',
  managementFeeRatio: '',
  collectionAccount: '',
});

// 计算并更新税额和管理费额
watch(
  () => [form.planAmount, form.taxPoints],
  ([planAmount, taxPoints]) => {
   
    if (planAmount && taxPoints) {
      // 计算税额
      form.taxAmount = ((planAmount / (1 + taxPoints / 100)) * (taxPoints / 100)).toFixed(2);
    } else {
      form.taxAmount = '0.00';
    }
  },
  { immediate: true, deep: true }
);

watch(
  () => [form.planAmount, form.managementFeePoints],
  ([planAmount, managementFeePoints]) => {
    if (managementFeePoints) {
      // 计算管理费额
      form.managementFeeAmount = (
        (Number(props.detailForm.contractTotalPrice) * Number(managementFeePoints)) /
        100
      ).toFixed(2);
      // 如果已有比例，则更新本次付管理费金额
      if (form.managementFeeRatio) {
        calculateCurrentFee();
      }
    } else {
      form.managementFeeAmount = '0.00';
    }
  },
  { immediate: true, deep: true }
);

watch(() => form.sealContractId,(val) => {
  if(val){
    emit('getDetailForm',val)
  }
})
const planList = computed(() => {
  return [
    ...props.detailForm.planCollectionVOList.filter(item => item.isCheck != 1),
    ...(props.rowData?.planVOList?.map(item => {
      return {
        planName: item.planName,
        id: item.planId,
      };
    }) || []),
  ];
});
// 监听rowData变化，更新表单数据
watch(
  () => props.rowData,
  newVal => {
    if (newVal && Object.keys(newVal).length > 0) {
      // 根据传入的行数据更新表单，用于编辑模式
      form.planAmount = newVal.applySettlementPrice || '';
      form.taxPoints = '' + parseFloat(newVal.taxPoints) || '';
      form.managementFeePoints = parseFloat(newVal.managementFeePoints) || '';
      // form.managementFeeAmount = newVal.managementFee || '';
      form.currentManagementFee = newVal.managementFee || '';
      form.sealContractId = newVal.sealContractId || '';

      form.planIds = newVal.planIds || [];
      nextTick(() => {
        calculateRatio();
      });
    }
  },
  { immediate: true, deep: true }
);
watch(() => props.detailForm,(val) => {
  
  if(val.managementFeePoints){
    form.managementFeePoints = parseFloat(val.managementFeePoints)
  }
},{
  deep: true,
  immediate:true
})

// 表单验证规则
const rules = {
  planIds: [{ required: true, message: '请选择结算计划', trigger: 'change' }],
  taxPoints: [{ required: true, message: '请选择税点', trigger: 'change' }],
  taxAmount: [{ required: true, message: '税额不能为空', trigger: 'blur' }],
  managementFeePoints: [{ required: true, message: '请输入管理费点数', trigger: 'blur' }],
  managementFeeAmount: [{ required: true, message: '管理费额不能为空', trigger: 'blur' }],
  currentManagementFee: [{ required: true, message: '请输入本次付管理费金额', trigger: 'blur' }],
  collectionAccount: [{ required: true, message: '请选择收款账号', trigger: 'change' }],
};

const formRef = ref(null);

// 计算管理费比例
const calculateRatio = () => {
  if (form.managementFeeAmount && form.currentManagementFee) {
    const ratio =
      (parseFloat(form.currentManagementFee) / parseFloat(form.managementFeeAmount)) * 100;
    form.managementFeeRatio = ratio.toFixed(2);
  }
};

// 根据比例计算本次付管理费金额
const calculateCurrentFee = () => {
  if (form.managementFeeAmount && form.managementFeeRatio) {
    const fee = (parseFloat(form.managementFeeAmount) * parseFloat(form.managementFeeRatio)) / 100;
    form.currentManagementFee = fee.toFixed(2);
  }
};

// 处理本次付管理费金额输入
const handleCurrentFeeInput = () => {
  // 当用户手动输入本次付管理费金额时，自动计算对应的比例
  calculateRatio();
};

// 处理比例输入
const handleRatioInput = () => {
  // 移除可能的百分号
  if (
    form.managementFeeRatio &&
    typeof form.managementFeeRatio === 'string' &&
    form.managementFeeRatio.includes('%')
  ) {
    form.managementFeeRatio = form.managementFeeRatio.replace('%', '');
  }
  // 当用户输入比例时，自动计算本次付管理费金额
  calculateCurrentFee();
};

// 监听管理费点数变化
const watchManagementFeePoint = () => {
  if (form.managementFeePoints && form.planAmount) {
    const amount = (parseFloat(form.managementFeePoints) / 100) * parseFloat(form.planAmount);
    form.managementFeeAmount = amount.toFixed(2);
    // 如果已有比例，则更新本次付管理费金额
    if (form.managementFeeRatio) {
      calculateCurrentFee();
    }
  }
};

// 监听本次付管理费金额变化
const watchCurrentManagementFee = () => {
  calculateRatio();
};

// 计算本次应开票金额
const calculateInvoiceAmount = () => {
  const taxAmount = parseFloat(form.taxAmount || 0);

  const invoiceAmount = taxAmount;
  return invoiceAmount.toFixed(2);
};

// 计算本次应收款
const calculateReceivableAmount = () => {
  // 本次应收款 = 收款额 - 本次付管理费
  const planAmount = parseFloat(form.planAmount || 0);
  const currentManagementFee = parseFloat(form.currentManagementFee || 0);

  const receivableAmount = planAmount - currentManagementFee;
  return receivableAmount.toFixed(2);
};

// 提交表单
const submitForm = value => {
  formRef.value.validate(valid => {
    if (valid) {
      const data = {
        ...form,
        planDTOList: form.planIds.map(planId => {
          return {
            planId: planId,
          };
        }),
        shouldSettmentPrice: calculateReceivableAmount(),
        invoicePrice: calculateInvoiceAmount(),
        managementFee: form.currentManagementFee,
        sealContractId: props.detailForm.id,
        isDeductManagementFee: 1,
        settlementStatus: value,
      };

      // 如果是编辑模式（rowData有id），则调用更新接口
      if (props.rowData && props.rowData.id) {
        data.id = props.rowData.id; // 设置ID用于更新
        axios.post('/api/vt-admin/sealContractSettlement/update', data).then(res => {
          emit('submit-success');
        });
      } else {
        // 否则调用保存接口
        axios.post('/api/vt-admin/sealContractSettlement/save', data).then(res => {
          emit('submit-success');
        });
      }
    } else {
      ElMessage.error('请填写必填项');
      return false;
    }
  });
};

// 重置表单
const resetForm = () => {
  formRef.value.resetFields();
};

onMounted(() => {
  // 可以在这里加载初始数据
  // 初始化时，如果已有管理费额和比例，计算本次付管理费金额
  if (form.managementFeeAmount && form.managementFeeRatio) {
    calculateCurrentFee();
  }
});

function handlePlanChange(val) {
  // 计算选中计划的总金额
  let totalAmount = 0;
  const selectedPlans = props.detailForm.planCollectionVOList.filter(item => val.includes(item.id));
  selectedPlans.forEach(plan => {
    totalAmount += Number(plan.planCollectionPrice);
  });
  form.planAmount = totalAmount.toFixed(2);
}
// 获取税率数据
let taxRateData = ref([]);
// 获取收款账号数据
let accountList = ref([]);
onMounted(() => {
  getTaxRate();
  getAccountList();
});
const getTaxRate = () => {
  axios.get('/api/blade-system/dict/dictionary?code=tax').then(res => {
    taxRateData.value = res.data.data;
  });
};
const getAccountList = () => {
  axios.get('/api/vt-admin/companyAccount/page?size=1000&current=1').then(res => {
    accountList.value = res.data.data.records;
  });
}
defineExpose({
  submitForm,
  resetForm,
  handlePlanChange,
});
</script>

<style lang="scss" scoped>
.settlement-apply-container {
  padding: 20px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 15px 0;
  padding-left: 10px;
  border-left: 4px solid var(--el-color-primary);
}

.settlement-info {
  background-color: #f8f9fa;
  border-radius: 4px;
  padding: 15px;
  margin-bottom: 20px;
}

.settlement-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.settlement-title {
  font-weight: bold;
  margin-bottom: 10px;
}

.settlement-content {
  padding-left: 20px;
}

.settlement-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;

  &.total {
    font-weight: bold;
    border-top: 1px solid #dcdfe6;
    padding-top: 5px;
    margin-top: 5px;
  }
}

.item-label {
  color: #606266;
}

.item-value {
  font-weight: 500;
}

.form-text {
  display: inline-block;
  min-height: 32px;
  line-height: 32px;
  padding: 0 15px;
  color: #606266;
  width: 100%;
  border-radius: 4px;
  background-color: #f5f7fa;
}
</style>
