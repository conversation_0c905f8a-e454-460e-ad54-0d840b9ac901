<template>
  <div>
    <div v-for="i in fileList" style="display: flex;justify-content: flex-start;align-items: center;gap: 10px;"  :key="i.fileName">
      <el-text size="small" title="双击预览文件，单击下载文件，如果无法预览请点击文件下载" style="cursor: pointer" @dblclick.prevent="viewFile(i)" @click.prevent="downLoadFile(i)" text type="primary">
        <span :title="i.originalName">{{
        i.originalName.slice(0,13)
      }}</span>
      </el-text>
      <el-icon title="如果无法预览请点击文件下载" text type="success" icon="view" @click="viewFile(i)" style="color: var(--el-color-success);cursor: pointer;">
          <View></View>
      </el-icon>
      <el-icon @click.prevent="downLoadFile(i)" style="color: var(--el-color-success);cursor: pointer;">

          <Download></Download>
      </el-icon>
  
    </div>
  </div>
</template>

<script setup>
import { loadFile } from '@/utils/file.js';
import config from '@/config/website';
import { Base64, encode } from 'js-base64';


defineProps({
  fileList: Array,
});
let timer = null
function downLoadFile(item) {
  clearTimeout(timer);
 timer =  setTimeout(() => {
    // 下载文件
  loadFile(item.link, item.originalName);
  }, 300);
}
function viewFile(row) {
  clearTimeout(timer)
  // 预览文件
  window.open(config.previewFileUrl + 'onlinePreview?url=' + encodeURIComponent(encode(row.link)));
}
</script>

<style lang="scss" scoped></style>
