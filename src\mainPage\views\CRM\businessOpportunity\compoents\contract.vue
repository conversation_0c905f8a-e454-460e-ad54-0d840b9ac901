<template>
    <div>
        <el-empty description="暂无数据" v-if="!props.sealContractId"></el-empty>
        <contractBaseInfo :form="form" v-else></contractBaseInfo>
    </div>
</template>

<script setup>
import contractBaseInfo from '@/views/Contract/customer/detail/contractBaseInfo.vue';
import { watch } from 'vue';
const props = defineProps(['sealContractId'])
let form = ref({})
watch(() => props.sealContractId,() => {
    if(props.sealContractId){
        getDetail()
    }
   
},{
    immediate: true
})
function getDetail() {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: props.sealContractId,
      },
    })
    .then(res => {
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
    });
}
</script>

<style lang="scss" scoped>

</style>