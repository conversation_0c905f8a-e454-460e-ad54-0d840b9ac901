import { createRouter, createWebHistory,createWebHashHistory } from 'vue-router'
import Home from '../views/Home.vue'
import Products from '../views/Products.vue'
import Brand from '../views/Brand.vue'
import Knowledge from '../views/Knowledge.vue'
import Training from '../views/Training.vue'
import HR from '../views/HR.vue'
import About from '../views/About.vue'
import Contact from '../views/Contact.vue'
// 假设 More 组件存在，如果不存在需要创建
import More from '../views/More.vue'
// 更多支持下拉菜单页面
import MiniProgram from '../views/MiniProgram.vue'
import FundingSupport from '../views/FundingSupport.vue'
import BusinessSolutions from '../views/BusinessSolutions.vue'

const routes = [
  {
    path: '/',
    // redirect: '/brand',
    name: 'Home',
    component: Home
  },
  {
    path: '/products',
    name: 'Products',
    component: Products
  },
  {
    path: '/brand',
    name: 'Brand',
    component: Brand
  },

  {
    path: '/knowledge',
    name: 'Knowledge',
    component: Knowledge
  },
  {
    path: '/training',
    name: 'Training',
    component: Training
  },
  {
    path: '/hr',
    name: 'HR',
    component: HR
  },
  {
    path: '/about',
    name: 'About',
    component: About
  },
  {    path: '/contact',    name: 'Contact',    component: Contact  },
  {
    path: '/more',
    name: 'More',
    component: More
  },
  {
    path: '/more/mini-program',
    name: 'MiniProgram',
    component: MiniProgram
  },
  {
    path: '/more/funding-support',
    name: 'FundingSupport',
    component: FundingSupport
  },
  {
    path: '/more/business-solutions',
    name: 'BusinessSolutions',
    component: BusinessSolutions
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

export default router