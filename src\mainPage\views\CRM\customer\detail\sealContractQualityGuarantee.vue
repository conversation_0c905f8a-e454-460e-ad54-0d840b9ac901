<template>
  
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
   
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    menuWidth: 270,
    menu:false,
    border: true,
    column: [
    // {
    //   label: '关联合同',
    //   prop: 'sealContractName',
      
    //   overHidden:true,
    // },
    {
      label: '关联合同',
      prop: 'contractName',
      search:true,
      // hide:true,
      overHidden:true,
    },
    {
      type: 'date',
      label: '开始质保日期',
      span: 24,
    

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },

    {
      type: 'input',
      label: '质保周期',
     
      span: 24,
      display: true,
      prop: 'cycleTime',
      formatter: (row) => {
        return row.cycleNumber + `${['天','月','年'][row.cycleType]}`
      }
    },
    {
      type: 'date',
      label: '质保到期时间',
      span: 24,
     

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'overDate',
    },
    {
      type: 'select',
      label: '提醒时间',
      
      span: 24,
      display: true,
      props: {
        value: 'value',
        label: 'label',
      },
      dicData: (() => {
        let arr = [];
        for (let i = 1; i <= 31; i++) {
          arr.push({ label: '到期' + i + '天前', value: i });
        }
        return arr;
      })(),
      prop: 'beforeDays',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
    },
  ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  const props = defineProps({
    contractCode: String,
    supplierName: String,
    sealContractInvoiceId: String,
    customerId: String,
    sealContractId: String,
    offerId: String,
  });
  const addUrl = ''
  const delUrl = ''
  const updateUrl = ''
  const tableUrl = '/api/vt-admin/businessOpportunityProduct/pageForRenew'
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
         
          size,
          current,
          ...params.value,
          customerId: props.customerId,
          businessSourceType:2
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        page.value.total = res.data.data.total;
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  </script>
  
  <style lang="scss" scoped></style>
  