<template>
  <avue-crud
    :option="option"
    :data="props.tableData"
    v-model:page="page"
    v-model:search="params"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @row-del="rowDel"
    v-model="form"
  >
    <template #menu-left>
      <el-button
        type="primary"
        @click="$refs.crud.rowAdd()"
        v-if="props.businessUser && props.date"
        icon="plus"
        >新增</el-button
      >
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,

  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  showSummary: true,
  sumColumnList: [
    {label:'小计:',name: 'amount', type: 'sum' },
   
  ],
  column: [
    {
      label: '费用金额',
      prop: 'amount',
      width: 250,
      type:"number",
      overHidden: true,
      rules:[{
        required:true,
        message:'请输入费用金额',
        trigger:'blur'
      }]
    },
    {
      label: '费用说明',
      prop: 'remark',
      span:24,
      type:'textarea',
      rules:[{
        required:true,
        message:'请输入费用说明',
        trigger:'blur'
      }]
    },
    // {
    //   label: '费用日期',
    //   prop: 'month',
    //   type: 'month',
    //   format: 'YYYY-MM',
    //   valueFormat: 'YYYY-MM',
    // },
    // {
    //   label: '关联业务员',
    //   prop: 'businessUser',
    //   component: 'wf-user-drop',
    //   hide: true,
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 50000,
  currentPage: 1,
  total: 0,
});
const props = defineProps(['businessUser', 'date','tableData']);
const emits = defineEmits(['addSuccess']);
const addUrl = '/api/vt-admin/profitBusinessUserExtra/save';
const delUrl = '/api/vt-admin/profitBusinessUserExtra/remove?ids=';
const updateUrl = '/vt-admin/profitBusinessUserExtra/update';
const tableUrl = '/api/vt-admin/profitBusinessUserExtra/page';
let params = ref({});

let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function rowSave(form, done, loading) {
  const data = {
    ...form,
    month: props.date,
    businessUser: props.businessUser,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
       
        done();
        emits('addSuccess')
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emits('addSuccess')
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        emits('addSuccess')
      });
    })
    .catch(() => {});
}

</script>

<style lang="scss" scoped></style>
