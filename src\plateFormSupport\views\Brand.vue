<template>
  <div class="h-screen bg-gray-50 flex flex-col">
    <!-- 顶部导航栏 -->
    <div class="h-16 bg-white shadow-sm border-b border-gray-200 flex items-center px-6">
      <div class="flex items-center gap-4">
        <h1 class="text-xl font-bold text-gray-800">品牌库</h1>
        <div v-if="selectedCategory" class="flex items-center gap-2 text-sm text-gray-500">
          <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M9 18l6-6-6-6" />
          </svg>
          <span class="text-gray-700">{{ selectedCategory.name }}</span>
        </div>
      </div>
      <button
        v-if="selectedCategory"
        @click="backToCategories"
        class="ml-auto inline-flex items-center gap-2 px-4 py-2 text-sm text-gray-600 hover:text-blue-600 transition-colors"
      >
        <svg class="w-4 h-4" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M15 18l-6-6 6-6" />
        </svg>
        返回分类
      </button>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex-1 flex overflow-hidden">
      <!-- 左侧：品牌分类 -->
      <div class="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-100">
          <h2 class="text-lg font-semibold text-gray-800">品牌分类</h2>
        </div>
        <div class="flex-1 overflow-y-auto">
          <div class="p-2">
            <div
              v-for="cat in categories"
              :key="cat.id"
              @click="selectCategory(cat)"
              :class="[
                'p-4 mb-2 rounded-lg cursor-pointer transition-all duration-200 border',
                selectedCategory?.id === cat.id
                  ? 'bg-blue-50 border-blue-200 text-blue-700'
                  : 'bg-white border-gray-200 hover:bg-gray-50 hover:border-gray-300'
              ]"
            >
              <div class="flex items-center gap-3">
                <div
                  class="w-3 h-3 rounded-full flex-shrink-0"
                  :style="{ background: cat.dot }"
                ></div>
                <div class="flex-1 min-w-0">
                  <h3 class="font-medium truncate">{{ cat.name }}</h3>
                  <p class="text-sm text-gray-500 mt-1">{{ cat.brandCount ?? 0 }} 个品牌</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 中间：品牌列表 -->
      <div v-if="selectedCategory" class="w-80 bg-white border-r border-gray-200 flex flex-col">
        <div class="p-4 border-b border-gray-100">
          <h2 class="text-lg font-semibold text-gray-800 mb-3">品牌列表</h2>
          <div class="relative">
            <input
              v-model="brandKeyword"
              type="text"
              placeholder="搜索品牌…"
              class="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-1 focus:ring-blue-500 text-sm"
            />
            <svg
              class="w-4 h-4 text-gray-400 absolute left-3 top-1/2 -translate-y-1"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
            >
              <circle cx="11" cy="11" r="8" />
              <path d="M21 21l-4.3-4.3" />
            </svg>
          </div>
        </div>
        <div class="flex-1 overflow-y-auto">
          <div class="divide-y divide-gray-100">
            <div
              v-for="brand in filteredBrands"
              :key="brand.name"
              @click="selectBrand(brand)"
              :class="[
                'p-4 cursor-pointer transition-colors flex items-center gap-3',
                currentBrand?.name === brand.name
                  ? 'bg-blue-50 text-blue-700'
                  : 'hover:bg-gray-50'
              ]"
            >
              <div class="w-10 h-10 rounded-lg overflow-hidden flex-shrink-0">
                <img
                  v-if="brand.logoUrl"
                  :src="brand.logoUrl"
                  :alt="brand.name"
                  class="w-full h-full object-cover"
                />
                <div
                  v-else
                  class="w-full h-full bg-gradient-to-br from-blue-500 to-indigo-500 text-white grid place-items-center text-sm font-bold"
                >
                  {{ brand.name.slice(0, 1) }}
                </div>
              </div>
              <div class="flex-1 min-w-0">
                <div class="font-medium truncate">{{ brand.name }}</div>
                <div class="text-sm text-gray-500 truncate">{{ brand.slogan }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：产品内容 -->
      <div v-if="currentBrand" class="flex-1 bg-white flex flex-col">
        <!-- Tab 导航 -->
        <div class="border-b border-gray-200 bg-white">
          <div class="px-6 pt-4">
            <div class="flex gap-6">
              <button
                v-for="tab in tabs"
                :key="tab.key"
                @click="activeTab = tab.key"
                :class="[
                  'relative py-3 text-sm font-medium transition-colors',
                  activeTab === tab.key ? 'text-blue-600' : 'text-gray-600 hover:text-gray-900'
                ]"
              >
                {{ tab.label }}
                <span
                  v-if="activeTab === tab.key"
                  class="absolute left-0 -bottom-px h-0.5 w-full bg-blue-600"
                />
              </button>
            </div>
          </div>
        </div>

        <!-- Tab 内容 -->
        <div class="flex-1 overflow-y-auto p-6">
          <!-- 品牌介绍 -->
          <div v-if="activeTab === 'intro'">
            <div class="prose max-w-none">
              <div class="text-gray-700 leading-7" v-html="brandIntroHtml"></div>
            </div>
          </div>

          <!-- 产品列表 -->
          <div v-else-if="activeTab === 'products'">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-3">
              <div
                v-for="product in currentBrand.products"
                :key="product.id"
                @click="openProductDetail(product)"
                class="bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-lg transition-all duration-200 overflow-hidden cursor-pointer group"
              >
                <!-- 产品图片 -->
                <div class="relative h-32 bg-gray-50">
                  <ElImage
                    preview-teleported
                    :src="
                      product.coverUrl ||
                      'https://img1.baidu.com/it/u=415449740,540746270&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500'
                    "
                    :alt="product.productName"
                    :preview-src-list="[
                      product.coverUrl ||
                        'https://img1.baidu.com/it/u=415449740,540746270&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500',
                    ]"
                    fit="cover"
                    class="w-full h-full group-hover:scale-105 transition-transform duration-200"
                  />
                  <!-- 价格标签 -->
                  <div v-if="product.costPrice" class="absolute top-2 right-2 bg-orange-500 text-white text-xs px-2 py-1 rounded-full">
                    ¥{{ product.costPrice }}
                  </div>
                </div>

                <!-- 产品信息 -->
                <div class="p-3">
                  <!-- 产品名称和型号在一排显示 -->
                  <div class="text-sm font-medium text-gray-800 line-clamp-1">
                    {{ product.productName }} {{ product.productSpecification }}
                  </div>
                </div>
              </div>
            </div>
            
            <!-- 无产品提示 -->
            <div v-if="currentBrand.products.length === 0" class="text-center py-16">
              <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <h3 class="text-lg font-semibold text-gray-600 mb-2">暂无产品</h3>
              <p class="text-gray-500">该品牌暂未上传产品</p>
            </div>
          </div>

          <!-- 培训资料 -->
          <div v-else>
            <div class="space-y-4">
              <a
                v-for="(d, i) in currentBrand?.resources || []"
                :key="i"
                :href="d.url || '#'"
                target="_blank"
                class="flex items-center gap-4 p-4 bg-gray-50 hover:bg-blue-50 transition-colors rounded-lg border border-gray-200 hover:border-blue-200"
              >
                <div class="w-12 h-12 rounded-lg bg-gradient-to-br from-blue-500 to-indigo-600 text-white grid place-items-center">
                  <svg class="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" />
                    <path d="M14 2v6h6" />
                  </svg>
                </div>
                <div class="flex-1 min-w-0">
                  <div class="font-medium text-gray-800 truncate">{{ d.filesName }}</div>
                  <div class="text-sm text-blue-600 mt-1" v-if="d.title">{{ d.title }}</div>
                  <div class="text-xs text-gray-500 mt-1">{{ d.type }} · {{ d.size }}</div>
                </div>
                <span class="px-3 py-1 bg-blue-100 text-blue-700 rounded text-sm font-medium hover:bg-blue-200 transition-colors">下载</span>
              </a>
              
              <!-- 无资料提示 -->
              <div v-if="(currentBrand?.resources || []).length === 0" class="text-center py-16">
                <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 class="text-lg font-semibold text-gray-600 mb-2">暂无培训资料</h3>
                <p class="text-gray-500">该品牌暂未上传培训资料</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧空状态 -->
      <div v-else-if="selectedCategory" class="flex-1 bg-white flex items-center justify-center">
        <div class="text-center">
          <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h3 class="text-lg font-semibold text-gray-600 mb-2">请选择品牌</h3>
          <p class="text-gray-500">从左侧列表中选择一个品牌查看详情</p>
        </div>
      </div>

      <!-- 初始状态 -->
      <div v-else class="flex-1 bg-white flex items-center justify-center">
        <div class="text-center">
          <svg class="w-20 h-20 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
          <h2 class="text-xl font-semibold text-gray-700 mb-3">欢迎使用品牌库</h2>
          <p class="text-gray-500 mb-6">从左侧选择品牌分类开始浏览</p>
        </div>
      </div>
    </div>
  </div>

  <!-- 产品详情弹窗 -->
  <div
    v-if="selectedProduct"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
    @click="closeProductDetail"
  >
    <div
      class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
      @click.stop
    >
      <div class="p-6">
        <div class="flex justify-between items-start mb-6">
          <h2 class="text-xl font-bold text-blue-600">{{ selectedProduct.productName }} - 详情</h2>
          <button
            @click="closeProductDetail"
            class="text-gray-500 hover:text-gray-700"
          >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- 产品宣传图 -->
        <div class="mb-6" v-if="selectedProduct.productFileUrls && selectedProduct.productFileUrls.length > 0">
          <h3 class="text-base font-bold mb-3">产品宣传图</h3>
          <el-carousel height="350px" indicator-position="outside" arrow="hover" class="rounded-lg overflow-hidden border border-gray-200">
            <el-carousel-item v-for="image in selectedProduct.productFileUrls" :key="image.id">
              <div class="h-full flex items-center justify-center bg-gray-50">
                <ElImage
                  :src="image.link"
                  :alt="selectedProduct.productName"
                  class="max-w-full max-h-full object-contain"
                  :preview-src-list="selectedProduct.productFileUrls.map(img => img.link)"
                  fit="contain"
                />
              </div>
            </el-carousel-item>
          </el-carousel>
        </div>

        <div class="grid grid-cols-1 gap-6">
          <div>
            <div class="grid grid-cols-2 gap-x-8 gap-y-4 mb-6">
              <div>
                <p class="text-sm text-gray-500">产品编号:</p>
                <p class="text-base font-medium">{{ selectedProduct.productCode }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">产品名称:</p>
                <p class="text-base font-medium">{{ selectedProduct.productName }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">品牌:</p>
                <p class="text-base font-medium">{{ selectedProduct.productBrand }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">单位:</p>
                <p class="text-base font-medium">{{ selectedProduct.unitName }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">规格型号:</p>
                <p class="text-base font-medium">{{ selectedProduct.productSpecification }}</p>
              </div>
              <div v-if="selectedProduct.costPrice">
                <p class="text-sm text-gray-500">成本价:</p>
                <p class="text-base font-medium text-orange-600">¥{{ selectedProduct.costPrice }}</p>
              </div>
            </div>

            <!-- 产品参数 -->
            <div class="mb-6" v-if="selectedProduct.productPropertyVos && selectedProduct.productPropertyVos.length > 0">
              <h3 class="text-base font-bold mb-3">产品参数</h3>

              <div v-for="(property, index) in selectedProduct.productPropertyVos" :key="index" class="mb-4">
                <p class="text-sm text-gray-700 mb-2">{{ property.propertyName }}</p>
                <div class="flex flex-wrap items-center gap-4">
                  <template v-if="property.propertyValues && property.propertyValues.length > 0">
                    <label v-for="value in property.propertyValues" :key="value.id" class="inline-flex items-center">
                      <input
                        :type="property.selectType === 'single' ? 'radio' : 'checkbox'"
                        :name="'property_' + property.id"
                        :value="value.value"
                        class="form-radio text-blue-600">
                      <span class="ml-2 text-sm">{{ value.value }}</span>
                    </label>
                  </template>
                  <span v-else class="text-sm text-gray-600">{{ property.propertyValue }}</span>
                </div>
              </div>
            </div>

            <!-- 商品描述 -->
            <div class="mb-6">
              <h3 class="text-base font-bold mb-3">商品描述</h3>
              <p class="text-sm text-gray-600">
                {{ selectedProduct.description  }}
              </p>
            </div>

            <!-- 用途 -->
            <div class="mb-6">
              <h3 class="text-base font-bold mb-3">用途</h3>
              <p class="text-sm text-gray-600">
                {{ selectedProduct.purpose  }}
              </p>
            </div>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="flex justify-end space-x-4 mt-6">
          <button
            @click="closeProductDetail"
            class="px-6 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200"
          >
            取消
          </button>
          <button class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200">
            确认
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted } from 'vue';
import axios from '../axios';
import { ElCarousel, ElCarouselItem, ElImage } from 'element-plus';

// 渐变色盘（为每个分类生成装饰色）
const palettes = [
  {
    bg: `linear-gradient(135deg, rgba(59,130,246,.88), rgba(99,102,241,.88))`,
    dot: 'linear-gradient(135deg,#60a5fa,#a78bfa)',
  },
  {
    bg: `linear-gradient(135deg, rgba(16,185,129,.88), rgba(59,130,246,.88))`,
    dot: 'linear-gradient(135deg,#34d399,#60a5fa)',
  },
  {
    bg: `linear-gradient(135deg, rgba(234,88,12,.88), rgba(147,51,234,.88))`,
    dot: 'linear-gradient(135deg,#fb923c,#a78bfa)',
  },
  {
    bg: `linear-gradient(135deg, rgba(99,102,241,.88), rgba(236,72,153,.88))`,
    dot: 'linear-gradient(135deg,#818cf8,#f472b6)',
  },
  {
    bg: `linear-gradient(135deg, rgba(2,132,199,.88), rgba(6,95,70,.88))`,
    dot: 'linear-gradient(135deg,#38bdf8,#10b981)',
  },
  {
    bg: `linear-gradient(135deg, rgba(79,70,229,.88), rgba(15,23,42,.88))`,
    dot: 'linear-gradient(135deg,#818cf8,#334155)',
  },
];

// 真实数据：分类与品牌
const categories = ref([]);
const brandList = ref([]);

// 交互状态
const selectedCategory = ref(null);
const brandKeyword = ref('');
const currentBrand = ref(null);
const activeTab = ref('intro');
const selectedProduct = ref(null);

const tabs = [
  { key: 'intro', label: '品牌介绍' },
  { key: 'products', label: '产品列表' },
  { key: 'resources', label: '培训资料' },
];

// 加载分类
const fetchCategories = async () => {
  try {
    const res = await axios.get('/api/vt-platform/supplierLabels/list');
    if (res?.data?.code === 200) {
      const arr = Array.isArray(res.data.data) ? res.data.data : [];
      categories.value = arr.map((item, idx) => {
        const { bg, dot } = palettes[idx % palettes.length];
        return {
          id: item.id ?? item.labelId ?? item.categoryId ?? item.value,
          name: item.name ?? item.labelName ?? item.categoryName ?? item.text ?? '未命名',
          tag: item.labelName,
          bg: item.labelUrl || bg,
          dot,
          brandCount: 0,
        };
      });
    }
  } catch (e) {
    console.error('获取品牌分类失败', e);
  }
};

// 加载某分类下的品牌
const fetchBrandsByCategory = async categoryId => {
  try {
    const res = await axios.get('/api/vt-platform/supplier/supplierByLabel', {
      params: { id: categoryId },
    });
    if (res?.data?.code === 200) {
      const list = Array.isArray(res.data.data) ? res.data.data : res.data.data?.records || [];
      const mapped = list.map(item => ({
        // 兼容多种后端字段
        id: item.id ?? item.brandId ?? item.supplierId,
        name: item.name ?? item.brandName ?? item.supplierName ?? '未知品牌',
        slogan: item.slogan ?? item.brief ?? item.remark ?? '',
        desc: item.introduce ?? item.desc ?? item.description ?? '暂无简介',
        logoUrl: item.logoUrl,
        products: [],
        resources: [],
      }));
      brandList.value = mapped;
      // 更新分类品牌数量
      const idx = categories.value.findIndex(c => c.id === categoryId);
      if (idx > -1) categories.value[idx].brandCount = mapped.length;
      // 默认选中第一个
      currentBrand.value = mapped[0] || null;
      if (currentBrand.value?.id) {
        // 选中首个品牌后，拉取产品与资料
        fetchSupplierProducts(currentBrand.value.id);
        fetchSupplierFiles(currentBrand.value.id);
      }
    }
  } catch (e) {
    console.error('获取品牌列表失败', e);
    brandList.value = [];
  }
};

// 供应商产品
const fetchSupplierProducts = async supplierId => {
  try {
    const params = { supplierId, current: 1, size: 100 };
    const res = await axios.get('/api/vt-platform/productSupplier/page', { params });
    if (res?.data?.code === 200) {
      const rows = Array.isArray(res.data.data) ? res.data.data : res.data.data?.records || [];
      const mapped = rows;
      currentBrand.value && (currentBrand.value.products = mapped);
    }
  } catch (e) {
    console.error('获取供应商产品失败', e);
    currentBrand.value && (currentBrand.value.products = []);
  }
};

// 供应商产品资料
const fetchSupplierFiles = async supplierId => {
  try {
    const params = { supplierId, current: 1, size: 100 };
    const res = await axios.get('/api/vt-platform/supplierFiles/page', { params });
    if (res?.data?.code === 200) {
      const rows = Array.isArray(res.data.data) ? res.data.data : res.data.data?.records || [];
      const mapped = rows.map(item => ({
        title: item.title ?? item.fileName ?? '资料',
        filesName: item.filesName ?? '',
        type: item.type ?? item.fileType ?? '文件',
        size:
          item.sizeText ??
          item.fileSizeText ??
          (item.fileSize ? `${(item.fileSize / 1024).toFixed(2)} MB` : ''),
        url: item.url ?? item.fileUrl ?? item.path ?? '#',
      }));
      currentBrand.value && (currentBrand.value.resources = mapped);
    }
  } catch (e) {
    console.error('获取供应商资料失败', e);
    currentBrand.value && (currentBrand.value.resources = []);
  }
};

const selectCategory = cat => {
  selectedCategory.value = cat;
  activeTab.value = 'intro';
  brandKeyword.value = '';
  brandList.value = [];
  currentBrand.value = null;
  fetchBrandsByCategory(cat.id);
};

const backToCategories = () => {
  selectedCategory.value = null;
  currentBrand.value = null;
  brandKeyword.value = '';
};

const filteredBrands = computed(() => {
  const list = brandList.value || [];
  if (!brandKeyword.value.trim()) return list;
  return list.filter(b => (b.name || '').includes(brandKeyword.value.trim()));
});

const selectBrand = b => {
  currentBrand.value = b;
  activeTab.value = 'intro';
  if (b?.id) {
    fetchSupplierProducts(b.id);
    fetchSupplierFiles(b.id);
  }
};

// 允许的富文本标签与属性（简易白名单）
function sanitizeHtml(unsafeHtml) {
  if (!unsafeHtml) return '';
  const ALLOWED_TAGS = new Set([
    'p', 'br', 'strong', 'b', 'em', 'i', 'u', 's', 'ul', 'ol', 'li', 'blockquote',
    'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'pre', 'code', 'table', 'thead', 'tbody',
    'tr', 'td', 'th', 'hr', 'a', 'img', 'span', 'div',
  ]);
  const ALLOWED_ATTRS = { a: ['href', 'title', 'target', 'rel'], img: ['src', 'alt', 'title'] };
  const container = document.createElement('div');
  container.innerHTML = unsafeHtml;
  const walk = (node, parent) => {
    const children = Array.from(node.childNodes);
    for (const child of children) {
      if (child.nodeType === 1) {
        // ELEMENT_NODE
        const tag = child.nodeName.toLowerCase();
        if (!ALLOWED_TAGS.has(tag)) {
          // 用其子节点替换自身
          while (child.firstChild) node.insertBefore(child.firstChild, child);
          node.removeChild(child);
          continue;
        }
        // 清理属性
        const attrs = Array.from(child.attributes);
        for (const { name, value } of attrs) {
          const lower = name.toLowerCase();
          const allowed = (ALLOWED_ATTRS[tag] || []).includes(lower);
          if (lower.startsWith('on') || /javascript:/i.test(value)) {
            child.removeAttribute(name);
            continue;
          }
          if (tag === 'a' && lower === 'href') {
            if (!/^https?:|^mailto:|^tel:/i.test(value)) {
              child.setAttribute('href', '#');
            }
            child.setAttribute('rel', 'noopener noreferrer nofollow');
            child.setAttribute('target', '_blank');
            continue;
          }
          if (tag === 'img' && lower === 'src') {
            if (!/^https?:/i.test(value)) {
              child.removeAttribute('src');
              continue;
            }
          }
          if (!allowed && lower !== 'class' && lower !== 'style') {
            child.removeAttribute(name);
          }
        }
        walk(child, node);
      } else if (child.nodeType === 8) {
        // COMMENT_NODE
        node.removeChild(child);
      }
    }
  };
  walk(container, null);
  return container.innerHTML;
}

// 计算后的品牌介绍富文本
const brandIntroHtml = computed(() => {
  const src = currentBrand.value?.desc || '';
  const html = sanitizeHtml(src);
  return html || '<p>暂无简介</p>';
});

const openProductDetail = (product) => {
  // 获取产品详情
  fetchProductDetail(product.id)
}

// 获取产品详情
const fetchProductDetail = async (productId) => {
  try {
    const response = await axios.get(`/api/vt-platform/productSupplier/detail?id=${productId}`)
    if (response.data && response.data.code === 200) {
      selectedProduct.value = response.data.data
    } else {
      console.error('获取产品详情失败:', response.data.msg)
    }
  } catch (error) {
    console.error('获取产品详情出错:', error)
  }
}

const closeProductDetail = () => {
  selectedProduct.value = null
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

onMounted(() => {
  fetchCategories();
});
</script>

<style scoped>
.prose :where(h3) {
  margin: 0 0 0.5rem 0;
}

.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
