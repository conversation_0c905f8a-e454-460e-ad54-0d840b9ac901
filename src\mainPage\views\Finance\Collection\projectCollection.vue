<template>
  <div class="project-main">
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex == index ? 'active' : ''"
        @click="handleTabClick(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="tab-main">
      <keep-alive :include="['ContractCollectionPlan', 'CollectionHistory']">
        <component :is="currentComponent" :key="tabIndex"></component>
      </keep-alive>
    </div>
  </div>
</template>

<script setup>
import contractCollectionPlan from './contractCollectionPlan.vue';
import collectionHistory from './collectionHistory.vue';
import { ref, computed } from 'vue';

let tabIndex = ref(0);

// 组件映射
const componentMap = {
  0: contractCollectionPlan,
  1: collectionHistory
};

// 当前组件
const currentComponent = computed(() => componentMap[tabIndex.value]);

let tabs = [
  {
    value: 0,
    label: '收款计划',
  },
  {
    value: 1,
    label: '收款记录',
  },
];

function handleTabClick(item, index) {
  if (tabIndex.value == index) {
    return;
  }
  tabIndex.value = index;
}
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin: 0 6px 0 7px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height:27px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        height:30px;
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
</style>
