<template>
  <div
    style="height: calc(100vh - 170px);padding: 10px 10px 10px 10px;box-sizing: border-box;"
    :class="{ isFullSreen: $route.query.isFromOther == 1? true : props.dialogView ? false : isFullSreen }"
    v-loading="loading"
  >
    <Title v-if="props.showTitle" style="background-color: #fff"
      >{{ isEdit ? '编辑方案' : `${form.name || ''}方案详情` }}
      <template #foot>
        <div v-if="!props.dialogView">
          <el-button
            icon="FullScreen"
            :title="isFullSreen ? '取消全屏' : '全屏'"
            v-if="$route.query.isFromOther != 1"
            @click="handleScreen"
          ></el-button>
          <el-button type="primary" @click="handleAddInquiryForm">询价({{selectListForInquiry.length}})</el-button>
          <el-button type="primary" @click="handleInquiry">询价历史</el-button>
          <el-button
            type="primary"
            @click="exportProgramme(0)"
            title="从历史的方案导入"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin' "
            plain
            >导入其他</el-button
          >
          <el-button
            type=""
            title="从模板库选择"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            @click="addTemplate(2)"
            >引入模板</el-button
          >
          <el-button type="primary" @click="baseDrawer = true">基本信息</el-button>

          <el-button
            type="primary"
            @click="handleImportInquirySheet"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >导入询价单</el-button
          >
          <el-button
            type="primary"
            @click="submit()"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >保存草稿</el-button
          >
          <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
            >保存草稿并生成报价单</el-button
            > -->
          <el-button
            type="primary"
            @click="draft('confirm')"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            >保存并提交</el-button
          >

          <el-button
            type="warning"
            @click="savehistory"
            v-if="
              isEdit &&
              !props.businessPerson &&
              route.query.isAdmin != 'admin' &&
              !route.query.isAddSubedition
            "
            >保存为历史版本</el-button
          >
          <el-button type="warning" v-if="$route.query.isFromOther != 1" @click="saveTemplate">保存为模板</el-button>
          <el-button
            type="primary"
            @click="route.query.isAddSubedition ? submit('confirm') : adminSubmit()"
            v-if="isEdit && route.query.isAdmin == 'admin'"
            >保存</el-button
          >
          <!-- <el-button
            type="primary"
            icon="check"
            v-if="form.auditStatus == 1 && route.query.isAdmin == 'admin'"
            @click="normalConfirm"
            >确认</el-button
          > -->
          <el-button @click="handleCloseTag" v-if="!props.businessPerson">关闭</el-button>
        </div>
      </template></Title
    >
    <div style="height: calc(100% - 100px)">
      <edit
        :dialogView="props.dialogView"
        ref="sheet"
        :openSheetProductSelect="openSheetProductSelect"
        :data="form"
        @exportProductByHistory="exportProductByHistory"
      ></edit>
    </div>
  </div>
  <el-drawer title="基本信息" v-model="baseDrawer">
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
      <template #baseInfo-header="column">
        <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
        <el-button @click.stop="viewBusiness" type="primary" size="small">商机详情</el-button>
        <el-button @click.stop="viewRequirement" type="primary" size="small"
          >需求采集信息</el-button
        >
      </template>
      <template #distributionOption-header="column">
        <span class="avue-group__title" style="margin-right: 10px; color: var(--el-color-warning)"
          >主管备注</span
        >
      </template>
      <template #optionFiles v-if="option.detail">
        <File :fileList="form.optionFileList"></File>
      </template>
      <template #gatherFiles>
        <File :fileList="form.gatherFilesList"></File>
      </template>
      <template #optionFileType-type="{ item, value, label }">
        <div style="display: flex; align-items: center; justify-content: space-between">
          <span>{{ item.label }}</span>
          <img
            :src="item.url"
            style="height: 20px; width: 20px"
            @click.stop="$ImagePreview([{ thumbUrl: item.url, url: item.url }], 0)"
            alt=""
          />
        </div>
      </template>
    </avue-form>
    <template #footer>
      <div style="flex: auto">
        <el-button type="primary" @click="baseDrawer = false">确认</el-button>
      </div>
    </template>
  </el-drawer>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-drawer v-model="drawer" :with-header="false" size="60%">
    <Title>{{ businessForm.name }}</Title>
    <BusinessDetail :form="businessForm" :isEdit="false"></BusinessDetail>
  </el-drawer>
  <div class="programmeSelect" :class="{ active: isExport && route.query.isView != 1 }">
    <Title style="margin-bottom: 10px"
      >历史方案
      <template #foot
        ><el-icon @click="isExport = false" style="cursor: pointer" size="large"
          ><CircleCloseFilled /></el-icon></template
    ></Title>
    <el-input
      v-model="optionName"
      placeholder="请输入商机名称"
      style="width: 100%"
      @input="handleInput"
    ></el-input>
    <div
      v-if="!isHistoryProgramme"
      style="height: 420px; overflow-y: auto"
      :infinite-scroll-disabled="programmeList.length < 7"
      v-infinite-scroll="loadMore"
    >
      <div
        class="content"
        style="box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); margin: 5px; border-radius: 5px"
        v-for="(i, index) in programmeList"
      >
        <div class="title">
         <div> <el-text size="large" style="margin-bottom: 5px" type="primary">{{ i.name }}</el-text></div>
          <el-text size="small" type="info">{{ i.customerName }}</el-text>
          <!-- <el-text type="warning" size="small">方案场景</el-text> -->
        </div>
        <!-- <span style="font-size: 12px" class="random-color-example">服务器</span> -->
        <div
          class="btn_box"
          style="display: flex; justify-content: flex-end; align-items: center; height: 31px"
        >
          <el-button type="primary" size="small" @click="handleView(i)" plain round>预览</el-button>
          <el-button
            type="success"
            @click="useOption(i.id, !i.isHasDataJson)"
            plain
            size="small"
            round
            >使用</el-button
          >
        </div>
      </div>
    </div>
    <!-- 历史 -->
    <div v-else style="height: 420px; overflow-y: auto">
      <div
        class="content"
        style="box-shadow: var(--el-box-shadow-light); margin: 5px; border-radius: 5px"
        v-for="(i, index) in programmeHistoryList"
      >
        <div class="title">
          <el-text size="large" type="primary">{{ i.optionName }}1111</el-text>
          <el-text type="warning" size="small">v${{ row.version }}.0</el-text>
        </div>
        <!-- <span style="font-size: 12px" class="random-color-example">服务器</span> -->
        <div class="btn_box" style="display: flex; justify-content: space-between; height: 31px">
          <div class="left_btn">
            <el-button type="primary" size="small" plain round>预览</el-button>
          </div>
          <div class="right_btn">
            <el-button type="success" @click="useOption(i.id, !i.dataJson)" plain size="small" round
              >使用</el-button
            >
          </div>
          <div class="right_btn">
            <!-- <el-button  plain size="small"  round>查看历史</el-button> -->
          </div>
        </div>
      </div>
      <el-empty v-if="programmeHistoryList.length == 0"></el-empty>
      <div style="text-align: center">
        <el-button @click="isHistoryProgramme = false" type="">返回</el-button>
      </div>
    </div>
  </div>
  <templateSelect
    ref="templateDialog"
    :templateType="0"
    :level="0"
    :businessType="$route.query.businessType"
    @change="handleTemplateChange"
  ></templateSelect>
  <el-drawer title="询价单管理" v-model="inquiryDrawer" size="80%">
    <!-- <el-card style="margin-bottom: 5px" :body-style="{ padding: '10px' }" :key="randomKey">
      <el-radio-group
        style="margin-right: 10px"
        v-model="currentInquirySheet"
        size="large"
        ref="sort-buttons"
      >
        <el-radio-button v-for="(item, index) in inquirySheetList" :key="item.id" :label="index">{{
          item.businessName
        }}</el-radio-button>
        <div style="display: flex; align-items: center">
          <el-button
            type="primary"
            size="large"
            title="添加询价单"
            plain
            v-if="$route.query.type != 'detail'"
            @click="addInquirySheet"
            style="width: 50px; margin-left: 5px; border-radius: 1px"
            icon="plus"
          ></el-button>
          <el-button
            type="primary"
            size="large"
            title="导入询价单"
            plain
            v-if="$route.query.type != 'detail'"
            @click="importDialogVisible = true"
            style="width: 50px; margin-left: 5px; border-radius: 1px"
            icon="Upload"
          ></el-button>
        
        </div>
      </el-radio-group>
    </el-card> -->
    <el-row :gutter="20" style="height: 100%">
      <el-col :span="8" style="height: 100%">
        <div style="display: flex; gap: 10px">
          <el-input
            placeholder="请输入询价单名称,品牌模糊搜索"
            v-model="inquirySheetParams.key"
            @input="
              () => (
                ((sheetPage.current = 1), (currentSheetData = null)((inquirySheetPageData = []))),
                getInquirySheetPage()
              )
            "
            style="padding-bottom: 5px; box-sizing: border-box"
          ></el-input>
          <el-button type="primary" @click="handleImportInquirySheet" icon="upload" plain
            >导入询价单</el-button
          >
        </div>
        <div
          style="height: calc(100% - 30px); overflow-y: scroll"
          :infinite-scroll-immediate="false"
          v-infinite-scroll="loadMoreSheet"
        >
          <el-card
            shadow="never"
            @click="handleClickCard(item)"
            :style="{
              border: item.id == currentSheetData.id ? '1px solid var(--el-color-primary)' : '',
            }"
            v-for="item in inquirySheetPageData"
            style="margin-bottom: 5px"
          >
            <el-descriptions :column="1" size="small" :title="item.businessName">
              <el-descriptions-item label="关联品牌">
                <el-tag
                  effect="plain"
                  style="margin-right: 2px"
                  size="small"
                  v-for="i in item.productBrand?.split(',').filter(item => item)"
                  >{{ i }}</el-tag
                >
              </el-descriptions-item>
              <el-descriptions-item label="关联供应商">
                {{ item.supplierName }}
              </el-descriptions-item>
              <el-descriptions-item label="报价时间">
                {{ item.offerDate.split(' ')[0] }}
              </el-descriptions-item>
              <!-- <el-descriptions-item label="备注">
              {{ item.remark }}
            </el-descriptions-item> -->
            </el-descriptions>
          </el-card>
        </div>
      </el-col>
      <el-col :span="16">
        <el-card>
          <el-empty
            style="height: 100%"
            description="暂无询价单"
            v-if="!currentInquirySheet && currentInquirySheet !== 0"
          />
          <inquirySheetDetail
            v-model="inquirySheetList[currentInquirySheet].tableData"
            :categoryTreeData="categoryTreeData"
            ref="inquirySheetDetailRef"
            @confirm="handleConfirmSheet"
            :isSelectFromSheet="isSelectFromSheet"
            v-else
          ></inquirySheetDetail>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <div v-if="isSelectFromSheet" style="flex: auto">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleConfirm">确 认</el-button>
      </div>
    </template>
  </el-drawer>
  <el-drawer title="编辑询价单" v-model="isEditInquirySheet" size="90%">
    <avue-form :option="fileOption" ref="fileRef" v-model="fileForm"></avue-form>
    <avue-crud
      @row-dblclick="handleRowDBLClick"
      @row-del="handleRowDel"
      ref="fileCrudRef"
      @row-update="handleRowUpdate"
      :option="inquirySheetOption"
      @selection-change="handleChange"
      :data="inquirySheetData"
    >
      <template #header>
        <el-alert type="success" size="small">双击可编辑</el-alert>
      </template>
      <template #menu-left>
        <el-button type="primary" @click="clearEmptyProduct">清除空产品</el-button>
        <el-dropdown style="margin-left: 10px" @command="handleCommand">
          <el-button type="primary">
            批量操作<el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="classifyName">编辑分类</el-dropdown-item>
              <el-dropdown-item command="productBrand">编辑品牌</el-dropdown-item>
              <el-dropdown-item command="customProductSpecification">编辑型号</el-dropdown-item>
              <el-dropdown-item command="costPrice">编辑单价</el-dropdown-item>
              <el-dropdown-item command="customUnit">编辑单位</el-dropdown-item>
              <el-dropdown-item command="number">编辑数量</el-dropdown-item>
              <el-dropdown-item command="deleteProduct"
                ><span style="color: var(--el-color-danger)">删除产品</span></el-dropdown-item
              >
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </template>
      <template #totalPrice="{ row }">
        {{ row.number * row.costPrice }}
      </template>
    </avue-crud>
    <template #footer>
      <div style="flex: auto">
        <el-button type="danger" @click="resetImport" plain>重新导入</el-button>
        <el-button type="primary" @click="handleOpenSave">保存为询价单</el-button>
      </div>
    </template>
  </el-drawer>
  <el-dialog
    v-model="importDialogVisible"
    title="导入询价单"
    style="width: 50%"
    class="avue-dialog avue-dialog--top"
  >
    <el-form label-width="160" class="uploadForm">
      <el-form-item label="导入的品牌:">
        <el-input
          style="margin-right: 5px"
          v-model="importBrand"
          size="large"
          placeholder="请输入导入的品牌"
        ></el-input>
      </el-form-item>
      <el-form-item label="工作表所在位置:" required>
        <el-input
          style="margin-right: 5px"
          v-model="sheetNum"
          size="large"
          placeholder="请输入所在表"
        ></el-input>
      </el-form-item>
      <el-form-item label="表头所在行:" required>
        <el-input v-model="headerRow" size="large" placeholder="请输入表头所在行"></el-input>
      </el-form-item>
      <el-form-item label="文件上传:" required>
        <el-upload
          :show-file-list="false"
          accept=".xlsx,.xls"
          style="width: 100%"
          :disabled="!headerRow || !sheetNum"
          :http-request="uploadFile"
          drag
        >
          <div style="width: 100%">
            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          </div>

          <!-- <el-button
              type=""
              size="large"
              :disabled="!headerRow && !sheetNum"
              :title="headerRow ? '导入询价单' : '请先选择表头所在行'"
              v-if="$route.query.type != 'detail'"
              @click="importSheet"
              style="width: 50px; margin-left: 5px; border-radius: 1px; margin-right: 5px"
              icon="Upload"
            ></el-button> -->
        </el-upload>
      </el-form-item>
    </el-form>
    <!-- <div class="avue-dialog__footer">
      <el-button @click="importDialogVisible = false">取 消</el-button>
      <el-button type="primary">确 定</el-button>
    </div> -->
  </el-dialog>
  <el-dialog
    v-model="inquirySheetFormDialog"
    title="保存询价单"
    style="width: 50%"
    class="avue-dialog avue-dialog--top"
  >
    <avue-form
      :option="inquirySheetFormOption"
      v-model="inquirySheetForm"
      ref="inquirySheetFormRef"
      @submit="handleSave"
    >
      <template #offerPerson="{ disabled }">
        <div style="display: flex; gap: 5px">
          <el-input
            :disabled="disabled"
            placeholder="请输入联系人"
            v-model="inquirySheetForm.offerPerson"
          ></el-input
          ><el-button
            type="primary"
            :disabled="!inquirySheetForm.supplierId || disabled"
            @click="$refs.supplierCotactSelectRef.open()"
            icon="plus"
          ></el-button>
        </div>
      </template>
    </avue-form>
    <div class="avue-dialog__footer">
      <el-button @click="inquirySheetFormDialog = false">取 消</el-button>
      <el-button type="primary" @click="$refs.inquirySheetFormRef.submit()">确 定</el-button>
    </div>
  </el-dialog>
  <inquirySheetSelect ref="inquirySheetSelectRef" @change="handleSheetChange"></inquirySheetSelect>
  <supplierCotactSelect
    ref="supplierCotactSelectRef"
    :supplierId="inquirySheetForm.supplierId"
    @change="handleContactChange"
  ></supplierCotactSelect>
  
  <!-- 询价管理组件 -->
  <InquiryManagement
    ref="inquiryManagement"
    v-model:visible="inquiryManagementDrawer"
    :business-id="form.businessOpportunityId"
    :option-id="form.id"
    :businessOpportunityId="form.businessOpportunityId"
    v-model:selectList="selectListForInquiry"
    @import="handleImportProducts"
    @update:visible="inquiryManagementDrawer = $event"
  />
</template>

<script setup>
import {
  ref,
  getCurrentInstance,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  handleError,
  computed,
  watch,
  provide,
} from 'vue';
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router';
import supplierCotactSelect from './supplierCotactSelect.vue';
import BusinessDetail from '@/views/CRM/businessOpportunity/detail/baseInfo.vue';
import templateSelect from './templateSelect.vue';
import inquirySheetDetail from './inquirySheetDetail.vue';
import inquirySheetSelect from './inquirySheetSelect.vue';
import InquiryManagement from '../../quotation/compoents/InquiryManagement.vue';
import { randomLenNum } from '@/utils/util';
import templateA from '@/assets/template/templateA.png';
import templateB from '@/assets/template/templateB.png';
import templateC from '@/assets/template/templateC.png';
import templateD from '@/assets/template/templateD.jpg';
import request from '@/axios';
import { loadFile } from '@/utils/file.js';
import { columnHideData as columnHideDataCopy } from '../../compoents/normal';
import { ElMessageBox, ElSelect, ElOption, ElLoading,ElMessage } from 'element-plus';

import edit from './edit.vue';

const route = useRoute();
const router = useRouter();
let isEdit = ref(route.query.type == 'edit' || route.query.type == 'add');
let form = ref({
  moduleDTOList: [
    {
      moduleName: '汇总',
    },
  ],
});
let isEditSubedition = ref(false);
const props = defineProps({
  stageStatus: {
    type: Number,
    default: -1,
  },
  businessPerson: {
    type: String,
    default: '',
  },
  showTitle: {
    type: Boolean,
    default: true,
  },
  businessOpportunityId: String,
  dialogView: Boolean,
});
// 是否要调保存接口
let isAutoSave = ref(true);
let loading = ref(false);
const isDeep = ref(route.query.deep == '1');
// watchEffect(() => {
//   if (route.query.id) {
//     getDetail();
//   }
// });
watch(
  () => route.query.id,
  () => {
    if (route.query.id && !props.dialogView) {
      getDetail();
    }
    isEdit.value = route.query.type == 'edit' || route.query.type == 'add';
  },
  {
    immediate: true,
  }
);
watch(
  () => props.businessOpportunityId,
  () => {
    if(props.businessOpportunityId){
      getDetail();
    }
    if (route.query.type != 'edit' && route.query.type != 'add') {
      isEdit.value = false;
      isAutoSave.value = false;
    }
  },
  {
    immediate: true,
  }
);
onMounted(() => {
  // if (route.query.id) {
  //   getDetail();
  // }
});
let companyList = ref([]);
let option = ref({
  submitBtn: false,
  labelWidth: 120,
  detail: !isEdit.value || isDeep.value,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      column: [
        {
          label: '方案名称',
          prop: 'optionName',
          span: 24,
          rules: [
            {
              required: true,
              message: '请填写方案名称',
            },
            {
              validator: (rule, value, callback) => {
                const reg = /^[^/\\?？\[\]]*$/;
                console.log(value, rule, reg);
                if (!reg.test(value)) {
                  callback(new Error('不能包含特殊字符"/\?？[]"'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        },
        {
          label: '客户名称',
          prop: 'customerName',
          readonly: true,
          span: 24,
        },
        {
          label: '关联联系人',
          prop: 'concatName',
          span: 24,
          readonly: true,
        },
        // {
        //   label: '人工费',
        //   prop: 'isNeedLabor',
        //   span:24,
        //   readonly: true,
        //   type: 'switch',

        //   dicData: [
        //     {
        //       value: 0,
        //       label: '否',
        //     },
        //     {
        //       value: 1,
        //       label: '是',
        //     },
        //   ],
        // },
        // {
        //   label: '自定义',
        //   prop: 'isHasCustom',
        //   readonly: true,
        //   type: 'switch',
        //   labelTip: '打开后点击文字即可编辑',
        //   span: 4,
        //   dicData: [
        //     {
        //       value: 0,
        //       label: '否',
        //     },
        //     {
        //       value: 1,
        //       label: '是',
        //     },
        //   ],
        // },
        // {
        //   label: '专项报价',
        //   prop: 'isHasSpecialPrice',
        //   type: 'radio',
        //   span: 24,
        //   value: 0,
        //   dicData: [
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //   ],
        // },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,

          // format: 'YYYY-MM-DD',
          // valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '上传附件',
          prop: 'optionFiles',
          type: 'upload',
          span: 24,
          dragFile: true,
          // rules: [
          //   {
          //     required: true,
          //     validator: validatorPath,
          //     trigger: "change",
          //   },
          // ],
          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',
          hide: true,
          viewDisplay: false,
          uploadPreview: (file, data) => {
            console.log(file, data);
            const { originalName, link } =
              form.value.optionFileList.find(item => item.id == file.url) || {};
            loadFile(link, originalName);
          },
        },
        {
          label: '报价公司',
          type: 'select',
          // value: '深圳市非常聚成科技有限公司',
          prop: 'offerCompany',
          span: 24,
          overHidden: true,
          cell: false,
          props: {
            label: 'companyName',
            value: 'id',
          },
          dicFormatter: res => {
            companyList.value = res.data.records;
            ;
            form.value.offerCompany = companyList.value[0]?.id;
            return res.data.records;
          },
          dicUrl: '/api/vt-admin/company/page?size=1000&current=1',
          rules:[{
            required: true,
            message: '请选择报价公司',
            trigger: 'change',
          }]
        },
        {
          label: '设备保修期(年)',
          prop: 'freeWarrantyYear',
          span: 24,
          type: 'number',
        },
        {
          label: '导出报价单',
          prop: 'optionFileType',
          span: 24,
          type: 'select',
          rules: [
            {
              required: true,
              message: '请选择导出报价单模板',
              trigger: 'change',
            },
          ],
          dicData: [
            {
              label: '模板A型',
              value: 0,
              url: templateA,
            },
            {
              label: '模板B型',
              value: 1,
              url: templateB,
            },
            {
              label: '模板C型',
              value: 2,
              url: templateC,
            },
            {
              label: '模板D型',
              value: 3,
              url: templateD,
            },
          ],
        },
      ],
    },
    {
      label: '主管备注',
      prop: 'distributionOption',
      arrow: true,
      column: [
        {
          label: '',
          type: 'textarea',
          span: 24,
          readonly: true,
          prop: 'distributionOptionRemark',
        },
      ],
    },
    // {
    //   label: '需求采集信息',
    //   prop: 'gatherInfo',
    //   arrow: true,
    //   column: [
    //     {
    //       label: '采集表附件',
    //       type: 'input',
    //       span: 24,
    //       readonly: true,
    //       prop: 'gatherFiles',
    //     },
    //   ],
    // },
  ],
});
let { proxy } = getCurrentInstance();

const editableTabsValue = ref(0);
// 是否是深化设计

let sheetOption = ref({
  option: '', //JSON数据
  detailList: [], //详情数据
  isRefresh: false, //是否刷新表格中的数据
  type: 2, //1 报价 2 方案
});
let isFetch = ref(false);
function getDetail(id) {
  let url = '/api/vt-admin/businessOpportunityOption/detailByOptionId';
  if (route.query.isHistory) {
    url = '/api/vt-admin/businessOpportunityOptionHistory/detail';
  }
  if (route.query.isTranslate) {
    isAutoSave.value = false;
    url = '/api/vt-admin/businessOpportunityOption/detailByHistoryOptionId';
  }
  if (route.query.isAddSubedition) {
    url = '/api/vt-admin/businessOpportunityOption/subeditionDetail';
  }
  if (route.query.isEditSubedition || isEditSubedition.value) {
    url = '/api/vt-admin/businessOpportunityOption/detail';
  }
  loading.value = true;
  axios
    .get(url, {
      params: {
        id: props.businessOpportunityId || route.query.id || id,
      },
    })
    .then(res => {
      loading.value = false;
      const data = res.data.data;

      form.value = data;

      if (!form.value.optionName) {
        form.value.optionName = route.query.name + '的方案';
      }
      if (form.value.optionFileList) {
        form.value.optionFiles = form.value.optionFileList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        });
      } else {
        form.value.optionFiles = [];
      }
      form.value.offerCompany = data.offerCompany || companyList.value[0]?.id;
      let { productRate, labourRate, otherRate, warrantyRate, configuredJson } = form.value;
      if (!configuredJson) {
        configuredJson = '{}';
      }
      if (form.value.id == null) {
        form.value.isHasTax = 1;
      }
      const {
        isFullSreen: newIsFullSreen = false,
        isFold = false,
        columnHideData = null,
        isLock = false,
        tax = '',
      } = JSON.parse(configuredJson);
      isFullSreen.value = newIsFullSreen;
      form.value.isLock = isLock;
      form.value.columnHideData = columnHideData;
      form.value.isFold = isFold;
    });
}

function formatData(type = '') {
  const sheetData = proxy.$refs.sheet.getData();

  console.log(sheetData);
  let moduleKey = type == '' ? 'moduleDTOList' : 'moduleHistoryDTOList';
  let detailKey = type == '' ? 'detailDTOList' : 'detailHistoryDTOS';
  // const { configuredJson } = sheetData;
  const data = {
    ...form.value,
    ...sheetData,
    optionFiles: form.value.optionFiles && form.value.optionFiles.map(item => item.value).join(','),
    configuredJson: JSON.stringify({
      // ...configuredJson,
      isFullSreen: isFullSreen.value,
      columnHideData: sheetData.columnHideData,
      isFold: sheetData.isFold,
    }),
    [moduleKey]: sheetData.moduleDTOList.map((item, index) => {
      return {
        moduleName: item.moduleName,
        remark: item.remark,
        isCheck:item.isCheck,
        sortNumber: index,
        isHasClassify: item.isHasClassify,
        [detailKey]: item.detailDTOList.map((item, i) => {
          return {
            ...item,
            sortNumber: i,
          };
        }),
      };
    }),
    auditStatus: null,
    auditType: null,
  };

  return data;
}
let isSubmit = ref(false);
// 非深化设计提交
function draft(type) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        submit(type, done);
      });
    } else {
      baseDrawer.value = true;
    }
  });
}
function submit(type, done = () => {}, isNotify = true) {
  let data = formatData();
  console.log(data);
  return new Promise((resolve, reject) => {
    if (data.id) {
      axios
        .post(
          `/api/vt-admin/businessOpportunityOption/${
            route.query.isAddSubedition ? 'subeditionUpdate' : 'update'
          }`,
          {
            ...data,
            optionStatus: type == 'confirm' ? 2 : 0,
          }
        )
        .then(res => {
          if (isNotify) {
            proxy.$message.success(res.data.msg);
            proxy.$store.dispatch('getMessageList');
          }
          // getDetail();
          done();
          resolve(data.id);
          if (type == 'confirm') {
            isEdit.value = false;
            isAutoSave.value = false;
            proxy.$router.$avueRouter.closeTag();
            proxy.$router.back();
          }
          // option.value.detail = true;
        });
    } else {
      data = {
        ...data,
        businessOpportunityId: route.query.businessOpportunityId,
        optionStatus: type == 'confirm' ? 2 : 0,
      };
      axios
        .post(
          `/api/vt-admin/businessOpportunityOption/${
            route.query.isAddSubedition ? 'subeditionSave' : 'save'
          }`,
          data
        )
        .then(res => {
          if (isNotify) {
            proxy.$message.success(res.data.msg);
            getDetail();
          }
          done();

          if (type == 'confirm') {
            isEdit.value = false;
            isAutoSave.value = false;
            proxy.$router.$avueRouter.closeTag();
            proxy.$router.back();
          } else {
            if (route.query.isAddSubedition) {
              isEditSubedition.value = true;
            }
            resolve(res.data.data);
          }

          // option.value.detail = true;
        });
    }
  });
}
function adminSubmit(isNotify = true) {
  return new Promise((resolve, reject) => {
    let data = formatData();
    axios
      .post('/api/vt-admin/businessOpportunityOption/updateOption', {
        ...data,
      })
      .then(res => {
        if (isNotify) {
          proxy.$message.success('保存成功');
          proxy.$store.dispatch('getMessageList');
        }
        resolve();
      })
      .catch(() => {
        reject();
      });
  });
}

// 深化设计提交
function draftDeep(type) {
  // let res = [];
  // let logList = [];
  // form.value.moduleDTOList.forEach((item, index) => {
  //   console.log(item);
  //   if (!item.detailDTOList) return;
  //   item.detailDTOList.forEach((item1, index1) => {
  //     item1.productList.forEach((item2, index2) => {
  //       proxy.$refs[`sealPrice-${index}-${index1}-${index2}`][0].validate(valid => {
  //         if (valid) {
  //           res.push(true);
  //         } else {
  //           logList.push(`${item.moduleName}-${item1.classify}-${item2.productName}单价未填`);
  //           res.push(false);
  //         }
  //         console.log(res);
  //       });
  //     });
  //   });
  // });

  // setTimeout(() => {
  //   if (!res.some(i => !i)) {
  //     proxy.$refs.addForm.validate((valid, done) => {
  //       if (valid) {
  //         proxy.$confirm('确认此次操作吗？', '提示').then(() => {
  //           deepSubmit(type, done);
  //         });
  //       }
  //     });
  //   } else {
  //     proxy.$message.warning(logList.join('\n'));
  //   }
  // }, 0);
  deepSubmit(type);
}
function deepSubmit(type) {
  let data = formatData('deep');
  if (data.id) {
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/editDeepenDesign', {
        ...data,
        optionStatus: type == 'confirm' ? 1 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();

        if (type == 'confirm') {
          isEdit.value = false;
          isAutoSave.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }
        // isEdit.value = false;
        // option.value.detail = true;
      });
  } else {
    data = {
      ...data,
      businessOpportunityId: route.query.id,
      optionStatus: type == 'confirm' ? 1 : 0,
    };
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/saveDeepenDesign', data)
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();

        if (type == 'confirm') {
          isEdit.value = false;
          isAutoSave.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }
        // isEdit.value = false;
        // option.value.detail = true;
      });
  }
}
function deepAdminSubmit(params) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        let data = formatData();

        axios
          .post('/api/vt-admin/businessOpportunityOption/updateOption', {
            ...data,
          })
          .then(res => {
            proxy.$message.success(res.data.msg);
            getDetail();
            done();
            // isEdit.value = false;
            // option.value.detail = true;
          });
      });
    }
  });
}
// 深化提交over

let drawer = ref(false);
let businessForm = ref({});
function viewBusiness(params) {
  drawer.value = true;
  getBusinessDetail();
}
function getBusinessDetail() {
  return axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id: route.query.businessOpportunityId || form.value.businessOpportunityId,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode } = res.data.data;
      businessForm.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        isProduct: res.data.data.productVOList && res.data.data.productVOList.length == 0 ? 0 : 1,
        productVOList:
          res.data.data.productVOList &&
          res.data.data.productVOList.map(item => {
            return {
              ...item,
              ...item.productVO,
            };
          }),
        // registeredCapital: Number(res.data.data.registeredCapital),
      };
    });
}

function normalConfirm(row) {
  adminSubmit(false)
    .then(() => {
      proxy.$refs.dialogForm.show({
        title: '审核',
        option: {
          column: [
            {
              label: '审核结果',
              type: 'radio',
              value: 2,
              dicData: [
                {
                  value: 2,
                  label: '通过',
                },
                {
                  value: 3,
                  label: '不通过',
                },
              ],
              prop: 'auditStatus',
              control: val => {
                return {
                  auditReason: {
                    display: val == 3,
                  },
                };
              },
            },
            {
              label: '审核原因',
              prop: 'auditReason',
              type: 'textarea',
              span: 24,
            },
          ],
        },
        callback(res) {
          console.log(form.value);
          axios
            .post('/api/vt-admin/businessOpportunityOption/audit', {
              id: form.value.id,
              ...res.data,
            })
            .then(e => {
              proxy.$message.success('操作成功');
              res.close();
              isAutoSave.value = false;
              proxy.$router.$avueRouter.closeTag();
              proxy.$router.back();
            });
        },
      });
    })
    .catch(() => {
      proxy.$message.warning('保存草稿失败，请重试');
    });
}
function deepConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type: 'radio',
          value: 2,
          dicData: [
            {
              value: 2,
              label: '通过',
            },
            {
              value: 3,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control: val => {
            return {
              auditReason: {
                display: val == 3,
              },
            };
          },
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunityOptionHistory/auditDeepenDesign', {
          id: form.value.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        });
    },
  });
}
function findValueByLabel(lable, arr) {
  return Boolean(arr.find(item => item.label === lable).value);
}

let baseDrawer = ref(route.query.isFromOther == 1? false : !props.dialogView);

let isFullSreen = ref(false);
function handleScreen() {
  isFullSreen.value = !isFullSreen.value;
  proxy.$nextTick(() => {
    luckysheet.resize();
  });
}
function savehistory() {
  proxy.$refs.dialogForm.show({
    title: '保存历史版本',
    option: {
      column: [
        {
          label: '方案名称',
          type: 'input',
          prop: 'optionName',
          value: form.value.optionName,
          rules: [
            {
              required: true,
              message: '请输入方案名称',
              trigger: 'blur',
            },
          ],
          span: 24,
        },
        {
          label: '备注',
          type: 'textarea',
          prop: 'remark',
          span: 24,
        },
      ],
    },
    callback(res) {
      // 先保存草稿
      submit().then(r => {
        axios
          .post('/api/vt-admin/businessOpportunityOption/saveHistoryOption', {
            ...res.data,
            id: r,
          })
          .then(r => {
            proxy.$message.success(r.data.msg);
            res.close();
          });
      });
    },
  });
}

//  导入
let isExport = ref(false);
let currentSelectProgramme = ref(0);
let viewType = ref(''); //  0  当前标签预览  1  打开另一个window 窗口预览
let viewObj = ref({})
function exportProgramme(value) {
  viewType.value = value;
  isExport.value = !isExport.value;
  programmeList.value = [];
  page.value.currentPage = 1;
  queryProgramme();
}
function handleClick(index) {
  currentSelectProgramme.value = index;
}
function loadMore() {
  console.log(2222);
  page.value.currentPage += 1;
  queryProgramme();
}
let page = ref({
  currentPage: 1,
  pageSize: 10,
});
let optionName = ref('');
let programmeList = ref([]);
let currentId = ref('');
let programmeHistoryList = ref([]);
let isHistoryProgramme = ref(false);
function queryProgramme(params) {
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/businessOpportunity/needOptionPage', {
      params: {
        size,
        current,
        selectType: 0,
        name: optionName.value,
        optionStatus: 2,
      },
    })
    .then(res => {
      programmeList.value.push(...res.data.data.records);
    });
}
function queryHistoryList(params) {
  axios
    .get('/api/vt-admin/businessOpportunityOptionHistory/page', {
      params: {
        optionId: currentId.value,
        size: 5000,
        current: 1,
      },
    })
    .then(res => {
      programmeHistoryList.value = res.data.data.records;
    });
}
function handleInput(value) {
  optionName.value = value;
  page.value.currentPage = 1;
  programmeList.value = [];
  queryProgramme();
}
function viewHistory(row) {
  console.log(1111);
  currentId.value = row.optionId;
  isHistoryProgramme.value = true;
  queryHistoryList();
}
function useOption(id, bol) {
  proxy
    .$confirm('引入历史方案将会替换所有内容，是否确定引入历史方案?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(async () => {
      const res = await axios.get('/api/vt-admin/businessOpportunityOption/detailByOptionId', {
        params: {
          id,
        },
      });
      console.log(res);
      const { moduleVOList } = res.data.data;
      form.value = {
        ...form.value,
        moduleVOList: moduleVOList.map(item => {
          return {
            ...item,
            id: null,
            detailVOList: item.detailVOList.map(item => {
              return {
                ...(item.productVO || {}),
                ...item,
                productBrand: item.productBrand,

                source: 1,

                id: null,
              };
            }),
          };
        }),
      };
      done();
    });
}
function handleView(row) {
  if (viewType.value == 0) {
    if (route.query.isAdmin) {
      adminSubmit(false).then(res => {
        proxy.$message.success('自动保存草稿成功');
        router.push({
          path: '/CRM/programme/compoents/updateVersion3',
          query: {
            id: row.id,
            name: `预览-${row.name}`,
            type: 'detail',
            isView: 1,
          },
        });
      });
    } else {
      submit(undefined, undefined, false).then(res => {
        proxy.$message.success('自动保存草稿成功');
        router.push({
          path: '/CRM/programme/compoents/updateVersion3',
          query: {
            id: row.id,
            name: `预览-${row.name}`,
            type: 'detail',
            isView: 1,
          },
        });
      });
    }
  } else {
    const {moduleName,classify} = viewObj.value
    window.open(
      `/CRM/programme/compoents/updateVersion3?id=${row.id}&name=${row.name}&type=detail&isView=1&isFromOther=1&moduleName=${moduleName}&classify=${classify}`
    );
    isExport.value = false
  }
}

onBeforeRouteLeave((to, from) => {
  if (
    (route.query.type != 'detail' || !props.dialogView) &&
    isAutoSave.value &&
    !route.query.isAddSubedition
  ) {
    if (route.query.isAdmin) {
      adminSubmit(false).then(res => {
        proxy.$message.success('自动保存草稿成功');
      });
    } else {
      submit(undefined, undefined, false).then(res => {
        proxy.$message.success('自动保存草稿成功');
      });
    }
  }
});
// 添加模板
function handleTemplateChange(id, done) {
  proxy
    .$confirm('引入模板将会替换所有内容，是否确定引入模板?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(async () => {
      const res = await axios.get('/api/vt-admin/optionTemplate/detailForAdd', {
        params: {
          id,
        },
      });
      console.log(res);
      const { moduleTemplateVOS } = res.data.data;
      form.value = {
        ...form.value,
        moduleVOList: moduleTemplateVOS.map(item => {
          return {
            ...item,
            id: null,
            detailVOList: item.optionModuleDetailTemplateVOS.map(item => {
              return {
                ...item,
                ...(item.productVO || {}),
                productBrand: item.productBrand,
                sealPrice: '',
                rgcbdj: '',
                ybcbdj: '',
                qtcbdj: '',
                ybhsdj: '',
                qthsdj: '',
                source: 1,
                laborCost: '',
                id: null,
              };
            }),
          };
        }),
      };
      done();
    });
}
function addTemplate(val, done) {
  proxy.$nextTick(() => {
    proxy.$refs.templateDialog.open();
  });
}
function saveTemplate() {
  const moduleDTOS = proxy.$refs.sheet.getData().moduleDTOList.map((item, index) => {
    return {
      ...item,
      classifyDTOS: item.detailDTOList.reduce((pre, cur, index) => {
        if (cur.detailType == 1) {
          const detailDTOS = item.detailDTOList
            .filter(item => item.classify == cur.classify && item.detailType != 1)
            .map(item => {
              return {
                ...item,
                sortNumber: index,
              };
            });
          pre.push({
            ...cur,
            classifySort: index,
            detailDTOS,
          });
        }
        return pre;
      }, []),
      sortNumber: index,
    };
  });

  proxy.$refs.dialogForm.show({
    title: '保存为模板',
    option: {
      column: [
        {
          label: '模板名称',
          prop: 'templateName',
          type: 'input',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入模板名称',
            },
          ],
        },
        {
          type: 'select',
          dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          label: '业务类型',
          // multiple: true,
          span: 12,
          width: 250,
          overHidden: true,
          parent: true,
          span: 24,
          rules: [
            {
              required: true,
              message: '请选择业务板块',
            },
          ],
          value: route.query.businessType,
          search: true,
          display: true,
          filterable: true,

          prop: 'businessType',
          checkStrictly: true,
        },
        {
          type: 'tree',
          label: '模板分类',
          rules: [
            {
              required: true,
              message: '请选择模板分类',
            },
          ],
          width: 220,
          span: 12,
          // parent: false,

          span: 24,

          search: true,
          dicUrl: '/api/vt-admin/templateCategory/tree',
          props: {
            label: 'categoryName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.map(item => {
              return {
                ...item,
                disabled: false,
                children:
                  item.children &&
                  item.children.map(item => {
                    return {
                      ...item,
                      disabled: true,
                      children:
                        item.children &&
                        item.children.map(item => {
                          return {
                            ...item,
                            disabled: true,
                          };
                        }),
                    };
                  }),
              };
            });
          },
          dataType: 'string',
          display: true,
          filterable: true,
          prop: 'templateCategory',
          checkStrictly: true,
        },
        {
          label: '模板描述',
          prop: 'remark',
          type: 'textarea',
          search: true,
          span: 24,
        },
      ],
    },
    callback(res) {
      const value = {
        moduleDTOS,
        ...res.data,

        templateType: 0,
      };
      axios.post('/vt-admin/optionTemplate/save', value).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
      });
    },
  });
}
// 询价单管理
let inquiryDrawer = ref(false);
let inquiryManagementDrawer = ref(false);
let currentInquirySheet = ref('');
let headerRow = ref('');
let sheetNum = ref(1);
let importBrand = ref('');
let inquirySheetList = ref([]);
let fileOptionData = ref([]);
let isEditInquirySheet = ref(false);
let fileForm = ref({});
let categoryTreeData = ref([]);
let importDialogVisible = ref(false);
function handleOpenInquirySheet() {
  inquiryDrawer.value = true;
  isSelectFromSheet.value = false;
  categoryTreeData.value = proxy.$refs.sheet.getTree();
  if (inquirySheetData.value.length > 0) {
    isEditInquirySheet.value = true;
  }
}
function addInquirySheet() {
  proxy.$refs.inquirySheetSelectRef.open();
}
let inquirySheetPageData = ref([]);
let inquirySheetParams = ref({});
let currentSheetData = ref(null);
let sheetPage = ref({
  current: 1,
  size: 10,
});
function getInquirySheetPage() {
  axios
    .get('/api/vt-admin/businessInquiry/page', {
      params: {
        ...sheetPage.value,
        ...inquirySheetParams.value,
      },
    })
    .then(res => {
      // loading.value = false;
      inquirySheetPageData.value.push(...res.data.data.records);

      currentSheetData.value = currentSheetData.value || inquirySheetPageData.value[0];
      handleSheetChange(currentSheetData.value);
    })
    .catch(() => {
      // loading.value = false;
    });
}
async function handleSheetChange(value, done) {
  console.log(value);
  const { businessName, id } = value;
  const res = await axios.get('/api/vt-admin/businessInquiry/detail?id=' + id);
  inquirySheetList.value.push({
    businessName,
    id,
    tableData: res.data.data.detailVOS,
  });
  currentInquirySheet.value = inquirySheetList.value.length - 1;
  if (typeof done == 'function') {
    done();
  }
}
function handleClickCard(item) {
  currentSheetData.value = item;
  handleSheetChange(item);
}
function handleConfirmSheet(data) {
  proxy.$refs.sheet.setData(data);
}
function loadMoreSheet() {
  console.log(1111);
  sheetPage.value.current += 1;
  getInquirySheetPage();
}
const fileOption = computed(() => {
  return {
    submitBtn: false,
    emptyBtn: false,
    size: 'small',
    labelWidth: 100,
    column: [
      {
        label: '序号',
        prop: 'serialNumber',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品名称',
        prop: 'customProductName',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '品牌',
        prop: 'productBrand',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '规格型号',
        prop: 'customProductSpecification',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品描述',
        prop: 'customProductDescription',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '单位',
        prop: 'customUnit',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '数量',
        prop: 'number',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品单价',
        prop: 'sealPrice',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '人工单价',
        prop: 'laborCost',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '延保单价',
        prop: 'ybhsdj',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '其他单价',
        prop: 'qthsdj',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '产品成本单价',
        prop: 'costPrice',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '人工成本单价',
        prop: 'rgcbdj',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '延保成本单价',
        prop: 'ybcbdj',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '其他成本单价',
        prop: 'qtcbdj',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
      {
        label: '备注',
        prop: 'remark',
        type: 'select',
        span: 4,
        dicData: fileOptionData.value,
      },
    ],
  };
});
let allData = ref([]);
let fileName = '';
function uploadFile(params) {
  var { file } = params;
  fileName = file.name.substring(0, file.name.lastIndexOf('.'));

  // 调用上传接口
  var formData = new FormData();
  formData.append('file', file);
  request({
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    url: '/vt-admin/businessInquiry/analyzeExcel',
    method: 'post',
    params: {
      parseRowNumber: headerRow.value * 1,
      sheetNum: sheetNum.value * 1,
    },

    data: formData,
  }).then(res => {
    if (!res.data.data || res.data.data.length == 0) {
      proxy.$message.error('解析失败');
      return;
    }
    fileOptionData.value = Object.keys(res.data.data[0]).map(item => {
      return {
        label: item,
        value: item,
      };
    });
    allData.value = res.data.data.map(item => {
      return {
        ...item,
        uuid: randomLenNum(10),
        source: 2,
      };
    });
    initData();

    isEditInquirySheet.value = true;
    importDialogVisible.value = false;
  });
}
let inquirySheetData = ref([]);
let inquirySheetOption = ref({
  menuWidth: 100,
  editBtn: false,
  height: 'auto',

  addBtn: false,
  delBtn: true,
  showSummary: true,
  selection: true,
  sumColumnList: [
    {
      name: 'totalPrice',
      type: 'sum',
    },
  ],
  border: true,
  index: false,
  column: {
    serialNumber: {
      label: '序号',
      overHidden: true,
      width: 80,
    },
    customProductName: {
      label: '产品名称',
      overHidden: true,
      width: 180,
    },
    productBrand: {
      label: '品牌',
      overHidden: true,
      width: 100,
    },
    customProductSpecification: {
      label: '规格型号',
      overHidden: true,
      span: 24,
      width: 180,
    },
    customProductDescription: {
      label: '产品描述',
      overHidden: true,
      span: 24,
      type: 'textarea',
      width: 200,
    },
    customUnit: {
      label: '单位',
      overHidden: true,
      label: '单位',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictValue',
        desc: 'desc',
      },

      dicUrl: '/blade-system/dict/dictionary?code=unit',
    },
    number: {
      label: '数量',
      overHidden: true,
      width: 80,
    },
    sealPrice: {
      label: '产品单价',
      overHidden: true,
      width: 100,
    },
    laborCost: {
      label: '人工单价',
      overHidden: true,
      width: 100,
    },
    ybhsdj: {
      label: '延保单价',
      overHidden: true,
      width: 100,
    },
    qthsdj: {
      label: '其他单价',
      overHidden: true,
      width: 100,
    },
    // totalPrice: {
    //   label: '金额',
    //   overHidden: true,
    //   editDisplay: false,
    // },
    costPrice: {
      label: '产品成本单价',
      width: 120,
      overHidden: true,
    },
    rgcbdj: {
      label: '人工成本单价',
      overHidden: true,
      width: 120,
    },
    ybcbdj: {
      label: '延保成本单价',
      overHidden: true,
      width: 120,
    },
    qtcbdj: {
      label: '其他成本单价',
      overHidden: true,
      width: 120,
    },
    remark:{
      label:'备注',
       width: 200,
    },
    classifyName: {
      label: '分类',
      span: 24,
      type: 'tree',
      parent: false,
      dicData: [],
      width: 200,
      props: {
        label: 'label',
        value: 'value',
        children: 'children',
      },
    },
  },
});
watch(
  () => fileForm.value,
  () => {
    initData();
  },
  {
    deep: true,
    immediate: true,
  }
);
function initData() {
  inquirySheetData.value = allData.value.map(item => {
    let sealPrice = item[fileForm.value.sealPrice]?.replace(',', '');
    sealPrice = item[fileForm.value.sealPrice] === '' || isNaN(sealPrice) ? '' : sealPrice;
    let laborCost = item[fileForm.value.laborCost]?.replace(',', '');
    laborCost = item[fileForm.value.laborCost] === '' || isNaN(laborCost) ? '' : laborCost;
    let ybhsdj = item[fileForm.value.ybhsdj]?.replace(',', '');
    ybhsdj = item[fileForm.value.ybhsdj] === '' || isNaN(ybhsdj) ? '' : ybhsdj;
    let qthsdj = item[fileForm.value.qthsdj]?.replace(',', '');
    qthsdj = item[fileForm.value.qthsdj] === '' || isNaN(qthsdj) ? '' : qthsdj;
    let costPrice = item[fileForm.value.costPrice]?.replace(',', '');
    costPrice = item[fileForm.value.costPrice] === '' || isNaN(costPrice) ? '' : costPrice;
    let rgcbdj = item[fileForm.value.rgcbdj]?.replace(',', '');
    rgcbdj = item[fileForm.value.rgcbdj] === '' || isNaN(rgcbdj) ? '' : rgcbdj;
    let ybcbdj = item[fileForm.value.ybcbdj]?.replace(',', '');
    ybcbdj = item[fileForm.value.ybcbdj] === '' || isNaN(ybcbdj) ? '' : ybcbdj;
    let qtcbdj = item[fileForm.value.qtcbdj]?.replace(',', '');
    qtcbdj = item[fileForm.value.qtcbdj] === '' || isNaN(qtcbdj) ? '' : qtcbdj;
    let number =
      item[fileForm.value.number] === '' || isNaN(item[fileForm.value.number])
        ? ''
        : parseFloat(item[fileForm.value.number]);
    let serialNumber =
      item[fileForm.value.serialNumber] === '' || isNaN(item[fileForm.value.serialNumber])
        ? item[fileForm.value.serialNumber]
        : parseFloat(item[fileForm.value.serialNumber]);
    return {
      customProductName: item[fileForm.value.customProductName],
      serialNumber: serialNumber,
      productBrand: item[fileForm.value.productBrand] || importBrand.value,
      customProductSpecification: item[fileForm.value.customProductSpecification],
      customProductDescription: item[fileForm.value.customProductDescription],
      customUnit: item[fileForm.value.customUnit],
      number: number,
      sealPrice: sealPrice,
      laborCost: laborCost,
      ybhsdj: ybhsdj,
      qthsdj: qthsdj,
      costPrice: costPrice,
      rgcbdj: rgcbdj,
      ybcbdj: ybcbdj,
      qtcbdj: qtcbdj,
      uuid: item.uuid,
      totalPrice:
        number == '' || costPrice == '' ? '' : item[fileForm.value.number] * 1 * costPrice,
        remark: item[fileForm.value.remark],
    };
  });
}
function handleRowDBLClick(row, event) {
  proxy.$refs.fileCrudRef.rowEdit(row, row.$index);
}
function handleRowUpdate(row, index, done, loading) {
  inquirySheetData.value[index] = row;
  done();
}
function handleRowDel(form, index, done) {
  console.log(form, index, done);

  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      inquirySheetData.value = inquirySheetData.value.filter((item, i) => index != i);
      done();
      proxy.$message({
        type: 'success',
        message: '删除成功!',
      });
    })
    .catch(() => {});
}
let selectValue = ref('customProductName');
function clearEmptyProduct() {
  ElMessageBox({
    title: '提示',
    type: 'warning',
    message: () => [
      h('div', null, [
        h('span', null, '此操作将删除所有'),
        h(
          ElSelect,
          {
            modelValue: selectValue.value,
            placeholder: '请选择',
            clearable: true,
            style: 'width: 120px',
            'onUpdate:modelValue': value => {
              console.log(value);
              selectValue.value = value;
            },
          },
          [
            h(ElOption, {
              label: '产品名称',
              value: 'customProductName',
            }),
            h(ElOption, {
              label: '品牌',
              value: 'productBrand',
            }),
            h(ElOption, {
              label: '规格型号',
              value: 'customProductSpecification',
            }),
            h(ElOption, {
              label: '单位',
              value: 'customUnit',
            }),
            h(ElOption, {
              label: '数量',
              value: 'number',
            }),
          ]
        ),
        h('span', null, '为空的数据, 是否继续,请确保关联的表头正确?'),
      ]),
    ],
  }).then(r => {
    inquirySheetData.value = inquirySheetData.value.filter(item => {
      return !!item[selectValue.value];
    });
  });
  // proxy
  //   .$confirm('此操作将删除所有空数据, 是否继续,请确保产品名称关联的表头正确?', '提示', {
  //     confirmButtonText: '确定',
  //     cancelButtonText: '取消',
  //     type: 'warning',
  //   })
  //   .then(() => {
  //     inquirySheetData.value = inquirySheetData.value.filter(item => {
  //       return !!item.customProductName;
  //     });
  //     proxy.$message({
  //       type: 'success',
  //       message: '删除成功!',
  //     });
  //   })
  //   .catch(() => {});
}
let selectList = ref([]);
function handleChange(list) {
  selectList.value = list;
}
function handleCommand(key) {
  let dicData = [];
  if (key == 'classifyName') {
    dicData = proxy.$refs.sheet.getTree();
  }
  const option = {
    productBrand: {
      label: '品牌',
      prop: 'productBrand',

      span: 24,
    },
    customProductSpecification: {
      label: '规格型号',
      prop: 'customProductSpecification',

      span: 24,
    },
    customUnit: {
      label: '单位',
      prop: 'customUnit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      span: 24,
      type: 'select',
    },
    number: {
      label: '数量',
      prop: 'number',
      span: 24,
    },
    costPrice: {
      label: '单价',
      prop: 'costPrice',
      span: 24,
    },
    classifyName: {
      label: '分类',
      prop: 'classifyName',
      span: 24,
      type: 'tree',
      parent: false,
      dicData: dicData,
      props: {
        label: 'label',
        value: 'value',
        children: 'children',
      },
    },
  };
  if (selectList.value.length > 0) {
    if (key == 'deleteProduct') {
      const uuids = selectList.value.map(item => item.uuid);
      inquirySheetData.value = inquirySheetData.value.filter(
        (item, index) => !uuids.includes(item.uuid)
      );
    } else {
      proxy.$refs.dialogForm.show({
        title: '编辑产品',

        option: {
          column: [option[key]],
        },
        callback(res) {
          selectList.value.forEach(item => {
            item[key] = res.data[key];
          });
          res.close();
          proxy.$refs.fileCrudRef.toggleSelection();
        },
      });
    }
  } else {
    proxy.$message({
      type: 'warning',
      message: '请选择需要编辑的数据!',
    });
  }
}
function resetImport() {
  clearData();
  isEditInquirySheet.value = false;
}
function clearData() {
  inquirySheetData.value = [];
  allData.value = [];
  importBrand.value = '';
  headerRow.value = '';
  fileOptionData.value = [];
  proxy.$refs.fileRef.resetForm();
  isSave.value = false;
}
let isSave = ref(false);
function handleImportInquirySheet() {
  if (inquirySheetData.value.length > 0) {
    isEditInquirySheet.value = true;
  } else {
    importDialogVisible.value = true;
  }
}
// 提示数据为保存
function handleClose(done) {
  if (!isSave.value) {
    proxy
      .$confirm('数据为保存，是否离开？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(res => {
        // clearData();
        done();
      });
  } else {
    done();
  }
}
// 保存询价单
let inquirySheetForm = ref({});
let inquirySheetFormOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '询价单名称',
      prop: 'businessName',
      type: 'input',
      span: 24,
    },
    {
      label: '报价时间',
      type: 'date',
      prop: 'offerDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '关联供应商',
      component: 'wf-supplier-select',
      prop: 'supplierId',
    },
    {
      label: '报价人',
      type: 'input',
      prop: 'offerPerson',
    },
    {
      label: '联系电话',
      prop: 'offerPhone',
    },
    {
      label: '备注',
      span: 24,
      type: 'textarea',
      prop: 'remark',
    },
  ],
});
let inquirySheetFormDialog = ref(false);
function handleOpenSave() {
  inquirySheetForm.value.businessName = fileName;
  inquirySheetFormDialog.value = true;
}
function handleContactChange({ concatName, concatPhone }, done) {
  inquirySheetForm.value.offerPerson = concatName;
  inquirySheetForm.value.offerPhone = concatPhone;
  done();
}
function handleSave(form, done, loading) {
  const result = inquirySheetData.value.some(item => {
    if (isNaN(item.costPrice) || isNaN(item.totalPrice) || isNaN(item.number)) {
      console.log(item);
    }
    return isNaN(item.costPrice) || isNaN(item.totalPrice) || isNaN(item.number);
  });
  if (result) return proxy.$message.error('请检查数量，单价，金额列不能出现非数字');
  axios
    .post('/api/vt-admin/businessInquiry/save', {
      ...inquirySheetForm.value,
      dtoList: inquirySheetData.value,
      businessId: route.query.businessOpportunityId || form.valu,
    })
    .then(r => {
      proxy.$message({
        type: 'success',
        message: '保存成功!',
      });
      proxy.$refs.inquirySheetFormRef.resetForm();
      isSave.value = true;
      isEditInquirySheet.value = false;
      inquirySheetFormDialog.value = false;
      done();
      const data = inquirySheetData.value.filter(item => item.classifyName);
      proxy.$refs.sheet.setData(data);
      sheetPage.value.current = 1;
      currentSheetData.value = null;
      inquirySheetPageData.value = [];
      getInquirySheetPage();
      clearData();
    });
}

// 从询价单添加产品
let isSelectFromSheet = ref(false);
let currentModuleName = ref('');
let currentClassify = ref('');
function openSheetProductSelect({ moduleName, classify }) {
  isSelectFromSheet.value = true;
  inquiryDrawer.value = true;
  currentModuleName.value = moduleName;
  currentClassify.value = classify;
  inquirySheetPageData.value = [];
  sheetPage.value.current = 1;
  currentSheetData.value = null;
  getInquirySheetPage({});
}
function handleConfirm() {
  const data = proxy.$refs.inquirySheetDetailRef.selectList.map(item => {
    return {
      ...item,
      source: 2,
      classifyName: `${currentModuleName.value}?${currentClassify.value}`,
    };
  });
  console.log(data);

  proxy.$refs.sheet.setData(data);
  handleCancel();
}
function handleCancel() {
  proxy.$refs?.inquirySheetDetailRef?.clearSelect();
  inquiryDrawer.value = false;
}
async function viewRequirement() {
  await getBusinessDetail();
  let type = 'view';
  axios
    .get('/api/vt-admin/clue/detail', {
      params: {
        id: businessForm.value.clueIds,
      },
    })
    .then(res => {
      res.data.data = {
        ...res.data.data,
        propertyVOList: res.data.data.propertyVOList.map(item => {
          return {
            ...item,
          };
        }),
      };
      proxy.$refs.dialogForm.show({
        title: (type == 'edit' ? '编辑' : '查看') + '需求采集信息',

        option: {
          labelPosition: 'top',
          detail: type == 'view',
          column: res.data.data.propertyVOList.map(item => {
            return {
              label: item.name,
              prop: item.id,
              label: item.collectionContent,
              span: 24,
              value: item.type == 1 ? item.value && item.value.split(',') : item.value,
              type: item.type == 0 ? 'radio' : item.type == 1 ? 'checkbox' : 'textarea',
              props: {
                value: 'value',
                label: 'value',
              },
              dicData: item.type == 1 || item.type == 0 ? item.requirementPropertyValuesVOS : [],
            };
          }),
        },
        callback(res) {
          res.close();
        },
      });
    });
}
function handleCloseTag() {
  
  if (proxy.$route.query.isFromOther == 1) {
    window.close();
  } else {
    proxy.$router.$avueRouter.closeTag();
    proxy.$router.back();
  }
}
function exportProductByHistory(value) {
  exportProgramme(1);
  viewObj.value = value
}



//向供应商询价
let selectListForInquiry = ref([])

provide('addInquiry', function (value) { 
  debugger
  if(selectListForInquiry.value.find(item => item.id == value.id)){
    ElMessage.warning('该产品已添加至询价中')
    return
  }
  selectListForInquiry.value.push(value)
})

// 处理询价按钮点击事件
function handleInquiry() {
  if (!form.value.id) {
    ElMessage.warning('请先保存方案');
    return;
  }
  // 打开询价历史抽屉
  inquiryManagementDrawer.value = true;
}

function handleAddInquiryForm() {
  if (!form.value.id) {
    ElMessage.warning('请先保存方案');
    return;
  }
  // 通过nextTick确保组件已渲染，然后调用新增询价方法
  proxy.$nextTick(() => {
    const inquiryComponent = proxy.$refs.inquiryManagement;
    if (inquiryComponent && inquiryComponent.handleAddInquiry) {
      inquiryComponent.handleAddInquiry();
    }
  });
}
// 处理从询价组件传来的产品导入事件
function handleImportProducts(products) {
  debugger
  const {detailId,isReplaceProduct,inquiryPrice} = products[0]
  if(detailId){
    if(isReplaceProduct == 1){
      ['customProductName','customProductSpecification','productBrand','customProductDescription','customUnit'].forEach(item => {
       proxy.$refs.sheet.setDataByInquiry({key:item,id:detailId,value:products[0][item]})
      })
    }
     proxy.$refs.sheet.setDataByInquiry({key:'costPrice',id:detailId,value:inquiryPrice})
     ElMessage.success('引入成功')
  }else{
   proxy.$confirm('该产品未在方案中存在，是否添加到方案中？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
       proxy.$refs.sheet.setDataByInquiry({data:{
        ...products[0],
        source:2,
        costPrice:inquiryPrice,
        number:parseFloat(products[0].number)
       }})
   })

  }
  

}

function handleAddInquiry(row) {
  const {id} = row
  if(selectListForInquiry.value.find(item => item.id == id)){
    ElMessage.warning('该产品已添加')
    return
  }else{
    selectListForInquiry.value.push(row)
    ElMessage.success('添加成功')
  }
}



</script>

<style scoped lang="scss">
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
/*  ::v-deep .el-table .el-table__cell {
  padding: 0;
}
::v-deep .el-collapse-item__header {
  line-height: 33px;
}
::v-deep .el-form-item {
  margin-bottom: 5px; 
}
::v-deep .el-tabs__header {
  margin-bottom: 5px;
}
.el-collapse-item__content {
  padding-bottom: 8px;
}
.el-table .el-form-item {
  margin-bottom: 0;
}
*/

.fullScreen {
  position: fixed;

  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  left: 0;
  top: 0;
  z-index: 999;
  background-color: #fff;
  padding: 15px;
}
::v-deep .affix .el-table .el-table__empty-block {
  display: none !important;
}
::v-deep .affix .el-table__body-wrapper {
  display: none !important;
}
.tab_box .move1 {
  display: none;
}
.tab_box:hover .move1 {
  display: inline-block;
}
.warningInput {
  /* border: 1px solid var(--el-color-warning) !important; */
}
::v-deep .warningInput .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}
.warningInput {
  color: var(--el-color-danger);
}
.isFullSreen {
  position: fixed;
  height: 100vh !important;
  width: 100vw;
  background-color: #fff;
  top: 0;
  left: 0;
  z-index: 1000;
}
.programmeSelect {
  position: fixed;
  padding: 5px;
  top: calc(50% - 250px);
  right: -500px;
  border-radius: 5px;
  height: 500px;
  width: 300px;
  transition: all 0.3s;
  z-index: 99999;
  // border: 1px solid #ccc;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  .content {
    padding: 15px;
    padding-bottom: 0px;
    overflow: hidden;
    .title {
      // display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ccc;
      flex-direction: column;
      align-items:center;
    }
    .btn_box {
      // background-color: red;
      height: 25px;
      transition: all 0.2s;
      line-height: 25px;
      div {
        width: 50%;
        text-align: center;
      }
    }
  }
}
.active {
  right: 20px;
}
.select {
  border: 1px solid $color-primary;
  .btn {
    height: 25px !important;
    margin-bottom: 5px;
  }
}
</style>
