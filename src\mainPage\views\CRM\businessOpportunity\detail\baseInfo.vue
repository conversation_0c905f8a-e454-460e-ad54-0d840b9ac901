<template>
  <el-row :gutter="20">
    <el-col :span="16">
      <!-- 基本信息 -->
      <el-card shadow="never">
        <div>
          <avue-form
            v-if="!isEdit"
            :option="detailOption"
            @submit="handleSubmit"
            :modelValue="props.form"
          >
            <template #fileIds> <File :fileList="form.attachList"></File></template>
            <template #gatherFiles> <File :fileList="form.gatherFilesList"></File></template>
          </avue-form>
          <avue-form v-else :option="editOption" @submit="handleSubmit" v-model="form1">
            <template #name>
              <el-autocomplete
                style="width: 100%"
                v-model="form1.name"
                :fetch-suggestions="querySearch"
                :trigger-on-focus="false"
                value-key="name"
                placeholder="请输入商机名称"
              ></el-autocomplete>
            </template>
          </avue-form>
          <!-- 产品选择弹窗 -->
          <wf-product-select
            ref="product-select"
            check-type="box"
            @onConfirm="handleUserSelectConfirm"
          ></wf-product-select>
        </div>
      </el-card>
    </el-col>
    <el-col :span="8">
      <el-card shadow="never" style="height: 100%" :body-style="{ padding: '5px' }">
        <Title
          >跟进信息
          <template #foot>
            <el-button type="primary" icon="plus" size="small" @click="handleAddFollow"
              >新增跟进</el-button
            >
          </template>
        </Title>
        <el-timeline >
          <el-timeline-item center
            v-for="(item, index) in followList"
            :timestamp="item.createTime"
            placement="top"
          >
            <el-form >
              <el-form-item label="跟进人:" style="margin:0;color:var(--el-color-primary)">{{item.followName}}  <el-link style="margin-left:10px" @click="viewDetail(item)" size="small" type="primary"><el-icon><View/></el-icon> </el-link> </el-form-item>
              <el-form-item label="跟进记录:" style="margin:0;white-space:wrap">
                <el-tooltip :content="item.followContent" placement="">
                  {{item.followContent}}
                </el-tooltip>
              </el-form-item>
            </el-form>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { businessOpportunityData } from '@/const/const';
const props = defineProps({
  form: {
    type: Object,
    default: () => {},
  },
  id:{
    type:String,
  },
  customerId:{
    type:String,
  },
  isEdit: Boolean,
  
});
let { proxy } = getCurrentInstance();
const form1 = ref({});

watch(
  () => props.isEdit,
  () => {
    if (props.isEdit) {
      form1.value = {
        ...props.form,
        scene: props.form.scene || '',
        fileIds: props.form.attachList.map(item => {
          return { value: item.id, label: item.originalName };
        }),
      };
      if (form1.value.assistant == proxy.$store.getters.userInfo.user_id) {
        setCustomerUrl();
      }
      
    }
  }
);
watch(() => props.form, (val) => {
  console.log(props.form)
  if (val.id) {
    getFollowList();
  }
},{deep:true});

function setCustomerUrl() {
  const customer = proxy.findObject(editOption.value.column, 'customerId');

  customer.params.Url = '/vt-admin/customer/page?type=2';
}
const route = useRoute();
const router = useRouter();
let detailOption = ref({
  labelWidth: 150,
  submitBtn: false,
  detail: true,
  emptyBtn: false,
  column: [
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择商机分类',
    //     },
    //   ],
    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    //   control: val => {
    //     return {
    //       projectLeader: {
    //         display: val == 1,
    //       },
    //       post: {
    //         display: val == 1,
    //       },
    //       leaderPhone: {
    //         display: val == 1,
    //       },
    //     };
    //   },
    // },
    {
      label: '商机名称',
      prop: 'name',
      blur: val => {
        reportForm.value.name = val.value;
      },
      rules: [
        {
          required: true,
          message: '请输入商机名称',
        },
      ],
    },
    {
      label: '关联需求',
      prop: 'clueIds',
      type: 'select',
      dicUrl: '/api/vt-admin/clue/page?size=50&current=1&selectType=0',
      props: { label: 'clueName', value: 'id', desc: 'status' },
      dicFormatter: res => {
        return res.data.records.map(item => {
          return {
            ...item,
            status: item.requirementStatus == 0 ? '未填写' : '已填写',
          };
        });
      },
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 12,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'type',
      // checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
      // lazy: true,

      // treeLoad: function (node, resolve) {
      //   axios
      //     .get('/api/vt-admin/productCategory/list', {
      //       params: {
      //         parentId: node.data.id,
      //       },
      //     })
      //     .then(res => {
      //       resolve(
      //         res.data.data.map(item => {
      //           return {
      //             ...item,
      //             leaf: !item.hasChildren,
      //             disabled: item.hasChildren,
      //           };
      //         })
      //       );
      //     });
      // },
      // checked(a, b) {
      //   currentChecked.value = b.checkedKeys.join(',');
      // },
    },
    {
      type: 'tree',
      label: '商机关联',
      multiple: true,
      width: 100,
      span: 12,
      // parent: false,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],

      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
      display: true,
      filterable: true,
      prop: 'businessCategoryName',
      checkStrictly: true,
    },
    {
      type: 'select',
      label: '商机场景',
      multiple: true,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'scene',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityScence',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '所在区域',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },
      rules: [
        {
          required: true,
          message: '所在区域必须填写',
          trigger: 'blur',
        },
      ],
      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      label: '对应客户',
      prop: 'customerName',
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      component: 'wf-contact-select',
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      dataType: 'string',
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
    {
      type: 'select',
      label: '商机来源',
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '介绍人',
      prop: 'introducer',
      type: 'input',
    },

    {
      label: '关联产品',
      prop: 'isProduct',
      labelTip: '简单商机可直接关联产品',
      type: 'switch',
      // control: val => {
      //   return {
      //     productVOList: {
      //       display: val == 1,
      //     },
      //   };
      // },
      display:false,
      dicData: [
        { value: 0, label: '否' },
        { value: 1, label: '是' },
      ],
    },
    {
      label: '下次跟进时间',
      prop: 'nextFollowTime',
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '产品',
      prop: 'productVOList',
      type: 'dynamic',
      display: false,
      span: 24,

      children: {
        rowAdd: done => {
          // console.log(proxy.$refs['product-select']);
          proxy.$refs['product-select'].visible = true;
        },
        column: [
          {
            label: '产品编号',
            prop: 'productCode',
            overHidden: true,
            cell: false,
            // placeholder: '自动生成',
          },
          {
            label: '产品名称',
            prop: 'productName',
            cell: false,
            overHidden: true,
          },
          {
            label: '品牌',
            prop: 'productBrand',
            cell: false,
            overHidden: true,
          },
          {
            label: '单位',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            width: 80,
            cell: false,
            prop: 'unit',
            dicUrl: '/blade-system/dict/dictionary?code=unit',
            remote: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            cell: false,
            span: 24,
            type: 'input',
          },

          {
            label: '产品图片',
            prop: 'coverUrl',
            type: 'upload',
            dataType: 'object',
            listType: 'picture-img',
            loadText: '图片上传中，请稍等',
            span: 24,
            slot: true,
            cell: false,
            limit: 1,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/blade-resource/attach/upload',
          },

          {
            label: '商品描述',
            prop: 'description',
            overHidden: true,
            cell: false,
            type: 'textarea',
            span: 24,
          },

          {
            label: '用途',
            prop: 'purpose',
            overHidden: true,
            type: 'textarea',
            span: 24,

            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            width: 120,
            change: a => {
              const { value, row } = a;
              row.totalPrice = row.referPurchasePrice * value;
            },
          },
        ],
      },
    },
    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '项目联系人',
      prop: 'projectLeader',
      display: false,
      span:24,
      type: 'input',
    },
    {
      label: '需求采集表',
      prop: 'gatherFiles',
      span:24,
      display: true,
      type: 'input',
    },
     {
      label:'是否协议商机',
      prop:'isFrameworkAgreement',
      type:'radio',
     span:24,
      rules:[{
        required: true,
        message: '请选择是否协议商机',

      }],
      dicData:[{
        value:0,
        label:'否'
      },{
        value:1,
        label:'是'
      }]
    },
      {
      label:'是否合作商机',
      prop:'isCooperation',
      type:'radio',
      value:0,
     
      dicData:[{
        value:0,
        label:'否'
      },{
        value:1,
        label:'是'
      }],
      control: val => {
        return {
          cooperationTenantId: {
            display: val == 1,
          },
        };
      },
    },
    // 租户列表
    {
      label: '合作方',
      prop: 'cooperationTenantName',
    },
    {
      label: '岗位',
      prop: 'post',
      display: false,
      type: 'input',
    },
    {
      label: '联系人手机',
      prop: 'leaderPhone',
      display: false,
      type: 'input',
    },
    
    {
      label: '商机描述',
      prop: 'description',
      span: 24,
      type: 'textarea',
    },
    {
      label: '报备',
      prop: 'isReport',
      span: 24,
      type: 'switch',

      dicData: [
        { value: 0, label: '否' },
        { value: 1, label: '是' },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);

let editOption = ref({
  labelWidth: 150,
  submitBtn: true,
  detail: false,
  emptyBtn: true,
  column: [
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择商机分类',
    //     },
    //   ],
    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    //   control: val => {
    //     return {
    //       projectLeader: {
    //         display: val == 1,
    //       },
    //       post: {
    //         display: val == 1,
    //       },
    //       leaderPhone: {
    //         display: val == 1,
    //       },
    //     };
    //   },
    // },
    {
      label: '商机名称',
      prop: 'name',
      blur: val => {
        reportForm.value.name = val.value;
      },
      rules: [
        {
          required: true,
          message: '请输入商机名称',
        },
      ],
    },
    {
      label: '关联需求',
      prop: 'clueIds',
      type: 'select',
      dicUrl: '/api/vt-admin/clue/page?size=50&current=1&selectType=0',
      props: { label: 'clueName', value: 'id', desc: 'status' },
      dicFormatter: res => {
        return res.data.records.map(item => {
          return {
            ...item,
            status: item.requirementStatus == 0 ? '未填写' : '已填写',
          };
        });
      },
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 12,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'type',
      // checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
      // lazy: true,

      // treeLoad: function (node, resolve) {
      //   axios
      //     .get('/api/vt-admin/productCategory/list', {
      //       params: {
      //         parentId: node.data.id,
      //       },
      //     })
      //     .then(res => {
      //       resolve(
      //         res.data.data.map(item => {
      //           return {
      //             ...item,
      //             leaf: !item.hasChildren,
      //             disabled: item.hasChildren,
      //           };
      //         })
      //       );
      //     });
      // },
      // checked(a, b) {
      //   currentChecked.value = b.checkedKeys.join(',');
      // },
    },
    {
      type: 'tree',
      label: '商机关联',
      multiple: true,
      width: 100,
      span: 12,
      // parent: false,
      overHidden: true,
      

      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
      display: true,
      filterable: true,
      prop: 'businessCategory',
      checkStrictly: true,
    },
    {
      type: 'select',
      label: '商机场景',
      span: 12,
      multiple: true,
      // rules: [
      //   {
      //     required: true,
      //     message: '请选择商机来源',
      //   },
      // ],
      display: true,
      prop: 'scene',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityScence',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '所在区域',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },
      rules: [
        {
          required: true,
          message: '所在区域必须填写',
          trigger: 'blur',
        },
      ],
      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      label: '对应客户',
      prop: 'customerId',
      component: 'wf-customer-select',
      change: val => {
        const contactPerson = proxy.findObject(editOption.value.column, 'contactPerson');
        contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val.value;
        setBaseInfo(val.value);
        customerId.value = val.value;
        reportForm.value.customerId = val.value;
      },
      params: {},
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      component: 'wf-contact-select',
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      dataType: 'string',
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
    {
      type: 'select',
      label: '商机来源',
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '介绍人',
      prop: 'introducer',
      type: 'input',
    },

    {
      label: '关联产品',
      prop: 'isProduct',
      labelTip: '简单商机可直接关联产品',
      type: 'switch',
      control: val => {
        return {
          productVOList: {
            display: val == 1,
          },
        };
      },
      display:false,
      dicData: [
        { value: 0, label: '否' },
        { value: 1, label: '是' },
      ],
    },
    {
      label: '下次跟进时间',
      prop: 'nextFollowTime',
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '产品',
      prop: 'productVOList',
      type: 'dynamic',
      display: false,
      span: 24,

      children: {
        rowAdd: done => {
          // console.log(proxy.$refs['product-select']);
          proxy.$refs['product-select'].visible = true;
        },
        column: [
          {
            label: '产品编号',
            prop: 'productCode',
            overHidden: true,
            cell: false,
            // placeholder: '自动生成',
          },
          {
            label: '产品名称',
            prop: 'productName',
            cell: false,
            overHidden: true,
          },
          {
            label: '品牌',
            prop: 'productBrand',
            cell: false,
            overHidden: true,
          },
          {
            label: '单位',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            cell: false,
            prop: 'unit',
            dicUrl: '/blade-system/dict/dictionary?code=unit',
            remote: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            cell: false,
            span: 24,
            type: 'input',
          },

          {
            label: '产品图片',
            prop: 'coverUrl',
            type: 'upload',
            dataType: 'object',
            listType: 'picture-img',
            loadText: '图片上传中，请稍等',
            span: 24,
            slot: true,
            cell: false,
            limit: 1,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/blade-resource/attach/upload',
          },

          {
            label: '商品描述',
            prop: 'description',
            overHidden: true,
            cell: false,
            type: 'textarea',
            span: 24,
          },

          {
            label: '用途',
            prop: 'purpose',
            overHidden: true,
            type: 'textarea',
            span: 24,

            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            change: a => {
              const { value, row } = a;
              row.totalPrice = row.referPurchasePrice * value;
            },
          },
        ],
      },
    },
     {
      label:'是否协议商机',
      prop:'isFrameworkAgreement',
      type:'radio',
      span:24,
      rules:[{
        required: true,
        message: '请选择是否协议商机',

      }],
      dicData:[{
        value:0,
        label:'否'
      },{
        value:1,
        label:'是'
      }]
    },
      {
      label:'是否合作商机',
      prop:'isCooperation',
      type:'radio',
      value:0,
     
      dicData:[{
        value:0,
        label:'否'
      },{
        value:1,
        label:'是'
      }],
      control: val => {
        return {
          cooperationTenantId: {
            display: val == 1,
          },
        };
      },
    },
    // 租户列表
    {
      label: '合作方',
      prop: 'cooperationTenantId',
      type: 'select',
      dicUrl: '/api/blade-system/tenant/selectList',
      props: {
        label: 'tenantName',
        value: 'tenantId',
      },
      rules: [
        {
          required: true,
          message: '请选择合作方',
        },
      ],
    },
    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '项目联系人',
      prop: 'projectLeader',
      display: false,
      type: 'input',
    },
    {
      label: '岗位',
      prop: 'post',
      display: false,
      type: 'input',
    },
    {
      label: '联系人手机',
      prop: 'leaderPhone',
      display: false,
      type: 'input',
    },
    {
      label: '商机描述',
      prop: 'description',
      span: 24,
      type: 'textarea',
    },
    {
      label: '报备',
      prop: 'isReport',
      span: 24,
      type: 'switch',
      dicData: [
        { value: 0, label: '否' },
        { value: 1, label: '是' },
      ],
      control: val => {
        console.log(val);
        return {
          productType: {
            display: val == 1,
          },
          productName: {
            display: val == 1,
          },
          manufacturer: {
            display: val == 1,
          },
          reportPerson: {
            display: val == 1,
          },
        };
      },
    },
    {
      label: '产品类型',
      prop: 'productType',
      search: true,
      type: 'input',
    },
    {
      label: '产品名称',
      prop: 'productName',
    },
    {
      label: '厂家名称',
      prop: 'manufacturer',
    },
    {
      label: '报备人',
      prop: 'reportPerson',
      component: 'wf-user-select',
    },
  ],
});

function handleSubmit(form, done, loading) {
  const { customerId, manufacturer, productName, productType, reportPerson } = form;
  const data = {
    ...form,
    id: props.id,
    fileIds: form.fileIds.map(item => item.value).join(','),
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
    businessOpportunityReportEntity: {
      customerId,
      manufacturer,
      productName,
      productType,
      reportPerson,
    },
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode } = res.data.data;
      form1.value.province_city_area = [provinceCode, cityCode, areaCode];
      form1.value.address = res.data.data.address;
      const mainPerson = res.data.data.customerContactVO?.id;
      form1.value.contactPerson = mainPerson;
    });
}
function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      form1.value.productVOList.push({
        ...r.data.data,
        productId: r.data.data.id,
        number: 1,
        id: null,
      });
    });
  });
}
function querySearch(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/businessOpportunity/list', {
      params: {
        size: 1000,
        name: val,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
let followList = ref([]);
onMounted(() => {
  // getFollowList();
});
function getFollowList() {

  axios
    .get('/api/vt-admin/customerFollow/pageForDetail', {
      params: {
        size: 5000,
        customerId: props.customerId  ,
        logicId: props.id || props.form.id,
      },
    })
    .then(res => {
      followList.value = res.data.data.records;
    });
}
function handleAddFollow() {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      type: 1,
      customerId: props.customerId || null,
      logicId: props.id || null,
    },
  });
}
function viewDetail(item) {
  router.push({
    path: '/CRM/follow/compoents/detail',
    query: {
      id: item.id,
    },
  });
}
</script>

<style lang="scss" scoped></style>
