<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button type="primary" text icon="Edit" v-if="row.auditStatus == '0'" @click="audit(row)">审核</el-button>
       
      </template>
      <template #contractName="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
        </el-link>
      </template>
      <template #auditStatus="{ row }">
        <el-tag effect='plain' type="success" v-if="row.auditStatus == '1'">通过</el-tag>
        <el-tag effect='plain' type="danger" v-if="row.auditStatus == '2'">拒绝</el-tag>
        <el-tag effect='plain' type="info" v-if="row.auditStatus == '0'">待审核</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '合同名称',
      prop: 'contractName',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '申请人',
      prop: 'createName',
    },

    {
      label: '申请理由',
      prop: 'applyReason',
      overHidden: true,
    },
    {
      label: '申请时间',
      prop: 'createTime',
    },
    {
      label: '审核状态',
      prop: 'auditStatus',
      type: 'select',
      dicData: [
        { label: '待审核', value: 0 },
        { label: '审核通过', value: 1 },
        { label: '审核拒绝', value: 2 },
      ],
    },
    // {
    //   label: '审核人',
    //   prop: 'auditName',
    // },
    {
      label: '审核时间',
      prop: 'auditTime',
    },
    {
      label: '审核意见',
      prop: 'auditReason',
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractCancel/auditPage';
let route = useRoute();
let params = ref({
  auditStatus: route.query.type? 0:null,
});
watch(() => route.query.type, () => {
  params.value.auditStatus = route.query.type? 0:null;
  onLoad();
})
let tableData = ref([]);
let { proxy } = getCurrentInstance();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function audit(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',

    option: {
      column: [
        {
          label: '审核结果',
          prop: 'auditStatus',
          type: 'radio',
          dicData: [
            { label: '通过', value: 1 },
            { label: '不通过', value: 2 },
          ],
        },
        {
          label: '备注',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealContractCancel/audit', {
          ...res.data,
          id: row.id,
        })
        .then(r => {
          proxy.$message({
            type: 'success',
            message: r.data.msg,
          });
          proxy.$store.dispatch('getMessageList');
          onLoad();
          res.close();
        });
    },
  });
}
function toDetail(row) {
  if(row.auditStatus == 1) return proxy.$message.warning('该合同已删除')
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId      ,
      
    },
  });
}
</script>

<style lang="scss" scoped></style>
