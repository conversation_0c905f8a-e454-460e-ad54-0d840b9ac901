<template>
  <el-dialog class="avue-dialog" title="选择工程师" v-model="dialogVisible" width="1000">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @selection-change="handleChange"
      @size-change="onLoad"
      v-model="form"
      @row-click="rowClick"
    >
      <template #radio="{ row }">
        <el-radio v-model="selectRow" :label="row.id">{{}}</el-radio>
      </template>
    </avue-crud>
    <span class="avue-dialog__footer">
      <el-button @click="dialogVisible = false" icon="close">取 消</el-button>
      <el-button type="primary" @click="handleConfirm" icon="check">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  searchMenuSpan: 6,
  selection: false,
  selectable: row => !selectList.value.length,
  border: true,
  menu: false,
  column: [
    {
      label: '',
      type: 'input',
      prop: 'radio',
      width: 50,
    },
    {
      label: '用户昵称',
      prop: 'name',
      overHidden: true,
    },
    {
      label: '真实姓名',
      prop: 'realName',
      searchSpan: 8,
      overHidden: true,
      search: true,
    },
    {
      label: '用户头像',
      prop: 'avatar',
      type: 'upload',
      value: [],
      dataType: 'string',
      listType: 'picture-card',
      limit: 1,
      loadText: '附件上传中，请稍等',
      span: 24,
      accept: 'image/png, image/jpeg,image/jpg',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },

    {
      label: '手机号',
      prop: 'phone',
    },
  ],
});
let form = ref({});
let dialogVisible = ref(false);
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/blade-system/user/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let selectRow = ref({});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        userType: 2,
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleIsHasCollectionCodeChange(item) {
  axios
    .post('/api/blade-system/user/update-info', {
      isHasCollectionCode: item.isHasCollectionCode,
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
let selectList = ref([]);
const emits = defineEmits(['onConfirm']);
function handleChange(list) {
  selectList.value = list.length ? [list.pop()] : [];
}
function rowClick(row) {
  //   proxy.$refs.crud.toggleSelection([row]);
  //   selectList.value = [row];
  selectRow.value = row.id;
}
function handleConfirm() {
  const data = tableData.value.find(item => item.id === selectRow.value);
  
  emits('onConfirm', [data]);
  dialogVisible.value = false;
}
defineExpose({
  dialogVisible,
});
</script>

<style lang="scss" scoped></style>
