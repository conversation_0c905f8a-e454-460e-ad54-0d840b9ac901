<template>
  <el-drawer title="采购成本明细" v-model="drawer" size="80%">
    <div style="height: 100%">
      <div style="height: 100%" v-loading="loading" element-loading-text="刷新数据中">
        <el-row style="height: 100%" :gutter="20">
          <el-col :span="5">
            <el-card shadow="never" style="height: 100%">
              <avue-tree
                ref="tree"
                :option="treeOption"
                :data="treeData"
                @node-click="handleNodeClick"
              ></avue-tree>
            </el-card>
          </el-col>
          <el-col :span="19">
            <el-card
              shadow="never"
              style="height: 500px; overflow-y: auto"
              :style="{ height: height + 'px' }"
            >
              <div :ref="`box_${item.moduleId}`" v-for="(item, index) in treeData" :key="item.id">
                <h3>{{ item.moduleName }}</h3>
                <el-table
                  class="avue-crud"
                  v-if="item.children && item.children.length > 0"
                  :span-method="arraySpanMethod"
                  :data="item.children"
                  :row-class-name="rowClassName"
                  border
                  :expand-row-keys="item.children && item.children.map(item => item.id)"
                  row-key="id"
                  align="center"
                >
                  <el-table-column type="expand">
                    <template #default="{ row: rowP }">
                      <div
                        style="
                          box-sizing: border-box;
                          margin-top: -9px;
                          display: flex;
                          align-items: flex-end;
                          margin-left: 47px;
                        "
                      >
                        <el-table
                          class="avue-crud"
                          :show-header="false"
                          ref="tableItem"
                          :data="rowP.detailVOList"
                          :row-class-name="tableRowClassName"
                          border
                          row-key="id"
                          :tree-props="{
                            children: 'splitDTOList',
                          }"
                          align="center"
                        >
                          <el-table-column
                            label="设备名称"
                            #default="{ row }"
                            show-overflow-tooltip
                            prop="customProductName"
                            width="130"
                          >
                            <el-tag
                              size="small"
                              type="danger"
                              v-if="row.splitDTOList && row.splitDTOList.length > 0"
                              effect="plain"
                              >拆</el-tag
                            >
                            <el-tag
                              type="danger"
                              effect="plain"
                              size="small"
                              v-if="!row.detailId && row.isChange == 1"
                              >新</el-tag
                            >{{ row.productName }}
                            <span>{{ row.customProductName }}</span>
                          </el-table-column>
                          <el-table-column
                            label="规格型号"
                            show-overflow-tooltip
                            #default="{ row }"
                            prop="customProductSpecification"
                            width="130"
                          >
                            {{
                              row.customProductSpecification || row.product?.productSpecification
                            }}</el-table-column
                          >

                          <el-table-column
                            label="产品描述"
                            show-overflow-tooltip
                            width="200"
                            #default="{ row }"
                            prop="customProductDescription"
                          >
                            {{ row.customProductDescription || row.product?.description }}
                          </el-table-column>
                          <el-table-column
                            label="品牌"
                            align="center"
                            #default="{ row }"
                            prop="productBrand"
                            width="80"
                          >
                            {{ row.productBrand || row.product?.productBrand }}
                          </el-table-column>
                          <el-table-column
                            label="单位"
                            align="center"
                            #default="{ row }"
                            width="80"
                            prop="product.unitName"
                          >
                            {{ row.customUnit || row.product?.unitName }}
                          </el-table-column>
                           <el-table-column label="数量" align="center" prop="deepenNumber">
                          </el-table-column>
                                   
                          <el-table-column label="已请购数量" align="center" prop="purchaseNums">
                               
                            <template #default="{ row }">
                                {{ parseFloat(row.purchaseNums || row.purchaseNumber || '0') }}    
                                  
                            </template>
                                               
                          </el-table-column>
                                   
                          <el-table-column label="未采购数量" align="center" prop="noPurchaseNums">
                                     
                            <template #default="{ row }">
                                {{ parseFloat(row.noPurchaseNums || noPurchaseNumber || '0') }}    
                            </template>
                                 
                          </el-table-column>
                        </el-table>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="所属分类" prop="classify" width="120"> </el-table-column> -->
                  <el-table-column
                    label="设备名称"
                    #default="{ row }"
                    show-overflow-tooltip
                    prop="customProductName"
                    width="130"
                  >
                    <span style="font-size: 18px; font-weight: bolder; color: black">{{
                      row.value
                    }}</span></el-table-column
                  >
                  <el-table-column
                    label="规格型号"
                    show-overflow-tooltip
                    #default="{ row }"
                    prop="customProductSpecification"
                    width="130"
                  >
                    {{
                      row.customProductSpecification || row.product?.productSpecification
                    }}</el-table-column
                  >

                  <el-table-column
                    label="产品描述"
                    show-overflow-tooltip
                    width="200"
                    #default="{ row }"
                    prop="customProductDescription"
                  >
                    {{ row.customProductDescription || row.product?.description }}
                  </el-table-column>
                  <el-table-column
                    label="品牌"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="productBrand"
                  >
                    {{ row.productBrand || row.product?.productBrand }}
                  </el-table-column>
                  <el-table-column
                    label="单位"
                    align="center"
                    width="80"
                    #default="{ row }"
                    prop="product.unitName"
                  >
                    {{ row.customUnit || row.product?.unitName }}
                  </el-table-column>
                   <el-table-column label="数量" align="center" prop="deepenNumber">
                  </el-table-column>
                           
                  <el-table-column
                    label="已请购数量"
                    align="center"
                    #default="{ row }"
                    prop="purchaseNums"
                  >
                                 
                  </el-table-column>
                           
                  <el-table-column
                    label="未采购数量"
                    align="center"
                    #default="{ row }"
                    prop="noPurchaseNums"
                  >
                                 
                  </el-table-column>

                
                </el-table>
                <div v-else style="padding-left: 48px; box-sizing: border-box">
                  <el-table
                    class="avue-crud"
                    ref="tableItem"
                    :data="item.detailVOList"
                    :row-class-name="tableRowClassName"
                    border
                    row-key="id"
                    align="center"
                    :tree-props="{
                      children: 'splitDTOList',
                    }"
                  >
                    <el-table-column
                      label="设备名称"
                      #default="{ row }"
                      show-overflow-tooltip
                      prop="customProductName"
                      width="130"
                    >
                      <el-tag
                        size="small"
                        type="danger"
                        v-if="row.splitDTOList && row.splitDTOList.length > 0"
                        effect="plain"
                        >拆</el-tag
                      >
                      <span>{{ row.customProductName }}</span>
                    </el-table-column>
                    <el-table-column
                      label="规格型号"
                      show-overflow-tooltip
                      #default="{ row }"
                      prop="customProductSpecification"
                      width="130"
                    >
                      {{
                        row.customProductSpecification || row.product?.productSpecification
                      }}</el-table-column
                    >

                    <el-table-column
                      label="产品描述"
                      show-overflow-tooltip
                      width="200"
                      #default="{ row }"
                      prop="customProductDescription"
                    >
                      {{ row.customProductDescription || row.product?.description }}
                    </el-table-column>
                    <el-table-column
                      label="品牌"
                      align="center"
                      width="80"
                      #default="{ row }"
                      prop="productBrand"
                    >
                      {{ row.productBrand || row.product?.productBrand }}
                    </el-table-column>
                    <el-table-column
                      label="单位"
                      align="center"
                      width="80"
                      #default="{ row }"
                      prop="product.unitName"
                    >
                      {{ row.customUnit || row.product?.unitName }}
                    </el-table-column>
                     <el-table-column label="数量" align="center" prop="deepenNumber">
                    </el-table-column>
                             
                    <el-table-column label="已请购数量" align="center" prop="purchaseNums">
                         
                      <template #default="{ row }">
                          {{ parseFloat(row.purchaseNums || row.purchaseNumber) }}         
                      </template>
                                         
                    </el-table-column>
                             
                    <el-table-column label="未采购数量" align="center" prop="noPurchaseNums">
                               
                      <template #default="{ row }">
                          {{ parseFloat(row.noPurchaseNums || noPurchaseNumber) }}    
                      </template>
                           
                    </el-table-column>
                  </el-table>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
  </el-drawer>

</template>

<script setup>
import { watchEffect, getCurrentInstance, onMounted, watch } from 'vue';

import { useRoute } from 'vue-router';
import { randomLenNum } from '@/utils/util';
const route = useRoute();
const props = defineProps({
  projectId: {
    type: String,
    default: '',
  },
});
const { proxy } = getCurrentInstance();
let treeData = ref([]);
let allData = ref([]);
let tableData = ref([]);
let form = ref({});
let loading = ref(false);
watch(() => props.projectId,  () => {
   getDetail();
},{
    immediate: true,
});


function getDetail() {
  allData.value = [];
  tableData.value = [];
  
  if(!props.projectId)return 
  loading.value = true;
  axios.post('/api/vt-admin/project/projectProducts?id=' + props.projectId, {}).then(res => {
    form.value = res.data.data;
    form.value.businessOpportunityId = props.id;
    const data = res.data.data.moduleVOList.map(item => {
      return {
        ...item,
        moduleId: item.id,
      };
    });
    formatData(data);
    form.value.moduleVOList = null;
    loading.value = false;
  });
}
let treeOption = ref({
  addBtn: false,
  defaultExpandAll: true,
  props: {
    value: 'value',
    label: 'label',
  },
});

function formatData(data) {
  treeData.value = data.map(item => {
    item.label = item.moduleName;
    const isHasClassify = item.detailVOList.every(item => item.classify);
    if (isHasClassify) {
      item.children = item.detailVOList
        .map(i => {
          return {
            value: i.classify,
            label: i.classify || '---',
            parentId: item.moduleId,
            id: i.moduleId + i.classify,
          };
        })
        .reduce((acc, cur) => {
          if (!acc.find(item => item.value === cur.value)) {
            acc.push({
              ...cur,

              detailVOList: item.detailVOList
                .filter(i => i.classify == cur.value)
                .map(item => {
                  return {
                    ...item,
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                    splitDTOList:
                      item.splitVOS && item.splitVOS.length > 0
                        ? item.splitVOS.map(item => {
                            item.number = 0;
                            return {
                              ...item,
                              uuid: randomLenNum(10),
                              number: parseFloat(item.number),
                              deepenNumber: item.deepenNumber
                                ? item.deepenNumber * 1
                                : item.number * 1,
                              leaderDeepenNumber:
                                form.value.deepenType == 0
                                  ? null
                                  : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                                  ? item.leaderDeepenNumber * 1
                                  : item.deepenNumber * 1,
                            };
                          })
                        : [],
                  };
                }),
            });
          }
          return acc;
        }, []);
    } else {
      item.detailVOList = item.detailVOList.map(item => {
        return {
          ...item,
          deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
          leaderDeepenNumber:
            form.value.deepenType == 0
              ? null
              : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
              ? item.leaderDeepenNumber * 1
              : item.deepenNumber * 1,
          splitDTOList:
            item.splitVOS && item.splitVOS.length > 0
              ? item.splitVOS.map(item => {
                  item.number = 0;
                  return {
                    ...item,
                    uuid: randomLenNum(10),
                    number: parseFloat(item.number),
                    deepenNumber: item.deepenNumber ? item.deepenNumber * 1 : item.number * 1,
                    leaderDeepenNumber:
                      form.value.deepenType == 0
                        ? null
                        : item.leaderDeepenNumber || item.leaderDeepenNumber == 0
                        ? item.leaderDeepenNumber * 1
                        : item.deepenNumber * 1,
                  };
                })
              : [],
        };
      });
    }
    item.isHasClassify = isHasClassify;

    return item;
  });
  getChangeList();
  allData.value = data;
}
let changeList = ref();
function getChangeList() {
  const uuid = randomLenNum(10);
  axios.post('/api/vt-admin/projectChange/completeListById?id=' + props.projectId).then(res => {
    changeList.value = res.data.data
      .filter(item => item.changeStatus == 1)
      .map(item => {
        return {
          ...item,
          moduleId: item.id,
          value: item.title,
          label: item.title,
          parentId: uuid,
          id: item.id + item.title,
          detailVOList: item.detailVOS.map(item1 => {
            return {
              ...item1,
              isLeaf: true,
              deepenNumber:
                item1.changeType == 2 ? `-${parseFloat(item1.number)}` : parseFloat(item1.number),
              parentId: item.id,
              customProductName: item1.productName,
              customProductDescription: item1.description,
              customProductSpecification: item1.productSpecification,
              customUnit: item1.unitName,

              customProductName: item1.productName,
              uuid: randomLenNum(10),
              isChange: 1,
            };
          }),
        };
      });

    if (changeList.value.length > 0) {
      treeData.value.push({
        label: '签证项',
        moduleName: '签证项',
        id: uuid,
        moduleId: uuid,
        children: [...changeList.value],
      });
    }
  });
}

function handleNodeClick(node) {
  const id = 'box_' + node.moduleId;
  proxy.$nextTick(() => {
    if (node.parentId) {
      const ele = document.querySelector(`.row_${node.parentId}${node.value}`);
      ele.scrollIntoView({ behavior: 'smooth' });
    } else {
      proxy.$refs[id][0].scrollIntoView({ behavior: 'smooth' });
    }
  });
}
function rowClassName(val) {
  return 'row_' + val.row.parentId + val.row.value;
}

function setId(id) {
  form.value.id = id;
}

function tableRowClassName(row) {
  return row.number == 0 ? 'success-row' : '';
}

let drawer = ref(false);
function open() {
    drawer.value = true;

}
defineExpose({ setId, getDetail ,open});
</script>

<style lang="scss" scoped>
:deep(.el-input-number.is-controls-right .el-input__wrapper) {
  padding-right: 30px !important;
}
</style>
