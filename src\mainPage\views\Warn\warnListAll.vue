<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex; align-items: center; gap: 10px">
          <span
            class="search-box"
            :class="{ active: params.type == item.value }"
            v-for="(item, index) in typeStatistics"
            @click="handleSearch(item)"
            style="color: #666; cursor: pointer; display: flex; align-items: center"
            :key="index"
            >{{ item.label }} (<span
              style="color: var(--el-color-danger); font-size: 25px; font-weight: bold"
              >{{ item.count }}</span
            >
            )</span
          >
        </div>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" v-if="row.alarmStatus == 0" @click="removeAlarm(row)" text
          >解除风控</el-button
        >
      </template>
      <template #alarmStatus="{ row }">
        <el-tag
          :type="row.alarmStatus == 0 ? 'danger' : row.alarmStatus == 1 ? 'warning' : 'success'"
          >{{ row.$alarmStatus }}</el-tag
        >
      </template>
      <template #alarmDegree="{ row }">
        <el-tooltip :content="row.$alarmDegree" placement="top">
          <div>
            <i
              v-for="item in (row.alarmDegree || 0) * 1 + 1"
              class="element-icons el-icon-huo"
              style="color: #d81e06"
            ></i>
          </div>
        </el-tooltip>
      </template>
      <template #content="{ row }">
        <div style="color: var(--el-color-danger)">
          <div style="display: flex; align-items: center; justify-content: center">
            <!-- 使用自定义方法处理内容 -->
            <template v-if="processContent(row.content)">
              <span>{{ processContent(row.content)[0] }}</span>
              [<el-link
                @click="handleViewDetail(row)"
                type="info"
                style="text-decoration: underline"
                >{{ processContent(row.content)[1] }}</el-link
              >]
              <span>{{ processContent(row.content)[2] }}</span>
            </template>
          </div>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <!--  -->
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  menu: false,
  column: [
    // {
    //   label: '风控编号',
    //   prop: 'code',
    //   width: 180,
    //   overHidden: true,
    //   // search: true,
    // },
    {
      label: '风控等级',
      prop: 'alarmDegree',
      width: '120',
      dicData: [
        {
          value: 0,
          label: '提醒',
        },
        {
          value: 1,
          label: '初警',
        },
        {
          value: 2,
          label: '告警',
        },
        {
          value: 3,
          label: '警报',
        },
      ],
    },
    {
      label: '风控模块',
      prop: 'moduleType',
      width: 120,
      search: true,
      type: 'select',
      dicData: [
        {
          value: 1,
          label: '客户管理',
        },
        {
          value: 2,
          label: '商机管理',
        },
        {
          value: 3,
          label: '方案管理',
        },
        {
          value: 4,
          label: '报价管理',
        },
        {
          value: 5,
          label: '采购管理',
        },
        {
          value: 6,
          label: '销售合同',
        },
        {
          value: 7,
          label: '财务管理',
        },
      ],
    },
    {
      label: '风控类型',
      prop: 'type',
      overHidden: true,
      width: 180,
      type: 'select',
      dicData: [
        {
          value: 1,
          label: '客户应收款额度',
        },
        {
          value: 2,
          label: '业务员应收款额度',
        },
        {
          value: 3,
          label: '客户应收款逾期',
        },
      ],
    },
    {
      label: '风控内容',
      prop: 'content',
      html: true,

      overHidden: true,
    },
    {
      label: '风控对象',
      prop: 'objectNames',
      width: 150,
      search: true,

      component: 'wf-user-drop',
      overHidden: true,
    },
    // {
    //   label: '风控次数',
    //   prop: 'alarmNumber',
    //   width: 120,
    //   overHidden: true,
    // },
    {
      label: '风控时间',
      prop: 'alarmTime',
      width: 120,
      type: 'date',
      component: 'wf-daterange-search',
      search: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      searchSpan: 6,
      overHidden: true,
    },
    {
      label: '状态',
      prop: 'alarmStatus',
      width: 150,
      dicData: [
        {
          value: 0,
          label: '风控 ',
        },
        {
          value: 1,
          label: '解除 ',
        },
        {
          value: 2,
          label: '解除 ',
        },
        {
          value: 3,
          label: '解除 ',
        },
        {
          value: 4,
          label: '解除 ',
        },
        {
          value: 5,
          label: '解除 ',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/alarmMessage/page';
let params = ref({
  type: '',
  // alarmStatus:0
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let typeStatistics = ref([
  {
    value: '',
    label: '全部',
    count: 0,
  },

  {
    label: '客户应收款额度',
    value: 1,
    count: 0,
  },
  {
    label: '业务员应收款额度',
    value: 2,
    count: 0,
  },
  {
    label: '客户应收款逾期',
    value: 3,
    count: 0,
  },
]);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.alarmTime && params.value.alarmTime[0],
        endTime: params.value.alarmTime && params.value.alarmTime[1],
        alarmTime: null,
        selectType: 1,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  axios
    .get('/api/vt-admin/alarmMessage/pageStatistics', {
      params:{
        ...params.value,
      startTime: params.value.alarmTime && params.value.alarmTime[0],
      endTime: params.value.alarmTime && params.value.alarmTime[1],
      alarmTime: null,
      selectType: 1,
      type: null,
      }
    })
    .then(res => {
      typeStatistics.value = typeStatistics.value.map((item, index) => {
        if (index == 0) {
          item.count = res.data.data.totalNumber;
        } else {
          item.count = res.data.data[`count${index}`];
        }
        return item
      });
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function removeAlarm(params) {
  proxy.$refs.dialogForm.show({
    option: {
      column: [
        {
          label: '原因',
          prop: 'removeReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/alarmMessage/removeAlarm', {
          id: params.id,
          ...res.data,
        })
        .then(r => {
          if (r.data.code === 200) {
            res.close();
            proxy.$message.success('解除风控成功');
            onLoad();
          }
        });
    },
  });
}

// 定义一个方法来处理 content 字符串
const processContent = content => {
  const regex = /(\[([^\]]+)\])/;
  const match = content.match(regex);
  if (match) {
    const left = content.slice(0, match.index);
    const middle = match[2];
    const right = content.slice(match.index + match[0].length);
    return [left, middle, right];
  }
  return [content, '', ''];
};
const handleSearch = item => {
  params.value.type = item.value;
  onLoad();
};

const handleViewDetail = row => {
  if (row.type == '1') {
    router.push({
      path: '/Finance/Collection/projectCollection',
      query: {
        collectionStatus: '0,1',
        customerName: processContent(row.content)[1],
      },
    });
  } else if (row.type == '2') {
    router.push({
      path: '/Finance/Collection/projectCollection',
      query: {
        collectionStatus: '0,1',
      },
    });
  } else if (row.type == '3') {
    router.push({
      path: '/Finance/Collection/projectCollection',
      query: {
        ids: row.relationIds,
      },
    });
  }
};
</script>

<style lang="scss" scoped>
.search-box {
  background: var(--el-color-info-light-9);
  padding: 5px;
  border-radius: 10px;
}
.active {
  background: var(--el-color-primary-light-5);
  padding: 5px;
  border-radius: 10px;
}
</style>
