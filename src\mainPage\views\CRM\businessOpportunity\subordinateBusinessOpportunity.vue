<template>
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <!-- <el-button
          type="primary"
          @click="handleAdd"
          icon="Plus"
          v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
          >新增</el-button
        > -->
        <div class="status_group">
          <statusGroup @click="handleStatusClick"></statusGroup>
        </div>
      </template>
      <template #stage="{ row }">
        <el-tooltip
          :content="row.failReason || row.sendBackReason || row.pauseReason"
          :disabled="
            !(
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
            )
          "
          placement=""
        >
          <el-tag effect='plain'
            :type="
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
                ? 'danger'
                : 'primary'
            "
            >{{
              row.$stage == '方案'
                ? `方案(${row.optionStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage == '报价'
                ? `报价(${row.offerStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage
            }}</el-tag
          >
        </el-tooltip>
      </template>
      <template #name="{ row }">
         <div style="display: flex;align-items: center;">
           <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag> <el-link type="primary" @click="handleView(row)">{{ row.name }}</el-link>
        </div>
      </template>
      <template #followDays="{row}">
        <el-tag   v-if="row.followDays" style="font-weight:bolder" effect="dark" round :type="row.followDays < 7?'success':row.followDays < 15?'warning':'danger'">{{row.followDays}}天</el-tag>
      </template>
      <template #menu="{ row, index }">
        <!-- <el-button type="primary" text @click="handleEdit(row)" icon="Edit">编辑</el-button> -->
        <!-- <el-button
          type="primary"
          text
          @click="handleView(row)"
          icon="View"
          v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
          >详情</el-button
        > -->

        <!-- <div style="display: flex; justify-content: center">
          <div>
            <el-button
              type="primary"
              text
              @click="follow(row)"
              icon="Service"
              v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
              >跟进</el-button
            >
          </div>
          <div
            v-if="
              row.stage != 3 &&
              (route.query.type == 0 || route.query.type == 2 || !route.query.type)
            "
          >
            <el-button
              @click="handleCommand(1, row)"
              text
              icon="DocumentAdd"
              type="primary"
              :command="1"
              v-if="row.stage == 0"
              >方案</el-button
            >

            <el-button
              :command="2"
              icon="Tickets"
              @click="handleCommand(2, row)"
              v-if="row.stage == 1 && row.optionStatus == 2"
              text
              type="primary"
              >报价</el-button
            >

            <el-button
              :command="3"
              @click="handleCommand(3, row)"
              text
              icon="SetUp"
              type="primary"
              v-if="row.stage != 3"
              >登记</el-button
            >
            <el-button
              :command="5"
              @click="handleCommand(5, row)"
              text
              icon="Tickets"
              type="primary"
              v-if="row.stage == 0"
              >报价</el-button
            >
            <el-button
              :command="6"
              @click="handleCommand(6, row)"
              text
              type="primary"
              v-if="row.bidStatus == 0 && row.classify == 1"
              >投标</el-button
            >
          </div>
        </div> -->
      </template>
      <template #bidStatus="{ row }">
        <el-tag effect='plain' :type="['info', 'warning', 'success', 'danger'][row.bidStatus]">{{
          row.$bidStatus
        }}</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <detail_drawer
      :type="currentType"
      :id="currentId"
      :customer-id="customerId"
      ref="detailRef"
    ></detail_drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { businessOpportunityData, bidStatus } from '@/const/const.js';
import statusGroup from './compoents/statusGroup.vue';
import detail_drawer from './compoents/detail_drawer.vue';
let route = useRoute();
let store = useStore();
let permission = computed(() => store.getters.permission);
console.log(permission.value.business_menu);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  searchLabelWidth: 110,
  menuWidth: 265,
  menu: false,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      overHidden: true,
      search: !route.query.id, component: 'wf-bussiness-drop',
    },
    // {
    //   label: '商机分类',
    //   type: 'radio',
    //   prop: 'classify',
    //   span: 24,
    //   width: 85,

    //   dicData: [
    //     {
    //       value: 0,

    //       label: '产品',
    //     },
    //     {
    //       value: 1,
    //       label: '项目',
    //     },
    //   ],
    // },
    {
      // type: 'tree',
      label: '业务板块',
      multiple: true,
      width: 100,
      span: 12,
      parent: false,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      display: true,
      filterable: true,
      prop: 'type',
      checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
    },
    {
      label: '商机描述',
      prop: 'description',
      width: 85,
      overHidden: true,
    },

    {
      type: 'select',
      label: '商机来源',
      search: !route.query.id,
      overHidden: true,
      width: 85,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机阶段',
      prop: 'stage',
      width: 100,
      slot: true,
      type: 'select',
      search: !route.query.id,
      dicData: businessOpportunityData,
    },
    {
      label: '投标状态',
      prop: 'bidStatus',
      width: 100,
      slot: true,
      type: 'select', hide: true,
      search: !route.query.id, //
      dicData: bidStatus,
      search: false,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true, component: 'wf-customer-drop',
      search: !props.customerId,
      hide:!!props.customerId
    },

    {
      label: '业务员',
      prop: 'businessPersonName',
      component: 'wf-user-drop',
      search: true,
      // hide: true,
      formatter: (row, column, cellValue) => {
        return row.businessPersonName;
      },
    },
    {
      label: '未更新',
      width: 80,
      prop: 'followDays',
      type: 'input',
      html:true,
      formatter(row) {
        if(!row.followDays )return null
        return `<span style="color: ${row.followDays > 15? 'red' : 'green'}">${row.followDays}天</span>`;
      }
    },
    {
      label: '登记时间',
      width: 135,
      prop: 'createTime',
      type: 'datetime',
      search: true,
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      format: 'YYYY-MM-DD',

      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
      width: 100,
    },
    {
      label: '介绍人',
      width: 80,
      prop: 'introducer',
      type: 'input',
    },
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      width: 110,
    },
    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
const props = defineProps(['customerId']);

onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId,
        selectType: props.customerId ? 2 : 1,
        startTime: params.value.createTime ? params.value.createTime[0] : null,
        endTime: params.value.createTime ? params.value.createTime[1] : null,
        createTime: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push({
    path: '/CRM/businessOpportunity/compoents/update',
    query: {
      customerId: route.query.id,
    },
  });
}
let customerId = ref(null);
let currentType = ref(null);
let currentId = ref(null);
let detailRef = ref(null);
function handleView(row) {
  router.push({
    path: '/CRM/businessOpportunity/compoents/detail',
    query: {
      type: 'detail',
      businessId: row.id,
      customerId: row.customerId,
    },
  });
}
// 跟进
function follow(row) {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      type: 1,
      customerId: row.customerId,
      logicId: row.id,
    },
  });
}
function handleCommand(val, row) {
  switch (val) {
    case 1:
      needProgramme(row);
      break;
    case 2:
      needQuotation(row);
      break;
    case 3:
      registration(row);
      break;
    case 5:
      toQuotation(row);
      break;
    case 6:
      toBid(row);
      break;
    case 7:
      bidResult(row);
      break;
    default:
      break;
  }
}
function needProgramme(row) {
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          label: '技术员',
          component: 'wf-user-select',
          prop: 'technicalPerson',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunity/transferOption', null, {
          params: {
            id: row.id,
            ...res.data,
          },
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function needQuotation(row) {
  console.log(222);
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          label: '报价人员',
          component: 'wf-user-select',
          prop: 'offerPerson',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunity/transferOffer', null, {
          params: {
            id: row.id,
            ...res.data,
          },
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function toQuotation(row) {
  router.push({
    path: '/CRM/quotation/compoents/add',
    query: {
      businessId: row.id,
    },
  });
}
function toBid(row) {
  router.push({
    path: '/businessOpportunity/bid',
    query: {
      businessId: row.id,
    },
  });
}
// 登记
function registration(row) {
  proxy.$refs.dialogForm.show({
    title: '登记',
    option: {
      column: [
        {
          label: '登记结果',
          prop: 'stage',
          type: 'radio',
          rules: [
            {
              required: true,
              message: '请选择登记结果',
              trigger: 'change',
            },
          ],
          dicData: [
            {
              label: '成交',
              value: 3,
            },
            {
              label: '失单',
              value: 4,
            },
          ],
          control: val => {
            return {
              failReason: {
                display: val == 4,
              },
              successFileIds: {
                display: val == 3,
              },
            };
          },
          span: 24,
        },
        {
          label: '失单原因',
          prop: 'failReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
        {
          label: '确认附件',
          prop: 'successFileIds',
          type: 'upload',

          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      console.log(res);
      axios
        .post('/api/vt-admin/businessOpportunity/setDown', {
          id: row.id,
          ...res.data,
          stage: res.data.stage,
          successFileIds: res.data.successFileIds.map(item => item.value).join(),
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function handleStatusClick(value) {
  params.value.stage = value;
  onLoad();
}
</script>

<style lang="scss" scoped></style>
