<template>
  <div class="knowledge-page py-20">
    <div class="container mx-auto px-4">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">知识库</h1>
        <p class="text-gray-600">查找产品文档、技术资料和使用指南</p>
      </div>

      <div class="flex flex-col lg:flex-row gap-8">
        <!-- 左侧文件分类 -->
        <div class="lg:w-1/6">
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-4">文件分类</h2>
            <ul class="knowledge-tree space-y-2">
              <li v-for="category in categories" :key="category.id">
                <div
                  class="category-item flex items-center justify-between p-2 rounded cursor-pointer transition-colors duration-200"
                  :class="{ 'bg-blue-50 text-blue-600': selectedCategory === category.id }"
                  @click="handleCategoryClick(category)"
                >
                  <div class="flex items-center">
                    <span
                      v-if="category.childrens && category.childrens.length > 0"
                      class="mr-2 transition-transform duration-200"
                      :class="{ 'rotate-45': isCategoryExpanded(category.id) }"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 5l7 7-7 7"
                        />
                      </svg>
                    </span>
                    <span>{{ category.name }}</span>
                  </div>
                </div>

                <!-- 使用递归组件显示子分类 -->
                <category-tree
                  v-if="
                    category.childrens &&
                    category.childrens.length > 0 &&
                    isCategoryExpanded(category.id)
                  "
                  :categories="category.childrens"
                  :selected-category="selectedCategory"
                  :expanded-categories="expandedCategories"
                  :label-field="'name'"
                  :children-field="'childrens'"
                  @category-click="handleCategoryClick"
                />
              </li>
            </ul>
          </div>
        </div>

        <!-- 右侧文件列表 -->
        <div class="lg:w-3/4">
          <!-- 搜索栏 -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
              <div class="flex-1">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="搜索文件名称..."
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @keyup.enter="searchFiles"
                />
              </div>
              <button
                @click="searchFiles"
                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                搜索
              </button>
              <button
                @click="resetFilters"
                class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
              >
                重置筛选
              </button>
            </div>
          </div>

          <!-- 加载状态 -->
          <div v-if="loading" class="flex justify-center items-center py-12">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span class="ml-2 text-gray-600">加载中...</span>
          </div>

          <!-- 文件卡片网格 -->
          <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div
              v-for="file in files"
              :key="file.id"
              class="file-card bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 p-4"
            >
              <!-- 文件图标和基本信息 -->
              <div class="flex items-start space-x-3">
                <!-- 文件图标 -->
                <div class="flex-shrink-0">
                  <div
                    class="w-12 h-12 rounded-lg flex items-center justify-center"
                    :class="{
                      'bg-red-100 text-red-600': file.type === '.pdf',
                      'bg-blue-100 text-blue-600': file.type === '.doc' || file.type === '.docx',
                      'bg-green-100 text-green-600': file.type === '.xls' || file.type === '.xlsx',
                    }"
                  >
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                  </div>
                </div>

                <!-- 文件信息 -->
                <div class="flex-1 min-w-0">
                  <h3 class="text-sm font-semibold text-gray-900 mb-1 truncate">{{ file.name }}</h3>
                  <div class="space-y-1">
                    <p class="text-xs text-gray-500">大小: {{ file.sizeFormatted }}</p>
                    <p class="text-xs text-gray-500">修改日期: {{ file.updateTimeFormatted }}</p>
                  </div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="mt-3 flex space-x-2">
                <button
                  @click="downLoadFile(file)"
                  class="flex-1 bg-blue-600 text-white py-1.5 px-3 rounded text-xs hover:bg-blue-700 transition-colors duration-200"
                >
                  下载
                </button>
                <button
                  @click="viewFile(file)"
                  class="flex-1 bg-gray-100 text-gray-700 py-1.5 px-3 rounded text-xs hover:bg-gray-200 transition-colors duration-200"
                >
                  预览
                </button>
              </div>
            </div>
          </div>

          <!-- 无结果提示 -->
          <div v-if="!loading && files.length === 0" class="mt-6">
            <div class="bg-white rounded-lg shadow-sm p-12 text-center">
              <svg
                class="w-16 h-16 text-gray-400 mx-auto mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
              <h3 class="text-lg font-semibold text-gray-600 mb-2">未找到相关文件</h3>
              <p class="text-gray-500">请尝试调整筛选条件或搜索关键词</p>
            </div>
          </div>

          <!-- 分页 -->
          <div
            v-if="!loading && files.length > 0 && totalPages > 1"
            class="mt-8 flex justify-center"
          >
            <nav class="flex items-center space-x-2">
              <button
                @click="handlePageChange(currentPage - 1)"
                :disabled="currentPage <= 1"
                class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>

              <button
                v-for="page in Math.min(5, totalPages)"
                :key="page"
                @click="handlePageChange(page)"
                class="px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors duration-200"
                :class="{ 'bg-blue-600 text-white border-blue-600': page === currentPage }"
              >
                {{ page }}
              </button>

              <button
                @click="handlePageChange(currentPage + 1)"
                :disabled="currentPage >= totalPages"
                class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </nav>

            <!-- 分页信息 -->
            <div class="mt-4 text-center text-sm text-gray-600">
              共 {{ totalCount }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import axios from '../axios';
import CategoryTree from '../components/CategoryTree.vue';
import config from '@/config/website';
import { Base64, encode } from 'js-base64';
// 状态管理
const files = ref([]);
const categories = ref([]);
const totalCount = ref(0);
const currentPage = ref(1);
const pageSize = ref(12);
const loading = ref(false);

const selectedCategory = ref(null);
const searchQuery = ref('');
const expandedCategories = ref(new Set());

// 获取知识库分类树
const fetchCategories = async () => {
  try {
    const response = await axios.post('/api/vt-admin/publishKnowledge/findByTree', {
      tyep: 0,
    });
    if (response.data && response.data.code === 200) {
      categories.value = response.data.data || [];
      // 添加全部分类选项
      categories.value.unshift({
        id: 'all',
        name: '全部文件',
      });
    } else {
      console.error('获取分类失败:', response.data?.msg);
    }
  } catch (error) {
    console.error('获取分类异常:', error);
  }
};

// 获取知识库文件列表
const fetchFiles = async () => {
  loading.value = true;
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value,
    };

    // 如果选择了分类，添加分类参数
    if (selectedCategory.value && selectedCategory.value !== 'all') {
      params.publishKnowledgeId = selectedCategory.value;
    }

    // 如果有搜索关键词，添加搜索参数
    if (searchQuery.value) {
      params.name = searchQuery.value;
    }

    const response = await axios.get('/api/vt-admin/publishKnowledgeFile/page', { params });
    if (response.data && response.data.code === 200) {
      const data = response.data.data;
      files.value = (data.records || []).map(file => ({
        ...file,
        // 将字节转换为MB，保留2位小数
        sizeFormatted: sizeFilter(file.size),
        // 格式化更新时间
        updateTimeFormatted: file.updateTime ? new Date(file.updateTime).toLocaleDateString() : '',
      }));
      totalCount.value = data.total || 0;
    } else {
      console.error('获取文件列表失败:', response.data?.msg);
    }
  } catch (error) {
    console.error('获取文件列表异常:', error);
  } finally {
    loading.value = false;
  }
};

// 检查分类是否展开
const isCategoryExpanded = categoryId => {
  return expandedCategories.value.has(categoryId);
};

// 处理分类点击
const handleCategoryClick = category => {
  if (category.childrens && category.childrens.length > 0) {
    toggleCategory(category.id);
  } else {
    setSelectedCategory(category.id);
  }
};

// 切换分类展开状态
const toggleCategory = categoryId => {
  if (expandedCategories.value.has(categoryId)) {
    expandedCategories.value.delete(categoryId);
  } else {
    expandedCategories.value.add(categoryId);
  }
};

// 设置选中的分类
const setSelectedCategory = categoryId => {
  selectedCategory.value = selectedCategory.value === categoryId ? null : categoryId;
  currentPage.value = 1; // 重置页码
  fetchFiles(); // 重新获取文件列表
};

// 重置筛选条件
const resetFilters = () => {
  selectedCategory.value = null;
  searchQuery.value = '';
  currentPage.value = 1;
  fetchFiles();
};

// 搜索文件
const searchFiles = () => {
  currentPage.value = 1;
  fetchFiles();
};

// 页码变化
const handlePageChange = page => {
  currentPage.value = page;
  fetchFiles();
};

// 页面大小变化
const handleSizeChange = size => {
  pageSize.value = size;
  currentPage.value = 1;
  fetchFiles();
};

// 计算总页数
const totalPages = computed(() => {
  return Math.ceil(totalCount.value / pageSize.value);
});

// 初始化数据
onMounted(() => {
  fetchCategories();
  fetchFiles();
});

function sizeFilter(size) {
  if (size) {
    size *= 1000;
    if (size < 0.1 * 1024) {
      //小于0.1KB，则转化成B
      size = size.toFixed(2) + 'B';
    } else if (size < 0.1 * 1024 * 1024) {
      // 小于0.1MB，则转化成KB
      size = (size / 1024).toFixed(2) + 'KB';
    } else if (size < 0.1 * 1024 * 1024 * 1024) {
      // 小于0.1GB，则转化成MB
      size = (size / (1024 * 1024)).toFixed(2) + 'MB';
    } else {
      // 其他转化成GB
      size = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
    }
    // 转成字符串
    let sizeStr = size + '',
      // 获取小数点处的索引
      index = sizeStr.indexOf('.'),
      // 获取小数点后两位的值
      dou = sizeStr.substr(index + 1, 2);

    // 判断后两位是否为00，如果是则删除00
    if (dou == '00') return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);

    return size;
  }
  return '0KB';
}
function viewFile(row) {
  // 预览文件
  window.open(config.previewFileUrl + 'onlinePreview?url=' + encodeURIComponent(encode(row.path)));
}
// 文件下载功能
async function downLoadFile(row) {
  try {
    console.log('开始下载文件:', row.name);

    // 构建下载URL
    const downloadUrl = row.path;

    // 使用axios下载文件流
    const response = await axios({
      method: 'GET',
      url: downloadUrl,
      responseType: 'blob', // 重要：设置响应类型为blob
      headers: {
        'Content-Type': 'application/octet-stream',
      },
    });

    // 创建blob对象
    const blob = new Blob([response.data]);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = row.name || '下载文件';
    link.style.display = 'none';

    // 添加到DOM并触发下载
    document.body.appendChild(link);
    link.click();

    // 清理资源
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    console.log('文件下载完成:', row.name);
  } catch (error) {
    console.error('下载文件失败:', error);
    alert('下载失败，请检查网络连接或稍后重试');
  }
}
</script>

<style scoped>
.category-item:hover {
  background-color: #f3f4f6;
}

.knowledge-tree .category-item {
  transition: all 0.2s ease;
}

.file-card {
  transition: all 0.2s ease;
}

.file-card:hover {
  transform: translateY(-2px);
}
</style>
