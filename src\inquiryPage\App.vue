<template>
  <Inquiry v-if="type == 0"></Inquiry>
  <Remand v-else-if="type == 1"></Remand>
</template>

<script setup>
import Inquiry from './views/inquiry/index.vue';
import Remand from './views/remand/index.vue';

const getQueryString = name => {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(decodeURI(r[2]));
  return null;
};
const type = getQueryString('type');
if(type == 0 ){
  document.title = '询价单';
}else{
  document.title = '需求调查表';
}
</script>

<style lang="scss" scoped></style>
