<template>
  <basic-container style="height: 100%">
    <Title
      >编辑方案
      <template #foot>
        <el-button type="primary" @click="submit">提交</el-button
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >

    <avue-form :option="option" ref="addForm" v-model="form"></avue-form>
    <div>
      <el-tabs
        v-model="editableTabsValue"
        type="card"
        editable
        class="demo-tabs"
        @edit="handleTabsEdit"
      >
        <el-tab-pane
          v-for="(item, index) in form.moduleDTOList"
          :key="item.moduleName"
          :label="item.moduleName"
          :name="index"
        >
          <template #label>
            <span>
              <span>{{ item.moduleName }}</span>
              <el-icon @click="editModuleName(item)" v-if="index !== 0"><edit /></el-icon>
            </span>
          </template>

          <el-form v-if="index !== 0">
            <el-form-item label="">
              <el-button
                type="primary"
                icon="plus"
                size="small"
                @click="addCategory(item.detailDTOList)"
                >新增分类</el-button
              >
            </el-form-item>
          </el-form>
          <el-collapse v-if="index !== 0" v-model="item.currentCollapse">
            <el-collapse-item :name="index" v-for="(i, index) in item.detailDTOList">
              <template #title>
                <span style="font-weight: bolder">{{ i.classify }}</span>
                <el-icon @click="editCategoryName(i)" class="header-icon">
                  <edit />
                </el-icon>
              </template>

              <div>
                <el-form>
                  <el-form-item label="">
                    <el-button
                      type="primary"
                      icon="plus"
                      size="small"
                      @click="addProduct(i.productList, i.classify)"
                      plain
                      >新增</el-button
                    >
                  </el-form-item>
                </el-form>

                <el-table  class="avue-crud" :data="i.productList" border show-summary>
                  <el-table-column label="设备名称" show-overflow-tooltip prop="productName"></el-table-column>
                  <el-table-column label="规格型号" show-overflow-tooltip prop="productSpecification"></el-table-column>
                  <el-table-column label="产品图片" #default="{ row }">
                    <el-image
                        style="width: 80px"
                        :z-index="1000000"
                        :preview-src-list="[row.coverUrl]"
                        :src="row.coverUrl"
                      ></el-image>
                  </el-table-column>
                  <el-table-column
                    label="产品描述"
                    show-overflow-tooltip
                    width="200"
                    prop="description"
                  ></el-table-column>
                  <el-table-column label="品牌" prop="productBrand"></el-table-column>
                  <el-table-column label="单位" prop="unitName"></el-table-column>
                  <el-table-column label="数量" prop="number"></el-table-column>
                  <el-table-column
                    label="操作"
                    align="center"
                    prop=""
                    width="200"
                    #default="{ row, index }"
                  >
                    <el-button
                      type="primary"
                      text
                      icon="edit"
                      @click="editProduct(i.productList, row, index)"
                      >编辑</el-button
                    >
                    <el-button
                      type="primary"
                      text
                      icon="delete"
                      @click="deleteProduct(i.productList, row)"
                      >删除</el-button
                    >
                  </el-table-column>
                </el-table>
              </div>
            </el-collapse-item>
          </el-collapse>
          <el-empty v-else-if="index == 0 && form.moduleDTOList.length <= 1" description="请先添加子项"></el-empty>
          <el-table
            v-else
            :data="form.moduleDTOList.filter((item, index) => index !== 0)"
            show-summary
            class="avue-crud"
          >
            <el-table-column label="子项名称" prop="moduleName"></el-table-column>
            <el-table-column label="备注" prop="remark"></el-table-column>
            <el-table-column label="数量" #default="{ row }">
              {{ totalNumber(row) }}
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- <div v-else>
      <el-form>
        <el-form-item label="">
          <el-button
            type="primary"
            icon="plus"
            size="small"
            
            plain
            >新增</el-button
          >
        </el-form-item>
      </el-form>

      <el-table :data="[]" border show-summary>
        <el-table-column label="设备名称" prop="productName"></el-table-column>
        <el-table-column label="规格型号" prop="productSpecification"></el-table-column>
        <el-table-column label="产品图片" #default="{ row }">
          <img style="width: 200px; margin-right: 20px" :src="row.link" />
        </el-table-column>
        <el-table-column
          label="产品描述"
          show-overflow-tooltip
          width="200"
          prop="description"
        ></el-table-column>
        <el-table-column label="品牌" prop="productBrand"></el-table-column>
        <el-table-column label="单位" prop="unitName"></el-table-column>
        <el-table-column label="数量" prop="number"></el-table-column>
        <el-table-column label="操作" align="center" prop="" width="200" #default="{ row, index }">
          <el-button type="primary" text icon="edit" @click="editProduct(i.productList, row, index)"
            >编辑</el-button
          >
          <el-button type="primary" text icon="delete" @click="deleteProduct(i.productList, row)"
            >删除</el-button
          >
        </el-table-column>
      </el-table>
    </div> -->
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const route = useRoute();
let form = ref({
  moduleDTOList: [
    {
      moduleName: '汇总',
    },
  ],
});
let option = ref({
  submitBtn: false,
  labelWidth: 100,
  emptyBtn: false,
  column: [
    {
      label: '方案名称',
      prop: 'name',
      rules: [
        {
          required: true,
          message: '请填写方案名称',
        },
      ],
    },
    {
      label: '客户名称',
      prop: 'customerId',
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
    },
    {
      label: '方案日期',
      prop: 'date',
    },
  ],
});
let { proxy } = getCurrentInstance();
let tabIndex = 2;
const editableTabsValue = ref(0);
const editableTabs = ref([
  {
    title: '汇总',
    name: '1',
    content: 'Tab 1 content',
  },
  {
    title: '智能控制系统',
    name: '2',
    content: 'Tab 2 content',
  },
]);
let tableOption = ref({
  header: false,
  menuLeft: false,
  border: true,
  column: [
    {
      label: '产品名称',
    },
  ],
});
function handleTabsEdit(tabName, action) {
 
  if (action == 'add') {
    proxy.$refs.dialogForm.show({
      title: '添加子项',
      option: {
        column: [
          {
            label: '子项名称',

            prop: 'moduleName',
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        form.value.moduleDTOList.push({
          moduleName: res.data.moduleName,
          remark: res.data.remark,
          currentCollapse: [0],
          detailDTOList: [
            {
              classify: '默认分类',
              productList: [],
            },
          ],
        });
        editableTabsValue.value = form.value.moduleDTOList.length - 1
        res.close();
      },
    });
  } else {
    if (tabName == 0) return;
    proxy
      .$confirm('删除该子项将删除下面所有产品，确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
    
        form.value.moduleDTOList = form.value.moduleDTOList.filter(
          (item, index) => index !== tabName
        );
      });
  }
}
function addProduct(productList, classify) {
  proxy.$refs.dialogForm.show({
    title: '添加产品',
    option: {
      column: [
        {
          label: '选择产品',
          component: 'wf-product-select',
          prop: 'productId',
          span: 24,
        },
        {
          label: '数量',
          type: 'number',
          prop: 'number',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios.get('/api/vt-admin/product/detail?id=' + res.data.productId).then(r => {
        productList.push({
          ...r.data.data,
          productId: r.data.data.id,
          number: res.data.number,
          id: null,
          classify: classify,
        });
        res.close();
      });
    },
  });
}
function editProduct(productList, row, i) {
  proxy.$refs.dialogForm.show({
    title: '编辑产品',
    option: {
      column: [
        {
          label: '选择产品',
          component: 'wf-product-select',
          prop: 'productId',
          value: row.productId,
          disabled: true,
          span: 24,
        },
        {
          label: '数量',
          type: 'number',
          prop: 'number',
          span: 24,
          value: row.number,
        },
      ],
    },
    callback(res) {
      row.number = res.data.number;
      res.close();
    },
  });
}
// function summary(params) {
//   const { columns, data } = param
//   columns.map((item,index) => {
//     if(index == 0){
//       return '合计'
//     }else if(index == )
//   })
// }
function deleteProduct(list, row) {
  proxy
    .$confirm('确定删除该产品?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const indexToRemove = list.findIndex(item => row.productId === item.productId);
      list.splice(indexToRemove, 1);
    });
}
function editCategoryName(i) {
  proxy.$refs.dialogForm.show({
    title: '修改分类名称',
    option: {
      column: [
        {
          label: '分类名称',
          value: i.classify,
          prop: 'classify',
          span: 24,
        },
      ],
    },
    callback(res) {
      i.classify = res.data.classify;
      res.close();
    },
  });
}
function editModuleName(i) {
  proxy.$refs.dialogForm.show({
    title: '修改子项名称',
    option: {
      column: [
        {
          label: '子项名称',
          value: i.moduleName,
          prop: 'moduleName',
          span: 24,
        },
      ],
    },
    callback(res) {
      i.moduleName = res.data.moduleName;
      res.close();
    },
  });
}
function addCategory(list) {
  proxy.$refs.dialogForm.show({
    title: '添加分类',
    option: {
      column: [
        {
          label: '分类名称',

          prop: 'categoryName',
          span: 24,
        },
      ],
    },
    callback(res) {
      list.push({
        classify: res.data.categoryName,
        productList: [],
      });
      res.close();
    },
  });
}
function submit() {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      let data = {
        ...form.value,
        businessOpportunityId: route.query.id,
        moduleDTOList: form.value.moduleDTOList
          .filter((item, index) => index !== 0)
          .map(i => {
            return {
              detailDTOList: i.detailDTOList.reduce((pre, cur) => {
                return pre.concat(cur.productList);
              }, []),
            };
          }),
      };

      axios.post('/api/vt-admin/businessOpportunityOption/save', data).then(res => {
        proxy.$message.success(res.data.msg);
        router.$avueRouter.closeTag();
        router.go(-1);
      });
    }
  });
}
function totalNumber(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p += c.number;
      return p;
    }, 0);
    return pre;
  }, 0);
}
</script>

<style scoped></style>
