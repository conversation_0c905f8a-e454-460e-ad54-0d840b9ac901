<template>
  <basic-container>
    <Title
      >新增采购订单
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form
      :option="option"
      ref="addFormRef"
      @submit="submit"
      style="margin-top: 5px"
      v-model="form"
    >
      <template #files v-if="route.query.type == 'detail'">
        <File></File>
      </template>
      <template #product>
        <el-form style="margin-bottom: 5px">
          <el-form-item label="">
            <el-button type="primary" icon="plus" size="small" plain @click="addProduct"
              >新增</el-button
            >
            <productSelectDrop
           
            @select="handleProductSelectConfirm"
            style="margin-left: 5px"
          ></productSelectDrop>
          </el-form-item>
        </el-form>
        <el-table class="avue-crud" show-summary :data="form.detailDTOList" border align="center">
          <el-table-column label="产品名称" show-overflow-tooltip prop="productName"></el-table-column>
          <!-- <el-table-column
            label="供应商"
            show-overflow-tooltip
            prop="supplierName"
            #default="{ row }"
          >
            <WfSupplierSelect
              v-model="row.supplierId"
              placeholder="请选择供应商"
              size="small"
              style="width: 100%"
            ></WfSupplierSelect>
          </el-table-column> -->
          <el-table-column label="规格型号" show-overflow-tooltip prop="productSpecification"></el-table-column>
          <el-table-column label="描述" show-overflow-tooltip prop="description"></el-table-column>
          <el-table-column label="产品图片" #default="{ row }">
            <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
          </el-table-column>
          <el-table-column label="品牌" width="90" prop="productBrand"></el-table-column>
          <el-table-column label="单位" prop="unitName"></el-table-column>
          <el-table-column label="数量" width="150" #default="{ row }" prop="number">
            <el-input-number v-model="row.number" style="width: 100%;" controls-position="right" ></el-input-number>
          </el-table-column>
        </el-table>
      </template>
    </avue-form>

    <div style="margin-top: 20px; padding-left: 75px"></div>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleProductSelectConfirm"
    ></wf-product-select>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import productSelectDrop from '@/views/CRM/quotation/compoents/productSelectDrop.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
let route = useRoute();
let router = useRouter();
let form = ref({
  detailDTOList: [],
});

let { proxy } = getCurrentInstance();
let isEdit = ref(true);
let option = ref({
  submitBtn: route.query.type !== 'detail',
  labelWidth: 140,
  detail: route.query.type == 'detail',
  emptyBtn: route.query.type !== 'detail',
  column: [
    {
      label: '采购人',
      prop: 'purchaseName',
      disabled: true,
      value: proxy.$store.getters.userInfo.real_name,
    },
    {
      label: '下单日期',
      prop: 'orderDate',
      type: 'date',
     
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      value: new Date(),
    },

    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: false,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    {
      label: '产品信息',
      prop: 'product',
      slot: true,
      span: 24,
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
});

function formatData(data) {
  return data.reduce((pre, cur) => {
    if (!cur.splitList) {
      pre.push(cur);
      return pre;
    }
    if (cur.splitList.length > 0) {
      pre.push(...cur.splitList);
    } else {
      pre.push(cur);
    }
    return pre;
  }, []);
}
function submit(form, done) {
  const data = {
    files: form.files.map(item => item.value).join(','),
    remark: form.remark,
    orderDate: form.orderDate,
    detailDTOList: formatData(form.detailDTOList)
      .map(item => {
        return {
          productId:item.productId,
          number:item.number
        };
      })
      .filter(item => item.number != 0),
  };
  axios.post('/api/vt-admin/purchaseOrder/add', data).then(
    res => {
      proxy.$message.success('入库成功');
      proxy.$router.$avueRouter.closeTag();
      proxy.$router.back();
    },
    err => {
      done();
    }
  );
}
function getDetail(id) {
  axios
    .get('/api/vt-admin/purchaseInStorage/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      form.value = res.data.data;
    });
}


function addProduct() {
  proxy.$refs['product-select'].visible = true;
}
function handleProductSelectConfirm(ids) {
  console.log(ids);
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      form.value.detailDTOList.push({
        ...r.data.data,
        productId: r.data.data.id,
        preSealPrice: r.data.data.sealPrice,
        unitPrice: r.data.data.purchasePrice,
        sealPrice: '',
        number: 1,
        id: null,
        inStorageAddress: 0,
        supplierId: null,
      });
    });
  });
}
</script>

<style lang="scss" scoped></style>
