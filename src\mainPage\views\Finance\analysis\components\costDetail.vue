<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    :before-open="beforeOpen"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  
    <template #menu="{ row }">
      <el-button
        type="primary"
        v-if="row.expenseStatus == 0"
        @click="$refs.crud.rowEdit(row)"
        icon="edit"
        text
        >编辑</el-button
      >
      <el-button
        type="primary"
        v-if="row.expenseStatus == 0"
        @click="$refs.crud.rowDel(row)"
        icon="delete"
        text
        >删除</el-button
      >
    </template>
    <template #expenseFiles="{ row }">
      <File :fileList="row.attachList"></File>
    </template>
    <template #expenseStatus="{ row }">
      <el-tag effect="plain" size="mini" :type="row.expenseStatus == 0 ? 'danger' : 'success'">
        {{ row.$expenseStatus }}
      </el-tag>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { dateFormat } from '@/utils/date';
let { proxy } = getCurrentInstance();
const props = defineProps({
  sealContractId: String,
});
watch(
  () => props.sealContractId,
  () => {
    onLoad();
  },
  {
    immediate: true,
  }
);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  menu:false,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 150,
  border: true,
  column: [
    {
      type: 'tree',
      label: '费用类型',
      dicUrl: '/blade-system/dict/dictionary-tree?code=expenseType',
      cascader: [],
      span: 12,
      width: 110,
      // search: true,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'expenseType',
      parent: false,
    },
    // {
    //   type: 'number',
    //   label: '票据张数',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      type: 'number',
      label: '费用金额',
      span: 12,
      display: true,
      width: 110,
      prop: 'expensePrice',
    },

    {
      type: 'textarea',
      label: '用途',
      span: 24,
      display: true,
      prop: 'purpose',
      showWordLimit: true,
    },

    // {
    //   type: 'textarea',
    //   label: '备注',
    //   span: 24,
    //   display: true,
    //   prop: 'remark',
    //   showWordLimit: true,
    // },
    {
      label: '证明附件',
      prop: 'expenseFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      overHidden: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '状态',
      type: 'select',
      width: 90,
      display: false,
      dicData: [
        {
          value: 0,
          label: '未报销',
        },
        {
          value: 1,
          label: '已报销',
        },
        {
          value: 2,
          label: '报销中',
        },
      ],
      prop: 'expenseStatus',
    },
    {
      type: 'input',
      label: '登记人',
      span: 12,
      width: 110,
      display: true,
      component: 'wf-user-select',
      prop: 'reimbursementUser',
      value: proxy.$store.getters.userInfo.user_id,
      showWordLimit: true,
      formatter: (row, column, cellValue) => {
        return row.reimbursementUserName;
      },
    },
    {
      type: 'date',
      label: '日期',
      span: 12,
      display: true,
      width: 110,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'expenseDate',
      disabled: false,
      readonly: false,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractExpense/save';
const delUrl = '/api/vt-admin/sealContractExpense/remove?ids=';
const updateUrl = '/api/vt-admin/sealContractExpense/update';
const tableUrl = '/api/vt-admin/sealContractExpense/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();

let loading = ref(false);
let totalPrice = ref(0);
onMounted(() => {
    onLoad()
})
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.filter(item => item.expenseTypeName != '商务费');
      page.value.total = res.data.data.total;
    });
 
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    customerId: props.customerId,
    projectId: props.projectId,
    expenseFiles: form.expenseFiles && form.expenseFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    expenseFiles: row.expenseFiles && row.expenseFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.expenseFiles = form.value.attachList.map(item => {
      return {
        label: item.originalName,
        value: item.id,
      };
    });
  }
  done();
}
function beforeClose(done, type) {
  if (type == 'edit') {
    form.value = {};
  }
  done();
}
function done() {
  onLoad();
  done();
  done();
}
</script>

<style lang="scss" scoped></style>
