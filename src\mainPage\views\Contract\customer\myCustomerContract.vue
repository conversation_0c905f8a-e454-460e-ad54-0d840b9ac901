<template>
  <!-- <div class="project-main">
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex === item.value ? 'active' : ''"
        @click="handleTabClick(item,index)"
      >
        {{ item.label }}
      </div>
    </div>
  </div> -->
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      :before-open="beforeOpen"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @current-change="onLoad"
      :cell-style="cellStyle"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: inline-block">
          <div style="display: flex; align-items: center; gap: 20px">
            <el-button type="primary" @click="handleAdd" icon="plus">新增报价合同</el-button>
            <el-button
              type="primary"
              @click="$refs.crud.rowAdd()"
              icon="plus"
              style="margin-left: -10px"
              >新增接单合同</el-button
            >
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
            <span style="font-weight: bolder">未收款总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (noReceivedPrice * 1).toLocaleString() }}</el-text
            >
          </div>
        </div>
      </template>
      <template #contractCode="{ row }">
        <el-link type="primary" @click="toDetail(row)">
            <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag> 
          <el-tag
            v-if="row.isPreOrder == 1"
            type="danger"
            size="small"
            effect="plain"
            title="预订单"
            >预</el-tag
          >
          {{ row.contractCode }}
          <i
            title="该订单正在申请撤销"
            v-if="row.isApplyCancel == 1"
            class="element-icons el-icon-chehui1"
            style="color: var(--el-color-warning); font-size: 20px"
          ></i>
        </el-link>
      </template>
      <template #menu="{ row }">
        <el-button
          text
          type="primary"
          v-if="
            $store.getters.userInfo.user_id == row.businessUser &&
            (!route.query.type || route.query.type == 0)
          "
          @click="edit(row)"
          icon="edit"
          >编 辑</el-button
        >
        <el-button
          text
          type="primary"
          v-if="
            $store.getters.userInfo.user_id == row.businessUser &&
            (!route.query.type || route.query.type == 0) &&
            row.isApplyCancel != 1 &&
            row.contractType != 2
          "
          @click="cancel(row)"
          icon="back"
          >撤 回</el-button
        >
        <el-button
          type="primary"
          v-if="
            $store.getters.userInfo.user_id == row.businessUser &&
            (!route.query.type || route.query.type == 0) &&
            row.isApplyCancel != 1 &&
            row.contractType == 2
          "
          text
          icon="delete"
          @click="$refs.crud.rowDel(row)"
          >删除</el-button
        >
        <el-button text type="primary" @click="toDetail(row)" icon="view">详 情</el-button>
      </template>
      <template #contractTotalPrice-search="{ row }">
        <div style="display: flex">
          <el-input placeholder="最小合同额" v-model.number="params.contractMinPrice"></el-input>-
          <el-input placeholder="最大合同额" v-model.number="params.contractMaxPrice"></el-input>
        </div>
      </template>
      <template #planCollectionPrice="{ row }">
        <div v-if="row.noReceivedPrice == 0" style="display: flex; justify-content: center">
          <div class="circle">
            <span class="text">收讫</span>
          </div>
        </div>
        <div v-else>
          <span
            :style="{
              color:
                row.planCollectionDays > 7
                  ? 'var(--el-color-success)'
                  : row.planCollectionDays <= 7 && row.planCollectionDays >= 0
                  ? 'var(--el-color-warning)'
                  : 'var(--el-color-danger)',
            }"
            class="planCollectionDays"
            >{{ row.planCollectionDays }}</span
          ><span>{{ row.planCollectionDays || row.planCollectionDays == 0 ? '天' : '' }}</span>
        </div>
      </template>
      <template #customerName="{row}">
        <el-link type="primary" @click="toCustomerDetail(row)">{{ row.customerName }}</el-link>
      </template>
      
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
   
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
const props = defineProps(['customerId']);

console.log(window);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  addBtnText: '新增工单合同',
  calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 6,
  searchIcon: true,
  searchIndex: 3,
  searchLabelWidth: 120,
  menuWidth: 220,
  border: true,
  dialogType: 'drawer',
  labelWidth: 120,
  column: [
    {
      label: '项目信息',
      display: false,
      children: [
        {
          label: '合同编号',
          prop: 'contractCode',
          overHidden: true,
          // search: true,
          searchSpan: 4,
          width: 160,
          display: false, // 设置 display 为 false
        },
        {
          label: '对方订单编号',
          prop: 'customerOrderNumber',
          overHidden: true,
          // search: true,
          searchSpan: 4,
          width: 160,
          display: false, // 设置 display 为 false
        },
        // {
        //   label: '类型',
        //   prop: 'type',
        //   searchType: 'radio',
        //   search: true,
        //   hide: true,
        //   dicData: [
        //     {
        //       label: '全部',
        //       value: '',
        //     },
        //     {
        //       label: '我的',
        //       value: 1,
        //     },
        //     {
        //       label: '我参与的',
        //       value: 2,
        //     },
        //   ],
        // },
        {
          label: '合同名称',
          prop: 'contractName',
          width: 200,
          overHidden: true,
          search: true,
          slot: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '客户名称',
          prop: 'customerName',
          //width: 150,
          search: !props.customerId,
          component: 'wf-customer-drop',
          hide: !!props.customerId,
          width: 200,
          display: false, // 设置 display 为 false
        },
        {
          label: '关联商机',
          prop: 'businessOpportunityName',
          //width: 150,
          // search: true,
          overHidden: true,
          width: 200,
          display: false, // 设置 display 为 false
        },

        // {
        //   label: '商机名称',
        //   prop: 'businessOpportunityName',
        //   //width: 150,
        //   width: 200,
        //   search: true,
        // },
        {
          label: '签订日期',
          type: 'date',
          prop: 'signDate',
          // hide: true,
          sortable: true,
          search: true,
          component: 'wf-daterange-search',
          search: true,
          searchSpan: 6,
          width: 120,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          display: false, // 设置 display 为 false
        },

        //   {
        //     label: "建设单位",
        //     prop: "ownerName",
        //     overHidden: true,
        //     search: true,
        //     //width: 150,
        //   },
      ],
    },
    {
      label: '财务信息',
      children: [
        {
          label: '合同总额',
          prop: 'contractTotalPrice',
          // search: true,
          // hide: true,
          searchSpan: 6,
          searchLabelWidth: 120,
          searchSlot: true,
          sortable: true,
          width: 100,
          display: false, // 设置 display 为 false
        },
        {
          label: '开票金额',
          prop: 'hasInvoice',
          width: 100,
          sortable: true,
          formatter: row => {
            if (row.isNeedInvoice == 0) {
              return '无需开票';
            } else {
              return row.hasInvoice;
            }
          },
          display: false, // 设置 display 为 false
        },
        {
          label: '未开票金额',
          prop: 'noInvoice',
          width: 100,
          sortable: true,
          formatter: row => {
            if (row.isNeedInvoice == 0) {
              return '无需开票';
            } else {
              return row.noInvoice;
            }
          },
          display: false, // 设置 display 为 false
        },
        {
          label: '离回款时间',
          prop: 'planCollectionPrice',
          width: 100,
          html: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '收讫状态',
          prop: 'isReceived',
          width: 100,
          hide: true,
          search: true,
          type: 'select',
          dicData: [
            {
              value: 0,
              label: '已收讫',
            },
            {
              value: 1,
              label: '未收讫',
            },
              {
              value: 2,
              label: '回款为空',
              desc:'没填写计划收款或收款未到100%'
            },
          ],
          display: false, // 设置 display 为 false
        },
        {
          label: '已收款',
          prop: 'receivedPrice',
          width: 100,
          sortable: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '未收款',
          prop: 'noReceivedPrice',
          width: 100,
          sortable: true,
          display: false, // 设置 display 为 false
        },
        {
          label: '回款比例(%)',
          prop: 'receiveRate',
          formatter: (row, value, column, cell) => {
            return row.receiveRate + '%';
          },
          display: false, // 设置 display 为 false
        },
      ],
    },
    // {
    //   label: "回款逾期",
    //   prop: "overdue",
    // },
    {
      label: '业务员',
      prop: 'businessName',
      // search: true,
      component: 'wf-user-drop',
      formatter: (row, value, column, cell) => {
        return row.businessUserName;
      },
      // hide: true,
      display: false, // 设置 display 为 false
    },
    {
      label: '最终用户',
      prop: 'finalCustomer',
      span: 12,
      hide: true,
      search: true,
      display: false, // 设置 display 为 false
    },
      {
      label: '签订公司',
      prop: 'companyName',
      span: 12,
      hide: true,
      dicUrl:'/api/vt-admin/company/page?size=1000',
      dicFormatter: (d) => {
        return d.data.records;
      },
      props: {
        label: 'companyName',
        value: 'id',
      },
      type:'select',
      search: true,
    },
    // {
    //   label: '业务员',
    //   prop: 'reason',
    //   // hide: true,
    // },
    // {
    //   label: '状态',
    //   children: [
    //     // {
    //     //   label: "合同签订时间",
    //     //   prop: "contractSignDate",
    //     // },
    //     {
    //       label: '合同状态',
    //       prop: 'contractStatus',
    //       dicData: [
    //         {
    //           value: 0,
    //           label: '待审批',
    //         },
    //         {
    //           value: 1,
    //           label: '执行中',
    //         },
    //         {
    //           value: 2,
    //           label: '待执行',
    //         },
    //         {
    //           value: 3,
    //           label: '暂停',
    //         },
    //         {
    //           value: 4,
    //           label: '终止',
    //         },
    //         {
    //           value: 5,
    //           label: '诉讼',
    //         },
    //         {
    //           value: 6,
    //           label: '待签订',
    //         },
    //         {
    //           value: 7,
    //           label: '已签未回',
    //         },
    //       ],
    //       width: 80,
    //       type: 'select',
    //       dataType: 'string',
    //     },
    //   ],
    // },
  ],
  group: [
    {
      label: '合同信息',
      prop: 'contractInfo',
      column: [
        {
          label: '关联客户',
          prop: 'customerId',
          placeholder: '请选择关联客户',
          change: val => {
            setCustomerInfo(val.value);
          },
          component: 'wf-customer-select',
        },
        {
          label: '合同名称',
          prop: 'contractName',
        },
          {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择联系人',
          // disabled: true,
          params: {},
          change: val => {
            setContactInfo(val.value);
          },
        },
        {
          label: '合同金额',
          prop: 'contractTotalPrice',
          placeholder: '请输入合同金额',
          type: 'number',
          rules: [{
            required: true,
            message: '请输入合同金额',
          }],
        },
      
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        // {
        //   label: '预计开始时间',
        //   prop: 'expectedStartTime',
        //   placeholder: '请选择预计开始时间',
        //   type: 'date',
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD',
        // },
        // {
        //   label: '预计结束时间',
        //   prop: 'expectedEndTime',
        //   placeholder: '请选择预计结束时间',
        //   type: 'date',
        //   format: 'YYYY-MM-DD',
        //   valueFormat: 'YYYY-MM-DD',
        // },
        {
          type: 'date',
          label: '签订日期',
          span: 12,
          display: true,
          prop: 'signDate',
          required: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          rules: [
            {
              required: true,
              message: '签订日期必须填写',
            },
          ],
        },
        {
          type: 'date',
          label: '合同交付日期',
          span: 12,
          display: true,
          prop: 'contractDeliveryDate',
          required: true,
          format: 'YYYY-MM-DD',
             value: dateFormat(new Date(), 'yyyy-MM-dd'),
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 12,
          prop: 'paymentDeadline',
          dicFormatter: res => {
            return res.data;
          },

          dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '是否开票',
          prop: 'isNeedInvoice',
          type: 'radio',
          value: 1,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
            label:'技术人员',
            prop:'operationTechnology',
            component:'wf-user-select',
            checkType: 'checkbox',
            params:{
               userUrl:'/api/blade-system/search/user?functionKeys=engineer'
            },
            span:12
        },
        // {
        //   label: '派单人',
        //   prop: 'assistUser',
        //   placeholder: '请选择派单人',
        //   checkType: 'checkBox',
        //   component: 'wf-user-select',
        // },
        
        {
          label: '合同附件',
          prop: 'contractFiles',
          type: 'upload',
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '备注',
          prop: 'remark',
          placeholder: '请输入备注',
          span: 24,
          type: 'textarea',

          showWordLimit: true,
          autosize: {
            minRows: 3,
            maxRows: 4,
          },
        },
      ],
    },
    {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      column: [
        {
          label:'服务客户名称',
          prop:'finalCustomer',
          type:'input',
          rules:[{
            required: true,
            message: '请输入服务客户名称',
          }],
          span:24,
        },
        {
          label: '服务联系人',
          prop: 'finalCustomerConcat',
        },
        {
          label: '服务联系电话',
          prop: 'finalCustomerPhone',
        },
        {
          label: '服务地址',
          prop: 'deliveryAddress',
          span:24
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
onActivated(() => {
  onLoad();
});
let route = useRoute();
let params = ref({
  contractMinPrice: '',
  contractMaxPrice: '',
  ids: route.query.ids,
  signDate: [`${new Date().getFullYear()}-01-01`, `${new Date().getFullYear()}-12-31`],
});
watch(
  () => route.query.ids,
  val => {
    if (val) {
      params.value = {
        contractMinPrice: '',
        contractMaxPrice: '',
        ids: route.query.ids,
      };
      onLoad();
    }
  }
);
watch(
  () => route.query.customerName,
  val => {
    if (val) {
      params.value = {
        customerName: route.query.customerName,
        signDate: [route.query.signStartDate, route.query.signEndDate],
      };

      onLoad();
    }
  },
  {
    immediate: true,
  }
);
let tabs = ref([
  {
    label: '全部合同',
    value: null,
  },
  {
    label: '订单合同',
    value: 0,
  },
  {
    label: '项目合同',
    value: 1,
  },
]);

let tabIndex = ref(null);
function handleTabClick(item, index) {
  tabIndex.value = item.value;
  onLoad();
}
const addUrl = '/api/vt-admin/sealContract/saveWorkOrderSealContract';
const delUrl = '/api/vt-admin/sealContract/remove?ids=';
const updateUrl = '/api/vt-admin/sealContract/update';
const tableUrl = '/api/vt-admin/sealContract/page';

let tableData = ref([]);
let { proxy } = getCurrentInstance();

// onMounted(() => {
//   onLoad();
// });
let loading = ref(false);
let totalPrice = ref(0);
let noReceivedPrice = ref(0);
// let isMy = ref(route.query.type == 0 || !route.query.type);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId || null,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        contractTotalPrice: null,
        selectType: props.customerId?2:0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        customerId: props.customerId || null,
        contractTotalPrice: null,
        selectType: props.customerId?2:0,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noReceivedPrice.value = res.data.data.noReceivedPrice;
    });
}
let router = useRouter();
function rowSave(form, done, loading) {
  
  const data = {
    ...form,
    contractType: 2,
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  
  const data = {
    ...row,
    contractFiles: row.contractFiles && row.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function beforeOpen(done, type) {
  
  if (type == 'edit') {
    form.value.contractFiles = form.value.attachList.map(item => {
      return {
        label: item.originalName,
        value: item.id,
      };
    });
    form.value.operationTechnology = form.value.operationTechnology || null;

  }
  
  done();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function addContract() {
  router.push({
    path: '/Contract/editContract',
  });
}
function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      delBtn: 1,
       name:row.contractName,
      selectType: 0,
    },
  });
}
function reset() {
  params.value.contractMaxPrice = '';
  params.value.contractMinPrice = '';
  params.value.ids = null;
  onLoad();
}
function edit(row) {
 
  if (row.contractType == 2) {
    proxy.$refs.crud.rowEdit(row);
  } else {
    router.push({
      path: '/Contract/editContract',
      query: {
        id: row.id,
        name: `编辑-${row.contractName}`,
        isAdmin: row.isPreOrder,
      },
    });
  }
}
function cancel(row) {
  proxy.$refs.dialogForm.show({
    title: '撤 回',
    tip: '撤回到报价可编辑状态，需要重新走流程',
    option: {
      column: [
        {
          label: '撤回原因',
          prop: 'applyReason',
          span: 24,
          rules: [{ required: true, message: '请输入撤回原因', trigger: 'blur' }],
          type: 'textarea',
          placeholder: '请输入撤回原因',
          maxlength: 200,
          showWordLimit: true,
          rows: 4,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/sealContractCancel/save', {
          ...res.data,
          sealContractId: row.id,
        })
        .then(r => {
          proxy.$message({
            type: 'success',
            message: '申请成功',
          });
          onLoad();
          res.close();
        });
    },
  });
}
function cellStyle({ row, column }) {
  if (row.hasInvoice == 0 && column.property == 'hasInvoice' && row.isNeedInvoice == 1) {
    return {
      color: '#fff',
      backgroundColor: 'var(--el-color-danger-light-7)',
    };
  }
}

/* 
设置基础信息
*/
function setCustomerInfo(id) {
  if (!id) return;
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerAddress = res.data.data.address;
      const customerContact1 = proxy.findObject(option.value.group[0].column, 'customerContact');
      customerContact1.params.Url = '/vt-admin/customerContact/page?customerId=' + id;

      const { id: cotactId, phone,name } = res.data.data.customerContactVO || {};
      form.value.customerContact = cotactId;
      form.value.customerPhone = phone;

      const {customerName,address} = res.data.data
      form.value.finalCustomer = customerName
      form.value.finalCustomerConcat = name
      form.value.finalCustomerPhone = phone
      form.value.deliveryAddress = address
    });
}

function setContactInfo(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { phone } = res.data.data;
      form.value.customerPhone = phone;
    });
}
function handleAdd() {
  router.push({
    path: '/Order/salesOrder/compoents/addOrder',
  });
}
function toCustomerDetail(row) {
   router.push({
    path: '/CRM/customer/detail/detail',
    query: {
      customerId: row.customerId,
      name:row.customerName,
      type:0
    },
  });
}
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin-bottom: -10px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
:deep(.planCollectionDays) {
  font-size: 25px;
  font-weight: bolder;
}
.circle {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  border: 1px solid var(--el-color-success);

  color: var(--el-color-dsuccessanger);
  line-height: 50px;
  text-align: center;
  margin-right: 10px;
}
</style>
