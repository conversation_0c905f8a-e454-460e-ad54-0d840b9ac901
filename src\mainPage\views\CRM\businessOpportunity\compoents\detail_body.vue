<template>
  <basic-container shadow="never" style="height: 100%">
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="预计销售金额">
            <el-tag size="large">{{ form.preSealPrice }}</el-tag>
          </el-form-item>
          <el-form-item label="成交金额">
            <el-tag size="large">{{ form.a || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="商机阶段">
            <el-tag size="large">{{
              form.stage == 1
                ? `方案(${form.optionStatus == 1 ? '进行中' : '完成'})`
                : form.stage == 2
                ? `报价(${form.offerStatus == 1 ? '进行中' : '完成'})`
                : form.stage == 0
                ? '新增'
                : form.stage == 3
                ? '成交'
                : '失单'
            }}</el-tag>
          </el-form-item>
          <el-form-item label="业务">
            <el-tag>{{ form.businessPersonName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
                <Edit></Edit>
              </el-icon> -->
          </el-form-item>
          <el-form-item label="商务">
            <el-tag>{{ form.assistantName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
                <Edit></Edit>
              </el-icon> -->
          </el-form-item>
          <el-form-item label="技术">
            <el-tag>{{ form.technicalPersonnelName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
                <Edit></Edit>
              </el-icon> -->
          </el-form-item>
          <el-form-item label="业务配合人">
            <el-tag>{{ form.businessCoordinationPersonName || '---' }}</el-tag>
            <el-icon
              style="cursor: pointer; margin-left: 5px"
              @click="edit"
              v-if="form.businessPerson.split(',').includes($store.getters.userInfo.user_id)"
            >
              <Edit></Edit>
            </el-icon>
          </el-form-item>
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="
            (!isEdit &&
              form.businessPerson == $store.getters.userInfo.user_id &&
              activeName == 'baseInfo') ||
            (!isEdit &&
              form.assistant == $store.getters.userInfo.user_id &&
              activeName == 'baseInfo')
          "
          @click="isEdit = true"
          >编辑</el-button
        >
        <!-- <el-button
            icon="upload"
            v-if="
              (!isEdit &&
                form.businessPerson == $store.getters.userInfo.user_id &&
                activeName == 'baseInfo') ||
              (!isEdit &&
                form.assistant == $store.getters.userInfo.user_id &&
                activeName == 'baseInfo')
            "
            @click="uploadFile"
            >上传采集表</el-button
          > -->
        <el-button icon="close" v-else-if="isEdit" @click="isEdit = false">取消</el-button>
      </div>
    </div>
    <!-- <div style="display: flex">
          <div class="left_content">
            <div class="main_box">
              <div
                class="item"
                v-for="(item, index) in tabArr"
                :class="{ active: currentIndex == index }"
                @click="handleClick(index)"
              >
                <div class="arrow"></div>
                {{ item }}
              </div>
            </div>
          </div>
          <div style="width: calc(100% - 100px)">
            <component
              :is="currentCompoent"
              :form="form"
              :isEdit="isEdit"
              @getDetail="getDetail"
            ></component>
          </div>
        </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <BaseInfo
            :form="form"
            v-loading="loading"
            :isEdit="isEdit"
            :customer-id="customerId"
            :id="form.id"
            @getDetail="getDetail"
          ></BaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :userId="form.businessPerson"
      :info="{
        type: 1,
        customerId: form.customerId,
        logicId: form.id,
      }"
      :stageStatus="stageStatus"
      :offerId="form.offerId"
      :optionId="form.optionId"
      :currentId="form.id"
      :type="0"
      :id="form.id"
      :sealContractId="form.sealContractId"
      :businessPerson="form.businessPerson"
      :url="'/vt-admin/customerFollow/pageForDetail'"
      :is="tabArray.find(item => item.name == activeName).component"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';

import BaseInfo from '../detail/baseInfo.vue';
// // 跟进
// import Follow from '../../follow/allFollow.vue';
// 报备
import Report from '../detail/report.vue';
// 方案
import Programme from '../detail/programme.vue';
// 报价
import Quotation from '../detail/quotation.vue';
// 合同
import Contract from './contract.vue';
// 产品
import product from '../detail/product.vue';
// 产品
import Process from '../../quotation/compoents/process.vue';
import axios from 'axios';

let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
const props = defineProps(['customerId', 'id']);

const stageStatus = computed(() => {
  let stageStatus;
  console.log(form.value.stage);
  if (form.value.stage == 0) {
    // 新增
    stageStatus = 0;
  } else if (form.value.stage == 1 && form.value.optionStatus == 1) {
    // 方案进行中
    stageStatus = 1;
  } else if (form.value.stage == 1 && form.value.optionStatus != 1) {
    // 方案完成
    stageStatus = 2;
  } else if (form.value.stage == 2 && form.value.offerStatus == 1) {
    // 报价进行中
    stageStatus = 3;
  } else if (form.value.stage == 2 && form.value.offerStatus != 1) {
    //报价完成
    stageStatus = 4;
  } else if (form.value.stage == 3) {
    // 成交
    stageStatus = 5;
  } else {
    // 失单
    stageStatus = 6;
  }
  return stageStatus;
});
console.log(stageStatus.value);

watch(
  () => props.id,
  () => {
    getDetail();
    activeName.value = 'baseInfo';
  },
  {
    immediate: true,
  }
);
const conpletArr = ['report', 'programme', 'quotation', 'contract', 'product', 'Process'];
const tabArray = [
  {
    label: '方案',
    name: 'programme',
    component: Programme,
  },
  {
    label: '报价',
    name: 'quotation',
    component: Quotation,
  },
  // {
  //   label: '投标',
  //   name: 'bid',
  //   component: Follow,
  // },
  {
    label: '合同',
    name: 'contract',
    component: Contract,
  },
  {
    label: '报备信息',
    name: 'report',
    component: Report,
  },
  {
    label: '成交产品',
    name: 'product',
    component: product,
  },

  // {
  //   label: '跟进',
  //   name: 'follow',
  //   component: Follow,
  // },
  // {
  //   label: '提醒信息',
  //   name: 'remind',
  //   component: Follow,
  // },
  // {
  //   label: '流程图',
  //   name: 'Process',
  //   component: Process,
  // },
];
function getDetail() {
  isEdit.value = false;
  loading.value = true;

  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id: props.id,
      },
    })
    .then(res => {
      loading.value = false;
      const { provinceCode, cityCode, areaCode } = res.data.data;
      form.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        isProduct: res.data.data.productVOList && res.data.data.productVOList.length == 0 ? 0 : 1,
        productVOList:
          res.data.data.productVOList &&
          res.data.data.productVOList.map(item => {
            return {
              ...item,
              ...item.productVO,
            };
          }),
        // registeredCapital: Number(res.data.data.registeredCapital),
      };
    });
}
let moreInfoDetail = ref(false);
function loadMore() {
  moreInfoDetail.value = true;
}
let currentIndex = ref(0);
let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  console.log(value.index);

  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
  if (value.index == 1) {
    if (form.value.isHasOption == 1) {
       router.push({
        path: '/CRM/programme/compoents/updateVersion3',
        query: {
          id: form.value.id,
          type: 'detail',
          name: form.value.name,
          // businessOpportunityId: row.businessOpportunityId,
        },
      });
    
    } else {
      return false;
    }
  }
  if (value.index == 2) {
    if (form.value.isHasOffer == 1 && form.value.isHasOption == 1) {
        router.push({
        path: '/CRM/programme/compoents/updateVersion3',
        query: {
          id: form.value.id,
          type: 'detail',
          name: form.value.name,
          // businessOpportunityId: row.businessOpportunityId,
        },
      });
      
    }else if(form.value.isHasOffer == 1 && form.value.isHasOption == 0){
       router.push({
        path: '/CRM/quotation/compoents/addVersion3',
        query: {
          id: form.value.offerId,
          type: 'detail',
          name: form.value.name,
          businessOpportunityId: form.value.isHasOption == 1 ? form.value.id : null,
        },
      });

    }else{
      return false;

    }
  }
}
let { proxy } = getCurrentInstance();
function edit() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '业务配合人',
          component: 'wf-user-select',
          prop: 'businessCoordinationPerson',
          value: form.value.businessCoordinationPerson,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunity/update', {
          ...res.data,
          id: props.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
const store = useStore();
const tag = computed(() => store.getters.tag);
const activeName = ref('baseInfo');
function uploadFile() {
  proxy.$refs.dialogForm.show({
    title: '上传需求采集表',
    option: {
      labelWidth: 120,
      column: [
        {
          label: '采集表附件',
          prop: 'gatherFiles',
          type: 'upload',
          value: form.value.gatherFilesList?.map(item => {
            return {
              value: item.id,
              label: item.originalName,
            };
          }),
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 24,
          slot: true,
          dragFile: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
          rules: [
            {
              required: true,
              message: '请上传附件',
              trigger: 'change',
            },
          ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: form.value.id,
        gatherFiles: res.data.gatherFiles.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/businessOpportunity/uploadGatherFiles', data).then(e => {
        proxy.$message.success('操作成功');
        res.close();
        getDetail();
      });
    },
  });
}
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
