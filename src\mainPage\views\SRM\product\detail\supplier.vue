<template>
  <div>
    <el-button type="primary" size="normal" icon="plus" @click="handleAdd()"> 新增 </el-button>
    <SupplierList :productId="productId" ref="supplierListRef"></SupplierList>
    <!-- 供应商选择弹窗 -->
    <wf-supplier-select
      ref="supplier-select"
      check-type="box"
      :userUrl="`/api/vt-admin/supplier/pageForSupplier?productId=${productId}`"
      @onConfirm="handleUserSelectConfirm"
    ></wf-supplier-select>

    <el-drawer title="关联供应商" size="70%" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <avue-form
        :option="addOption"
        ref="dialogForm"
        v-model="addForm"
        @submit="submit"
      ></avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue';
import WfSupplierSelect from '@/components/Y-UI/wf-supplier-select.vue';
import axios from 'axios';
import SupplierList from './supplierList.vue';
import { dateFormat } from '@/utils/date';
const props = defineProps({
  // 父组件传递过来的值
  productList: Array,
  productId: String,
});

const { proxy } = getCurrentInstance();

let dialogVisible = ref(false);
let addForm = ref({
  supplierList: [],
});
const addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      labelWidth: 0,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          addProduct();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '供应商',
            prop: 'supplierId',
            component: 'wf-supplier-select',
          },
          {
            label: '报价时间',
            prop: 'offerDate',
            type: 'date',
            span: 12,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
          {
            label: '产品质保期（年）',
            prop: 'warrantyPeriod',
            type: 'number',
            span: 12,
          },
          {
            label: '是否含税',
            prop: 'isHasTax',
            type: 'switch',
            value: 1,
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
            span: 12,
          },
          {
            label: '税率',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            formatter: val => {
              return val + '%';
            },
          },
          {
            label: '单价',
            prop: 'unitPrice',
            type: 'number',
            span: 12,
          },
        ],
      },
    },
  ],
});
function addProduct() {
  proxy.$refs['supplier-select'].visible = true;
}

function handleUserSelectConfirm(ids) {
  addForm.value = {
    supplierList: ids.split(',').map(item => {
      return {
        supplierId: item,
        isHasTax:1,
        offerDate:dateFormat(new Date(), 'yyyy-MM-dd'),
      };
    }),
  };
}
function submit(form, done) {
  const data = form.supplierList.map(item => {
    return {
      productId: props.productId,
      ...item,
    };
  });
  axios.post('/api/vt-admin/supplierProduct/saveBatch', data).then(res => {
    proxy.$message.success('添加成功');
    done();
    dialogVisible.value = false;
    addForm.value = {};
    proxy.$refs.supplierListRef.onLoad();
    done();
  });
}

function handleAdd() {
  dialogVisible.value = true;
  addForm.value.supplierList = [];
}
</script>

<style lang="scss" scoped></style>
