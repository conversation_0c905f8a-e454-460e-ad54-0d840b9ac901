<template>

    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :permission="permission"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #externalHandleUser-form="{ type }">
        <div v-if="type !== 'view'" style="display: flex; align-items: center">
          <div>
            <el-avatar
              shape="circle"
              :size="60"
              style="cursor: pointer; position: relative; overflow: visible; margin-right: 5px"
              v-for="item in form.externalHandleUserList"
            >
              <el-text style="font-weight: bolder; color: #fff">{{ item.realName }}</el-text>
              <!-- <el-icon class="closeIcon" @click="handleDelete(item)" :size="20"
          ><CircleCloseFilled
        /></el-icon> -->
            </el-avatar>
          </div>
          <div
            style="
              display: flex;
              flex-direction: column;
              align-items: center;

              justify-content: center;
            "
          >
            <div>
              <!-- <el-button type="primary" icon="plus" @click="handleAddIn" text
                >添加内部工程师</el-button
              > -->
            </div>
            <div>
              <el-button type="primary" icon="plus" @click="handleAddOut" text
                >添加外部工程师</el-button
              >
            </div>
          </div>
        </div>
        <el-text style="padding-left: 15px" disabled v-else>
          {{ form.handleUserName }}
        </el-text>
      </template>
      <template #objectStatus="{ row }">
        <el-tag :type="['info', 'warning', 'success'][row.objectStatus]" effect="plain">{{
          row.$objectStatus
        }}</el-tag>
      </template>
      <template #lables="{ row }">
        <el-link type="primary" @click="viewDetail(row)" target="_blank">{{
          row.labelName
        }}</el-link>
      </template>
      <template #filesDetail-form>
        <File :fileList="form.fileList"></File>
      </template>
      <template #completeFiles-form>
        <File :fileList="form.completeFilesList"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wfUserSelect ref="userSelectRef" @onConfirm="handleConfirm"></wfUserSelect>
    <userSelect ref="userSelectRefOut" @onConfirm="handleConfirmOut"></userSelect>
     <el-drawer title="详情" v-model="drawer" size="50%">
      <avue-form :option="detailOption" v-model="detailForm">
        <template #taskName>
          <el-descriptions :title="detailForm.labelName">
            <el-descriptions-item label="要求交付时间">{{
              (detailForm.planStartTime && detailForm.planStartTime.split(' ')[0]) || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务客户">{{
              detailForm.finalCustomer || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务联系人">{{
              detailForm.contact || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{
              detailForm.contactPhone || '--'
            }}</el-descriptions-item>
            <el-descriptions-item label="服务地址" :span="2">
              {{ detailForm.distributionAddress || '--' }}
            </el-descriptions-item>
            <el-descriptions-item label="任务描述" :span="3">
              <span style="white-space: pre-line"> {{ detailForm.taskDescription || '--' }}</span>
            </el-descriptions-item>
          </el-descriptions>
        </template>
        <template #filesDetail>
        <File :fileList="detailForm.fileList"></File>
      </template>
      <template #completeFiles>
        <File :fileList="detailForm.completeFilesList"></File>
      </template>
      </avue-form>
    </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
import userSelect from '@/views/Contract/wokerOrder/component/userSelect.vue';
import { dateFormat } from '@/utils/date.js';
const props = defineProps(['sealContractId','form'])
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 180,
  labelWidth: 120,
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,

  column: [
    // {
    //   label: '任务编号',
    //   prop: 'objectNo',
    //   display: false,
    // },
    // {
    //   label: '任务名称',
    //   prop: 'objectName',
    //   search: true,
    //   display: false,
    // },
    {
      label: '任务类型',
      prop: 'lables',
      placeholder: '请输入任务类型',
      type: 'tree',
      search:true,
      display: false,
      props: {
        value: 'id',
        label: 'dictValue',
      },
      dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
    },
    {
        label:'任务描述',
        prop:'taskDescription',
        display:false,
    },
   
    {
      label: '里程碑',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      viewDisplay: false,
      labelWidth: 0,
    },
    {
      label: '要求交付时间',
      prop: 'planStartTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      span: 12,
      width: 150,
      labelWidth: 110,
      display: false,
    },
   
    {
      label: '派单人',
      prop: 'projectLeaderName',
      width: 110,
      display: false,
      search: true,
    },
    {
      label: '任务状态',
      prop: 'objectStatus',
      type: 'select',
      search: true,
      display: false,
      width: 110,
      dicData: [
        {
          label: '待派单',

          value: 0,
        }, {
          label: '待接单',
          value: 3,
        },
        {
          label: '处理中',
          value: 1,
        },
        {
          label: '处理完成',
          value: 2,
        },
       
      ],
    },
  ],
  group: [
    // {
    //   label: '合同信息',
    //   prop: 'contractInfo',
    //   addDispla: true,
    //   column: [
    //     {
    //       label: '关联客户',
    //       prop: 'customerId',
    //       placeholder: '请选择关联客户',
    //       change: val => {
    //         setCustomerInfo(val.value);
    //       },
    //       component: 'wf-customer-select',
    //     },
    //     {
    //       label: '合同名称',
    //       prop: 'contractName',
    //     },
    //     {
    //       label: '合同金额',
    //       prop: 'contractTotalPrice',
    //       placeholder: '请输入合同金额',
    //       type: 'number',
    //     },
    //     {
    //       label: '关联联系人',
    //       type: 'input',
    //       prop: 'customerContact',
    //       component: 'wf-contact-select',
    //       placeholder: '请选择联系人',
    //       // disabled: true,
    //       params: {},
    //       change: val => {
    //         setContactInfo(val.value);
    //       },
    //     },
    //     {
    //       type: 'input',
    //       label: '电话',
    //       span: 12,
    //       display: true,
    //       prop: 'customerPhone',
    //     },
    //     {
    //       type: 'date',
    //       label: '签订日期',
    //       span: 12,
    //       display: true,
    //       prop: 'signDate',
    //       required: true,
    //       value: dateFormat(new Date(), 'yyyy-MM-dd'),
    //       format: 'YYYY-MM-DD',
    //       valueFormat: 'YYYY-MM-DD',
    //       rules: [
    //         {
    //           required: true,
    //           message: '签订日期必须填写',
    //         },
    //       ],
    //     },
    //     {
    //       type: 'date',
    //       label: '合同要求交付日期',
    //       span: 12,
    //       display: true,
    //       prop: 'contractDeliveryDate',
    //       required: true,
    //       format: 'YYYY-MM-DD',
    //       change:(val) => {
    //         
    //         form.value.planStartTime = val.value
    //       },
    //       valueFormat: 'YYYY-MM-DD',
    //     },
    //     {
    //       label: '付款期限',
    //       type: 'radio',
    //       span: 12,
    //       prop: 'paymentDeadline',
    //       dicFormatter: res => {
    //         return res.data;
    //       },

    //       dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
    //       props: {
    //         value: 'id',
    //         label: 'dictValue',
    //       },
    //     },
    //     {
    //       label: '是否开票',
    //       prop: 'isNeedInvoice',
    //       type: 'radio',
    //       value: 1,
    //       dicData: [
    //         {
    //           label: '是',
    //           value: 1,
    //         },
    //         {
    //           label: '否',
    //           value: 0,
    //         },
    //       ],
    //     },
      

    //     {
    //       label: '合同附件',
    //       prop: 'contractFiles',
    //       type: 'upload',
    //       value: [],
    //       dataType: 'object',
    //       loadText: '附件上传中，请稍等',
    //       span: 24,
    //       slot: true,
    //       // align: 'center',
    //       propsHttp: {
    //         res: 'data',
    //         url: 'id',
    //         name: 'originalName',
    //         // home: 'https://www.w3school.com.cn',
    //       },
    //       action: '/blade-resource/attach/upload',
    //     },
    //     {
    //       label: '备注',
    //       prop: 'remark',
    //       placeholder: '请输入备注',
    //       span: 24,
    //       type: 'textarea',

    //       showWordLimit: true,
    //       autosize: {
    //         minRows: 3,
    //         maxRows: 4,
    //       },
    //     },
    //   ],
    // },
      {
      label: '服务客户信息',
      prop: 'wokerOrderInfo',
      detail:true,
      column: [
        {
          label:'服务客户名称',
          prop:'finalCustomer',
          value:props.form.finalCustomer || props.form.customerName,
          span:24,
        },
        {
          label: '服务联系人',
          prop: 'contact',
            value:props.form.finalCustomerConcat || props.form.customerContactName,
        },
        {
          label: '服务联系电话',
          prop: 'contactPhone',
            value:props.form.finalCustomerPhone || props.form.customerPhone,
        },
        {
          label: '服务地址',
          prop: 'distributionAddress',
            value:props.form.deliveryAddress,
          span:24
        },
      ],
    },
    {
      label: '任务信息',
      prop: 'workOrderInfo',
      column: [
      
        // {
        //   type: 'datetime',
        //   label: '服务时间',
        //   span: 12,

        //   width: 140,
        //   format: 'YYYY-MM-DD HH:mm',
        //   valueFormat: 'YYYY-MM-DD HH:mm:ss',
        //   prop: 'planTime',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请选择服务时间',
        //       trigger: 'blur',
        //     },
        //   ],
        // },
       
        {
          label: '任务类型',
          prop: 'lables',
          placeholder: '请输入任务类型',
          type: 'tree',
          props: {
            value: 'id',
            label: 'dictValue',
          },
          rules:[{
            required: true,
            message: '请选择任务类型',
            trigger: 'blur',
          
          }],
          dicUrl: '/blade-system/dict-biz/dictionary?code=wokerOrderType',
        },
         {
          type: 'date',
          label: '要求交付时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planStartTime',
          rules: [
            {
              required: true,
              message: '请选择要求交付时间',
              trigger: 'blur',
            },
          ],
        },
         {
          label: '派单人',
          prop: 'projectLeader',
          placeholder: '请选择派单人',
        
          component: 'wf-user-select',
          rules:[{
            required: true,
            message: '请选择派单人',
          }]
        },
        {
          label: '任务描述',
          prop: 'taskDescription',
          placeholder: '请输入任务描述',
          type: 'textarea',
          span: 24,
        },
      
       
     
       
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      addDisplay:false,
      editDisplay:false,
      column: [
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },

        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 12,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
      
        {
          label: '工单附件',
          prop: 'filesDetail',
          type: 'upload',
          addDisplay: false,
          editDisplay: false,
          viewDisplay: true,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '指派工程师',
          prop: 'externalHandleUser',
          component: 'wf-user-select',
          rules: [
            {
              required: true,
              message: '请选择工程师',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '完成时间',
          prop: 'completeTime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          type: 'datetime',
          span: 24,
        },
        {
          label: '完成附件',
          span: 24,
          prop: 'completeFiles',
        },
        {
          label: '完成备注',
          prop: 'completeRemark',
          span: 24,
          type: 'textarea',
        },
      ],
    },
   
  ],
});
let form = ref({
  externalHandleUserList: [],
});
/*
设置基础信息
*/
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.contact = res.data.data.finalCustomerConcat;
      form.value.finalCustomer = res.data.data.finalCustomer;
      form.value.contactPhone = res.data.data.finalCustomerPhone;
      form.value.distributionAddress = res.data.data.deliveryAddress;
    
    });
}
function setCustomerInfo(id) {
  if (!id) return;
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      form.value.customerAddress = res.data.data.address;
      form.value.deliveryAddress = res.data.data.address;
      const customerContact1 = proxy.findObject(option.value.group[0].column, 'customerContact');
    

      customerContact1.params.Url = '/vt-admin/customerContact/page?customerId=' + id;

      const { id: cotactId, phone, name } = res.data.data.customerContactVO || {};
      form.value.customerContact = cotactId;
      form.value.contact = name;
      form.value.customerPhone = phone;
      form.value.contactPhone = phone;
        const {customerName,address} = res.data.data
      form.value.finalCustomer = customerName
      form.value.finalCustomerConcat = name
      form.value.finalCustomerPhone = phone
      
    });
}
function permission(key, row) {
  console.log(row);
  if (['editBtn', 'delBtn'].includes(key) && row.objectStatus != 0) {
    return false;
  } else {
    return true;
  }
}
function setContactInfo(id, type) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const { phone } = res.data.data;
      form.value[type] = phone;
    });
}

// 添加内部工程师
function handleAddIn() {
  proxy.$refs.userSelectRef.visible = true;
}
function handleConfirm(id) {
  axios
    .get('/api/blade-system/user/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      if (!form.value.externalHandleUserList) {
        form.value.externalHandleUserList = [];
      } else {
        form.value.externalHandleUserList.push(res.data.data);
        form.value.externalHandleUser = form.value.externalHandleUserList
          .map(item => item.id)
          .join(',');
      }
    });
}
function handleConfirmOut(data) {
  ;
  if (!form.value.externalHandleUserList) {
    form.value.externalHandleUserList = [data];
  } else {
    form.value.externalHandleUserList.push(...data);
    form.value.externalHandleUser = form.value.externalHandleUserList
      .map(item => item.id)
      .join(',');
  }
}
function handleAddOut() {
  if (form.value.externalHandleUserList && form.value.externalHandleUserList.length >= 1)
    return proxy.$message.warning('只能选择一个外部处理人');
  proxy.$refs.userSelectRefOut.dialogVisible = true;
}
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let crud = ref(null);
onMounted(() => {
  // crud.value.rowAdd();
});
const addUrl = '/api/vt-admin/sealContractObject/saveWorkOrder';
const delUrl = '/api/vt-admin/sealContractObject/remove?ids=';
const updateUrl = '/vt-admin/sealContractObject/updateWorkOrder';
const tableUrl = '/api/vt-admin/sealContractObject/externalPcPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        selectType: 4,
        sealContractId:props.sealContractId,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
}
let router = useRouter();
function beforeOpen(done, type) {
  form.value.objectRemark = form.value.remark;
  if (['add', 'view'].includes(type)) {
   
  } else {
   
    form.value.files =
      form.value.fileList &&
      form.value.fileList.map(item => {
        // 修正拼写错误，将 reuturn 改为 return
        return {
          label: item.originalName,
          value: item.id,
        };
      });
    
    form.value.objectRemark = form.value.remark;
  }
  done();
}
function rowSave(form, done, loading) {
  if (props.sealContractId) {
    const data = {
      ...form,
      files: form.files && form.files.map(item => item.url).join(','),
      sealContractId:props.sealContractId
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {});
  } 
}

function addContract(form, done, loading) {
  const data = {
    ...form,
    finalCustomerConcat: form.contact,
    finalCustomerPhone: form.contactPhone,
    contractType: 2,
  
    contractFiles: form.contractFiles && form.contractFiles.map(item => item.value).join(','),
  };
  axios
    .post('/api/vt-admin/sealContract/saveWorkOrderSealContract', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
        form.sealContractId = res.data.data;
        rowSave(form, done, loading);
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  ;
  const data = {
    ...row,
    files: row.files && row.files.map(item => item.value).join(','),
    remark: row.objectRemark,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}


let drawer = ref(false);
let detailForm = ref({})
let detailOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 200,
  labelWidth: 120,
  updateBtnText: '提交',
  dialogWidth: '1000',
  dialogType: 'drawer',
  border: true,
  submitBtn: false,
  emptyBtn: false,
  detail: true,

  group: [
    {
      label: '任务信息',
      prop: 'taskInfo',
      addDisplay: true,
      disabled: true,
      column: [
        {
          labelWidth: 0,
          span: 24,
          prop: 'taskName',
        },
      ],
    },
    {
      label: '工单信息',
      prop: 'workOrderInfo',
      column: [
        {
          label: '工单类型',
          type: 'radio',
          dicData: [
            {
              value: 0,
              label: '内部工单',
            },
            {
              value: 1,
              label: '外部工单',
            },
          ],
          rules: [
            {
              required: true,
              message: '请选择工单类型',
            },
          ],
          prop: 'objectType',
          control: val => {
            return {
              orderPrice: {
                display: val == 1,
              },
            };
          },
        },
        {
          label: '工单名称',
          prop: 'objectName',
          placeholder: '请输入工单名称',
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入工单名称',
            },
          ],
          type: 'input',
        },
        {
          type: 'datetime',
          label: '服务时间',
          span: 12,

          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
          rules: [
            {
              required: true,
              message: '请选择服务时间',
              trigger: 'blur',
            },
          ],
        },

        {
          label: '工单价格',
          prop: 'orderPrice',
          placeholder: '请输入工单价格',
          type: 'number',
          span: 12,
        },
        {
          label: '工单描述',
          prop: 'remark',
          placeholder: '请输入工单描述',
          type: 'textarea',
          span: 24,
        },
     
        {
          label: '工单附件',
          prop: 'filesDetail',
          type: 'upload',
        
          viewDisplay: true,
          value: [],
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },

        {
          label: '指派工程师',
          prop: 'handleUserName',
        },
      ],
    },
    {
      label: '完成信息',
      prop: 'finishInfo',
      addDisplay: false,
      editDisplay: false,
      viewDisplay: true,

      column: [
        {
          label: '完成时间',
          prop: 'completeTime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          type: 'datetime',
          span: 24,
        },
        {
          label: '完成附件',
          span: 24,
          prop: 'completeFiles',
        },
        {
          label: '完成备注',
          prop: 'completeRemark',
          span: 24,
          type: 'textarea',
        },
      ],
    },
  ],
});
function viewDetail(row) {
  detailForm.value = {
    ...row,
  }
  nextTick(() => {
    drawer.value = true
  })
}


</script>

<style lang="scss" scoped></style>
