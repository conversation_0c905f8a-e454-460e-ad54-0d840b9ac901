<template>
  <el-popover placement="bottom" width="350" trigger="click" @show="queryTopMessage">
    <template #reference>
      <el-badge :hidden="unReadCount === 0" :value="unReadCount">
        <el-icon size="large">
          <Bell />
        </el-icon>
      </el-badge>
    </template>
    <el-tabs v-model="activeName">
      <el-tab-pane
        v-for="item in topMsgType"
        :label="item.label"
        :name="item.name"
        :key="item.name"
      >
        <ul class="msg-list">
          <li v-for="item in topMsgData" :key="item.id" :class="{ hasRead: item.isRead === 0 }">
            <em class="new" v-if="item.isRead === 1"></em>
            <i v-if="item.type === 3" class="el-icon-message ri-mail-fill" />
            <i v-else class="el-icon-user-solid ri-mail-fill" />
            <div class="info" @click="openMessageDetail(item)">
              <div class="title">{{ item.title }}</div>
              <div class="date">{{ item.createTime }}</div>
            </div>
          </li>
        </ul>
        <div class="msg-more" @click="allMessage">查看所有消息</div>
      </el-tab-pane>
    </el-tabs>

    <el-drawer title="我的消息" v-model="drawer" direction="rtl" size="80%" append-to-body>
      <div class="msg-con">
        <el-form :inline="true" :model="params" class="demo-form-inline">
          <el-form-item label="标题">
            <el-input v-model="params.title" placeholder="请输入标题" size="small"></el-input>
          </el-form-item>
          <el-form-item label="内容">
            <el-input v-model="params.content" size="small" placeholder="请输入内容"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" size="small" @click="queryMessagePage">查询</el-button>
          </el-form-item>
        </el-form>
        <el-row>
          <el-col :span="24">
            <div class="tool-box">
              <el-button
                icon="el-icon-check"
                size="small"
                :disabled="unReadCount === 0"
                type="success"
                @click="handleRead"
                >全部已读</el-button
              >
            </div>
          </el-col>
        </el-row>
        <el-row>
          <el-table
            class="avue-crud"
            :data="msgData"
            style="width: 100%"
            border
            @cell-click="toDetail"
          >
            <el-table-column label="消息类型" prop="type" width="100">
              <template #default="scope">
                {{ typeDict(scope.row.type) }}
              </template>
            </el-table-column>
            <el-table-column label="标题" prop="title" width="180" />
            <el-table-column label="内容" prop="content" #default="scope">
              <span>{{ scope.row.content.split('|')[0] }}</span>
            </el-table-column>
            <el-table-column label="状态" prop="isRead" width="80">
              <template #default="scope">
                <el-tag effect="plain" :type="scope.row.isRead === 0 ? 'info' : 'danger'">
                  {{ scope.row.isRead === 0 ? '已读' : '未读' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="发送时间" prop="createTime" width="180" />
            <el-table-column fixed="right" label="操作" width="200">
              <template #default="scope">
                <el-button plain size="small" type="" @click="openMessageDetail(scope.row)"
                  >查看</el-button
                >
                <el-button size="small" type="danger" @click="handleDelete(scope.$index, scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </el-row>
        <el-row>
          <el-pagination
            :page-size="10"
            :page-sizes="[10, 20, 30, 50]"
            :total="page.total"
            background
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="sizeChange"
            @current-change="currentChange"
          ></el-pagination>
        </el-row>
      </div>
    </el-drawer>
  </el-popover>
  <div class="message-box" :class="{ active: !isHidden }">
    <div class="content_box" v-if="!isHidden">
     
      <div>
        <div
          class="messageBox"
          v-for="item in messageList"
          @click="toPage(item)"
          :key="item.message"
        >
          <div class="title_box">
            <span class="title">{{
              remindTypeData.find(i => i.value == item.type)?.typeName
            }}</span>
            <span class="sub_title">{{ item.message }}</span>
          </div>
          <span class="number">{{ item.number.length }}</span>
        </div>
      </div>
    </div>

    <div class="btn_box">
      <el-badge :value="totalRemind" :hidden='totalRemind == 0'>
        <el-button
          @click="handleClick"
          type="primary"
          plain
          circle
          size="large"
          icon="BellFilled"
        />
      </el-badge>
    </div>
  </div>
</template>

<script>
import { MQTT_SERVICE, MQTT_USERNAME, MQTT_PASSWORD, MQTT_ENV } from '@/api/plugin/message/mqtt';
import { topList, getDetail, queryMyMessage, readAll, remove } from '@/api/plugin/message/message';
import { mapGetters, mapState, mapMutations } from 'vuex';
import Stomp from 'stompjs';
import {remindTypeData} from '@/const/const.js'
let client;
export default {
  name: 'top-notice',
  data() {
    return {
      activeName: 'msg',
      newMessage: true,
      //消息类型字典
      messageType: [
        {
          value: 1,
          label: '普通消息',
        },
        {
          value: 2,
          label: '部门群发',
        },
        {
          value: 3,
          label: '审批消息',
        },
        {
          value: 4,
          label: '角色群发',
        },
      ],
      topMsgType: [{ label: '消息', name: 'msg' }],
      //顶部消息数据，只查询未读
      topMsgData: [],
      //所有消息数据
      msgData: [],
      drawer: false,
      // client: Stomp.client(MQTT_SERVICE),
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      query: {},
      params: {},
      topParams: { isRead: 0 },
      unReadCount: 0,

      remindTypeData: remindTypeData,
    };
  },
  created() {
    this.connect();
    //查询顶部消息数据
    this.queryTopMessage();

    setInterval(() => {
      this.$store.dispatch('getMessageList');
    }, 60 * 1000 * 5);
  },
  mounted() {
    this.$store.dispatch('getMessageList', true);
  },
  beforeUnmount() {
    // 销毁监听
    client.disconnect();
  },
  computed: {
    ...mapGetters(['userInfo']),
    ...mapState({
      isHidden: state => state.notice.isHidden,
      messageList: state => state.notice.messageList,
    }),
    totalRemind() {
      return this.messageList.reduce((pre, cur) => {
        return pre + cur.number.length;
      }, 0);
    },
  },
  directives: {
    // dragable: {
    //   mounted(el, binding) {
    //     console.log(el);
    //     let oDiv = el; //当前元素
    //     let self = this; //上下文
    //     oDiv.onmousedown = function (e) {
    //       //鼠标按下，计算当前元素距离可视区的距离
    //       let disX = e.clientX - oDiv.offsetLeft;
    //       let disY = e.clientY - oDiv.offsetTop;
    //       document.onmousemove = function (e) {
    //         //通过事件委托，计算移动的距离
    //         let l = e.clientX - disX;
    //         let t = e.clientY - disY;
    //         var xMax = document.documentElement.clientWidth - 100;
    //         var xMin = 0;
    //         var yMax = document.documentElement.clientHeight - 100;
    //         var yMin = 50;
    //         if (l >= xMax) {
    //           l = xMax;
    //         } else if (l <= xMin) {
    //           l = xMin;
    //         }
    //         if (t >= yMax) {
    //           t = yMax;
    //         } else if (t <= yMin) {
    //           t = yMin;
    //         }
    //         //移动当前元素
    //         oDiv.style.left = l + "px";
    //         oDiv.style.top = t + "px";
    //         //将此时的位置传出去
    //         // binding.value({ x: e.pageX, y: e.pageY });
    //       };
    //       document.onmouseup = function (e) {
    //         document.onmousemove = null;
    //         document.onmouseup = null;
    //       };
    //     };
    //   },
    // },
  },
  methods: {
    typeDict(value) {
      if (value === 1) {
        return '普通消息';
      }
      if (value === 2) {
        return '部门群发';
      }
      if (value === 3) {
        return '审批消息';
      }
      if (value === 4) {
        return '角色群发';
      }
    },

    queryTopMessage() {
      if (this.newMessage) {
        topList().then(res => {
          const data = res.data.data;
          this.topMsgData = data.messageList;
          this.unReadCount = data.unReadCount;
        });
        this.newMessage = false;
      }
    },

    openMessageDetail(row) {
      getDetail(row.id).then(res => {
        if (row.isRead === 1) {
          this.unReadCount--;
        }
        row.isRead = 0;
        const data = res.data.data;
        this.$alert(data.content && data.content.split('|')[0], data.title, {
          confirmButtonText: '确定',
        }).then(() => {
          if (data.content && data.content.split('|')[1]) {
            this.drawer = false;
            this.$router.push(data.content && data.content.split('|')[1]);
          }
        });
      });
    },
    queryMessagePage() {
      queryMyMessage(
        this.page.currentPage,
        this.page.pageSize,
        Object.assign(this.params, this.query)
      ).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.msgData = data.records;
      });
    },

    //全部已读
    handleRead() {
      this.$confirm('确定将消息设为全部已读?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        readAll().then(res => {
          if (res.data.success) {
            this.queryMessagePage();
            this.newMessage = true;
            this.queryTopMessage();
            this.$message({
              type: 'success',
              message: '操作成功！',
            });
          } else {
            this.$message({ type: 'error', message: res.data.msg });
          }
        });
      });
    },

    //查看所有消息
    allMessage() {
      this.drawer = true;
      this.queryMessagePage();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
      this.queryMessagePage();
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.queryMessagePage();
    },

    handleDelete(index, row) {
      this.$confirm('确定删除此消息?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        remove(row.id).then(res => {
          if (res.data.success) {
            this.queryMessagePage();
            this.newMessage = true;
            this.queryTopMessage();
            this.$message({
              type: 'success',
              message: '操作成功！',
            });
          } else {
            this.$message({ type: 'error', message: res.data.msg });
          }
        });
      });
    },

    onConnected() {
      // 单消息订阅
      const singleTopic =
        `/exchange/directExchange/single_msg_${MQTT_ENV}_` + this.userInfo.user_id;
      client.subscribe(singleTopic, this.messageCallback, this.onSingleFailed);
      client.debug = true;
    },
    onSingleFailed(frame) {
      // 可此处尝试重连
      console.log('连接mq失败: ' + frame);
      setTimeout(() => {
        this.connect();
      }, 5000);
    },
    //接收到消息回调
    messageCallback(frame) {
      this.unReadCount++;
      this.newMessage = true;
      console.log('消息回调:' + frame);
      const info = JSON.parse(frame.body);
      console.log(info);
      this.$notify({
        iconClass: 'el-icon-message',
        dangerouslyUseHTMLString: true,
        title: info.title,
        position: 'top-right',
        onClick: () => {
          if (info.content && info.content.split('|')[1]) {
            this.drawer = false;
            this.$router.push(info.content && info.content.split('|')[1]);
          }
          console.log(this.$route);
          getDetail(info.id).then(res => {
            if (info.isRead === 1) {
              this.unReadCount--;
            }
          });
        },
        onClose: () => {
          console.log(this.$route);
          if (this.$route.path.indexOf('login') > -1) return;
          getDetail(info.id).then(res => {
            if (info.isRead === 1) {
              this.unReadCount--;
            }
          });
        },
        message: info.content && info.content.split('|')[0],
        duration: 0,
      });
      this.notifyMe(info);
    },
    notifyMe(info) {
      if (!('Notification' in window)) {
        // 检查浏览器是否支持通知
        alert('当前浏览器不支持桌面通知');
      } else if (Notification.permission === 'granted') {
        // 检查是否已授予通知权限；如果是的话，创建一个通知
        const notification = new Notification(info.title, {
          body: info.content && info.content.split('|')[0],
          icon: '/favicon.png',
          requireInteraction: true,
          data: {
            url: window.location.origin,
          },
        });

        // …
      } else if (Notification.permission !== 'denied') {
        // 我们需要征求用户的许可
       
          this.$message({
            showClose: true,
            message: '请同意或设置浏览器通知权限，或联系管理员',
            type: 'warning',
            duration:0
          });
 
        Notification.requestPermission().then(permission => {
          // 如果用户接受，我们就创建一个通知
          if (permission === 'granted') {
            const notification = new Notification('设置成功！');
            // …
          }
        });
      }

      // 最后，如果用户拒绝了通知，并且你想尊重用户的选择，则无需再打扰他们
    },
    connect() {
      const headers = {
        login: MQTT_USERNAME,
        passcode: MQTT_PASSWORD,
      };
      client = Stomp.client(MQTT_SERVICE);
      client.connect(headers, this.onConnected, this.onSingleFailed);
    },
    toDetail(data, b) {
      if (data.content && data.content.split('|')[1]) {
        this.drawer = false;
        this.$router.push(data.content && data.content.split('|')[1]);
      }
    },
    handleClick() {
      this.$store.dispatch('getMessageList');
      this.$store.commit('SET_ISHIDDEN',this.messageList.length > 0? !this.isHidden : true);
    },
    toPage(item) {
      const link = this.remindTypeData.find(i => i.value == item.type)?.link;
      this.$router.push({
        path: link,
        query: {
          ids: item.number && item.number.slice(0,100) && item.number.join(','),
          type: item.type,
          name: '',
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.msg-list {
  max-height: 300px;
  overflow-y: auto;
  -ms-scroll-chaining: none;
  list-style: none;
  min-width: 100px;
  min-height: 100px;
  padding: 0;
  margin: 0;
  overscroll-behavior: contain;
  &::-webkit-scrollbar {
    width: 8px;
  }
  &::-webkit-scrollbar-thumb {
    width: 8px;
    background: #ccc;
    border-radius: 4px;
  }
  li {
    display: flex;
    align-items: flex-start;
    padding: 15px 10px;
    -webkit-transition: 0.3s;
    transition: 0.3s;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    position: relative;
    &.hasRead {
      opacity: 0.6;
    }
    .new {
      position: absolute;
      top: 30px;
      left: 0;
      width: 6px;
      height: 6px;
      border-radius: 3px;
      background: red;
    }
    i {
      margin-right: 10px;
      padding: 10px;
      border-radius: 50%;
      color: #fff;
      background-color: #3391e5;
      &.ri-service-fill {
        background-color: #fe5d58;
      }
      &.ri-file-edit-fill {
        background-color: #dab033;
      }
    }
    .info {
      .title[data-v-081b0652] {
        font-size: 14px;
        line-height: 1.5;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
      .date[data-v-081b0652] {
        margin-top: 5px;
        font-size: 12px;
        color: #999;
      }
    }
  }
}
.msg-more {
  padding: 15px 0;
  margin-bottom: -10px;
  text-align: center;
  color: #666;
  border-top: 1px solid #eee;
  cursor: pointer;
}
.msg-con {
  padding: 0 20px;
  .el-pagination {
    margin-top: 20px;
    text-align: right;
  }
}

.message-box {
  position: fixed;
  bottom: 00px;
  right: 5px;
  z-index: 2000;
  width: 55px;
  min-height: 55px;
  transition: all 0.3s linear;
  border-radius: 30px;
  box-shadow: var(--el-box-shadow);
  background-color: #fff;
  overflow: visible;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  cursor: pointer;
  .content_box {
    transition: all 0.3s linear;
  }
  .info_box {
    padding: 10px;
    height: 80px;
    border: 1px solid var(--el-color-primary-light-5);
    margin: 5px;
    margin-bottom: 10px;
    border-radius: 5px;
    .title {
      font-size: 16px;
      line-height: 16px;
      margin-bottom: 10px;
      color: #000;
      display: flex;
      padding: 0 5px;
      justify-content: space-between;
      align-items: center;
    }

    .content {
      font-size: 14px;
      line-height: 14px;
      white-space: wrap;
    }
    .content {
    }
  }
  .messageBox {
    width: 100%;

    border-top: 1px solid var(--el-color-info-light-5);
    // border-top: 1px solid var(--el-color-info-light-5);
    display: flex;
    padding: 5px;
    box-sizing: border-box;
    //height: 50px;
    line-height: 50px;
    justify-content: space-between;
    overflow: visible;
    align-items: center;
    .title_box {
      display: flex;
      width: 90%;
      flex-direction: column;
      justify-content: flex-start;
      height: 100%;
      .title {
        font-weight: bold;
        font-size: 14px;
        line-height: 20px;
        color: #000;
      }
      .sub_title {
        line-height: 20px;
        font-size: 12px;
        white-space: pre-wrap;
      }
    }

    .number {
      color: #fff;
      font-size: 16px;
      font-weight: bolder;
      height: 20px;
      width: 20px;
      text-align: center;
      line-height: 20px;
      border-radius: 15px;
      background-color: var(--el-color-danger);
    }
  }
  .btn_box {
    bottom: 0;
    width: 100%;
    display: flex;
    justify-content: flex-end;

    // height: 55px;
  }
}
// .message-box:hover{
//   right: 50px;
// }
.active {
  width: 300px !important;
  height: 400px !important;
  border-radius: 10px;
}
.message-box.active .content_box {
  max-height: 400px;
  overflow-y: scroll;
}
.message-box.active .btn_box {
  background-color: var(--el-color-primary-light-9);
}
:deep(.btn_box .el-button) {
  height: 55px;
  width: 55px !important;
  animation: left-Right-shaking 0.5s linear infinite;
}
:deep(.btn_box .el-button i) {
  font-size: 25px;
  color: var(--el-color-danger);
}
:deep(.el-badge__content) {
  top: 4px !important;
  right: 24px !important;
}
/*左右摇摆关键帧动画*/
@keyframes left-Right-shaking {
  0% {
    transform: rotate(0deg);
  }
  1% {
    transform: rotate(1deg);
  }
  2% {
    transform: rotate(2deg);
  }
  3% {
    transform: rotate(3deg);
  }
  4% {
    transform: rotate(4deg);
  }
  5% {
    transform: rotate(5deg);
  }
  6% {
    transform: rotate(6deg);
  }
  7% {
    transform: rotate(7deg);
  }
  8% {
    transform: rotate(8deg);
  }
  9% {
    transform: rotate(9deg);
  }
  10% {
    transform: rotate(10deg);
  }
  11% {
    transform: rotate(11deg);
  }
  12% {
    transform: rotate(12deg);
  }
  13% {
    transform: rotate(13deg);
  }
  14% {
    transform: rotate(14deg);
  }
  15% {
    transform: rotate(15deg);
  }
  16% {
    transform: rotate(16deg);
  }
  17% {
    transform: rotate(17deg);
  }
  18% {
    transform: rotate(18deg);
  }
  19% {
    transform: rotate(19deg);
  }
  20% {
    transform: rotate(20deg);
  }
  21% {
    transform: rotate(21deg);
  }
  22% {
    transform: rotate(22deg);
  }
  23% {
    transform: rotate(23deg);
  }
  24% {
    transform: rotate(24deg);
  }
  25% {
    transform: rotate(25deg);
  }
  26% {
    transform: rotate(24deg);
  }
  27% {
    transform: rotate(23deg);
  }
  28% {
    transform: rotate(22deg);
  }
  29% {
    transform: rotate(21deg);
  }
  30% {
    transform: rotate(20deg);
  }
  31% {
    transform: rotate(19deg);
  }
  32% {
    transform: rotate(18deg);
  }
  33% {
    transform: rotate(17deg);
  }
  34% {
    transform: rotate(16deg);
  }
  35% {
    transform: rotate(15deg);
  }
  36% {
    transform: rotate(14deg);
  }
  37% {
    transform: rotate(13deg);
  }
  38% {
    transform: rotate(12deg);
  }
  39% {
    transform: rotate(11deg);
  }
  40% {
    transform: rotate(10deg);
  }
  41% {
    transform: rotate(9deg);
  }
  42% {
    transform: rotate(8deg);
  }
  43% {
    transform: rotate(7deg);
  }
  44% {
    transform: rotate(6deg);
  }
  45% {
    transform: rotate(5deg);
  }
  46% {
    transform: rotate(4deg);
  }
  47% {
    transform: rotate(3deg);
  }
  48% {
    transform: rotate(2deg);
  }
  49% {
    transform: rotate(1deg);
  }
  50% {
    transform: rotate(0deg);
  }
  51% {
    transform: rotate(-1deg);
  }
  52% {
    transform: rotate(-2deg);
  }
  53% {
    transform: rotate(-3deg);
  }
  54% {
    transform: rotate(-4deg);
  }
  55% {
    transform: rotate(-5deg);
  }
  56% {
    transform: rotate(-6deg);
  }
  57% {
    transform: rotate(-7deg);
  }
  58% {
    transform: rotate(-8deg);
  }
  59% {
    transform: rotate(-9deg);
  }
  60% {
    transform: rotate(-10deg);
  }
  61% {
    transform: rotate(-11deg);
  }
  62% {
    transform: rotate(-12deg);
  }
  63% {
    transform: rotate(-13deg);
  }
  64% {
    transform: rotate(-14deg);
  }
  65% {
    transform: rotate(-15deg);
  }
  66% {
    transform: rotate(-16deg);
  }
  67% {
    transform: rotate(-17deg);
  }
  68% {
    transform: rotate(-18deg);
  }
  69% {
    transform: rotate(-19deg);
  }
  70% {
    transform: rotate(-20deg);
  }
  71% {
    transform: rotate(-21deg);
  }
  72% {
    transform: rotate(-22deg);
  }
  73% {
    transform: rotate(-23deg);
  }
  74% {
    transform: rotate(-24deg);
  }
  75% {
    transform: rotate(-25deg);
  }
  76% {
    transform: rotate(-24deg);
  }
  77% {
    transform: rotate(-23deg);
  }
  78% {
    transform: rotate(-22deg);
  }
  79% {
    transform: rotate(-21deg);
  }
  80% {
    transform: rotate(-20deg);
  }
  81% {
    transform: rotate(-19deg);
  }
  82% {
    transform: rotate(-18deg);
  }
  83% {
    transform: rotate(-17deg);
  }
  84% {
    transform: rotate(-16deg);
  }
  85% {
    transform: rotate(-15deg);
  }
  86% {
    transform: rotate(-14deg);
  }
  87% {
    transform: rotate(-13deg);
  }
  88% {
    transform: rotate(-12deg);
  }
  89% {
    transform: rotate(-11deg);
  }
  90% {
    transform: rotate(-10deg);
  }
  91% {
    transform: rotate(-9deg);
  }
  92% {
    transform: rotate(-8deg);
  }
  93% {
    transform: rotate(-7deg);
  }
  94% {
    transform: rotate(-6deg);
  }
  95% {
    transform: rotate(-5deg);
  }
  96% {
    transform: rotate(-4deg);
  }
  97% {
    transform: rotate(-3deg);
  }
  98% {
    transform: rotate(-2deg);
  }
  99% {
    transform: rotate(-1deg);
  }
  100% {
    transform: rotate(0deg);
  }
}
</style>
