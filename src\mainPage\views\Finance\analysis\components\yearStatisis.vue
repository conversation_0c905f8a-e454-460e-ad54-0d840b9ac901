<template>
  <div style="margin-bottom: 10px">
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>按月利润分析</div>
          <div style="display: flex; gap: 20px">
            <el-date-picker
              v-model="query.year"
              value-format="YYYY"
              @change="getDetail"
              type="year"
              :clearable="false"
              placeholder=""
            ></el-date-picker>
            <!-- <el-radio-group @change="getDetail" v-model="query.type1">
                <el-radio-button label="0">月</el-radio-button>
                <el-radio-button label="1">季度</el-radio-button>
                <el-radio-button label="2">年</el-radio-button>
              </el-radio-group> -->
            <wfUserSelectDrop
              v-model="query.userId"
              @change="getDetail"
              v-if="form.selectType != 0"
              style="width: 100px"
            ></wfUserSelectDrop>
          </div>
        </div>
      </template>
      <!-- 图表 -->
      <div style="height: 400px">
        <div ref="chartRef" style="height: 100%"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { inject } from 'vue';
import wfUserSelectDrop from '@/views/desk/components/userSelect.vue';
import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';
import moment from 'moment';
const chartRef = ref(null);
const radio2 = ref('月');
onMounted(() => {
  getDetail();
});
const form = inject('form');
watch(form.value, getDetail);
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999',
        },
      },
    },

    legend: {
      show: true,
    },
    xAxis: [
      {
        type: 'category',
        data: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月',
        ],
        axisPointer: {
          type: 'shadow',
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        name: '金额',

        interval: 50,
        axisLabel: {
          formatter: '{value}',
        },
      },
      {
        type: 'value',
        name: '初利润',
        max: 1,
        interval: 5,
      },
    ],
    series: [
      {
        name: '采购成本',
        type: 'bar',
        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        data: [2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3],
      },
      {
        name: '人工成本',
        type: 'bar',
        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        data: [2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3],
      },
      {
        name: '税金',
        type: 'bar',

        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        data: [2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2],
      },
      {
        name: '初利润',
        type: 'bar',

        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        data: [0.26, 0.2, 0.3, 0.5, 0.3, 0.2, 0.3, 0.4, 0.2, 0.5, 0.2, 0.2],
      },
      {
        name: '初利润率',
        type: 'line',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        data: [0.26, 0.2, 0.3, 0.5, 0.3, 0.2, 0.3, 0.4, 0.2, 0.5, 0.2, 0.2],
      },
    ],
  };
  chart.setOption(option);
}
let query = ref({
  year: moment(new Date()).format('YYYY'),
});
let detailForm = ref({ y: [] });
function getDetail() {
  axios
    .get('/api/vt-admin/statistics/followNumberStatistics', {
      params: {
        ...query.value,
        ...form.value,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      initChart();
    });
}
</script>

<style lang="scss" scoped>
.el-card {
  height: 500px;
}
</style>
