<template>
  <div>
    <el-form>
      <el-form-item label="">
        <el-button icon="plus" type="primary" @click="invoiceApply">开票申请</el-button>
      </el-form-item>
    </el-form>
    <el-table
      class="avue-crud"
      :data="productList"
      border
      align="center"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        type="selection"
        width="55"
        :selectable="(row, index) => row.isInvoice === 0"
      />
      <el-table-column
        label="设备名称"
        show-overflow-tooltip
        prop="customProductName"
      ></el-table-column>
      <el-table-column
        label="规格型号"
        show-overflow-tooltip
        prop="customProductSpecification"
      ></el-table-column>
      <el-table-column label="产品图片" #default="{ row }">
        <el-image
          style="width: 80px"
          :preview-src-list="[row.product.coverUrl]"
          :src="row.product.coverUrl"
        ></el-image>
      </el-table-column>
      <el-table-column
        label="产品描述"
        show-overflow-tooltip
        width="200"
        prop="customProductDescription"
      ></el-table-column>
      <el-table-column label="品牌" prop="product.productBrand"></el-table-column>
      <el-table-column label="单位" prop="product.unitName"></el-table-column>
      <el-table-column label="数量" #default="{ row }" prop="number"> </el-table-column>
      <el-table-column label="单价" #default="{ row }" prop="sealPrice">
        <span>{{ row.sealPrice }}</span>
      </el-table-column>
      <el-table-column label="金额" #default="{ row }" prop="sealPrice">
        <span>{{ (row.sealPrice * row.number).toLocaleString() }}</span>
      </el-table-column>
      <el-table-column label="是否开票">
        <template #default="{ row }">
          <el-tag effect='plain' v-if="row.isInvoice === 1" type="success">是</el-tag>
          <el-tag effect='plain' v-else-if="row.isInvoice === 2" type="success">部分开票</el-tag>
          <el-tag effect='plain' v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
  <el-dialog title="开票申请" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <avue-form
      v-if="dialogVisible"
      ref="dialogForm"
      :option="addOption"
      @submit="submit"
      v-model="addForm"
    >
      <template #customerInvoiceInfoId>
        <wfInvoiceDrop
          v-model="addForm.customerInvoiceInfoId"
          :id="props.customerId"
        ></wfInvoiceDrop>
        <productSelect
          ref="productSelectRef"
          @select="handleConfirm"
          :sealContractId="props.sealContractId"
          :offerId="props.offerId"
        ></productSelect>
      </template>
    </avue-form>
    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { computed, onMounted, getCurrentInstance } from 'vue';
import { dateFormat } from '@/utils/date';
import wfInvoiceDrop from '../compoents/wf-invoice-drop.vue';
import productSelect from '../compoents/productSelect.vue';
const props = defineProps({
  sealContractId: String,
  sealContractInvoiceId: String,
  offerId: String,
  customerId: String,
});
let { proxy } = getCurrentInstance();
onMounted(() => {
  getProductList();
});
watchEffect(() => {
  console.log(props.sealContractId);
  if (props.saleContractId) {
    getProductList();
  }
});

let productList = ref([]);
function getProductList(params) {
  axios
    .get('/api/vt-admin/sealContract/productPage', {
      params: {
        current: 1,
        size: 500,
        offerId: props.offerId,
      },
    })
    .then(res => {
      productList.value = res.data.data.records;
    });
}
let selectList = ref([]);
function handleSelectionChange(list) {
  selectList.value = list;
}
function invoiceApply() {
  if (selectList.value.length === 0) return proxy.$message.warning('请选择开票产品');
  addForm.value.detailDTOList = selectList.value.map(item => {
    return {
      detailId: item.id,
      ...item,
      id: null,
    };
  });
  //  totalAmount(form.value.taxRate);
  addForm.value.invoicePrice = totalAmount();
 
  dialogVisible.value = true;
}
let dialogVisible = ref(false);
let addForm = ref({});
let addOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  emptyBtn: false,
  submitBtn: false,
  viewBtn: false,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  addTitle: '开票申请',
  addBtnText: '开票申请',
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'input',
      label: '开票信息',
      span: 24,
      display: true,
      hide: true,
      value: props.sealContractInvoiceId,
      prop: 'customerInvoiceInfoId',
    },
    {
      type: 'date',
      label: '开票日期',
      span: 12,

      display: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      span: 12,
      display: true,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      width: 110,
      // search: true,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      cell: false,
      prop: 'taxRate',
      // change:({value}) => {
      //   totalAmount(value)
      // },
      dicUrl: '/blade-system/dict/dictionary?code=tax',
    },
    {
      label: '开票公司',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      cell: false,
      prop: 'billingCompany',
      dicUrl: '/blade-system/dict-biz/dictionary?code=billingCompany',
    },
    {
      label: '申请人',
      type: 'input',
      value: proxy.$store.getters.userInfo.nick_name,
      prop: 'createName',
      readonly: true,
    },
    {
      type: 'date',
      label: '申请时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
    },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          proxy.$refs.productSelectRef.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品',
            prop: 'productId',
            bind: 'product.productName',
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            bind: 'product.productSpecification',
            overHidden: true,
            // search: true,
            span: 24,
            cell: false,
            type: 'input',
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            span: 12,
            cell: false,
          },
          {
            label: '单价',
            prop: 'sealPrice',
            type: 'number',
            span: 12,
            cell: false,
          },
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            cell: false,
          },
        ],
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
function submit(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
    createUser: null,
    createTime: null,
  };
  axios
    .post('/api/vt-admin/sealContractInvoice/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getProductList();
        done();
        dialogVisible.value = false;
      }
    })
    .catch(err => {
      done();
    });
}
function totalAmount(val) {
  // form.value.invoicePrice =
  //   form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0) *
  //   (1 - (val * 1) / 100);
  return addForm.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0);
}
</script>

<style lang="scss" scoped></style>
