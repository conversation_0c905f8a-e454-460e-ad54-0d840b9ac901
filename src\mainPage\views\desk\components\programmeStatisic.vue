<template>
  <div style="margin-bottom: 10px">
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>方案数量统计</div>
          <div style="display: flex; gap: 20px">
            <el-date-picker
              v-model="query.year"
              :clearable="false"
              value-format="YYYY"
              @change="getDetail"
              type="year"
              placeholder=""
            ></el-date-picker>
            <!-- <el-radio-group @change="getDetail" v-model="query.type1">
                    <el-radio-button label="0"  >月</el-radio-button>
                    <el-radio-button label="1" >季度</el-radio-button>
                    <el-radio-button label="2"  >年</el-radio-button>
                  </el-radio-group> -->
            <!-- <el-select v-model="query.onTimeComplete" @change="getDetail" >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="未按时完成" value="0"></el-option>
                    <el-option label="按时完成" value="1"></el-option>
                  </el-select>
                  <el-select v-model="query.status" @change="getDetail" >
                    <el-option label="全部" value=""></el-option>
                    <el-option label="未成交" value="0"></el-option>
                    <el-option label="成交" value="1"></el-option>
                  </el-select> -->
            <wfUserSelectDrop
              v-model="query.userId"
              @change="getDetail"
              v-if="form.selectType != 0"
              style="width: 100px"
            ></wfUserSelectDrop>
          </div>
        </div>
      </template>
      <!-- 图表 -->
      <div style="height: 400px">
        <div ref="chartRef" style="height: 100%"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import wfUserSelectDrop from './userSelect.vue';
import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';
import moment from 'moment';
const chartRef = ref(null);
const radio2 = ref('月');
onMounted(() => {
  getDetail();
});
const form = inject('form');
let query = ref({
  year: moment(new Date()).format('YYYY'),
  type1: 0,
  // onTimeComplete:'',
  // status:''
});
watch(form.value, () => {
  query.value.userId = form.value.userId;
  query.value.year = form.value.year;
  getDetail();
});
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],

    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: true,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: detailForm.value.x, //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '方案数量', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: [
      {
        name: '方案数', //名称
        type: 'bar', //类型
        barWidth: 13, //宽度
        data: detailForm.value.y, //数值
        z: 1,
        label: {
          show: true,
          position: 'top',
         
        }, barGap: 0,
        // 添加 markLine 配置（平均值）
      },
      {
        name: '按时完成数', //名称
        type: 'bar', //类型
        barWidth: 13, //宽度
        data: compeleteForm.value.y, //数值
        z: 1,
        label: {
          show: true,
          position: 'top',
          
        },barGap: 0,
        // 添加 markLine 配置（平均值）
      },
      {
        name: '成交数', //名称
        type: 'bar', //类型
        barWidth: 13, //宽度
        data: cjForm.value.y, //数值
        z: 1,
        label: {
          show: true,
          position: 'top',
        
        },  barGap: 0,
        // 添加 markLine 配置（平均值）
      },
    ],
  };
  chart.setOption(option);
}

let detailForm = ref({ y: [] });

function getDetail() {
  getAll();
  getCompelete();
  getCj();
}
function getAll() {
  axios
    .get('/api/vt-admin/statistics/optionNumberStatistics', {
      params: {
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      initChart();
    });
}
let compeleteForm = ref(null);
function getCompelete(params) {
  axios
    .get('/api/vt-admin/statistics/optionNumberStatistics', {
      params: {
        ...form.value,
        ...query.value,
        onTimeComplete: 1,
      },
    })
    .then(res => {
      compeleteForm.value = res.data.data;
      initChart();
    });
}
let cjForm = ref(null);
function getCj(params) {
  axios
    .get('/api/vt-admin/statistics/optionNumberStatistics', {
      params: {
        status: 1,
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      cjForm.value = res.data.data;
      initChart();
    });
}
</script>

<style lang="scss" scoped>
.el-card {
  height: 500px;
}
</style>
