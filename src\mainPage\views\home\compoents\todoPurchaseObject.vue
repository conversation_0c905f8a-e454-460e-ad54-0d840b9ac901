<template>
  <div style="margin: 0 5px; height: 100%">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
     
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button type="primary" text @click="topage" icon="more"> 更多</el-button>
      </template>
    </avue-crud>
  </div>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  header: false,
  search: false,
  editBtn: false,
  delBtn: false,
  size:'small',
  calcHeight: 30,
  searchMenuSpan: 4,
  menu: true,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  column: [
    {
      type: 'input',
      label: '合同名称',
      span: 24,
      display: true,

      overHidden: true,
      addDisplay: false,

      editDisplay: false,
      prop: 'contractName',
    },
    {
      type: 'input',
      label: '标的名称',
      span: 24,
      display: true,

      overHidden: true,
      prop: 'objectName',
    },
    {
      type: 'input',
      label: '任务描述',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'durationNode',
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'remark',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 5,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  path: String,
});
let router = useRouter();
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObject/page?selectType=3';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
function topage(params) {
  router.push(props.path);
}
</script>

<style lang="scss" scoped></style>
