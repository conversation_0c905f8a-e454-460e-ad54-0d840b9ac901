<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
      @expand-change="expandChanges"
      :row-class-name="getClass"
    >
      <template #menu-left="{}">
        <div style="display: flex">
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">总设计额：</span>
            <el-text type="primary" size="large"
              >￥{{ (totalPrice * 1).toLocaleString() || 0 }}</el-text
            >
          </div>
        </div>
      </template>
      <template #stage="{ row, a, b, c }">
        <el-tag effect="plain">{{ (a, b, c) }}{{ row.$stage }}</el-tag>
      </template>
      <template #menu="{ row, index }">
        <!-- <el-button
          type="primary"
          text
          @click="handleEdit(row)"
          v-if="row.optionStatus == 1 && $store.getters.userInfo.user_id == row.technicalPersonnel"
          icon="Edit"
          >编辑方案</el-button
        > -->
        <!-- <el-button type="primary" text @click="handleAdd(row)" v-else icon="plus"
          >新增方案</el-button
        > -->
        <!-- <el-button type="primary" text @click="submit(row)" icon="check" v-if="row.optionStatus != 2 && $store.getters.userInfo.user_id == row.technicalPersonnel">提交</el-button> -->
        <el-button type="primary" text @click="handleView(row)" icon="View">详情</el-button>
        <el-button type="primary" text @click="handleHistory(row)" icon="Clock">历史</el-button>
        <el-button
          text
          :type="row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'"
          icon="el-icon-download"
          @click="download(row)"
          v-if="row.optionId"
          >下载</el-button
        >
      </template>
      <template #auditStatus="{ row }">
        <span v-if="!row.auditStatus">---</span>
        <el-tag effect="plain" v-if="row.auditStatus == 1" size="small" type="info"
          >待主管审核</el-tag
        >
        <el-tag effect="plain" v-if="row.auditStatus == 2" size="small" type="success"
          >审核成功</el-tag
        >
        <el-tooltip
          class="box-item"
          effect="dark"
          :content="row.auditReason"
          placement="top-start"
          v-if="row.auditStatus == 3"
        >
          <el-tag effect="plain" size="small" type="danger">审核失败</el-tag>
        </el-tooltip>
      </template>
      <template #expand="{ row }">
        <div style="padding: 0 10px">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <el-col :span="4" v-for="item in row.businessOpportunityOptionVOS || []">
              <el-card
                class="maCard"
                :shadow="item.optionVersionType == 0 ? 'never' : 'always'"
                style="height: 200px; overflow-y: auto; border: 1px solid var(--el-color-primary)"
              >
                <template #header>
                  <el-row>
                    <el-col
                      :span="24"
                      style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                    >
                      <el-tooltip :content="item.optionName">
                        <el-text
                          type="primary"
                          size="default"
                          style="
                            font-weight: bolder;
                            font-family: Inter, Helvetica Neue, Helvetica, PingFang SC,
                              Hiragino Sans GB, Microsoft YaHei, \5fae\8f6f\96c5\9ed1, Arial,
                              sans-serif;
                          "
                          >{{ item.optionName }}</el-text
                        >
                      </el-tooltip>
                    </el-col>

                    <el-col :span="24">
                      <div
                        v-if="item.optionVersionType == 1"
                        style="display: flex; justify-content: flex-end"
                      >
                        <!-- <el-button
                          type="info"
                          @click="handleEditSub(item)"
                          circle
                          icon="edit"
                          text
                        ></el-button> -->
                        <el-button
                          type="info"
                          @click="handleViewSub(item)"
                          circle
                          icon="view"
                          text
                        ></el-button>
                        <el-button
                          text
                          icon="el-icon-download"
                          v-if="row.optionId"
                          type="info"
                          @click="download(item)"
                        ></el-button>
                      </div>
                      <div v-else style="display: flex; justify-content: flex-end">
                        <el-button
                          type="primary"
                          text
                          @click="handleView(row)"
                          icon="View"
                        ></el-button>
                        <el-button
                          type="primary"
                          text
                          @click="handleHistory(row)"
                          icon="Clock"
                        ></el-button>
                        <el-button
                          text
                          :type="
                            row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'
                          "
                          icon="el-icon-download"
                          @click="download(row)"
                          v-if="row.optionId"
                        ></el-button>
                      </div>
                    </el-col>
                  </el-row>
                </template>
                <el-row style="margin-top: 10px; margin-left: 5px">
                  <el-col :span="16">
                    <el-text
                      style="font-size: 30px"
                      :type="item.optionStatus == 0 ? 'info' : 'success'"
                      >￥{{ item.sealTotalPrice }}</el-text
                    >
                  </el-col>
                  <el-col :span="8">
                    <el-tag effect="plain" :type="item.optionStatus == 0 ? 'info' : 'success'">
                      {{
                        [
                          {
                            value: 0,
                            label: '草稿',
                          },
                          // {
                          //   value: 1,
                          //   label: '--',
                          // },
                          {
                            value: 2,
                            label: '已做',
                          },
                        ].find(i => item.optionStatus == i.value).label
                      }}
                    </el-tag>
                  </el-col>
                </el-row>
                <el-row style="margin-top: 10px; margin-left: 6px">
                  <el-descriptions size="default" column="1">
                    <el-descriptions-item label="备注:">{{ item.remark }}</el-descriptions-item>
                    <el-descriptions-item label="撤回原因:">{{
                      item.auditReason
                    }}</el-descriptions-item>
                  </el-descriptions>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="历史记录" v-model="dialogVisible">
      <History ref="history" :id="currentId"></History>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { programmeStatus, businessOpportunityData, auditStatus } from '@/const/const.js';
import History from './compoents/history.vue';
import moment from 'moment';
import { download as downloadOffer } from '@/utils/download.js';
let store = useStore();
let permission = computed(() => store.getters.permission);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  viewBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  index: true,
  searchMenuSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  searchSpan: 4,
  searchLabelWidth: 110,
  menuWidth: 220,
  menu: true,
  border: true,
  expand: true,
  rowKey: 'id',
  column: [
    {
      label: '商机名称',
      prop: 'name',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '商机分类',
      type: 'radio',
      prop: 'classify',
      span: 24,

      dicData: [
        {
          value: 0,

          label: '产品',
        },
        {
          value: 1,
          label: '项目',
        },
      ],
    },
    {
      type: 'select',
      label: '业务板块',
      span: 12,
      search: true,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择商机关联',
        },
      ],
      display: true,
      prop: 'type',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机描述',
      prop: 'description',
      overHidden: true,
    },

    {
      type: 'select',
      label: '商机来源',
      // search: true,
      span: 12,
      rules: [
        {
          required: true,
          message: '请选择商机来源',
        },
      ],
      display: true,
      prop: 'source',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityResource',
      props: {
        label: 'dictValue',
        value: 'id',
      },
    },
    {
      label: '商机阶段',
      prop: 'stage',
      slot: true,
      type: 'select',
      search: true,
      dicData: businessOpportunityData,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
    },
    {
      label: '关联联系人',
      prop: 'contactPersonName',
    },

    {
      prop: 'businessPersonName',
      label: '业务员',
      component: 'wf-user-drop',
      search: true,
    },
    {
      label: '总金额',
      width: 130,
      prop: 'sealTotalPrice',
      overHidden: true,
    },
    {
      label: '要求完成时间',
      prop: 'optionRequireTime',
      html: true,
      overHidden: true,
      width: 110,
      formatter: row => {
        if (!row.optionRequireTime) return '--';
        console.log(moment(new Date()).format('YYYY-MM-DD'));
        const day = moment(moment(row.optionRequireTime).format('YYYY-MM-DD')).diff(
          moment(new Date()).format('YYYY-MM-DD'),
          'days'
        );
        console.log(day);
        return `<div style='color:${
          day <= 1 && row.auditStatus != 2 ? 'var(--el-color-danger)' : ''
        }'>${moment(row.optionRequireTime).format('YYYY-MM-DD')}</div>`;
      },
    },

    {
      label: '预计签单日期',
      prop: 'preSignDate',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      type: 'date',
      width: 110,
      rules: [
        {
          required: true,
          message: '请选择预计签单日期',
        },
      ],
    },
    {
      label: '方案状态',
      type: 'select',
      dicData: programmeStatus,
      prop: 'optionStatus',
      search: true,
    },
    {
      label: '审核状态',
      type: 'select',
      width: 100,
      dicData: auditStatus,
      prop: 'auditStatus',
      search: true,
    },

    {
      label: '技术员',
      prop: 'technicalPersonnelName',
      component: 'wf-user-drop',
      search: true,
    },
    {
      label: '审核时间',
      width: 135,
      hide: true,
      prop: 'auditTime',
      type: 'dateTime',
      search: true,
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      type: 'datetime',
      format: 'YYYY-MM-DD',

      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    
    {
      label: '预计销售金额',
      prop: 'preSealPrice',
      type: 'number',
      width: 110,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const tableUrl = '/api/vt-admin/businessOpportunity/needOptionPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
        optionStartTime: params.value.auditTime ? params.value.auditTime[0] : null,
        optionEndTime: params.value.auditTime ? params.value.auditTime[1] : null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  axios
    .get('/api/vt-admin/businessOpportunity/needOptionStatistics', {
      params: {
        ...params.value,
        selectType: 2,
        optionStartTime: params.value.auditTime ? params.value.auditTime[0] : null,
        optionEndTime: params.value.auditTime ? params.value.auditTime[1] : null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.sealTotalPrice;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}

function handleView(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        type: 'detail',
      },
    });
  } else if (row.optionId && !row.isHasDataJson) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        type: 'detail',
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
function handleViewSub(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else if (row.isHasDataJson == 0 || !row.isHasDataJson) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.id,
        name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
function handleEdit(row) {
  router.push({
    path: '/CRM/programme/compoents/update',
    query: {
      id: row.id,
      type: 'edit',
    },
  });
}
function handleAdd(row) {
  router.push({
    path: '/CRM/programme/compoents/update',
    query: {
      id: row.id,
      type: 'add',
    },
  });
}
let currentId = ref(0);
let dialogVisible = ref(false);
function handleHistory(row) {
  currentId.value = row.optionId;
  dialogVisible.value = true;
}
function submit(row) {
  proxy
    .$confirm('提交之后将会不可修改，你可以在历史记录查看?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/businessOpportunityOption/confirm', {
          id: row.optionId,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
        });
    });
}
function download(row) {
  downloadOffer('/api/vt-admin/businessOpportunityOption/downloadOption', {
    id: row.optionId || row.id,
  });
}
function expandChanges(row, expendList) {
  if (expendList.length) {
    option.value.expandRowKeys = [];
    if (row) {
      option.value.expandRowKeys.push(row.id);
    }
  } else {
    option.value.expandRowKeys = [];
  }
}
function getClass({ row }) {
  return row.isHasManyOption == 0 ? 'hide_icon' : '';
}
</script>

<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 5px;
}
:deep(.el-col) {
  margin-bottom: 0px;
}
:deep(.hide_icon td:first-child .cell) {
  visibility: hidden;
}
</style>
