<template>
    <div style="height: 100%">
      <el-row style="height: 100%">
        <el-col :span="6" style="height: 100%">
          <basic-container style="height: 100%">
            <el-input placeholder="输入关键字进行过滤" v-model="filterText" @input="getTreeData">
            </el-input>
            <avue-tree :option="treeOption" style="max-height: 660px;overflow-y: auto;"  :data="treeData" @node-click="nodeClick">
              <template #default="{ node, data }">
                <span class="custom-tree-node">
                  <span>{{ node.label }}</span>
                </span>
              </template>
            </avue-tree>
          </basic-container>
        </el-col>
        <el-col :span="18" style="height: 100%">
          <basic-container style="height: 100%;">
            <Title>{{ form.categoryName }}</Title>
            <avue-form
              v-if="!form.hasChildren"
              style="margin-top: 20px;"
              :option="option"
              v-model="form"
              @submit="submit"
            >
              <!-- 每天工价 -->
              <template #labourPrice>
                <el-input v-model="form.labourPrice" placeholder="请输入每天工价" clearable>
                  <template #append>/天</template>
                </el-input>
              </template>
              <!-- 每小时工价 -->
              <template #labourPriceHour>
                <el-input v-model="form.labourPriceHour" placeholder="请输入每小时工价" clearable>
                  <template #append>/时</template>
                </el-input>
              </template>
              <!-- 权限设置 -->
              <template #functionIds>
                <div class="permission-tree">
                  <div v-for="parentItem in permissionOptions" :key="parentItem.dictKey" class="permission-parent">
                    <div class="parent-label">{{ parentItem.dictValue }}</div>
                    <el-checkbox-group v-model="selectedPermissions" @change="handlePermissionChange" class="children-group">
                      <div class="permission-children-row">
                        <div v-for="childItem in parentItem.children" :key="childItem.id" class="permission-child">
                          <el-checkbox :label="childItem.id">{{ childItem.dictValue }}</el-checkbox>
                        </div>
                      </div>
                    </el-checkbox-group>
                  </div>
                </div>
              </template>
            </avue-form>
            <el-empty v-else description="请先选中人员"></el-empty>
          </basic-container>
        </el-col>
      </el-row>
  

     
    </div>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  let option = ref({
    labelWidth: 150,
    column: [
      {
        label: '工价/天',
        prop: 'labourPrice',
        type: 'number',
      },
      {
        label: '工价/时',
        prop: 'labourPriceHour',
        type: 'number',
      },
      {
        label: '功能人员配置',
        labelTip:'被选择的权限',
        prop: 'functionIds',
        type: 'checkbox',
        span: 24,
      },
    ],
  });
  let form = ref({
    hasChildren: true,
  });

  // 权限选项数据
  let permissionOptions = ref([]);
  // 选中的权限
  let selectedPermissions = ref([]);
  let treeOption = ref({
    //   defaultExpandAll: true,
    menu: false,
    filter: false,
    addBtn: false,
    props: {
      labelText: '标题',
      label: 'name',
      value: 'id',
      children: 'children',
    },
  });
  let { proxy } = getCurrentInstance();
  onMounted(() => {
    getTreeData();
    loadPermissionOptions();
  });

  // 加载权限选项
  function loadPermissionOptions() {
    axios.get('/blade-system/dict/dictionary-tree?code=allowSelect').then(res => {
      permissionOptions.value = res.data.data || [];
    }).catch(err => {
      console.error('加载权限选项失败:', err);
    });
  }

  // 权限变化处理
  function handlePermissionChange(value) {
    form.value.functionIds = value.join(',');
  }
  
  let treeData = ref([]);
  function getTreeData(value) {
    axios
      .get('/api/blade-system/search/user', {
        params: {
          categoryName: value,
          size:5000
        },
      })
      .then(res => {
        treeData.value = res.data.data.records.map(item => {
          return {
            ...item,
            leaf: !item.hasChildren,
          };
        });
      });
  }
  let filterText = ref('');
  
  function nodeClick(val) {
    // 加载工价和权限数据
    axios.get('/api/vt-admin/userConfig/detail?userId=' + val.id).then(res => {
      if(!res.data.data){
        form.value = {
          labourPrice: 0,
          labourPriceHour: 0,
          userId: val.id,
          categoryName: val.name,
          functionIds: ''
        }
        selectedPermissions.value = [];
      } else {
        form.value = {
          ...res.data.data,
          userId: res.data.data.userId,
          categoryName: val.name
        };
        // 回显权限数据
        if (res.data.data.functionIds) {
          selectedPermissions.value = res.data.data.functionIds.split(',').filter(id => id);
        } else {
          selectedPermissions.value = [];
        }
      }
    });
  }
  function submit(form, done) {
    const data = {
      ...form,
      userId: form.userId,
      id: null
    };
    axios
      .post('/api/vt-admin/userConfig/saveOrUpdate', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          done();
        }
      })
      .catch(err => {
        done();
      });
  }

  </script>
  
  <style lang="scss" scoped>
  .permission-tree {
    .permission-parent {
      margin-bottom: 12px;

      .parent-label {
        font-weight: 600;
        font-size: 15px;
        color: #303133;
        margin-bottom: 6px;
        padding: 4px 0;
        border-bottom: 1px solid #ebeef5;
      }

      .children-group {
        padding-left: 16px;

        .permission-children-row {
          display: flex;
          flex-wrap: wrap;
          gap: 16px;

          .permission-child {
            .el-checkbox {
              margin-right: 0;

              .el-checkbox__label {
                font-size: 14px;
                color: #606266;
              }
            }
          }
        }
      }
    }
  }
  </style>
  