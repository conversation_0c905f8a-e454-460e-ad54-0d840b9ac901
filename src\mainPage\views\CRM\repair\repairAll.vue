<template>
  <basic-container block :shadow="customerId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button
          type="primary"
          @click="$refs.crud.rowEdit(row, index)"
          text
          icon="edit"
          v-if="row.repairStatus == 0"
          >编辑</el-button
        >

        <!-- <el-button
          type="primary"
          v-if="row.repairStatus == 0"
          text
          icon="back"
          @click="confirmReturn(row)"
          >确认送修</el-button
        >
        <el-button
          type="primary"
          v-if="row.repairStatus == 1"
          @click="openReturn(row)"
          text
          icon="back"
          >返还客户</el-button
        > -->
        <el-button
          v-if="row.repairStatus == 0"
          type="primary"
          @click="$refs.crud.rowDel(row, index)"
          text
          icon="delete"
          >删除</el-button
        >
      </template>
      <template #repairCode="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.repairCode }}</el-link>
      </template>
      <template #repairStatus="{ row }">
        <el-tag
          :type="row.repairStatus == 0 ? 'info' : row.repairStatus == 1 ? 'warning' : 'success'"
          effect="plain"
          >{{ row.$repairStatus }}</el-tag
        >
        <el-tag
           style="margin-left: 5px;"
           v-if="row.repairStatus == 2"
            :type="row.repairCondition == 0 ? 'success' : row.repairCondition == 1 ? 'warning' : 'danger'"
            effect="plain"
            >{{ ['已修好','换良品','无法维修','未修好'][row.repairCondition] }}</el-tag
          >
      </template>
      <template #offerStatus="{ row }">
        <span effect="plain" type="info" v-if="row.isOffer == 0">未有报价</span>
        <span>{{ row.$offerStatus }}</span>
      </template>
      <template #fileList-form>
        <File :fileList="form.repairFileList"></File>
      </template>
      <template #completeFile-form="{ type }">
        <File :fileList="form.completeFileList"></File>
      </template>
    
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <productSelect
      :customerId="props.customerId || form.customerId"
      @confirm="handleConfirm"
      ref="productSelectRef"
    ></productSelect>
    <el-dialog title="返还客户" v-model="returnDialog" width="80%">
      <avue-form
        :option="returnOption"
        ref="returnRef"
        @submit="handleReturnConfirm"
        v-model="returnForm"
      ></avue-form>
      <div class="avue-dialog__footer">
        <el-button @click="returnDialog = false">取 消</el-button>
        <el-button @click="$refs.returnRef.submit()" type="primary">提交</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productSelect from '@/views/CRM/component/customerProductSelect.vue';
import { ElMessage } from 'element-plus';
import { dateFormat } from '@/utils/date';
import { offerStatus, auditStatus } from '@/const/const.js';
let productSelectRef = ref();
const props = defineProps(['offerId', 'customerId']);
let { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  menu:false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  labelWidth: 120,
  border: true,
  dialogClickModal: true,
  dialogType: 'drawer',
  dialogWidth: '80%',
  column: [
    {
      label: '维修单编号',
      prop: 'repairCode',
      overHidden: true,

      disabled: true,
      width: 180,
      search: true,
      searchLabelWidth: 120,
    },
    {
      type: 'input',
      label: '关联客户',
      display: true,
      rules: [
        {
          required: true,
          message: '请选择关联订单',
        },
      ],
      search: !props.customerId,
      display: !props.customerId,
      hide: !!props.customerId,
      component: 'wf-customer-select',
      overHidden: true,
      prop: 'customerId',
      params: {
        Url: '/api/vt-admin/customer/page?type=0',
        checkType: 'radio',
      },
      formatter: (row, value) => {
        return row.customerName;
      },
      change: val => {
        handleChange(val.value);
      },
    },

    {
      label: '关联产品',
      prop: 'repairProductDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        rowAdd: done => {
          productSelectRef.value.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },
        size: 'small',
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true, width: 200,
         
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true, width: 200,
          
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
          
            cell: false,
            width: 100,
          },
          {
            label: '数量',
            prop: 'number',
            width: 100,
            type: 'number',
            span: 12,
            cell: true,
          },
          // {
          //   label: '单价',
          //   prop: 'sealPrice',
          //   type: 'number',
          //   span: 12,
          //   width: 100,
          //   cell: false,
          // },
          // {
          //   label: '金额',
          //   prop: 'totalPrice',
          //   type: 'number',
          //   span: 12,
          //   width: 100,
          //   cell: false,
          //   formatter: row => {
          //     return row.number * row.sealPrice;
          //   },
          // },
          {
            label: '是否在保修期',
            type: 'switch',
            prop: 'isWarrantyPeriod',
            cell: true,
            width: 100,
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
          },
          {
            type: 'select',
            label: '维修方式',
            dicUrl: '/blade-system/dict/dictionary?code=repairType',
            cascader: [],
            span: 12,
            width: 120,
            // search: true,

            cell: true,
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            editDisplay: false,
            addDisplay: false,
            prop: 'repairType',
          },
          {
            label: '序列号',
            prop: 'serialNumber',
            span: 12,
            cell: true,

            width: 200,
            type: 'textarea',
          },

          {
            label: '产品故障描述',
            prop: 'remark',
            span: 12,
            cell: true,
            width: 200,
            type: 'textarea',
          },
          {
            label: '预计维修金额',
            type: 'number',
            prop: 'preRepairPrice',
            width: 150,
            cell: true,
            change: val => {
              form.value.preRepairPrice = totalAmount();
            },
          },
          {
            label: '实际维修金额',
            type: 'number',
            prop: 'actualRepairPrice',
            width: 150,
            cell: true,
            editDisplay: false,
            addDisplay: false,
          },
        ],
      },
    },
 
    {
      label: '实际维修费用',
      prop: 'actualRepairPrice',
      addDisplay: false,
      editDisplay: false,
      type: 'number',
      width: 120,
      span: 12,
    },
    {
      label: '返还时间',
      prop: 'completeTime',
      width: 110,
      display: false,
    },
    {
      label: '接收时间',
      prop: 'arriveDate',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      hide: true,
    },
    {
      label: '是否有附带品',
      prop: 'isHasOther',
      type: 'switch',
      hide: true,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      control: val => {
        return {
          otherThingsRemark: {
            display: val == 1,
          },
        };
      },
    },
    {
      label: '维修供应商',
      prop: 'repairSupplierId',
      component: 'wf-supplier-select',
      span: 24,
      formatter: row => {
        return row.repairSupplierName;
      },

      overHidden: true,
    },
    {
      label: '登记时间',
      prop: 'registrationDate',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      addDisplay: true,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '登记人',
      prop: 'registrationPerson',
      value: proxy.$store.getters.userInfo.real_name,
      readonly: true,
      addDisplay: true,
      hide: true,
      editDisplay: false,
      viewDisplay: false,
    },
    {
      label: '附带品备注',
      prop: 'otherThingsRemark',
      type: 'textarea',
      span: 24,
      display: false,
      hide: true,
      overHidden: true,
    },

    {
      label: '附件',
      prop: 'repairFile',
      type: 'upload',
      span: 24,

      // rules: [
      //   {
      //     required: true,
      //     validator: validatorPath,
      //     trigger: "change",
      //   },
      // ],
      hide: true,
      dataType: 'object',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/api/blade-resource/attach/upload',
      viewDisplay: false,
    },
    {
      label: '附件',
      prop: 'fileList',
      span: 24,
      addDisplay: false,
      editDisplay: false,
      hide: true,
    },
    {
      label: '维修备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
      overHidden: true,
    },

    {
      label: '维修状态',
      prop: 'repairStatus',
      display: false,
      type: 'select',
      search: true,
      width: 160,
      dicData: [
        {
          value: 0,
          label: '维修登记',
        },
        {
          value: 1,
          label: '维修中',
        },
        {
          value: 2,
          label: '已返还',
        },
      ],
    },
    {
      label: '报价状态',
      type: 'select',
      dicData: offerStatus,
      width: 120,
      prop: 'offerStatus',
      search: true,
    },
    {
      label: '业务员',
      prop: 'createName',
      search: true,
      component: 'wf-user-drop',
      display: false,
      width: 100,
    },

    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '送修信息',
      prop: 'rereturnInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
      {
          label: '预计维修费用',
          prop: 'preRepairPrice',
          type: 'number',
          width: 120,
          span: 12,
        },
        {
          label: '预计修复时间',
          prop: 'preRepairTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          hide: true,
        },
        {
          label: '维修供应商',
          prop: 'repairSupplierId',
          component: 'wf-supplier-select',
          span: 24,
          formatter: row => {
            return row.repairSupplierName;
          },

          overHidden: true,
        },
        {
          label: '送修时间',
          prop: 'sendRepairTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          span: 24,
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '备注',
          type: 'textarea',
          span: 24,
          prop: 'sendRepairRemark',
        },
      ],
    },
    {
      label: '返还信息',
      prop: 'refundInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
        // {
        //   label: '退款金额',
        //   prop: 'preRepairPrice',
        //   rules: [
        //     {
        //       required: true,
        //       message: '请输入退款金额',
        //     },
        //   ],
        // },
        {
          label: '返还时间',
          prop: 'completeTime',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '返还附件',
          prop: 'completeFile',
          type: 'upload',
          span: 12,

          // rules: [
          //   {
          //     required: true,
          //     validator: validatorPath,
          //     trigger: "change",
          //   },
          // ],
          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',
        },
        {
          label: '备注',
          type: 'textarea',
          span: 24,
          prop: 'completeRemark',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/repair/save';
const delUrl = '/api/vt-admin/repair/remove?ids=';
const updateUrl = '/api/vt-admin/repair/update';
const tableUrl = '/api/vt-admin/repair/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: props.customerId,
        selectType: 1,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    completeFile: null,
    repairFile: form.repairFile && form.repairFile.map(item => item.value).join(','),
    customerId: props.customerId || form.customerId,
    repairProductDTOList: form.repairProductDTOList.map(item => {
      return {
        ...item,
        detailId: item.id,
        id: null,
      };
    }),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    completeFile: null,
    repairFile: row.repairFile && row.repairFile.map(item => item.value).join(','),
    repairProductDTOList: row.repairProductDTOList.map(item => {
      return {
        ...item,
        detailId: item.id,
        id: null,
      };
    }),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm(list) {
  console.log(list);

  form.value.repairProductDTOList = list.map(item => {
    return {
      ...item,
      productVO: item.product,
      sealPrice: item.zhhsdj,
      preRepairPrice: 0,
    };
  });
}
function totalAmount(val) {
  //  console.log(val);
  // form.value.invoicePrice =  form.value.repairProductDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0) * (1 - (val * 1) / 100)
  return form.value.repairProductDTOList
    .reduce((total, item) => (total += item.preRepairPrice * 1), 0)
    .toFixed(2);
}
function actualRepairAmount() {
  return returnForm.value.repairProductDTOList
    .reduce((total, item) => (total += item.actualRepairPrice * 1), 0)
    .toFixed(2);
}
function handleChange(value) {
  form.value.customerId = value;
}
function beforeOpen(done, type) {
  if (['edit', 'view'].includes(type)) {
    axios.get('/api/vt-admin/repair/detail', { params: { id: form.value.id } }).then(res => {
      form.value = {
        ...res.data.data,
        repairFile:
          res.data.data.repairFileList &&
          res.data.data.repairFileList.map(item => {
            return {
              value: item.id,
              label: item.originalName,
            };
          }),
        repairProductDTOList: res.data.data.productVOList.map(item => {
          return {
            ...item,
            number: item.number * 1,
            id: item.detailId,
          };
        }),
      };
      done()
    });
  }else{
     done();
  }

 
}
function confirmReturn(row) {
  axios.get('/api/vt-admin/repair/detail', { params: { id: row.id } }).then(res => {
    const repairProductDTOList = res.data.data.productVOList.map(item => {
      return {
        ...item,
        number: item.number * 1,
      };
    });
    proxy.$refs.dialogForm.show({
      title: '确认送修',
      width: '80%',
      option: {
        column: [
          {
            label: '关联产品',
            prop: 'repairProductDTOList',
            type: 'dynamic',
            labelWidth: 0,
            value: repairProductDTOList,
            hide: true,

            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              rowAdd: done => {
                productSelectRef.value.open();
                // done();
              },
              rowDel: (row, done) => {
                done();
              },

              column: [
                {
                  label: '产品名称',
                  prop: 'productName',
                  overHidden: true,
                  bind: 'productVO.productName',
                  cell: false,
                },

                {
                  label: '规格型号',
                  prop: 'productSpecification',
                  overHidden: true,
                  bind: 'productVO.productSpecification',
                  // search: true,
                  cell: false,
                  span: 24,
                  type: 'input',
                },
                {
                  label: '品牌',
                  prop: 'productBrand',
                  overHidden: true,
                  bind: 'productVO.productBrand',
                  cell: false,
                  width: 120,
                },
                {
                  label: '数量',
                  prop: 'number',
                  type: 'number',
                  span: 12,
                  cell: true,
                  width: 120,
                },
                // {
                //   label: '单价',
                //   prop: 'sealPrice',
                //   type: 'number',
                //   span: 12,
                //   cell: false,
                //   width: 100,
                // },
                // {
                //   label: '金额',
                //   prop: 'totalPrice',
                //   type: 'number',
                //   span: 12,
                //   width: 100,
                //   cell: false,
                //   formatter: row => {
                //     return row.number * row.sealPrice;
                //   },
                // },
                {
                  type: 'select',
                  label: '维修方式',
                  dicUrl: '/blade-system/dict/dictionary?code=repairType',
                  cascader: [],
                  span: 12,
                  width: 160,
                  // search: true,
                  display: true,
                  cell: true,
                  props: {
                    label: 'dictValue',
                    value: 'id',
                    desc: 'desc',
                  },
                  prop: 'repairType',
                },
                // {
                //   label: '序列号',
                //   prop: 'serialNumber',
                //   span: 12,
                //   cell: true,
                //   row: 1,
                //   width: 200,
                //   type: 'textarea',
                // },
                // {
                //   label: '产品故障描述',
                //   prop: 'remark',
                //   span: 12,
                //   cell: true,
                //   width: 200,
                //   type: 'textarea',
                // },
                {
                  label: '预计维修金额',
                  type: 'number',
                  prop: 'preRepairPrice',
                  width: 150,
                  cell: true,
                  change: val => {
                    form.value.preRepairPrice = totalAmount();
                  },
                },
              ],
            },
          },
          {
            label: '送修时间',
            prop: 'sendRepairTime',
            type: 'date',
            format: 'YYYY-MM-DD',
            span: 24,
            valueFormat: 'YYYY-MM-DD',
          },
          {
            label: '送修备注',
            type: 'textarea',
            span: 24,
            prop: 'sendRepairRemark',
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin/repair/repair', {
            ...row,
            ...res.data,
          })
          .then(ref => {
            ElMessage.success('操作成功');
            onLoad();
            res.close();
          });
      },
    });
  });
}
let returnOption = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  column: [
    // {
    //   label: '退款金额',
    //   prop: 'preRepairPrice',
    //   value:row.preRepairPrice,
    //   rules: [
    //     {
    //       required: true,
    //       message: '请输入退款金额',
    //     },
    //   ],
    // },
    {
      label: '关联产品',
      prop: 'repairProductDTOList',
      type: 'dynamic',
      labelWidth: 0,

      hide: true,

      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        addBtn: false,
        delBtn: false,
        rowAdd: done => {
          // productSelectRef.value.open();
          // done();
        },
        rowDel: (row, done) => {
          // done();
        },

        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            bind: 'productVO.productName',
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            bind: 'productVO.productSpecification',
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
            bind: 'productVO.productBrand',
            cell: false,
            width: 120,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
            span: 12,
            cell: false,
            width: 120,
          },
          // {
          //   label: '单价',
          //   prop: 'sealPrice',
          //   type: 'number',
          //   span: 12,
          //   cell: false,
          //   width: 100,
          // },
          // {
          //   label: '金额',
          //   prop: 'totalPrice',
          //   type: 'number',
          //   span: 12,
          //   width: 100,
          //   cell: false,
          //   formatter: row => {
          //     return row.number * row.sealPrice;
          //   },
          // },
          // {
          //   type: 'select',
          //   label: '维修方式',
          //   dicUrl: '/blade-system/dict/dictionary?code=repairType',
          //   cascader: [],
          //   span: 12,
          //   width: 160,
          //   // search: true,
          //   display: true,
          //   cell: true,
          //   props: {
          //     label: 'dictValue',
          //     value: 'id',
          //     desc: 'desc',
          //   },
          //   prop: 'invoiceType',
          // },
          // {
          //   label: '序列号',
          //   prop: 'serialNumber',
          //   span: 12,
          //   cell: true,
          //   row: 1,
          //   width: 200,
          //   type: 'textarea',
          // },
          // {
          //   label: '产品故障描述',
          //   prop: 'remark',
          //   span: 12,
          //   cell: true,
          //   width: 200,
          //   type: 'textarea',
          // },

          {
            label: '预计维修金额',
            type: 'number',
            prop: 'preRepairPrice',
            width: 150,
            cell: false,
          },
          {
            label: '实际维修金额',
            type: 'number',
            prop: 'actualRepairPrice',
            width: 150,
            cell: true,
            change: val => {
              returnForm.value.actualRepairPrice = actualRepairAmount();
            },
          },
        ],
      },
    },
    {
      label: '返还时间',
      prop: 'completeTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      // value: dateFormat(new Date(), 'yyyy-MM-dd'),
    },
    {
      label: '实际维修金额',
      type: 'number',
      prop: 'actualRepairPrice',
      width: 150,
      cell: true,
      rules: [
        {
          required: true,
          message: '请输入实际维修金额',
        },
      ],
    },
    {
      label: '返还附件',
      prop: 'completeFile',
      type: 'upload',
      span: 24,
      dragFile: true,
      // rules: [
      //   {
      //     required: true,
      //     validator: validatorPath,
      //     trigger: "change",
      //   },
      // ],
      dataType: 'object',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/api/blade-resource/attach/upload',

      viewDisplay: false,
    },
    {
      label: '备注',
      type: 'textarea',
      span: 24,
      prop: 'completeRemark',
    },
  ],
});
let returnDialog = ref(false);
let returnForm = ref({});
function openReturn(row) {
  axios.get('/api/vt-admin/repair/detail', { params: { id: row.id } }).then(res => {
    const repairProductDTOList = res.data.data.productVOList.map(item => {
      return {
        ...item,
        number: item.number * 1,
        actualRepairPrice: item.preRepairPrice,
      };
    });
    if (proxy.$refs.returnRef) {
      proxy.$refs.returnRef.resetForm();
    }
    returnForm.value = {
      ...res.data.data,
      repairProductDTOList,
      completeTime: dateFormat(new Date(), 'yyyy-MM-dd'),
    };
    returnDialog.value = true;
  });
}
function handleReturnConfirm(form, done, loading) {
  axios
    .post('/api/vt-admin/repair/complete', {
      ...form,
      completeFile: form.completeFile && form.completeFile.map(item => item.value).join(','),
    })
    .then(r => {
      ElMessage.success('操作成功');
      done();
      onLoad();
      returnDialog.value = false;
    });
}
function toDetail(row) {
  router.push({
    path: '/CRM/customer/detail/detail',
    query: {
      id: row.customerId,
      delBtn: 1,
    },
  });
}
</script>

<style lang="scss" scoped></style>
