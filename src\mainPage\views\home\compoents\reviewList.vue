<template>
  <Title style="padding: 10px 0">复盘列表</Title>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    size="mini"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #fileIds-form="row">
      <file :file-list="form.attachList"></file>
    </template>
    <template #reviewName="{ row, $index }">
      <el-link type="primary" @click="$refs.crud.rowView(row, $index)">{{
        row.reviewName
      }}</el-link>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import file from './../../../components/file/index.vue';
import template from './../../CRM/programme/compoents/template.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  header: false,
  editBtn: true,
  delBtn: true,
  size: 'small',
  dialogType: 'drawer',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  border: true,
  column: [
    {
      label: '复盘主题',
      prop: 'reviewName',
      // search: true,
      searchLabelWidth: 120,
      span: 24,
      // rules: [
      //   {
      //     required: true,
      //     message: "请输入文件名称",
      //     trigger: "change",
      //   },
      // ],
      overHidden: true,
    },
    {
      label: '复盘描述',
      prop: 'reviewDescription',
      type: 'textarea',
      span: 24,
      // rules: [
      //   {
      //     required: true,
      //     message: "请输入文件名称",
      //     trigger: "change",
      //   },
      // ],
      overHidden: true,
    },
    {
      label: '复盘日期',
      prop: 'reviewDate',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      overHidden: true,
      width: 120,
      span: 24,
      align: 'center',
    },
    {
      label: '复盘附件',
      prop: 'fileIds',
      type: 'upload',
      span: 24,
      dragFile: true,
      hide: true,
      value: [],
      dataType: 'object',
      // rules: [
      //   {
      //     required: true,
      //     validator: validatorPath,
      //     trigger: "change",
      //   },
      // ],
      formatter: row => {
        return row.attachList;
      },
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      // tip: "只能上传图片和PDF 文件不超过100M",
      // tip: '文件不超过100M',
      action: '/api/blade-resource/attach/upload',
      // hide: true,
      viewDisplay: true,
    },
    {
      label: '创建者',
      prop: 'createName',
      width: 150,
      align: 'center',
      addDisplay: false,
      editDisplay: false,
      rules: [
        {
          required: true,
          message: '请输入创建人',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm',
      addDisplay: false,
      editDisplay: false,
      rules: [
        {
          required: true,
          message: '请输入修改时间',
          trigger: 'blur',
        },
      ],
      width: 150,
      align: 'center',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 5,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/review/page';
let params = ref({
  isPush: 1,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
</script>

<style lang="scss" scoped></style>
