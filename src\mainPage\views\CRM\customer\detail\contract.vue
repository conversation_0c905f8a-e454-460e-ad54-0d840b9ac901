<template>
  <div>
   
    <myCustomerContract
      v-if="props.info.type == 0"
      :customerId="props.info.customerId"
    ></myCustomerContract>
    <subCustomerContract
      v-if="props.info.type == 1"
      :customerId="props.info.customerId"
    ></subCustomerContract>
    <assistContract
      v-if="props.info.type == 2"
      :customerId="props.info.customerId"
    ></assistContract>
    <allCustomerContract
      v-if="props.info.type == 3 || props.info.type == 4 || props.info.type == 5"
      :customerId="props.info.customerId"
    ></allCustomerContract>
  </div>
</template>

<script setup>
import myCustomerContract from '@/views/Contract/customer/myCustomerContract.vue';
import subCustomerContract from '@/views/Project/projectContract/subCustomerContract.vue';
import assistContract from '@/views/Contract/customer/assistContract.vue';
import allCustomerContract from '@/views/Contract/customer/allCustomerContract.vue';
const props = defineProps(['info']);
</script>

<style lang="scss" scoped></style>
