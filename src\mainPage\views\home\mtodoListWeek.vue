<template>
  <div class="list">
    <div class="list-title">
      {{ todoDate[0] }} - {{ todoDate[1] }}

      <div style="display: flex; align-items: center; gap: 5px">
        <el-icon
          @click="handleAddTask"
          v-if="type == 'self'"
          style="font-size: 18px; cursor: pointer"
          ><CirclePlus
        /></el-icon>
        <el-icon
          @click="handleFull(1)"
          v-if="type == 'self' && isFull == 0"
          style="font-size: 18px; cursor: pointer"
          ><FullScreen
        /></el-icon>
        <el-icon @click="handleFull(0)" v-if="isFull == 1" style="font-size: 18px; cursor: pointer"
          ><Close
        /></el-icon>
      </div>
    </div>
    <draggable
      v-model="list"
      :animation="100"
      @sort="
        a => {
          onMoveCallback(a, list);
        }
      "
      handle=".handle"
    >
      <transition-group>
        <div v-for="(item, index) in list" :key="item['id']" class="todo-item">
          <div class="handle" style="cursor: n-resize; display: flex; align-items: center">
            <svg
              v-if="type == 'self'"
              t="1739165828084"
              class="icon"
              viewBox="0 0 1024 1024"
              version="1.1"
              xmlns="http://www.w3.org/2000/svg"
              p-id="4516"
              width="20"
              height="20"
            >
              <path
                d="M256 768.064a64 64 0 1 1-64 64 64 64 0 0 1 64-64z m0-320a64 64 0 1 1-64 64 64 64 0 0 1 64-64z m0-320a64 64 0 1 1-64 64A64 64 0 0 1 256 128z m512 640a64 64 0 1 1-64 64 64 64 0 0 1 64-64z m0-320a64 64 0 1 1-64 64 64 64 0 0 1 64-64z m0-320a64 64 0 1 1-64 64A64 64 0 0 1 768 128z"
                fill="#585858"
                p-id="4517"
              ></path>
            </svg>
            <span style="font-size: 20px; font-weight: bolder; margin-left: 5px"
              >{{ index + 1 }}.</span
            >
          </div>

          <el-input
            v-if="item.completeStatus != 1"
            type="textarea"
            autosize
            :readonly="type != 'self'"
            v-model="item.scheduleName"
            input-style="color: var(--el-color-danger)"
            @focus="item['focus'] = true"
            @blur="handleTodoItemBlur(index)"
            :ref="'input' + index"
          >
          </el-input>
          <div v-else class="complete">{{ item.scheduleName }}</div>
          <div v-if="!item['focus'] && type == 'self'" class="control">
            <div
              v-if="item.completeStatus != 1"
              @click="handleCompleteTodoItem(index, true)"
              class="control-item enter"
            >
              <el-icon><CircleCheck /></el-icon>
            </div>
            <!-- <div
              @click.stop="handleCompleteTodoItem(index, false)"
              class="control-item enter"
              v-else
            >
              <el-icon><RefreshLeft /></el-icon>
            </div> -->
            <div @click.stop="handleDelTodoItem(index)" class="control-item close">
              <el-icon><CircleClose /></el-icon>
            </div>
          </div>
          <div v-if="item.completeStatus == 1" style="display: flex; align-items: center">
            <el-icon style="position: absolute; right: 0; top: 0; color: var(--el-color-success)"
              ><SuccessFilled
            /></el-icon>
          </div>
        </div>
      </transition-group>
    </draggable>
  </div>
</template>

<script>
import axios from 'axios';
import { nextTick } from 'vue';
import { VueDraggableNext as draggable } from 'vue-draggable-next';
export default {
  name: 'MToDoList',
  emits: ['isFull'],
  props: {
    data: {
      type: Array,
    },
    date: {
      type: String,
    },
    type: String,
  },
  components: {
    draggable,
  },
  data() {
    return {
      list: [],
      todoDate: '',
      newList: [],
      isFull: 0,
    };
  },
  watch: {
    data: function (val) {
      this.list = val.map(item => {
        return {
          ...item,
          focus: false,
          scheduleName: item.planContent,
        };
      });
    },
    date: function (val) {
      this.todoDate = val;
    },
  },
  created() {
    this.todoDate = this.date;
    this.list = this.data.map(item => {
      return {
        ...item,
        scheduleName: item.planContent,
        focus: false,
      };
    });
  },
  methods: {
    // 新增日程
    handleAddTodo(index) {
      if (
        index == this.list.length - 1 &&
        this.list[index]['scheduleName'] !== null &&
        this.list[index]['scheduleName'] !== undefined &&
        this.list[index]['scheduleName'] !== ''
      ) {
        this.list.push({
          // 日期
          scheduleDate: this.todoDate,
          // 内容
          scheduleName: '',
          // 未完成
          status: 0,
        });
      }
      this.$nextTick(() => {
        let str = this.list[index]['scheduleName'];
        this.list[index].scheduleName = str.substring(0, str.length - 1);
        this.$refs['input' + (index + 1)][0].focus();
      });
    },
    // 删除日程
    handleDelTodoItem(index) {
      axios.post('/api/vt-admin/weekPlan/remove?ids=' + this.list[index]['id']).then(e => {
        if (e.data.code == 200) {
          this.list.splice(index, 1);
          this.handleRefreshCalendar();
        }
      });
    },
    /**
     * 任务item取消焦点
     */
    handleTodoItemBlur(index) {
      if (this.type != 'self') return;
      this.list[index]['focus'] = false;
      if (
        this.list[index]['scheduleName'] === null ||
        this.list[index]['scheduleName'] === undefined ||
        this.list[index]['scheduleName'] === ''
      ) {
        if (this.list[index]['id']) {
          this.handleDelTodoItem(index);
        } else {
          this.list.splice(index, 1);
        }
        return;
      }

      // 新增/修改
      const { scheduleName } = this.list[index];
      const data = {
        planContent: scheduleName,
        startDate: this.todoDate[0],
        endDate: this.todoDate[1],
        id: this.list[index].id || null,
      };
      axios.post(`/api/vt-admin/weekPlan/${data.id ? 'update' : 'save'}`, data).then(e => {
        if (e.data.code == 200) {
          this.handleRefreshCalendar();
        }
      });
    },
    // 新增默认数据
    handleFirstAdd() {
      if (this.list.length > 0) {
        for (let i = 0; i < this.list.length; i++) {
          if (this.list[i]['status'] == 0) {
            return;
          }
        }
      }
      this.list.unshift({
        // 日期
        scheduleDate: this.todoDate,
        // 内容
        scheduleName: '',
        // 完成状态
        status: 0,
      });
      this.$nextTick(() => {
        this.$refs['input0'][0].focus();
      });
    },
    // 完成日程
    handleCompleteTodoItem(index, flag) {
      this.$confirm('是否完成此计划，完成后将不可更改', '提示', {
        type: 'warning',
      }).then(res => {
        this.list[index].status = flag ? 1 : 0;
        // 切换状态
        axios
          .post('/api/vt-admin/weekPlan/complete', {
            id: this.list[index]['id'],
          })
          .then(e => {
            if (e.data.code == 200) {
              this.handleRefreshCalendar();
            }
          });
      });
    },
    // 刷新日历数据状态
    handleRefreshCalendar() {
      this.$parent.$refs['calendarRef'].initCalendar();
    },
    // 新增任务
    handleAddTask() {
      this.list.push({
        // 日期
        scheduleDate: this.todoDate,
        // 内容
        scheduleName: '',
        // 完成状态
        status: 0,
      });
      this.$nextTick(() => {
        this.$refs[`input${this.list.length - 1}`][0].focus();
      });
    },
    onMoveCallback(e, newList) {
      console.log(newList);
      const data = newList.map((item, idnex) => {
        return {
          ...item,
          sort: idnex,
        };
      });
      axios
        .post('/api/vt-admin/weekPlan/updateSort', {
          weekPlanEntityList: data,
        })
        .then(res => {});
    },
    handleFull(value) {
      this.isFull = value;
      this.$emit('isFull', value);
      nextTick(() => {
        this.list.forEach((item, index) => {
          this.$refs[`input${index}`][0].resizeTextarea();
        });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;
  // padding-top: 40px;
  // box-sizing: border-box;

  .list-title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #409eff;
    color: #fff;
    font-size: 16px;
    padding-left: 15px;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 15px;
    box-sizing: border-box;
  }
}

::v-deep .el-textarea__inner {
  box-shadow: none !important;
  resize: none !important;
}
.todo-item {
  width: 100%;
  padding: 5px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #ebeef5;
  // background-color: red;
  position: relative;
  display: flex;
  align-items: flex-start;
  .complete {
    width: 100%;
    padding: 5px 10px;
    box-sizing: border-box;
    color: #000;

    font-size: 14px;
  }

  .control {
    height: 100%;

    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    visibility: hidden;

    padding-right: 10px;
    box-sizing: border-box;

    .control-item {
      width: 20px;
      height: 20px;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      color: #fff;

      cursor: pointer;

      &.enter {
        background-color: var(--el-color-primary);
        margin-right: 10px;
      }
      &.close {
        background-color: var(--el-color-danger);
      }
    }
  }

  &:hover {
    .control {
      visibility: visible;
    }
  }
}
// /deep/ .el-card__body {
//   padding: 5px 10px;
// }
</style>
