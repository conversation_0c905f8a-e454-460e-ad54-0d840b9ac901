<template>
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="searchReset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
      @expand-change="expandChanges"
      :row-class-name="getClass"
    >
      <template #menu-left="{}">
        <div style="display: flex">
          <!-- <el-button
              type="primary"
              icon="el-icon-plus"
              @click="handleAdd"
              v-if="!route.query.type || route.query.type == 0"
              >新增</el-button
            > -->
          <!-- <div style="display: flex; align-items: center">
              <span style="font-weight: bolder">总报价额：</span>
              <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
            </div> -->
        </div>
      </template>
      <template #menu="{ row }">
        <div style="display: flex; justify-content: center; flex-wrap: wrap">
          <!-- <div v-if="row.offerPeople == $store.getters.userInfo.user_id">
              <el-button
                text
                type="primary"
                icon="el-icon-link"
                @click="handleEditOffer(row)"
                v-if="row.offerStatus == 0 && $store.getters.permission.quation_qua"
                >报价</el-button
              >
  
            
            </div> -->
          <el-button
            text
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit(row)"
            v-if="
              ((row.auditStatus == 0 && row.offerStatus == 0) ||
                row.auditStatus == 2 ||
                (row.offerStatus == 3 && row.auditStatus == 1)) &&
              (!route.query.type || route.query.type == 0)
            "
            >编辑</el-button
          >

          <el-button
            text
            type="primary"
            v-if="
              row.offerStatus == 3 &&
              row.auditStatus == 1 &&
              (!route.query.type || route.query.type == 0)
            "
            icon="el-icon-view"
            @click="customerConfirm(row)"
            >确认</el-button
          >
          <el-button
            text
            :type="row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'"
            icon="el-icon-download"
            @click="download(row)"
            v-if="row.auditStatus == 1 || row.offerStatus == 2"
            >下载</el-button
          >
          <el-button
            text
            type="primary"
            icon="CaretLeft"
            @click="back(row)"
            v-if="
              row.offerStatus == 3 &&
              row.isHasOption == 1 &&
              (!route.query.type || route.query.type == 0)
            "
            >撤回</el-button
          >
          <el-button
            text
            type="primary"
            icon="CaretLeft"
            @click="backToConfirm(row)"
            v-if="row.offerStatus == 2 && row.isHasContract == 0"
            >撤回</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.isHasOption !== 1 && row.offerStatus != 6"
            icon="CopyDocument"
            @click="copyOffer(row)"
            >复制</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus != 6 && row.isHasOption !== 1 && row.offerStatus != 3"
            icon="VideoPause"
            @click="pause(row)"
            >暂停</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus == 6 && row.isHasOption !== 1"
            icon="VideoPlay"
            @click="start(row)"
            >重启</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.isHasOption !== 1 && row.offerStatus != 2"
            icon="delete"
            @click="$refs.crud.rowDel(row)"
            >删除</el-button
          >
        </div>

        <!-- <el-button
            text
            type="primary"
            v-if="row.offerStatus == 6 && row.isHasOption !== 1"
            icon="VideoPlay"
            @click="start(row)"
            >重启</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus != 6 && row.isHasOption !== 1 && row.offerStatus == 3"
            icon="VideoPause"
            @click="pause(row)"
            >暂停</el-button
          > -->
      </template>
      <template #offerName="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.offerName }}</el-link>
      </template>
      <template #auditStatus="{ row }">
        <div v-if="row.auditStatus == 1 || row.auditStatus == 2">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.technologyRemark"
            :disabled="!row.technologyRemark"
            placement="top-start"
            v-if="row.auditStatus == 1"
          >
            <el-badge :hidden="!row.technologyRemark" is-dot class="item">
              <el-tag effect="plain" v-if="row.auditStatus == 1" size="small" type="success"
                >审核成功</el-tag
              >
            </el-badge>
          </el-tooltip>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditReason"
            placement="top-start"
            v-if="row.auditStatus == 2"
          >
            <el-tag effect="plain" size="small" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
          <el-tag effect="plain" v-if="row.auditType == 1" size="small" type="info"
            >待采购审核</el-tag
          >
          <el-tag effect="plain" v-if="row.auditType == 2" size="small" type="info"
            >待主管审核</el-tag
          >
          <el-tag effect="plain" v-if="row.auditType == 3" size="small" type="info"
            >待总经理审核</el-tag
          >
        </div>
      </template>
      <template #offerStatus="{ row }">
        <el-tooltip
          :content="row.failReason || row.pauseReason"
          v-if="row.offerStatus == 4 || row.offerStatus == 6"
        >
          <el-tag effect="plain" size="small" type="danger">{{ row.$offerStatus }}</el-tag>
        </el-tooltip>
        <span v-else>{{ row.$offerStatus }}</span>
      </template>
      <template #repairCode="{ row }">
        <el-link type="info" >{{ row.repairCode }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="历史记录" v-model="dialogVisible">
      <history :id="currentId" :currentStatus="currentStatus"></history>
    </el-dialog>
    <process ref="processRef" :currentId="currentId" :type="1"></process>
    <el-drawer v-model="drawer" :with-header="false" size="80%" title="拆解">
      <el-row :gutter="20" style="height: 100%">
        <el-col :span="10">
          <el-card shadow="never" style="height: 100%">
            <el-alert type="success" :closable="false">请将需要拆分的产品勾选上</el-alert>
            <avue-crud
              :option="needDeposeOption"
              :data="needDeposeData"
              @row-click="handleRowClick"
            >
              <template #menu="{ row }">
                <el-switch v-model="row.isDepose" :active-value="1" :inactive-value="0">
                </el-switch>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
        <el-col style="height: 100%" :span="14">
          <el-row style="margin-bottom: 10px">
            <el-col :span="24">
              <el-card shadow="never">
                <!-- <avue-form :value="detailForm" :option="decomposeOption"></avue-form> -->
                <el-descriptions border :title="detailForm.customProductName" :column="3">
                  <el-descriptions-item label="规格型号">{{
                    detailForm.customProductSpecification
                  }}</el-descriptions-item>
                  <el-descriptions-item label="品牌">{{
                    detailForm.productBrand
                  }}</el-descriptions-item>
                  <el-descriptions-item label="单位">{{
                    detailForm.unitName
                  }}</el-descriptions-item>
                  <el-descriptions-item label="描述">
                    {{ detailForm.customProductDescription }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-card shadow="never">
                <template #header>
                  <!-- <el-alert type="info" :closable="false"
                      >拆解会删除被拆解的产品，如需保留请再拆解列表里面添加</el-alert
                    > -->
                  <div style="display: flex; justify-content: space-between">
                    <div>拆解产品</div>
                    <div>
                      <el-form v-if="detailForm.productId">
                        <el-form-item label="保留原产品">
                          <el-switch
                            v-model="detailForm.isPre"
                            @change="handleIsPreChange"
                          ></el-switch>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </template>
                <avue-crud
                  @row-del="rowDelProduct"
                  :data="detailForm.detailDTOList"
                  :option="decomposeEditFormOption"
                >
                  <template #menu-left>
                    <div v-if="detailForm.isDepose">
                      <el-button
                        icon="plus"
                        type="primary"
                        size="small"
                        @click="$refs.productSelectRef.visible = true"
                      ></el-button>
                      <productSelectDrop
                        v-if="$route.query.type != 'detail'"
                        @select="handleProductSelectConfirm"
                        style="margin-left: 5px"
                      ></productSelectDrop>
                    </div>
                  </template>
                  <template #number="{ row }">
                    <el-input-number
                      size="small"
                      style="width: 80%"
                      controls-position="right"
                      v-model="row.number"
                    ></el-input-number>
                  </template>
                </avue-crud>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <template #footer>
        <el-button type="primary" icon="check" @click="decomposeSubmit">确 定</el-button>
        <el-button icon="close" @click="drawer = false">取 消</el-button>
      </template>
    </el-drawer>
    <wfProductSelect
      @onConfirm="handleProductSelectConfirm"
      ref="productSelectRef"
    ></wfProductSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { offerStatus, auditStatus } from '@/const/const.js';
import { downloadOwn } from '@/utils/util';
import { ElNotification } from 'element-plus';
import { h } from 'vue';
import { download as downloadOffer } from '@/utils/download.js';

import progress from '@/components/progress/index.vue';

import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { detail } from '@/api/flow/flow';
import productSelectDrop from '@/views/CRM/quotation/compoents/productSelectDrop.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  menuWidth: 330,
  border: true,
  expand: true,
  rowKey: 'id',
  column: [
  {
      label: '维修单编号',
      prop: 'repairCode',
      overHidden: true,

      disabled: true,
      width: 180,
      search: true,
      searchLabelWidth: 120,
    },
    {
      label: '报价名称',
      prop: 'offerName',
      width: 200,
      overHidden: true,
      search: true,
    },

    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      component: 'wf-customer-drop',
      search: !props.customerId,
      hide: !!props.customerId,
    },
    // {
    //   label: '关联联系人',
    //   prop: 'contactPersonName',
    // },

    {
      label: '业务员',
      prop: 'createName',
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      search: true,
      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '总金额（元）',
      prop: 'offerPrice',
      width: 120,
      formatter: row => {
        return parseFloat(row.offerPrice).toLocaleString();
      },
    },
    {
      label: '有效期(天)',
      prop: 'offerValidity',
      width: 100,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'date',
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
    },
    {
      label: '报价状态',
      type: 'select',
      dicData: offerStatus,
      width: 100,
      prop: 'offerStatus',
      search: true,
    },

    {
      label: '产品名称',
      type: 'input',
      width: 100,
      hide: true,
      prop: 'productName',
      search: true,
    },
    {
      label: '版本',
      type: 'input',
      width: 80,
      prop: 'version',
      formatter: row => {
        return `v${row.version}.0`;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  customerId: String,
});
const addUrl = '';
const delUrl = '/api/vt-admin/offer/deletedById?id=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/offer/pageForRepair';
let route = useRoute();
let params = ref({
  ids: route.query.ids,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
onActivated(() => {
  onLoad();
});
let loading = ref(false);
let totalPrice = ref(0);
// let isMy = ref(route.query.type == 0 || !route.query.type);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        // selectType: isMy.value ? 0 : 2,
        selectType: 0,
        customerId: props.customerId || null,

        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });

  // 获取统计数据
  axios
    .get('/api/vt-admin/offer/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        // selectType: isMy.value ? 0 : 2,
        customerId: props.customerId || null,

        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function searchReset() {
  (params.value.ids = null), onLoad();
}
function download(row) {
  downloadOffer('/api/vt-admin/offer/downloadOffer', { id: row.id }, () => {
    onLoad();
    proxy.$store.dispatch('getMessageList');
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push('/CRM/quotation/compoents/addVersion3?type=add');
}
function handleEdit(row) {
  if (row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'edit',
        name: '编辑方案',
        isAdmin: 'admin',
      },
    });
  } else {
    router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        name: row.offerName,
      },
    });
  }
}
function handleEditOffer(row) {
  router.push({
    path: '/CRM/quotation/compoents/editOffer',
    query: {
      id: row.id,
      type: 'edit',
    },
  });
}
function handleView(row) {
  if (row.dataJson && row.isHasOption != 1) {
    router.push({
      path: '/CRM/quotation/compoents/add',
      query: {
        id: row.id,
        name: row.offerName,
        type: 'detail',
      },
    });
  } else if (row.dataJson && row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.offerName,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  } else if (!row.dataJson && !row.isHasOption) {
    router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        type: 'detail',
        name: row.offerName,
        repairId: row.repairId,
        businessOpportunityId: row.isHasOption == 1 ? row.businessOpportunityId : null,
      },
    });
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.offerName,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  }

  // }
}
function handleViewSub(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        // name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else if (row.isHasDataJson == 0 || !row.isHasDataJson) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.optionId,
        // name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
let dialogRef = '';
function customerConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: '登记',
    option: {
      labelWidth: 150,
      column: [
        {
          label: '登记结果',
          prop: 'offerStatus',
          type: 'radio',
          rules: [
            {
              required: true,
              message: '请选择登记结果',
              trigger: 'change',
            },
          ],
          dicData: [
            {
              label: '成交',
              value: 2,
            },

            {
              label: '失单',
              value: 4,
            },
          ],
          control: val => {
            return {
              failReason: {
                display: val == 4,
              },
              isSplit: {
                display: val == 2 && row.isIntelligentizeProject != 1,
              },
              purchaseRemark: {
                display: val == 2,
              },
            };
          },
          span: 24,
        },
        // {
        //   label: '拆解产品',
        //   prop: 'isSplit',
        //   type: 'switch',
        //   dicData: [
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //   ],
        //   span: 12,
        //   display: false,
        // },
        // {
        //   label: '采购注意事项',
        //   prop: 'purchaseRemark',
        //   type: 'textarea',
        //   display: false,
        //   span: 24,
        // },
        {
          label: '失单原因',
          prop: 'failReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
      ],
    },
    callback(res) {
      console.log(res);
      dialogRef = res;
      if (res.data.isSplit == 1) {
        customerConfirmAndDecompose(row);
      } else {
        if (res.data.offerStatus == 2) {
          if (row.isIntelligentizeProject == 1) {
            router.push({
              path: '/Project/add',
              query: {
                offerId: row.id,
              },
            });
          } else {
            router.push({
              path: '/Order/salesOrder/compoents/addOrder',
              query: {
                offerId: row.id,
              },
            });
          }
          res.close();
        } else {
          axios
            .post('/api/vt-admin/offer/customerConfirm', {
              id: row.id,
              ...res.data,
            })
            .then(e => {
              proxy.$message.success(e.data.msg);
              res.close();

              onLoad();
            });
        }
      }
    },
  });
}

let dialogVisible = ref(false);
let currentId = ref(null);
let currentStatus = ref(null);
function viewHistory(row) {
  currentId.value = row.id;
  currentStatus.value =
    ((row.auditStatus == 0 && row.offerStatus == 0) ||
      row.auditStatus == 2 ||
      (row.offerStatus == 3 && row.auditStatus == 1)) &&
    row.isHasOption == 0;

  dialogVisible.value = true;
}
function back(row) {
  proxy.$refs.dialogForm.show({
    title: '撤回',
    option: {
      column: [
        {
          label: '撤回原因',
          prop: 'failReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/offer/backOption', {
          id: row.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('撤回成功');
          res.close();
          onLoad();
        });
    },
  });
}
function backToConfirm(row) {
  proxy.$confirm('确定要退回到待确认状态吗？', '提示', {}).then(() => {
    axios
      .post('/api/vt-admin/offer/returnWaitConfirm', {
        id: row.id,
      })
      .then(e => {
        proxy.$message.success('操作成功');
        onLoad();
      });
  });
}
// 流程
function viewProcess(row) {
  currentId.value = row.id;
  proxy.$refs.processRef.open();
}
function expandChanges(row, expendList) {
  if (expendList.length) {
    option.value.expandRowKeys = [];
    if (row) {
      option.value.expandRowKeys.push(row.id);
    }
  } else {
    option.value.expandRowKeys = [];
  }
}
function getClass({ row }) {
  return row.isHasManyOffer == 0 ? 'hide_icon' : '';
}
// 拆解
let drawer = ref(false);
let currentRow = ref(null);
function customerConfirmAndDecompose(row) {
  drawer.value = true;
  currentRow.value = row;
  getNeedDeposeData(row);
}

let decomposeEditFormOption = ref({
  submitBtn: false,
  emptyBtn: false,
  editBtn: false,
  // header:false,
  addBtn: false,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 120,
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
let needDeposeData = ref([]);
let needDeposeOption = ref({
  header: false,
  addBtn: false,
  border: true,
  editBtn: false,
  delBtn: false,
  menuWidth: 80,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
function getNeedDeposeData(row) {
  axios
    .get('/api/vt-admin/offer/getOfferProducts', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      needDeposeData.value = res.data.data;
    });
}
let detailForm = ref({});
function handleRowClick(row) {
  detailForm.value = row;
  if (!detailForm.value.detailDTOList) {
    detailForm.value.detailDTOList = [];
  }
}
async function handleProductSelectConfirm(id) {
  console.log(detailForm.value == needDeposeData.value[0]);
  const res = await axios.get('/api/vt-admin/product/detail?id=' + id);
  detailForm.value.detailDTOList.push({
    productId: res.data.data.id,
    productName: res.data.data.productName,
    productSpecification: res.data.data.productSpecification,
    productBrand: res.data.data.productBrand,
    unit: res.data.data.unitName,
    number: detailForm.value.number,
  });
}
function rowDelProduct(form) {
  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      detailForm.value.detailDTOList = detailForm.value.detailDTOList.filter(
        item => item.productId !== form.productId
      );
    })
    .catch(() => {});
}
function handleIsPreChange(val) {
  console.log(val);
  if (val) {
    handleProductSelectConfirm(detailForm.value.productId);
  } else {
    rowDelProduct({
      productId: detailForm.value.productId,
    });
  }
}
function decomposeSubmit() {
  const data = {
    id: currentRow.value.id,
    offerStatus: 2,
    dismantleList: needDeposeData.value
      .filter(item => item.isDepose == 1)
      .map(item => {
        return {
          id: item.id,
          productList: item.detailDTOList.map(item => {
            return {
              productId: item.productId,
              number: item.number,
            };
          }),
        };
      }),
  };
  console.log(data);
  axios.post('/api/vt-admin/offer/customerConfirm', data).then(e => {
    proxy.$message.success(e.data.msg);
    dialogRef.close();
    drawer.value = false;

    onLoad();

    if (currentRow.value.isIntelligentizeProject == 1) {
      router.push({
        path: '/Project/add',
        query: {
          offerId: currentRow.value.id,
        },
      });
    } else {
      router.push({
        path: '/Order/salesOrder/compoents/addOrder',
        query: {
          offerId: currentRow.value.id,
        },
      });
    }
  });
}
function copyOffer(row) {
  router.push({
    path: '/CRM/quotation/compoents/addVersion3',
    query: {
      id: row.id,
      name: row.offerName,
      copy: 1,
    },
  });
}
function pause(row) {
  proxy.$refs.dialogForm.show({
    title: '暂停',

    option: {
      column: [
        {
          label: '暂停原因',
          prop: 'pauseReason',
          span: 24,
          type: 'textarea',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/offer/pause', {
          id: row.id,
          pauseReason: res.data.pauseReason,
        })
        .then(r => {
          proxy.$message.success(r.data.msg);
          res.close();
          onLoad();
        });
    },
  });
}
function start(row) {
  proxy
    .$confirm('是否重启?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/offer/restart', {
          id: row.id,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          onLoad();
        });
    });
}
</script>

<style lang="scss" scoped>
.item {
  margin-top: 5px;
}
:deep(.el-card__header) {
  padding: 5px;
}
:deep(.el-col) {
  margin-bottom: 0px;
}

:deep(.hide_icon td:first-child .cell) {
  visibility: hidden;
}
</style>
