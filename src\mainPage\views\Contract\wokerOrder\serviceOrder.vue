<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #serviceFiles="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          v-if="row.paymentStatus == 0"
          text
          icon="edit"
          @click="$refs.crud.rowEdit(row)"
          >编辑</el-button
        >
        <!-- <el-button
          type="primary"
          v-if="row.paymentStatus == 0"
          text
          @click="cancel(row)"
          icon="CircleCloseFilled"
          >取消</el-button
        > -->
        <el-button
          type="primary"
          v-if="row.paymentStatus == 0"
          text
          @click="pay(row)"
          icon="CircleCheckFilled"
          >完成</el-button
        >
        <el-button
          type="primary"
          v-if="row.paymentStatus == 1"
          text
          @click="confirm(row)"
          icon="CircleCheckFilled"
          >确认收款</el-button
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import moment from 'moment';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 210,
  border: true,
  column: [
    {
      label: '服务单号',
      prop: 'orderNo',
      width: 200,
      span: 12,
      overHidden: true,
      search: true,
      display: false,
    },
    {
      label: '对应客户',
      prop: 'customerId',
      component: 'wf-customer-select',

      params: {},
      change: val => {
        handleCustomerIdChange(val.value);
      },
      formatter: row => row.customerName,
      control: val => {
        console.log(val);
        return {
          customerConcat: {
            disabled: val ? false : true,
          },
        };
      },
    
    },
    {
      label: '联系人',
      prop: 'customerConcat',
      component: 'wf-contact-select',
      placeholder: '请先选择客户',
      formatter: row => row.customerConcatName,
     
      change: handleContactChange,
      disabled: false,
      params: {
        // checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },
    {
      label: '联系电话',
      prop: 'concatPhone',
    },

    {
      label: '服务标题',
      prop: 'serviceTitle',
      width: 250,
      span: 12,
      overHidden: true,
      search: true,
    },
    {
      label: '服务金额',
      prop: 'totalPrice',
      width: 120,
      span: 12,
    },
    {
      label: '服务内容',
      span: 24,
      type: 'textarea',
      prop: 'serviceDetail',
    },
    {
      label: '服务附件',
      prop: 'serviceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '服务状态',
      prop: 'paymentStatus',
      type: 'select',
      display: false,
      dicData: [
        {
          label: '新增',
          value: 0,
        },
        {
          label: '已完成',
          value: 1,
        },
        {
          label: '已收款',
          value: 4,
        },
        {
          label: '已退款',
          value: 2,
        },
        {
          label: '已取消',
          value: 3,
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/serviceOrder/add';
const delUrl = '/api/vt-admin/serviceOrder/delete?ids=';
const updateUrl = '/api/vt-admin/serviceOrder/edit';
const tableUrl = '/api/vt-admin/serviceOrder/myPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    serviceFiles: form.serviceFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    serviceFiles: row.serviceFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'edit') {
    form.value.serviceFiles = form.value.attachList?.map(item => {
      return {
        value: item.id,
        label: item.originalName,
      };
    });
  }
  done();
}
function cancel(row) {
  proxy
    .$confirm('确认取消此服务工单?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/serviceOrder/cancel', { id: row.id }).then(res => {
        proxy.$message.success('操作成功');
      });
    });
}
function pay(row) {
  proxy.$refs.dialogForm.show({
    title: '确认工单完成',
    option: {
      column: [
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/serviceOrder/payment', {
          ...res.data,
          id: row.id,
        })
        .then(r => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function confirm(row) {
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          type: 'number',
          label: '收款金额',
          controls: true,
          span: 12,
          display: true,
          disabled: true,
          value: row.totalPrice,
          prop: 'actualPrice',
        },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          value: row.paymentTime,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDate',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        {
          type: 'select',
          label: '收款账号',
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/serviceOrder/confirm', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function handleCustomerIdChange(id) {
  if (!id) return;
  setUrl(id);
  setBaseInfo(id);
}
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const mainPerson = res.data.data.customerContactVO?.id;
      const concatPhone = res.data.data.customerContactVO?.phone;
      form.value.customerConcat = mainPerson;
      form.value.concatPhone = concatPhone;
    });
}
function setUrl(id) {
  const customerConcat = proxy.findObject(option.value.column, 'customerConcat');
  customerConcat.params.Url = '/vt-admin/customerContact/page?customerId=' + id;
}
function handleContactChange({ value: id }) {
  axios.get('/api/vt-admin/customerContact/detail?id=' + id).then(res => {
    form.value.concatPhone = res.data.data.phone;
  });
}
</script>

<style lang="scss" scoped></style>
