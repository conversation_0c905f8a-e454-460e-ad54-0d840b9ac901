import request from '@/axios';


export const getList = (current, size, params, deptId) => {
    return request({
      url: '/blade-system/user/page',
      method: 'get',
      params: {
        ...params,
        current,
        size,
        deptId,
      },
    });
  };
  export const grant = (userId, meunList, selectedPermissions) => {
    return request({
      url: '/blade-system/userMenu/updateMenu',
      method: 'post',
      data: {
        userId,
        meunList,
        selectedPermissions,
      },
    });
  };
  export const getRole = userId => {
    return request({
      url: '/blade-system/userMenu/user-tree-keys',
      method: 'get',
      params: {
        userId,
      },
    });
  };

  export const grantTree = () => {
    return request({
      url: '/blade-system/menu/grant-tree',
      method: 'get',
    });
  };