<template>
  <avue-form :option="option" v-model="form" v-if="form.id"> 
    <template #reportStatus="{ row }">
        <el-tag effect="plain" v-if="row.reportStatus == 1" size="small" type="success"
          >报备中</el-tag
        >
        <!-- <el-tag effect='plain' v-if="row.reportStatus == 0" size="small" type="info">{{row.$reportStatus}}</el-tag> -->

        <el-tag effect="plain" v-if="row.reportStatus == 2" size="small" type="danger">{{
          row.$reportStatus
        }}</el-tag>

        <el-tag
          effect="plain"
          v-if="row.reportStatusType == 0"
          style="margin-left: 5px"
          size="small"
          type="success"
          >成交</el-tag
        >
        <el-tag
          effect="plain"
          v-if="row.reportStatusType == 1"
          style="margin-left: 5px"
          size="small"
          type="danger"
          >失单</el-tag
        >
        <el-tag
          effect="plain"
          style="margin-left: 5px"
          v-if="row.reportStatusType == 2"
          size="small"
          type="warning"
          >过期</el-tag
        >
      </template>
      <template #reportFiles="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
  </avue-form>
  <el-empty v-else :description="'该商机无需报备'"></el-empty>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import { followType } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed, onMounted } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();
const props = defineProps(['id']);
let userInfo = computed(() => store.getters.userInfo);
const option = ref({
  labelWidth: 140,
  detail: true,
  submitBtn: false,
  emptyBtn: false,
  column: [
    
    {
      label: '厂家名称',
      prop: 'manufacturer',
      width: 250,
      overHidden: true,
      search: true,
      rules: [
        {
          required: true,
          message: '请输入厂家名称',
          trigger: 'blur',
        },
      ],
    },

  
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
    },
    {
      label: '报备方式',
      prop: 'reportMethod',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '报备方式不能为空',
          trigger: 'change',
        },
      ],
      dicUrl: '/blade-system/dict/dictionary?code=reportingMethods',
      overHidden: true,
    },
    {
      label: '报备平台',
      prop: 'reportPlatform',
      overHidden: true,
    },
    {
      label: '报备人',
      prop: 'reportName',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
    },
    {
      label: '报备时间',
      prop: 'reportTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择报备时间',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '报备有效期(天)',
      prop: 'reportValidity',
      overHidden: true,
      type: 'number',
      hide: true,
      rules: [
        {
          required: true,
          message: '请输入报备有效期',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '离有效期还剩(天)',
      prop: 'days',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      width: 140,
      html: true,
      formatter: row => {
        if (row.days == null || (row.reportStatus == 2 && row.reportStatusType != 2)) {
          return '';
        }
        return `<div ><span style="color: ${
          row.days > 7
            ? 'var(--el-color-success)'
            : row.days <= 7 && row.days >= 0
            ? 'var(--el-color-warning)'
            : 'var(--el-color-danger)'
        }" class='days'>${row.days}</span><span>${
          row.days || row.days == 0 ? '天' : ''
        }</span></div>`;
      },
    },
    {
      label: '报备状态',
      prop: 'reportStatus',
      overHidden: true,
      search: true,
      width: 140,
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      dicData: [
        // {
        //   value: 0,
        //   label: '未报备',
        // },
        {
          value: 1,
          label: '报备中',
        },
        {
          value: 2,
          label: '报备失效',
        },
      ],
    },
    {
      label: '附件',
      prop: 'reportFiles',
      type: 'upload',
      dataType: 'object',
      overHidden: true,
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
  ],
});
onMounted(() => {
  
    getDetail();

});
function getDetail() {
  axios
    .get('/api/vt-admin/businessOpportunityReport/detailByBusinessOpportunityId?id=' + props.id)
    .then(res => {
      form.value = {
        ...res.data.data,
      };
    });
}
</script>

<style lang="scss" scoped></style>
