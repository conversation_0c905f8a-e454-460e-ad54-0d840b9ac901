<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #faultStatus="{ row }">
        <el-tag
          :type="
            row.faultStatus == 0 || row.faultStatus == 1
              ? 'primaru'
              : row.faultStatus == 2
              ? 'success'
              : 'danger'
          "
          effect="plain"
          >{{ row.$faultStatus }}</el-tag
        >
      </template>
      <template #menu="{ row }">
        <el-button @click="deal(row)" type="primary" icon="edit" :disabled="row.faultStatus == 3" text>处理</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { ElMessage } from 'element-plus';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  column: [
    {
      label: '客户名称',
      prop: 'companyName',
      width: 250,
      overHidden: true,
      search: true,
      component:'wf-customer-drop'
    },
    {
      label: '产品SN',
      prop: 'serialNumber',
    },
    {
      label: '故障描述',
      prop: 'faultDescription',
    },
    {
      label: '故障图片',
      prop: 'faultPictures',
      type: 'upload',
    },
    {
      label: '联系人',
      prop: 'personName',
      width: 110,
    },
    {
      label: '联系电话',
      prop: 'phone',
      width: 110,
    },
    {
      label: '维修状态',
      prop: 'faultStatus',
      type: 'select',
      search:true,
      dicData: [
        {
          label: '待维修',
          value: 0,
        },
        {
          label: '维修中',
          value: 1,
        },
        {
          label: '已维修',
          value: 2,
        },
        {
          label: '无法维修',
          value: 3,
        },
      ],
      width: 110,
    },
    {
      label: '下单时间',
      prop: 'createTime',
    //   search: true,
      searchSpan: 6,
      searchRange: true,
      
      type: 'date',
      format: 'YYYY-MM-DD',
      width: 110,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/vt-admin/faultRepair/page';
let params = ref({
    faultStatus:0
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function deal(item) {
  proxy.$refs.dialogForm.show({
    title: '工单处理',
    option: {
      column: [
        {
          label: '处理结果',
          prop: 'faultStatus',
          type: 'radio',
          dicData: [
            {
              value: 1,
              label: '维修中',
              disabled: item.faultStatus == 1,
            },
            {
              value: 2,
              label: '维修完成',
              disabled: item.faultStatus != 1,
            },
            {
              value: 3,
              label: '无法维修',
            },
          ],
          control:val =>{
            return {
                remark :{
                   display: val == 3
                }
            }
          }
        },
        {
            label:"备注",
            prop:"remark",
            type:"textarea",
            span:24,
          
        }
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/faultRepair/deal', {
          ...res.data,
          id: item.id,
        })
        .then(r => {
          ElMessage.success('操作成功');
          onLoad();
          res.close();
        });
    },
  });
}
</script>

<style lang="scss" scoped></style>
