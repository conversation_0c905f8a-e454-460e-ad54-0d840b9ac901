<template>
    <div>
      <el-table class="avue-crud" :data="productListBf" border align="center">
        <el-table-column label="设备名称" show-overflow-tooltip prop="productName"></el-table-column>
        <el-table-column label="规格型号" show-overflow-tooltip prop="productSpecification"></el-table-column>
        <el-table-column label="产品图片" #default="{ row }">
          <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
        </el-table-column>
        <el-table-column
          label="产品描述"
          show-overflow-tooltip
          width="200"
          prop="description"
        ></el-table-column>
        <el-table-column label="品牌" prop="productBrand"></el-table-column>
        <el-table-column label="单位" prop="unitName"></el-table-column>
        <el-table-column label="数量" #default="{ row }" prop="number"> </el-table-column>
        <el-table-column label="入库数量" #default="{ row }" prop="arriveNumber"> </el-table-column>
        <el-table-column label="单价" #default="{ row }" prop="unitPrice">
          <span>{{ row.unitPrice }}</span>
        </el-table-column>
        <el-table-column label="金额" #default="{ row }" prop="totalPrice">
          {{ row.unitPrice ? row.number * row.unitPrice : '---' }}
        </el-table-column>
      
      </el-table>
    </div>
  </template>
  
  <script setup>
  import { computed } from 'vue';
  const props = defineProps({
    // 父组件传递过来的值
    productList: {
        type:Array,
        default:() => []
    },
  });
  
  </script>
  
  <style lang="scss" scoped></style>
  