<template>
  <el-select
    size="small"
    placeholder="根据名称，品牌，型号快捷搜索产品"
    filterable
    remote
    clearable
    style="width: 300px"
    :remote-method="remoteMethod"
    v-model="currentProduct"
    @focus="focus"
    @change="select"
  >
    <el-option
      v-for="item in productList"
      :key="item.id"
      :label="item.productName"
      :value="item.id"
    >
      <div class="productBox">
        <div><span>名称：</span>{{ item.productName }}</div>
        <div><span>品牌：</span>{{ item.productBrand }}</div>
        <div><span>型号：</span>{{ item.productSpecification }}</div>
      </div>
    </el-option>
  </el-select>
</template>

<script setup>
import { ref } from 'vue';
const currentProduct = ref('');
let productList = ref([]);
const emits =defineEmits(['select','focus'])
function remoteMethod(val) {
  if (!val) return;
  axios
    .get('/api/vt-admin/product/list', {
      params: {
        keys: val,
        size: 100,
      },
    })
    .then(res => {
      productList.value = res.data.data.records;
      // console.log(res.data.data);
    });
}
function select(val) {
    if(!val) return 
   emits('select',val)
   currentProduct.value = ''
   productList.value = []
}
function focus() {
    emits('focus')
}
</script>

<style lang="scss" scoped>
    .productBox div{
        height: 23px;
        span {
            color:var(--el-color-primary);
            font-weight: bold;
        }
    }
    :deep(.el-select-dropdown__item_hover){
        height: 70px!important;
    }
    ::v-deep .el-select-dropdown__item.hover, .el-select-dropdown__item:hover{
        height: 73px!important;}
</style>
