<template>
  <basic-container style="height: 100%">
    <Title v-if="!isBusenissDetail"
      >{{ isEdit ? '编辑方案' : '方案详情' }}
      <template #foot>
        <el-button
          type="primary"
          @click="draft"
          v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
          plain
          >保存草稿</el-button
        >
        <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
          >保存草稿并生成报价单</el-button
        > -->
        <el-button
          type="primary"
          @click="draft('confirm')"
          v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
          >保存并提交</el-button
        >
        <!-- <el-button
          type="primary"
          @click="handleEdit"
          v-if="
            !isEdit &&
            form.optionStatus != 2 &&
            !props.businessPerson &&
            route.query.isAdmin != 'admin'
          "
          >编辑</el-button
        > -->
        <el-button
          type="primary"
          @click="adminSubmit"
          v-if="isEdit && route.query.isAdmin == 'admin'"
          >保存</el-button
        >

        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          v-if="!props.businessPerson"
          >关闭</el-button
        >
      </template></Title
    >
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
      <template #baseInfo-header="column">
        <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
        <el-button @click.stop="viewBusiness" type="primary" v-if="!isBusenissDetail" size="small"
          >商机详情</el-button
        >
      </template>
    </avue-form>
    <div>
      <el-tabs
        v-model="editableTabsValue"
        type="card"
        :before-leave="handleTabsLeave"
        class="demo-tabs"
        style="margin-top: 10px"
        @edit="handleTabsEdit"
      >
        <el-tab-pane
          v-for="(item, index) in form.moduleDTOList"
          :key="item.moduleName"
          :label="item.moduleName"
          :name="index"
        >
          <template #label>
            <span style="display: flex; align-items: center">
              <el-icon
                @click="editModuleName(item)"
                v-if="index !== 0 && isEdit"
                style="margin-right: 5px"
                ><edit
              /></el-icon>
              <span>{{ item.moduleName }}</span>
              <el-icon
                @click="handleTabsEdit(index, 'edit')"
                v-if="index !== 0 && isEdit"
                style="margin-left: 5px"
                ><close
              /></el-icon>
            </span>
          </template>
          <el-affix v-if="index != 0" style="height: 50px" :offset="155" class="affix">
            <el-table
              show-header
              :row-class-name="tableRowClassName"
              :data="[]"
              border
              class="avue-crud"
            >
              <el-table-column label="基本信息" align="center">
                <el-table-column
                  type="index"
                  :index="val => customIndex(val, index1, item.detailDTOList)"
                  width="40"
                />
                <el-table-column
                  label="设备名称"
                  show-overflow-tooltip
                  prop="productName"
                ></el-table-column>
                <el-table-column
                  label="规格型号"
                  prop="productSpecification"
                  show-overflow-tooltip
                ></el-table-column>
                <el-table-column
                  label="商品描述"
                  class-name="allowDrag"
                  show-overflow-tooltip
                  prop="description"
                ></el-table-column>
                <el-table-column label="产品图片" #default="{ row }">
                  <el-image
                    style="width: 80px"
                    :z-index="1000000"
                    :preview-src-list="[row.coverUrl]"
                    :src="row.coverUrl"
                  ></el-image>
                </el-table-column>

                <el-table-column
                  width="100"
                  show-overflow-tooltip
                  label="品牌"
                  prop="productBrand"
                ></el-table-column>
                <el-table-column width="80" label="单位" prop="unitName"></el-table-column>
              </el-table-column>
              <el-table-column label="报价信息" align="center">
                <el-table-column width="80" label="数量" #default="{ row }" prop="number">
                  <el-input
                    v-if="isEdit"
                    v-model="row.number"
                    size="small"
                    style="width: 80%"
                  ></el-input>
                  <span v-else>{{ row.number }}</span>
                </el-table-column>
                <el-table-column width="100" label="单价" #default="{ row }" prop="sealPrice">
                  <el-input
                    v-if="isEdit"
                    v-model="row.sealPrice"
                    placeholder="请输入单价"
                    size="small"
                    style="width: 80%"
                  ></el-input>
                  <span v-else>{{ parseFloat(row.sealPrice).toLocaleString() }}</span>
                </el-table-column>
                <el-table-column
                  label="人工费"
                  #default="{ row }"
                  v-if="form.isNeedLabor"
                  prop="laborCost"
                >
                  <el-input
                    v-if="isEdit"
                    v-model="row.laborCost"
                    placeholder="请输入人工费"
                    size="small"
                    style="width: 80%"
                  ></el-input>
                  <span v-else>{{
                    parseFloat(row.laborCost).toLocaleString() == 'NaN'
                      ? ''
                      : parseFloat(row.laborCost).toLocaleString()
                  }}</span>
                </el-table-column>
                <el-table-column width="120" label="金额" #default="{ row }" prop="totalPrice">
                  {{
                    parseFloat(
                      row.number *
                        (row.sealPrice * 1 + (form.isNeedLabor == 1 ? row.laborCost * 1 : 0))
                    ).toLocaleString() || '---'
                  }}
                </el-table-column>
                <el-table-column
                  label="专项成本"
                  #default="{ row, $index }"
                  width="120"
                  v-if="form.isHasSpecialPrice == 1"
                  prop="specialCostPrice"
                >
                  <el-input
                    v-if="isEdit"
                    v-model="row.specialCostPrice"
                    placeholder="请输入专项成本"
                    size="small"
                    style="width: 100%"
                  ></el-input>
                  <span v-else>{{ row.specialCostPrice }}</span>
                </el-table-column>
                <el-table-column
                  label="专项供应商"
                  #default="{ row, $index }"
                  width="120"
                  v-if="form.isHasSpecialPrice == 1"
                  show-overflow-tooltip
                  prop="specialSupplierId"
                >
                  <WfSupplierSelect
                    v-if="isEdit"
                    v-model="row.specialSupplierId"
                    placeholder="请选择专项供应商"
                    size="small"
                    style="width: 100%"
                  ></WfSupplierSelect>
                  <span v-else>{{ row.specialSupplierName }}</span>
                </el-table-column>
                <el-table-column width="130" label="备注" show-overflow-tooltip  #default="{ row }" prop="number">
                  <el-input
                    v-if="isEdit"
                    v-model="row.remark"
                    size="small"
                    type="textarea"
                    autosize
                    style="width: 100%"
                  ></el-input>
                  <span v-else>{{ row.remark }}</span>
                </el-table-column>
              </el-table-column>
              <el-table-column label="参考信息" align="center">
                <el-table-column
                  width="100"
                  label="最近销售价"
                  #default="{ row }"
                  prop="preSealPrice"
                >
                  <span>{{
                    parseFloat(row.preSealPrice).toLocaleString() == 'NaN'
                      ? '--'
                      : parseFloat(row.preSealPrice).toLocaleString()
                  }}</span>
                </el-table-column>
                <el-table-column width="120" label="成本价" #default="{ row }" prop="costPrice">
                  <div v-if="!row.priceMaintenanceDays">
                    <div>
                      {{
                        parseFloat(row.costPrice).toLocaleString() == 'NaN'
                          ? '--'
                          : parseFloat(row.costPrice).toLocaleString()
                      }}
                    </div>
                  </div>
                  <div v-else>
                    <div
                      v-if="row.intervalDays < row.priceMaintenanceDays"
                      style="color: var(--el-color-success)"
                    >
                      {{
                        parseFloat(row.costPrice).toLocaleString() == 'NaN'
                          ? '--'
                          : parseFloat(row.costPrice).toLocaleString()
                      }}
                    </div>
                    <div
                      v-else-if="
                        row.intervalDays > row.priceMaintenanceDays &&
                        row.intervalDays - row.priceMaintenanceDays < 7
                      "
                      style="color: var(--el-color-warning)"
                    >
                      {{
                        parseFloat(row.costPrice).toLocaleString() == 'NaN'
                          ? '--'
                          : parseFloat(row.costPrice).toLocaleString()
                      }}
                    </div>
                    <div v-else style="color: var(--el-color-danger)">
                      {{
                        parseFloat(row.costPrice).toLocaleString() == 'NaN'
                          ? '--'
                          : parseFloat(row.costPrice).toLocaleString()
                      }}
                    </div>
                  </div>
                </el-table-column>
                <el-table-column
                  width="100"
                  label="最低销售价"
                  #default="{ row }"
                  prop="minSealPrice"
                >
                  <span>{{
                    parseFloat(row.minSealPrice).toLocaleString() == 'NaN'
                      ? '--'
                      : parseFloat(row.minSealPrice).toLocaleString()
                  }}</span>
                </el-table-column>
                <el-table-column
                  width="100"
                  label="参考销售价"
                  #default="{ row }"
                  prop="referSealPrice"
                >
                  <span>{{
                    parseFloat(row.referSealPrice).toLocaleString() == 'NaN'
                      ? '--'
                      : parseFloat(row.referSealPrice).toLocaleString()
                  }}</span>
                </el-table-column>
                <el-table-column width="100" label="市场价" #default="{ row }" prop="marketPrice">
                  <span>{{
                    parseFloat(row.marketPrice).toLocaleString() == 'NaN'
                      ? '--'
                      : parseFloat(row.marketPrice).toLocaleString()
                  }}</span>
                </el-table-column>
              </el-table-column>
              <el-table-column
                label="操作"
                align="center"
                prop=""
                width="200"
                #default="{ row, index }"
                v-if="isEdit"
              >
                <el-button
                  type="primary"
                  text
                  icon="delete"
                  @click="deleteProduct(i.productList, row)"
                  >删除</el-button
                >
              </el-table-column>
            </el-table>
          </el-affix>
          <div v-if="index !== 0">
            <el-collapse v-model="item.currentCollapse">
              <el-collapse-item :name="index" v-for="(i, index) in item.detailDTOList">
                <template #title>
                  <div style="display: flex; justify-content: center; align-items: center">
                    <el-text size="large" style="font-weight: bolder">{{ i.classify }}</el-text>
                    <el-icon @click.stop="editCategoryName(i)" v-if="isEdit" class="header-icon">
                      <edit />
                    </el-icon>

                    <el-button
                      type="primary"
                      style="margin-left: 10px"
                      icon="plus"
                      v-if="isEdit"
                      size="small"
                      @click.stop="addProduct(i.productList, i.classify)"
                      plain
                      >添加产品</el-button
                    >
                  </div>
                </template>

                <div>
                  <el-table
                    class="avue-crud"
                    :show-header="false"
                    :row-class-name="tableRowClassName"
                    :data="i.productList"
                    border
                    row-key="uuid"
                    show-summary
                    :summary-method="params => productSum(params, item, item.detailDTOList)"
                  >
                    <el-table-column label="基本信息" align="center">
                      <el-table-column
                        type="index"
                        :index="val => customIndex(val, index, item.detailDTOList)"
                        width="40"
                      />
                      <el-table-column label="设备名称" prop="customProductName">
                        <template #default="{ row }">
                          <el-popover
                            placement="top"
                            width="600"
                            effect="light"
                            trigger="click"
                            :content="row.productName"
                          >
                            <template #default>
                              <Title>原信息:</Title>
                              <el-form>
                                <el-form-item label="产品名称:">
                                  <span>{{ row.productName }}</span>
                                </el-form-item>
                                <el-form-item label="规格型号:">
                                  <span>{{ row.productSpecification }}</span>
                                </el-form-item>
                                <el-form-item label="产品描述:">
                                  <span>{{ row.description }}</span>
                                </el-form-item>
                              </el-form>
                            </template>
                            <template #reference>
                              <el-text style="cursor: pointer" type="primary">{{
                                row.customProductName || '---'
                              }}</el-text>
                            </template>
                          </el-popover></template
                        >
                      </el-table-column>
                      <el-table-column
                        label="规格型号"
                        prop="customProductSpecification"
                        show-overflow-tooltip
                      ></el-table-column>
                      <el-table-column
                        label="商品描述"
                        class-name="allowDrag"
                        show-overflow-tooltip
                        prop="customProductDescription"
                      ></el-table-column>
                      <el-table-column label="产品图片" #default="{ row }">
                        <el-image
                          style="width: 80px"
                          :z-index="1000000"
                          :preview-src-list="[row.coverUrl]"
                          :src="row.coverUrl"
                        ></el-image>
                      </el-table-column>

                      <el-table-column
                        width="100"
                        show-overflow-tooltip
                        label="品牌"
                        prop="productBrand"
                      ></el-table-column>
                      <el-table-column width="80" label="单位" prop="unitName"></el-table-column>
                    </el-table-column>
                    <el-table-column label="报价信息" align="center">
                      <el-table-column width="80" label="数量" #default="{ row }" prop="number">
                        <el-input
                          v-if="isEdit"
                          v-model="row.number"
                          size="small"
                          style="width: 80%"
                        ></el-input>
                        <span v-else>{{ row.number }}</span>
                      </el-table-column>
                      <el-table-column width="100" label="单价" #default="{ row }" prop="sealPrice">
                        <el-input
                          v-if="isEdit"
                          v-model="row.sealPrice"
                          placeholder="请输入单价"
                          size="small"
                          style="width: 80%"
                        ></el-input>
                        <span
                          :title="
                            row.sealPrice * 1 <= row.minSealPrice * 1
                              ? '该价格小于或等于最低销售价'
                              : ''
                          "
                          :class="{ warningInput: row.sealPrice * 1 <= row.minSealPrice * 1 }"
                          v-else
                          >{{ parseFloat(row.sealPrice).toLocaleString() }}</span
                        >
                      </el-table-column>
                      <el-table-column
                        label="人工费"
                        #default="{ row }"
                        v-if="form.isNeedLabor"
                        prop="laborCost"
                      >
                        <el-input
                          v-if="isEdit"
                          v-model="row.laborCost"
                          placeholder="请输入人工费"
                          size="small"
                          style="width: 80%"
                        ></el-input>
                        <span v-else>{{
                          parseFloat(row.laborCost).toLocaleString() == 'NaN'
                            ? ''
                            : parseFloat(row.laborCost).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        width="120"
                        label="金额"
                        #default="{ row }"
                        prop="totalPrice"
                      >
                        {{
                          parseFloat(
                            row.number *
                              (row.sealPrice * 1 + (form.isNeedLabor == 1 ? row.laborCost * 1 : 0))
                          ).toLocaleString() || '---'
                        }}
                      </el-table-column>
                      <el-table-column
                        label="专项成本"
                        #default="{ row, $index }"
                        width="120"
                        v-if="form.isHasSpecialPrice == 1"
                        prop="specialCostPrice"
                      >
                        <el-input
                          v-if="isEdit"
                          v-model="row.specialCostPrice"
                          placeholder="请输入专项成本"
                          size="small"
                          style="width: 100%"
                        ></el-input>
                        <span v-else>{{ row.specialCostPrice }}</span>
                      </el-table-column>
                      <el-table-column
                        label="专项供应商"
                        #default="{ row, $index }"
                        width="120"
                        v-if="form.isHasSpecialPrice == 1"
                        show-overflow-tooltip
                        prop="specialSupplierId"
                      >
                        <WfSupplierSelect
                          v-if="isEdit"
                          v-model="row.specialSupplierId"
                          placeholder="请选择专项供应商"
                          size="small"
                          style="width: 100%"
                        ></WfSupplierSelect>
                        <span v-else>{{ row.specialSupplierName }}</span>
                      </el-table-column>
                      <el-table-column label="备注" show-overflow-tooltip width="130" prop="remark"></el-table-column>
                    </el-table-column>
                    <el-table-column label="参考信息" align="center">
                      <el-table-column
                        width="100"
                        label="最近销售价"
                        #default="{ row }"
                        prop="preSealPrice"
                      >
                        <span>{{
                          parseFloat(row.preSealPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.preSealPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        width="120"
                        label="成本价"
                        #default="{ row }"
                        prop="costPrice"
                      >
                        <div v-if="!row.priceMaintenanceDays">
                          <div>
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                        </div>
                        <div v-else>
                          <div
                            v-if="row.intervalDays < row.priceMaintenanceDays"
                            style="color: var(--el-color-success)"
                          >
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                          <div
                            v-else-if="
                              row.intervalDays > row.priceMaintenanceDays &&
                              row.intervalDays - row.priceMaintenanceDays < 7
                            "
                            style="color: var(--el-color-warning)"
                          >
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                          <div v-else style="color: var(--el-color-danger)">
                            {{
                              parseFloat(row.costPrice).toLocaleString() == 'NaN'
                                ? '--'
                                : parseFloat(row.costPrice).toLocaleString()
                            }}
                          </div>
                        </div>
                      </el-table-column>
                      <el-table-column
                        width="100"
                        label="最低销售价"
                        #default="{ row }"
                        prop="minSealPrice"
                      >
                        <span>{{
                          parseFloat(row.minSealPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.minSealPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        width="100"
                        label="参考销售价"
                        #default="{ row }"
                        prop="referSealPrice"
                      >
                        <span>{{
                          parseFloat(row.referSealPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.referSealPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                      <el-table-column
                        width="100"
                        label="市场价"
                        #default="{ row }"
                        prop="marketPrice"
                      >
                        <span>{{
                          parseFloat(row.marketPrice).toLocaleString() == 'NaN'
                            ? '--'
                            : parseFloat(row.marketPrice).toLocaleString()
                        }}</span>
                      </el-table-column>
                    </el-table-column>
                    <el-table-column
                      label="操作"
                      align="center"
                      prop=""
                      width="200"
                      #default="{ row, index }"
                      v-if="isEdit"
                    >
                      <el-button
                        type="primary"
                        text
                        icon="delete"
                        @click="deleteProduct(i.productList, row)"
                        >删除</el-button
                      >
                    </el-table-column>
                  </el-table>
                </div>
              </el-collapse-item>
            </el-collapse>
            <el-form v-if="index !== 0 && isEdit">
              <el-form-item label="">
                <el-button
                  type="primary"
                  style="margin-top: 5px; width: 100%"
                  icon="plus"
                  size="small"
                  @click="addCategory(item)"
                  >新增分类</el-button
                >
              </el-form-item>
            </el-form>
            <el-form inline style="margin-top: 5px">
              <el-form-item label="服务费、人工费">
                <el-input v-if="isEdit" v-model="item.servicePrice" size="small"></el-input>
                <span v-else>￥{{ parseFloat(item.servicePrice).toLocaleString() }}</span>
              </el-form-item>
              <!-- <el-form-item label="人工费">
                <el-input v-if="isEdit" v-model="item.constructionPrice" size="small"></el-input>
                <span v-else>￥{{ parseFloat(item.constructionPrice).toLocaleString() }}</span>
              </el-form-item> -->
              <el-form-item label="税费">
                <el-input v-if="isEdit" v-model="item.taxPrice" size="small"></el-input>
                <span v-else>{{ parseFloat(item.taxPrice).toLocaleString() }}</span>
              </el-form-item>
              <el-form-item label="总计">
                <span style="color: var(--el-color-danger)">
                  ￥{{ parseFloat(moduleTotal(item)).toLocaleString() }}</span
                ><span v-if="form.isNeedLabor == 1"
                  >(含人工<span style="color: var(--el-color-danger)"
                    >￥{{ parseFloat(totalLaborCost(item)).toLocaleString() }}</span
                  >
                  )</span
                >
              </el-form-item>
              <el-form-item label="成本总计">
                <span style="color: var(--el-color-danger)">
                  ￥{{ parseFloat(moduleCostTotal(item)).toLocaleString() }}</span
                >
              </el-form-item>
            </el-form>
          </div>
          <el-empty
            v-else-if="index == 0 && form.moduleDTOList.length <= 1"
            description="请先添加子项"
          ></el-empty>
          <el-table
            class="avue-crud"
            v-else
            :data="form.moduleDTOList.filter((item, index) => index !== 0)"
            show-summary
            border
            :summary-method="totalSum"
          >
            <el-table-column label="子项名称" prop="moduleName" #default="{ row }">
              <el-button
                type="primary"
                @click="
                  editableTabsValue = form.moduleDTOList.findIndex(
                    item => item.moduleName == row.moduleName
                  )
                "
                text
                >{{ row.moduleName }}</el-button
              >
            </el-table-column>
            <el-table-column label="服务费、人工费" #default="{ row }" prop="servicePrice">
              <span>{{ parseFloat(row.servicePrice).toLocaleString() }}</span>
            </el-table-column>
            <!-- <el-table-column label="施工费" #default="{ row }" prop="constructionPrice">
              <span>{{ parseFloat(row.constructionPrice).toLocaleString() }}</span>
            </el-table-column> -->
            <el-table-column label="税费" #default="{ row }" prop="taxPrice">
              <span>{{ parseFloat(row.taxPrice).toLocaleString() }}</span>
            </el-table-column>
            <el-table-column label="数量" #default="{ row }" prop="number">
              {{ totalNumber(row) }}
            </el-table-column>
            <el-table-column label="总计" #default="{ row }" prop="totalPrice">
              {{ parseFloat(totalPrice(row)).toLocaleString()
              }}<span v-if="form.isNeedLabor == 1"
                >(含人工：{{ parseFloat(totalLaborCost(row)).toLocaleString() }})</span
              >
            </el-table-column>
            <el-table-column label="成本总计" #default="{ row }" prop="totalCostPrice">
              {{ parseFloat(totalCostPrice(row)).toLocaleString() }}
            </el-table-column>
           
          </el-table>
        </el-tab-pane>
        <el-tab-pane name="CustoBtn" v-if="isEdit" :closable="false">
          <template #label>
            <el-button
              icon="plus"
              circle
              plain
              type="primary"
              @click="handleTabsEdit(undefined, 'add')"
            ></el-button>
          </template>
        </el-tab-pane>
      </el-tabs>
    </div>
    <!-- <div v-else>
        <el-form>
          <el-form-item label="">
            <el-button
              type="primary"
              icon="plus"
              size="small"
              
              plain
              >新增</el-button
            >
          </el-form-item>
        </el-form>
  
        <el-table :data="[]" border show-summary>
          <el-table-column label="设备名称" prop="productName"></el-table-column>
          <el-table-column label="规格型号" prop="productSpecification"></el-table-column>
          <el-table-column label="产品图片" #default="{ row }">
            <img style="width: 200px; margin-right: 20px" :src="row.link" />
          </el-table-column>
          <el-table-column
            label="产品描述"
            show-overflow-tooltip
            width="200"
            prop="description"
          ></el-table-column>
          <el-table-column label="品牌" prop="productBrand"></el-table-column>
          <el-table-column label="单位" prop="unitName"></el-table-column>
          <el-table-column label="数量" prop="number"></el-table-column>
          <el-table-column label="操作" align="center" prop="" width="200" #default="{ row, index }">
            <el-button type="primary" text icon="edit" @click="editProduct(i.productList, row, index)"
              >编辑</el-button
            >
            <el-button type="primary" text icon="delete" @click="deleteProduct(i.productList, row)"
              >删除</el-button
            >
          </el-table-column>
        </el-table>
      </div> -->
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select ref="product-select" check-type="box" @onConfirm="handleUserSelectConfirm">
    </wf-product-select>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" :with-header="false" size="60%">
      <Title>{{ businessForm.name }}</Title>
      <BusinessDetail :form="businessForm" :isEdit="false"></BusinessDetail>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import { ref, getCurrentInstance, onBeforeUnmount, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import BusinessDetail from '@/views/CRM/businessOpportunity/detail/baseInfo.vue';
import { randomLenNum } from '@/utils/util';
const route = useRoute();
const router = useRouter();
let isEdit = ref(route.query.type == 'edit' || route.query.type == 'add');
let form = ref({
  moduleDTOList: [
    {
      moduleName: '汇总',
    },
  ],
});
const isDeep = ref(route.query.deep == '1');
const props = defineProps({
  stageStatus: {
    type: Number,
    default: -1,
  },
  businessPerson: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  isBusenissDetail: {
    type: Boolean,
    default: false,
  },
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
// onMounted(() => {
//   console.log(111);
//   if(route.query.id){
//     getDetail();
//   }
// })
let option = ref({
  submitBtn: false,
  labelWidth: 100,
  detail: !isEdit.value,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      column: [
        {
          label: '方案名称',
          prop: 'optionName',
          rules: [
            {
              required: true,
              message: '请填写方案名称',
            },
          ],
        },
        {
          label: '客户名称',
          prop: 'customerName',
          readonly: true,
        },
        {
          label: '关联联系人',
          prop: 'concatName',
          readonly: true,
        },
        {
          label: '人工费',
          prop: 'isNeedLabor',
          readonly: true,
          type: 'switch',
          span: 4,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '自定义',
          prop: 'isHasCustom',
          readonly: true,
          type: 'switch',
          span: 4,
          dicData: [
            {
              value: 0,
              label: '否',
            },
            {
              value: 1,
              label: '是',
            },
          ],
        },
        {
          label: '专项报价',
          prop: 'isHasSpecialPrice',
          type: 'radio',
          span: 4,
          value: 0,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        // {
        //   label: '方案日期',
        //   prop: 'optionDate',
        //   type: 'date',
        //   readonly: true,
        //   // format: 'YYYY-MM-DD',
        //   // valueFormat: 'YYYY-MM-DD',
        // },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span:24,
         
          // format: 'YYYY-MM-DD',
          // valueFormat: 'YYYY-MM-DD',
        },
      ],
    },
  ],
});
let { proxy } = getCurrentInstance();
let tabIndex = 2;
const editableTabsValue = ref(0);

let tableOption = ref({
  header: false,
  menuLeft: false,
  border: true,
  column: [
    {
      label: '产品名称',
    },
  ],
});
function handleTabsEdit(tabName, action) {
  if (action == 'add') {
    proxy.$refs.dialogForm.show({
      title: '添加子项',
      option: {
        column: [
          {
            label: '子项名称',

            prop: 'moduleName',
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        form.value.moduleDTOList.push({
          moduleName: res.data.moduleName,
          remark: res.data.remark,
          currentCollapse: [0],
          taxPrice: 0,
          constructionPrice: 0,
          servicePrice: 0,
          detailDTOList: [
            {
              classify: '默认分类',
              productList: [],
            },
          ],
        });
        editableTabsValue.value = form.value.moduleDTOList.length - 1;
        res.close();
      },
    });
  } else {
    if (tabName == 0) return;
    proxy
      .$confirm('删除该子项将删除下面所有产品，确定删除?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        form.value.moduleDTOList = form.value.moduleDTOList.filter(
          (item, index) => index !== tabName
        );
      });
  }
}
let classify = ref('');
let productList = ref('');
function addProduct(productListBf, classifyName) {
  classify.value = classifyName;
  productList.value = productListBf;
  proxy.$refs['product-select'].visible = true;
}
function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      productList.value.push({
        ...r.data.data,
        productId: r.data.data.id,
        preSealPrice: r.data.data.sealPrice,
        sealPrice: 0.0,
        number: 1,
        laborCost: r.data.data.laborCost,
        id: null,
        classify: classify.value,
        uuid: randomLenNum(10, true),
      });
    });
  });
}
function editProduct(productList, row, i) {
  proxy.$refs.dialogForm.show({
    title: '编辑产品',
    option: {
      column: [
        {
          label: '选择产品',
          component: 'wf-product-select',
          prop: 'productId',
          value: row.productId,
          disabled: true,
          span: 24,
        },
        {
          label: '数量',
          type: 'number',
          prop: 'number',
          span: 24,
          value: row.number,
        },
      ],
    },
    callback(res) {
      row.number = res.data.number;
      res.close();
    },
  });
}
// function summary(params) {
//   const { columns, data } = param
//   columns.map((item,index) => {
//     if(index == 0){
//       return '合计'
//     }else if(index == )
//   })
// }
function getDetail() {
  let url = '/api/vt-admin/businessOpportunityOption/detailByOptionId';
  if (isDeep.value) {
    url = '/vt-admin/businessOpportunityOptionHistory/detailByOptionId';
  }
  if (route.query.isAdmin || route.query.isHistory) {
    url = '/api/vt-admin/businessOpportunityOptionHistory/detail';
  }
  axios
    .get(url, {
      params: {
        id: props.id || route.query.id,
      },
    })
    .then(res => {
      const data = parseData(res.data.data);
      form.value = data;
    });
}
function deleteProduct(list, row) {
  proxy
    .$confirm('确定删除该产品?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const indexToRemove = list.findIndex(item => row.uuid === item.uuid);
      list.splice(indexToRemove, 1);
    });
}
function editCategoryName(i) {
  proxy.$refs.dialogForm.show({
    title: '修改分类名称',
    option: {
      column: [
        {
          label: '分类名称',
          value: i.classify,
          prop: 'classify',
          span: 24,
        },
      ],
    },
    callback(res) {
      i.classify = res.data.classify;
      res.close();
    },
  });
}
function editModuleName(i) {
  proxy.$refs.dialogForm.show({
    title: '修改子项名称',
    option: {
      column: [
        {
          label: '子项名称',
          value: i.moduleName,
          prop: 'moduleName',
          span: 24,
        },
      ],
    },
    callback(res) {
      i.moduleName = res.data.moduleName;
      res.close();
    },
  });
}
function addCategory(item) {
  proxy.$refs.dialogForm.show({
    title: '添加分类',
    option: {
      column: [
        {
          label: '分类名称',
          prop: 'categoryName',
          span: 24,
        },
      ],
    },
    callback(res) {
      item.detailDTOList.push({
        classify: res.data.categoryName,
        productList: [],
      });
      item.currentCollapse.push(item.detailDTOList.length - 1);
      res.close();
    },
  });
}
function formatData() {
  let data = {
    ...form.value,

    moduleDTOList: form.value.moduleDTOList
      .filter((item, index) => index !== 0)
      .map(i => {
        return {
          ...i,
          detailDTOList: i.detailDTOList.reduce((pre, cur) => {
            return pre.concat(
              cur.productList.map(item => {
                return {
                  ...item,
                  classify: cur.classify,
                  unitPrice: item.purchasePrice,
                  laborCost: form.value.isNeedLabor == 1 ? item.laborCost : 0,
                  uuid: randomLenNum(10, true),
                };
              })
            );
          }, []),
        };
      }),
  };
  return data;
}
let isSubmit = ref(false);
function draft(type) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        submit(type, done);
      });
    }
  });
}
function submit(type, done) {
  let data = formatData();
  if (data.id) {
    axios
      .post('/api/vt-admin/businessOpportunityOption/update', {
        ...data,
        optionStatus: type == 'confirm' ? 2 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();
        done();
        isEdit.value = false;
        option.value.detail = true;
      });
  } else {
    data = {
      ...data,
      businessOpportunityId: route.query.id,
      optionStatus: type == 'confirm' ? 2 : 0,
    };
    axios.post('/api/vt-admin/businessOpportunityOption/save', data).then(res => {
      proxy.$message.success(res.data.msg);
      getDetail();
      done();
      isEdit.value = false;
      option.value.detail = true;
    });
  }
}
function totalNumber(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p += c.number * 1;
      return p;
    }, 0);
    return pre;
  }, 0);
}
function totalPrice(row) {
  return (
    row.detailDTOList.reduce((pre, cur) => {
      pre += cur.productList.reduce((p, c) => {
        // p += c.number * (c.sealPrice * 1);
        p += c.number * (c.sealPrice * 1 + (form.value.isNeedLabor == 1 ? c.laborCost * 1 : 0));
        return p;
      }, 0);
      return pre;
    }, 0) +
    // row.taxPrice * 1 +
    row.servicePrice * 1
    // row.constructionPrice * 1
  );
}
function totalCostPrice(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p +=
        c.number *
        (form.value.isHasSpecialPrice == 1 && c.specialCostPrice
          ? c.specialCostPrice * 1
          : c.costPrice * 1);
      // p += c.number * (c.sealPrice * 1);
      return p;
    }, 0);
    return pre;
  }, 0);
  // row.taxPrice * 1 +
  // row.servicePrice * 1
  // row.constructionPrice * 1
}
function totalLaborCost(row) {
  return row.detailDTOList.reduce((pre, cur) => {
    pre += cur.productList.reduce((p, c) => {
      p += c.number * c.laborCost * 1;
      // p += c.number * (c.sealPrice * 1);
      return p;
    }, 0);
    return pre;
  }, 0);
  // row.taxPrice * 1 +
  // row.servicePrice * 1
  // row.constructionPrice * 1
}
function parseData(resData) {
  const data = {
    ...resData,
    // optionDate: resData.optionDate || new Date(),
    moduleDTOList: [
      {
        moduleName: '汇总',
      },
      ...((resData.moduleVOList &&
        resData.moduleVOList.map(item => {
          return {
            ...item,
            currentCollapse: item.detailVOList.map((item, index) => index),
            detailDTOList: item.detailVOList.reduce((result, item) => {
              const existingEntry = result.find(entry => entry.classify === item.classify);
              if (existingEntry) {
                existingEntry.productList.push({
                  ...item.product,
                  ...item,
                  costPrice: resData.auditStatus == 2 ? item.costPrice : item.product.costPrice,
                  preSealPrice:
                        resData.auditStatus == 2
                          ? item.sealPrice
                          : item.product && item.product.sealPrice,
                      minSealPrice:
                        resData.auditStatus == 2
                          ? item.minSealPrice
                          : item.product && item.product.minSealPrice,
                      referSealPrice:
                        resData.auditStatus == 2
                          ? item.referSealPrice
                          : item.product && item.product.referSealPrice,
                  uuid: randomLenNum(10, true),
                });
              } else {
                result.push({
                  classify: item.classify,
                  productList: [
                    {
                      ...item.product,
                      ...item,
                      costPrice: resData.auditStatus == 2 ? item.costPrice : item.product.costPrice,
                      preSealPrice:
                        resData.auditStatus == 2
                          ? item.sealPrice
                          : item.product && item.product.sealPrice,
                      minSealPrice:
                        resData.auditStatus == 2
                          ? item.minSealPrice
                          : item.product && item.product.minSealPrice,
                      referSealPrice:
                        resData.auditStatus == 2
                          ? item.referSealPrice
                          : item.product && item.product.referSealPrice,
                      sealPrice: item.sealPrice * 1,
                      uuid: randomLenNum(10, true),
                    },
                  ],
                });
              }
              return result;
            }, []),
          };
        })) ||
        []),
    ],
    optionName: route.query.type == 'edit' ? route.query.name + '方案' : resData.optionName,
  };

  return data;
}
function productSum(param, i, allData) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => Number(item[column.property]));

      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'prePrice') {
      // const values = data.map(item => Number(item[column.property]));
      // sums[index] = `￥ ${values.reduce((prev, curr) => {
      //   const value = Number(curr);
      //   if (!Number.isNaN(value)) {
      //     return prev + curr;
      //   } else {
      //     return prev;
      //   }
      // }, 0)}`
    } else if (column.property == 'totalPrice') {
      const values = data.map(item =>
        Number(
          item.number *
            (item.sealPrice * 1 + (form.value.isNeedLabor == 1 ? item.laborCost * 1 : 0))
        )
      );
      const total = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);

      setConstructionPrice(allData, i);
      sums[index] = `￥ ${parseFloat(total).toLocaleString()}`;
    } else if (column.property == 'costPrice') {
      const values = data.map(item => {
        return Number(
          item.number *
            (form.value.isHasSpecialPrice == 1 && item.specialCostPrice
              ? item.specialCostPrice * 1
              : item.costPrice * 1)
        );
      });

      const total = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);

      sums[index] = `￥ ${parseFloat(total).toLocaleString()}`;
    }
  });

  return sums;
}
function setConstructionPrice(data, i) {
  const allValueArr = data.map(item => {
    const arr = item.productList.map(item =>
      Number(item.number * (form.value.isNeedLabor == 1 ? item.laborCost * 1 : 0))
    );
    const value = arr.reduce((prev, curr) => {
      const value = Number(curr);
      if (!Number.isNaN(value)) {
        return prev + curr;
      } else {
        return prev;
      }
    }, 0);
    return value;
  });

  i.constructionPrice = allValueArr.reduce((prev, curr) => {
    const value = Number(curr);
    if (!Number.isNaN(value)) {
      return prev + curr;
    } else {
      return prev;
    }
  }, 0);
}
function totalSum(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => totalNumber(item));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'taxPrice') {
      const values = data.map(item => Number(item[column.property]));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'servicePrice') {
      const values = data.map(item => Number(item[column.property]));
      sums[index] = `￥ ${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'constructionPrice') {
      const values = data.map(item => Number(item[column.property]));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'totalPrice') {
      const values = data.map(item => totalPrice(item));
      const allPrice = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      form.value.allPrice = allPrice;
      sums[index] = `￥ ${parseFloat(allPrice).toLocaleString()}  `;
    } else if (column.property == 'totalCostPrice') {
      const values = data.map(item => totalCostPrice(item));
      const allPrice = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      // form.value.allPrice = allPrice;
      sums[index] = `￥ ${parseFloat(allPrice).toLocaleString()}  `;
    }
  });

  return sums;
}
function tableRowClassName({ row }) {
  if (row.isNew === 1) {
    return 'warning-row';
  } else {
    return '';
  }
}
function moduleTotal(item) {
  return totalPrice(item);
}
function moduleCostTotal(item) {
  return totalCostPrice(item);
}
function handleEdit() {
  isEdit.value = true;
  option.value.detail = false;
}
function handleTabsLeave(activeName) {
  if (activeName == 'CustoBtn') {
    return false;
  }
}
function adminSubmit(params) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        let data = formatData();

        axios
          .post('/api/vt-admin/businessOpportunityOption/updateOption', {
            ...data,
          })
          .then(res => {
            proxy.$message.success(res.data.msg);
            getDetail();
            done();
            isEdit.value = false;
            option.value.detail = true;
          });
      });
    }
  });
}
let businessOpportunityId = ref(route.query.id);
// onBeforeUnmount(() => {
//   console.log(route.query.id);
//   let data = formatData();
//   if (data.id) {
//     axios
//       .post('/api/vt-admin/businessOpportunityOption/update', {
//         ...data,
//         optionStatus: 0,
//       })
//       .then(res => {});
//   } else {
//     data = {
//       ...data,
//       businessOpportunityId: businessOpportunityId.value,
//       optionStatus: 0,
//     };
//     axios.post('/api/vt-admin/businessOpportunityOption/save', data).then(res => {});
//   }
// });

let drawer = ref(false);
let businessForm = ref({});
function viewBusiness(params) {
  drawer.value = true;
  getBusinessDetail();
}
function getBusinessDetail() {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id: route.query.businessOpportunityId || form.value.businessOpportunityId,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode } = res.data.data;
      businessForm.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        isProduct: res.data.data.productVOList && res.data.data.productVOList.length == 0 ? 0 : 1,
        productVOList:
          res.data.data.productVOList &&
          res.data.data.productVOList.map(item => {
            return {
              ...item,
              ...item.productVO,
            };
          }),
        // registeredCapital: Number(res.data.data.registeredCapital),
      };
    });
}
// 自定义序号
function customIndex(index, parentIndex, detailDTOList) {
  // 计算前面的所有数组总长度
  let sum = 0;
  for (let i = 0; i < parentIndex; i++) {
    sum += detailDTOList[i].productList.length;
  }
  // 返回序号
  index = sum + index + 1;

  return index;
}
</script>

<style scoped>
::v-deep .el-table .el-table__cell {
  padding: 0;
}
::v-deep .el-collapse-item__header {
  line-height: 33px;
}
::v-deep .el-form-item {
  /* margin-bottom: 5px; */
}
::v-deep .el-tabs__header {
  margin-bottom: 5px;
}
::v-deep .el-collapse-item__content {
  padding-bottom: 8px;
}
::v-deep .affix .el-table .el-table__empty-block {
  display: none !important;
}
::v-deep .affix .el-table__body-wrapper {
  display: none !important;
}
.warningInput {
  color: var(--el-color-danger);
}
</style>
