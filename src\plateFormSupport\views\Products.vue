<template>
  <div class="products-page py-20">
    <div class="container mx-auto px-4">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800 mb-2">产品库</h1>
        <p class="text-gray-600">浏览我们的完整产品系列，找到适合您的解决方案</p>
      </div>
      
      <div class="flex flex-col lg:flex-row gap-4">
        <!-- 左侧分类导航 -->
        <div class="lg:w-1/6">
          <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-lg font-semibold mb-4">产品分类</h2>
            <ul class="product-tree space-y-2">
              <li v-for="category in categories" :key="category.id">
                <div 
                  class="category-item flex items-center justify-between p-2 rounded cursor-pointer transition-colors duration-200"
                  :class="{ 'bg-blue-50 text-blue-600': selectedCategory === category.id }"
                  @click="handleCategoryClick(category)"
                >
                  <div class="flex items-center">
                    <span 
                      v-if="category.children && category.children.length > 0" 
                      class="mr-2 transition-transform duration-200"
                      :class="{ 'rotate-45': isCategoryExpanded(category.id) }"
                    >
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </span>
                    <span>{{ category.categoryName }}</span>
                  </div>
                </div>
                
                <!-- 使用递归组件显示子分类 -->
                <category-tree
                  v-if="category.children && category.children.length > 0 && isCategoryExpanded(category.id)"
                  :categories="category.children"
                  :selected-category="selectedCategory"
                  :expanded-categories="expandedCategories"
                  :label-field="'categoryName'"
                  :children-field="'children'"
                  @category-click="handleCategoryClick"
                />
              </li>
            </ul>
          </div>
        </div>
        
        <!-- 右侧产品列表 -->
        <div class="lg:w-3/4">
          <!-- 搜索栏 -->
          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col sm:flex-row gap-4">
              <div class="flex-1">
                <input 
                  v-model="searchQuery"
                  type="text" 
                  placeholder="搜索产品名称、型号或品牌..." 
                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  @keyup.enter="searchProducts"
                >
              </div>
              <button 
                @click="searchProducts"
                class="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-200"
              >
                搜索
              </button>
              <button 
                @click="resetFilters"
                class="px-6 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors duration-200"
              >
                重置筛选
              </button>
            </div>
          </div>
          
          <!-- 产品网格 -->
          <div v-if="loading" class="flex justify-center items-center py-20">
            <div class="flex flex-col items-center">
              <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
              <p class="text-gray-600">加载中...</p>
            </div>
          </div>
          
          <div v-else class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <div 
              v-for="product in filteredProducts" 
              :key="product.id"
              class="product-card bg-white rounded-lg shadow-sm hover:shadow-md transition-all duration-200 flex flex-row overflow-hidden"
              :data-category="product.categoryId"
            >
              <!-- 左侧图片 -->
              <div class="relative w-24 h-24 flex-shrink-0">
                <ElImage
                preview-teleported
                  :src="product.coverUrl || 'https://img1.baidu.com/it/u=415449740,540746270&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500'" 
                  :alt="product.productName" 
                  :preview-src-list="[product.coverUrl || 'https://img1.baidu.com/it/u=415449740,540746270&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500']"
                  fit="cover"
                />
              </div>
              
              <!-- 右侧内容 -->
              <div class="flex-1 p-2 flex flex-col justify-between">
                <div>
                  <h3 class="text-sm font-semibold mb-1 line-clamp-1">{{ product.productName }}</h3>
                  <div class="space-y-1 text-xs text-gray-600 mb-1">
                    <p><span class="font-medium">规格:</span> {{ product.productSpecification }}</p>
                    <p><span class="font-medium">品牌:</span> {{ product.productBrand }}</p>
                    <p><span class="font-medium">单位:</span> {{ product.unitName }}</p>
                    <p><span class="font-medium">编码:</span> {{ product.productCode }}</p>
                    <!-- 成本价信息 -->
                    <p v-if="product.costPrice" class="text-orange-600">
                      <span class="font-medium">成本价:</span> ¥{{ product.costPrice }}
                    </p>
                  </div>
                  <!-- 时间信息 -->
                  <div class="text-xs text-gray-500 mt-1 space-y-0.5">
                    <p v-if="product.purchasePriceDate">
                      <span class="font-medium">更新:</span> {{ formatDate(product.purchasePriceDate) }}
                    </p>
                    <p v-if="product.validStartTime && product.validEndTime" class="text-green-600">
                      <span class="font-medium">有效期:</span> {{ formatDate(product.validStartTime) }} ~ {{ formatDate(product.validEndTime) }}
                    </p>
                  </div>
                </div>
                
                <button 
                  @click="openProductDetail(product)"
                  class="w-full bg-blue-600 text-white py-1 rounded text-xs hover:bg-blue-700 transition-colors duration-200"
                >
                  查看详情
                </button>
              </div>
            </div>
          </div>
          
          <!-- 无结果提示 -->
          <div 
            v-if="!loading && filteredProducts.length === 0" 
            class="text-center py-12"
          >
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <h3 class="text-lg font-semibold text-gray-600 mb-2">未找到相关产品</h3>
            <p class="text-gray-500">请尝试调整筛选条件或搜索关键词</p>
          </div>
          
          <!-- 分页 -->
          <div v-if="!loading && filteredProducts.length > 0" class="mt-8 flex justify-center">
            <nav class="flex items-center space-x-2">
              <button 
                class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="currentPage === 1"
                @click="handlePageChange(currentPage - 1)"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
                </svg>
              </button>
              
              <template v-if="totalCount > 0">
                <button 
                  v-for="page in Math.min(5, Math.ceil(totalCount / pageSize))" 
                  :key="page"
                  class="px-3 py-2 rounded-lg border border-gray-300 hover:bg-gray-50 transition-colors duration-200"
                  :class="{ 'bg-blue-600 text-white border-blue-600': page === currentPage }"
                  @click="handlePageChange(page)"
                >
                  {{ page }}
                </button>
              </template>
              
              <button 
                class="p-2 rounded-lg border border-gray-300 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                :disabled="currentPage >= Math.ceil(totalCount / pageSize)"
                @click="handlePageChange(currentPage + 1)"
              >
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 产品详情弹窗 -->
    <div 
      v-if="selectedProduct" 
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click="closeProductDetail"
    >
      <div 
        class="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto"
        @click.stop
      >
        <div class="p-6">
          <div class="flex justify-between items-start mb-6">
            <h2 class="text-xl font-bold text-blue-600">{{ selectedProduct.productName }} - 详情</h2>
            <button 
              @click="closeProductDetail"
              class="text-gray-500 hover:text-gray-700"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="md:col-span-1">
              <ElImage
                :src="selectedProduct.coverUrl || 'https://img1.baidu.com/it/u=415449740,540746270&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500'" 
                :alt="selectedProduct.productName" 
                class="w-full rounded-lg border border-gray-200"
                :preview-src-list="[selectedProduct.coverUrl || 'https://img1.baidu.com/it/u=415449740,540746270&fm=253&fmt=auto&app=138&f=GIF?w=500&h=500']"
                fit="cover"
              />
            </div>
            
            <div class="md:col-span-2">
              <div class="grid grid-cols-2 gap-x-8 gap-y-4 mb-6">
                <div>
                  <p class="text-sm text-gray-500">产品编号:</p>
                  <p class="text-base font-medium">{{ selectedProduct.productCode }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">产品名称:</p>
                  <p class="text-base font-medium">{{ selectedProduct.productName }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">品牌:</p>
                  <p class="text-base font-medium">{{ selectedProduct.productBrand }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">单位:</p>
                  <p class="text-base font-medium">{{ selectedProduct.unitName }}</p>
                </div>
                <div>
                  <p class="text-sm text-gray-500">规格型号:</p>
                  <p class="text-base font-medium">{{ selectedProduct.productSpecification }}</p>
                </div>
                <div v-if="selectedProduct.costPrice">
                  <p class="text-sm text-gray-500">成本价:</p>
                  <p class="text-base font-medium text-orange-600">¥{{ selectedProduct.costPrice }}</p>
                </div>
              </div>
              
              <!-- 时间信息 -->
              <div class="grid grid-cols-1 gap-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
                <div >
                  <p class="text-sm text-gray-500">更新时间:</p>
                  <p class="text-base font-medium">{{ formatDate(selectedProduct.purchasePriceDate) }}</p>
                </div>
                <div v-if="selectedProduct.validStartTime && selectedProduct.validEndTime">
                  <p class="text-sm text-gray-500">有效时间范围:</p>
                  <p class="text-base font-medium text-green-600">
                    {{ formatDate(selectedProduct.validStartTime) }} ~ {{ formatDate(selectedProduct.validEndTime) }}
                  </p>
                </div>
              </div>
              
              <!-- 产品参数 -->
              <div class="mb-6" v-if="selectedProduct.productPropertyVos && selectedProduct.productPropertyVos.length > 0">
                <h3 class="text-base font-bold mb-3">产品参数</h3>
                
                <div v-for="(property, index) in selectedProduct.productPropertyVos" :key="index" class="mb-4">
                  <p class="text-sm text-gray-700 mb-2">{{ property.propertyName }}</p>
                  <div class="flex flex-wrap items-center gap-4">
                    <template v-if="property.propertyValues && property.propertyValues.length > 0">
                      <label v-for="value in property.propertyValues" :key="value.id" class="inline-flex items-center">
                        <input 
                          :type="property.selectType === 'single' ? 'radio' : 'checkbox'" 
                          :name="'property_' + property.id" 
                          :value="value.value" 
                          class="form-radio text-blue-600">
                        <span class="ml-2 text-sm">{{ value.value }}</span>
                      </label>
                    </template>
                    <span v-else class="text-sm text-gray-600">{{ property.propertyValue }}</span>
                  </div>
                </div>
              </div>
              
              <!-- 商品描述 -->
              <div class="mb-6">
                <h3 class="text-base font-bold mb-3">商品描述</h3>
                <p class="text-sm text-gray-600">
                  {{ selectedProduct.description  }}
                </p>
              </div>
              
              <!-- 用途 -->
              <div class="mb-6">
                <h3 class="text-base font-bold mb-3">用途</h3>
                <p class="text-sm text-gray-600">
                  {{ selectedProduct.purpose  }}
                </p>
              </div>
            </div>
          </div>
          
          <!-- 底部按钮 -->
          <div class="flex justify-end space-x-4 mt-6">
            <button 
              @click="closeProductDetail"
              class="px-6 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 transition-colors duration-200"
            >
              取消
            </button>
            <button class="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200">
              确认
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import axios from '../axios'
import CategoryTree from '../components/CategoryTree.vue'
import { ElImage } from 'element-plus'
// 状态管理
const categories = ref([])
const products = ref([])
const selectedCategory = ref(null)
const searchQuery = ref('')
const selectedProduct = ref(null)
const expandedCategories = ref(new Set())
const loading = ref(false)
const totalCount = ref(0)
const currentPage = ref(1)
const pageSize = ref(12)

// 获取产品分类树
const fetchCategories = async () => {
  try {
    const response = await axios.get('/api/vt-admin/productCategory/tree')
    if (response.data && response.data.code === 200) {
      categories.value = response.data.data || []
      // 添加全部分类选项
      categories.value.unshift({
        id: 'all',
        categoryName: '全部产品'
      })
    } else {
      console.error('获取产品分类失败:', response.data.msg)
    }
  } catch (error) {
    console.error('获取产品分类出错:', error)
  }
}

// 获取产品列表
const fetchProducts = async () => {
  loading.value = true
  try {
    const params = {
      current: currentPage.value,
      size: pageSize.value
    }
    
    // 添加分类筛选
    if (selectedCategory.value && selectedCategory.value !== 'all') {
      params.categoryId = selectedCategory.value
    }
    
    // 添加搜索关键词
    if (searchQuery.value.trim()) {
      params.keys = searchQuery.value.trim()
    }
    
    const response = await axios.get('/api/vt-admin/product/page', { params })
    if (response.data && response.data.code === 200) {
      products.value = response.data.data.records || []
      totalCount.value = response.data.data.total || 0
    } else {
      console.error('获取产品列表失败:', response.data.msg)
    }
  } catch (error) {
    console.error('获取产品列表出错:', error)
  } finally {
    loading.value = false
  }
}

// 获取产品详情
const fetchProductDetail = async (productId) => {
  try {
    const response = await axios.get(`/api/vt-admin/product/detail?id=${productId}`)
    if (response.data && response.data.code === 200) {
      selectedProduct.value = response.data.data
    } else {
      console.error('获取产品详情失败:', response.data.msg)
    }
  } catch (error) {
    console.error('获取产品详情出错:', error)
  }
}

// 计算属性
const filteredProducts = computed(() => {
  return products.value
})

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  fetchProducts()
}


// 方法
const isCategoryExpanded = (categoryId) => {
  return expandedCategories.value.has(categoryId)
}

const toggleCategory = (categoryId) => {
  if (expandedCategories.value.has(categoryId)) {
    expandedCategories.value.delete(categoryId)
  } else {
    expandedCategories.value.add(categoryId)
  }
}

const setSelectedCategory = (categoryId) => {
  selectedCategory.value = categoryId
  // 重置分页并获取产品列表
  currentPage.value = 1
  fetchProducts()
}

const resetFilters = () => {
  selectedCategory.value = 'all'
  searchQuery.value = ''
  expandedCategories.value.clear()
  currentPage.value = 1
  fetchProducts()
}

const handleCategoryClick = (category) => {
  if (category.children && category.children.length > 0) {
    toggleCategory(category.id)
  } else {
    setSelectedCategory(category.id)
  }
}

const openProductDetail = (product) => {
  // 获取产品详情
  fetchProductDetail(product.id)
}

const closeProductDetail = () => {
  selectedProduct.value = null
}

// 搜索产品
const searchProducts = () => {
  currentPage.value = 1
  fetchProducts()
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

onMounted(() => {
  // 获取分类数据
  fetchCategories()
  // 默认选中全部分类
  selectedCategory.value = 'all'
  // 获取产品列表
  fetchProducts()
})
</script>

<style scoped>
.product-card {
  transition: all 0.3s ease;
  min-height: 100px;
}

.product-card:hover {
  transform: translateY(-5px);
}

.category-item:hover {
  background-color: #f3f4f6;
}

.sub-category-item:hover {
  background-color: #f3f4f6;
}


</style>