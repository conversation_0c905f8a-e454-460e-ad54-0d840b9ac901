<template>
  <div style="width: 100%">
    <el-autocomplete
      v-model="value"
      style="width: 100%"
      :fetch-suggestions="querySearch"
      :trigger-on-focus="false"
      clearable
      v-if="dataType === 'name'"
      value-key="productSpecification"
      class="inline-input w-50"
      :placeholder="placeholder"
      @select="handleUserSelectConfirm"
      @blur="handleBlur"
    />
    <el-select v-else style="width: 100%" filterable remote placeholder="请输入产品型号" :remote-method="querySearch" v-model="value" clearable @change="handleBlur">
      <el-option :label="item.productSpecification" :value="item.id" v-for="item in productData"></el-option>
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'product-drop',
};
</script>
<script setup>
import axios from 'axios';
import { watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
  },
  url: {
    type: String,
    default: '/api/vt-admin/product/page',
  },
  dataType:{
    type: String,
    default: 'name'
  },
  placeholder:{
    type: String,
    default: '请输入产品型号'
  }
});
watch(
  () => props.modelValue,
  val => {
    value.value = val;
  }
);
const emits = defineEmits(['update:modelValue']);
let value = ref('');
let productData = ref([]);
function querySearch(value, cb) {
  if (!value) {
    productData.value = [];
    return [];
  }
  axios.get(props.url, { params: { productSpecification: value, size: 100 } }).then(res => {
    if(cb){
      cb(res.data.data.records);
    }
    productData.value = res.data.data.records;
  });
}
function handleUserSelectConfirm(value) {
  emits('update:modelValue', value.productSpecification);
}
function handleBlur() {
  emits('update:modelValue', value.value);
}
</script>

<style lang="scss" scoped></style>
