<template>
  <div class="settlement-detail-container">
    <el-form :model="form" label-width="150px" ref="formRef">
      <!-- 合同信息 -->
      <div class="section-title">合同信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="合同名称:" prop="contractName">
            <span class="form-text">{{ props.detailForm.contractName }}</span>
          </el-form-item>
        </el-col>
        <!-- 合同总额 -->
        <el-col :span="12">
          <el-form-item label="合同总额:" prop="contractTotalPrice">
            <span class="form-text">{{ props.detailForm.contractTotalPrice }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="管理费点数:" prop="managementFeePoints">
                <span class="form-text">{{ form.managementFeePoints }}%</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="管理费额:" prop="managementFeeAmount">
                <span class="form-text">{{ form.managementFeeAmount }}</span>
              </el-form-item>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <!-- 基本信息 -->
      <div class="section-title">基本信息</div>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划名称:">
            <div class="plan-list">
              <el-tag v-for="plan in selectedPlans" :key="plan.id" type="info" class="plan-tag">
                {{ plan.planName }}
              </el-tag>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="计划收款金额:">
            <span class="form-text">{{ form.planAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="税率:">
            <span class="form-text">{{ getTaxRateLabel(form.taxPoints) }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="税额:">
            <span class="form-text">{{ form.taxAmount }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="本次付管理费金额:">
            <div class="management-fee-info">
              <span class="form-text">{{ form.currentManagementFee }}</span>
              <span class="ratio-text">({{ form.managementFeeRatio }}%)</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 采购订单抵扣 -->
      <div class="section-title" v-if="props.rowData.orderVOList.length > 0">采购订单抵扣</div>
      <el-table
        v-if="props.rowData.orderVOList.length > 0"
        :data="props.rowData.orderVOList"
        style="width: 100%"
        border
        align="center"
        class="order-table readonly-table"
      >
        <el-table-column prop="orderNo" align="center" label="合同编号"></el-table-column>
        <el-table-column prop="orderTaxPoint" align="center" label="合同税率">
          <template #default="scope">
            <span>{{ parseFloat(scope.row.taxRate) }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderNotSettleAmount" align="center" label="合同不含税金额">
          <template #default="scope">
            <!-- 合同不含税金额 = 合同金额 / (1 + 税率) -->
            <span>{{ (scope.row.totalPrice / (1 + scope.row.taxRate / 100)).toFixed(2) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="orderSettleAmount" align="center" label="合同税额">
          <template #default="scope">
            <!-- 合同税额 = 合同金额 - 不含税金额 -->
            <span>{{
              (scope.row.totalPrice - scope.row.totalPrice / (1 + scope.row.taxRate / 100)).toFixed(
                2
              )
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="totalPrice" align="center" label="合同金额"></el-table-column>
      </el-table>

      <!-- 结算信息 -->
      <div class="section-title">结算信息</div>
      <el-row :gutter="20">
     
        <el-col :span="24">
          <div class="settlement-info readonly-info">
            <div class="settlement-section">
              <div class="settlement-title">本次开票应包含税额：</div>
              <div class="settlement-content">
                <div class="settlement-item">
                  <span class="item-label">税额:</span>
                  <span class="item-value">+{{ form.taxAmount || 0 }}</span>
                </div>
                <div class="settlement-item" v-if="props.rowData.purchaseTaxPrice > 0">
                  <span class="item-label">选中合同抵扣税额:</span>
                  <span class="item-value">-{{ props.rowData.purchaseTaxPrice }}</span>
                </div>
                <div class="settlement-item total">
                  <span class="item-label">总计:</span>
                  <span class="item-value">{{ calculateInvoiceAmount() }}</span>
                </div>
              </div>
            </div>
            <div class="settlement-section">
              <div class="settlement-title">本次应收款：</div>
              <div class="settlement-content">
                <div class="settlement-item">
                  <span class="item-label">收款额:</span>
                  <span class="item-value">+{{ form.planAmount || 0 }}</span>
                </div>
                <div class="settlement-item">
                  <span class="item-label">本次付管理费:</span>
                  <span class="item-value">-{{ form.currentManagementFee || 0 }}</span>
                </div>
                <div class="settlement-item" v-if="props.rowData.purchaseTotalPrice > 0">
                  <span class="item-label">选中合同抵扣金额:</span>
                  <span class="item-value">-{{ props.rowData.purchaseTotalPrice }}</span>
                </div>
                <div class="settlement-item total">
                  <span class="item-label">总计:</span>
                  <span class="item-value">{{ props.rowData.shouldSettmentPrice || 0 }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>

      <!-- 发票列表 -->
      <div class="section-title" v-if="props.rowData?.invoiceVOList?.length > 0">发票列表</div>

      <el-table
        :data="props.rowData.invoiceVOList"
        v-if="props.rowData?.invoiceVOList?.length > 0"
        style="width: 100%"
        border
        align="center"
        class="invoice-table"
        show-summary
        :summary-method="getInvoiceSummary"
      >
        <el-table-column
          prop="invoiceNumber"
          align="center"
          label="发票号码"
          width="150"
        ></el-table-column>
        <el-table-column prop="invoiceDate" align="center" label="发票日期" width="120">
          <template #default="scope">
            <span>{{ formatDate(scope.row.invoiceDate) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invoicePrice" align="center" label="发票金额" width="120">
          <template #default="scope">
            <span>{{ formatMoney(scope.row.invoicePrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="taxRate" align="center" label="发票税率" width="100">
          <template #default="scope">
            <span>{{ scope.row.taxRate }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="taxPrice" align="center" label="税额" width="120">
          <template #default="scope">
            <span>{{ formatMoney(scope.row.taxPrice) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="invoiceAttach" align="center" label="附件" width="200">
          <template #default="scope">
            <File :fileList="scope.row.attachList"></File>
          </template>
        </el-table-column>
        <el-table-column show-overflow-toolti label="备注" align="center">
          <template #default="scope">
            <span>{{ scope.row.remark || '-' }}</span>
          </template>
        </el-table-column>
      </el-table>

      <!-- 收款信息 -->
      <div class="section-title">收款信息</div>
      <el-row :gutter="20">
          <el-col :span="12">
          <el-form-item label="开户名:">
            <span class="form-text">{{ props.rowData.accountName || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行:">
            <span class="form-text">{{ props.rowData.bankName || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="开户行账号:">
            <span class="form-text">{{ props.rowData.accountNumber || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收款时间:">
            <span class="form-text">{{ props.rowData.collectionTime || '暂无' }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 付款信息 -->
      <div class="section-title">付款信息</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="付款账户:">
            <span class="form-text">{{ props.rowData.payAccountName || '暂无' }}</span>
          </el-form-item>
          <el-form-item label="付款账号:">
            <span class="form-text">{{ props.rowData.paymentAccount || '暂无' }}</span>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款时间:">
            <span class="form-text">{{ props.rowData.paymentTime || '暂无' }}</span>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="付款附件:">
            <span class="form-text"><File :fileList="props.rowData.attachList"></File></span>

          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="付款备注:">
            <span class="form-text">{{ props.rowData.paymentRemark || '暂无' }}</span>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 结算状态信息 -->
      <!-- <div class="section-title">结算状态</div>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="结算状态:">
            <el-tag :type="getStatusType(props.rowData.settlementStatus)">
              {{ getStatusLabel(props.rowData.settlementStatus) }}
            </el-tag>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结算时间:">
            <span class="form-text">{{ props.rowData.settlementTime || '暂无' }}</span>
          </el-form-item>
        </el-col>
      </el-row> -->
    </el-form>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, defineProps } from 'vue';
import axios from 'axios';

const props = defineProps({
  rowData: {
    type: Object,
    default: () => ({}),
  },
  detailForm: Object,
});

// 表单数据
const form = reactive({
  contractTotalPrice: '',
  planIds: [],
  planAmount: '',
  taxPoints: '',
  taxAmount: '',
  managementFeePoints: '',
  managementFeeAmount: '',
  currentManagementFee: '',
  managementFeeRatio: '',
});

// 选中的计划
const selectedPlans = computed(() => {
  if (!props.rowData?.planVOList) return [];
  return props.rowData.planVOList.map(item => ({
    id: item.planId,
    planName: item.planName,
  }));
});

// 选中的订单
const selectedOrders = computed(() => {
  if (!props.rowData?.orderVOList) return [];
  return props.rowData.orderVOList.filter(order => order.selected);
});

// 发票列表数据
const invoiceList = ref([
  {
    id: 1,
    invoiceNumber: 'INV202401001',
    invoiceDate: '2024-01-15',
    invoicePrice: 100000.0,
    taxRate: 13,
    taxAmount: 11504.42,
    remark: '首期发票',
  },
  {
    id: 2,
    invoiceNumber: 'INV202401002',
    invoiceDate: '2024-01-20',
    invoicePrice: 50000.0,
    taxRate: 13,
    taxAmount: 5752.21,
    remark: '二期发票',
  },
]);

// 发票金额汇总
const invoiceTotalAmount = computed(() => {
  return invoiceList.value.reduce((total, invoice) => {
    return total + parseFloat(invoice.invoicePrice || 0);
  }, 0);
});

// 发票税额汇总
const invoiceTotalTax = computed(() => {
  return invoiceList.value.reduce((total, invoice) => {
    return total + parseFloat(invoice.taxAmount || 0);
  }, 0);
});

// 税率数据
let taxRateData = ref([]);

// 获取税率标签
const getTaxRateLabel = value => {
  const taxRate = taxRateData.value.find(item => item.dictKey == value);
  return taxRate ? `${taxRate.dictValue}` : `${value}%`;
};

// 获取状态标签
const getStatusLabel = status => {
  const statusMap = {
    0: '草稿',
    1: '申请结算',
    2: '结算待确认',
    3: '结算确认',
    4: '已付款',
    5: '确认收款',
  };
  return statusMap[status] || '未知状态';
};

// 获取状态类型
const getStatusType = status => {
  const typeMap = {
    0: 'info',
    1: 'warning',
    2: 'warning',
    3: 'success',
    4: 'success',
    5: 'success',
  };
  return typeMap[status] || 'info';
};

// 获取选中合同税额
const getSelectedContractTaxAmount = () => {
  return selectedOrders.value
    .reduce((total, order) => {
      return total + parseFloat(order.orderSettleAmount || 0);
    }, 0)
    .toFixed(2);
};

// 获取选中合同金额
const getSelectedContractAmount = () => {
  return selectedOrders.value
    .reduce((total, order) => {
      return total + parseFloat(order.orderTaxAmount || 0);
    }, 0)
    .toFixed(2);
};

// 计算本次应开票金额
const calculateInvoiceAmount = () => {
  const taxAmount = parseFloat(form.taxAmount || 0);
  const contractTaxAmount = parseFloat(getSelectedContractTaxAmount());
  const invoiceAmount = taxAmount - contractTaxAmount;
  return invoiceAmount.toFixed(2);
};

// 计算本次应收款
const calculateReceivableAmount = () => {
  const planAmount = parseFloat(form.planAmount || 0);
  const currentManagementFee = parseFloat(form.currentManagementFee || 0);
  const contractAmount = parseFloat(getSelectedContractAmount());
  const receivableAmount = planAmount - currentManagementFee - contractAmount;
  return receivableAmount.toFixed(2);
};

// 格式化日期
const formatDate = dateStr => {
  if (!dateStr) return '-';
  return dateStr;
};

// 格式化金额
const formatMoney = amount => {
  if (!amount && amount !== 0) return '0.00';
  return parseFloat(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

// 表格汇总方法
const getInvoiceSummary = param => {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'invoicePrice') {
      const values = data.map(item => Number(item[column.property]));
      sums[index] = formatMoney(
        values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0)
      );
    } else if (column.property === 'taxPrice') {
      const values = data.map(item => Number(item[column.property]));
      sums[index] = formatMoney(
        values.reduce((prev, curr) => {
          const value = Number(curr);
          if (!isNaN(value)) {
            return prev + curr;
          } else {
            return prev;
          }
        }, 0)
      );
    } else {
      sums[index] = '';
    }
  });
  return sums;
};

// 计算税额
const calculateTaxAmount = () => {
  const planAmount = parseFloat(form.planAmount || 0);
  const taxPoints = parseFloat(form.taxPoints || 0);
  if (planAmount && taxPoints) {
    // 税额 = 计划收款金额 * 税率 / (1 + 税率)
    const taxAmount = (planAmount * (taxPoints / 100)) / (1 + taxPoints / 100);
    form.taxAmount = taxAmount.toFixed(2);
  }
};

// 计算管理费额
const calculateManagementFeeAmount = () => {
  const contractTotalPrice = parseFloat(props.detailForm?.contractTotalPrice || 0);
  const managementFeePoints = parseFloat(form.managementFeePoints || 0);
  if (contractTotalPrice && managementFeePoints) {
    // 管理费额 = 合同总额 * 管理费点数 / 100
    const managementFeeAmount = contractTotalPrice * (managementFeePoints / 100);
    form.managementFeeAmount = managementFeeAmount.toFixed(2);
  }
};

// 计算管理费占比
const calculateManagementFeeRatio = () => {
  const managementFeeAmount = parseFloat(form.managementFeeAmount || 0);
  const currentManagementFee = parseFloat(form.currentManagementFee || 0);
  if (managementFeeAmount && currentManagementFee) {
    // 管理费占比 = 本次付管理费金额 / 管理费额 * 100
    const ratio = (currentManagementFee / managementFeeAmount) * 100;
    form.managementFeeRatio = ratio.toFixed(2);
  } else {
    form.managementFeeRatio = '0.00';
  }
};

// 监听rowData变化，更新表单数据
watch(
  () => props.rowData,
  newVal => {
    if (newVal && Object.keys(newVal).length > 0) {
      form.planAmount = newVal.applySettlementPrice || '';
      form.taxPoints = '' + parseFloat(newVal.taxPoints) || '';
      form.taxAmount = newVal.taxAmount || '';
      form.managementFeePoints = parseFloat(newVal.managementFeePoints) || '';
      form.managementFeeAmount = newVal.managementFeeAmount || '';
      form.currentManagementFee = newVal.managementFee || '';
      form.planIds = newVal.planIds || [];

      // 执行计算
      calculateTaxAmount();
      calculateManagementFeeAmount();
      calculateManagementFeeRatio();
    }
  },
  { immediate: true, deep: true }
);

// 监听合同总额变化，重新计算管理费额
watch(
  () => props.detailForm?.contractTotalPrice,
  () => {
    calculateManagementFeeAmount();
    calculateManagementFeeRatio();
  }
);

// 监听计划收款金额和税率变化，重新计算税额
watch([() => form.planAmount, () => form.taxPoints], () => {
  calculateTaxAmount();
});

// 监听管理费相关字段变化，重新计算占比
watch([() => form.managementFeeAmount, () => form.currentManagementFee], () => {
  calculateManagementFeeRatio();
});

// 获取税率数据
const getTaxRate = () => {
  axios.get('/api/blade-system/dict/dictionary?code=tax').then(res => {
    taxRateData.value = res.data.data;
  });
};

onMounted(() => {
  getTaxRate();
});
</script>

<style lang="scss" scoped>
.settlement-detail-container {
  padding: 20px;
  background-color: #fff;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 15px 0;
  padding-left: 10px;
  border-left: 4px solid var(--el-color-primary);
  color: var(--el-color-primary);
}

.form-text {
  display: inline-block;
  min-height: 32px;
  line-height: 32px;
  padding: 0 15px;
  color: #606266;
  width: 100%;
  border-radius: 4px;
  background-color: #f5f7fa;
}

.plan-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.plan-tag {
  margin: 2px 0;
}

.management-fee-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.ratio-text {
  color: #909399;
  font-size: 14px;
}

.settlement-info {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  border: 1px solid #e4e7ed;
}

.readonly-info {
  background-color: #f5f7fa;
}

.settlement-section {
  margin-bottom: 20px;

  &:last-child {
    margin-bottom: 0;
  }
}

.settlement-title {
  font-weight: bold;
  margin-bottom: 12px;
  color: #303133;
  font-size: 15px;
}

.settlement-content {
  padding-left: 20px;
}

.settlement-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  padding: 4px 0;

  &.total {
    font-weight: bold;
    border-top: 2px solid #409eff;
    padding-top: 8px;
    margin-top: 8px;
    color: #409eff;
  }
}

.item-label {
  color: #606266;
  font-weight: 500;
}

.item-value {
  font-weight: 600;
  color: #303133;
}

.order-table {
  margin-bottom: 20px;
}

.readonly-table {
  :deep(.el-table__body-wrapper) {
    background-color: #fafbfc;
  }

  :deep(.el-table__row) {
    background-color: #fafbfc;
  }

  :deep(.el-table__cell) {
    background-color: #fafbfc !important;
  }
}

// 统一只读样式
:deep(.el-form-item__label) {
  color: #606266;
  font-weight: 500;
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

// 表格样式优化
:deep(.el-table) {
  border-radius: 6px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table td) {
  color: #606266;
}

// 标签样式
:deep(.el-tag) {
  font-weight: 500;
  border-radius: 4px;
}

// 发票表格样式
.invoice-table {
  margin-bottom: 20px;

  :deep(.el-table__footer-wrapper) {
    .el-table__footer {
      background-color: #f8f9fa;

      .cell {
        font-weight: 600;
        color: #409eff;
      }
    }
  }
}

// 发票汇总样式
.invoice-summary {
  background-color: #f0f9ff;
  border: 1px solid #bfdbfe;
  border-radius: 8px;
  padding: 16px 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-around;
  align-items: center;

  .summary-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .summary-label {
      font-size: 14px;
      color: #64748b;
      margin-bottom: 4px;
    }

    .summary-value {
      font-size: 18px;
      font-weight: 600;
      color: #1e40af;
    }
  }
}
</style>
