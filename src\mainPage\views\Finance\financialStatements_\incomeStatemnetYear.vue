<template>
  <div>
    <div class="project-main">
      <div class="tabs">
        <div
          v-for="(item, index) in tabs"
          :key="index"
          class="tab-item"
          :class="params.searchType === item.value ? 'active' : ''"
          @click="handleTabClick(item, item.value)"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <el-row :gutter="20" style="height: 100%">
      <el-col :span="24" style="height: 100%">
        <el-card body-style="padding:5px" style="height: 100%" shadow="never">
          <avue-crud
            :option="option"
            :data="tableData"
            v-model:page="page"
            v-model:search="params"
            v-if="params.searchType !== ''"
            @on-load="onLoad"
            :table-loading="loading"
            ref="crud"
            @keyup.enter="onLoad"
            @row-del="rowDel"
            @search-reset="reset"
            @search-change="searchChange"
            @refresh-change="onLoad"
            @current-change="onLoad"
            :cell-style="cellStyle"
            @size-change="onLoad"
            v-model="form"
          >
            <template #januaryPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('01', row)"
                >{{ row.januaryPrice }}
              </el-link>
            </template>
            <template #februaryPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('02', row)"
                >{{ row.februaryPrice }}
              </el-link>
            </template>

            <template #marchPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('03', row)"
                >{{ row.marchPrice }}
              </el-link>
            </template>
            <template #aprilPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('04', row)"
                >{{ row.aprilPrice }}
              </el-link>
            </template>
            <template #mayPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('05', row)"
                >{{ row.mayPrice }}
              </el-link>
            </template>
            <template #junePrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('06', row)"
                >{{ row.junePrice }}
              </el-link>
            </template>
            <template #julyPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('07', row)"
                >{{ row.julyPrice }}
              </el-link>
            </template>
            <template #augustPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('08', row)"
                >{{ row.augustPrice }}
              </el-link>
            </template>
            <template #septemberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('09', row)"
                >{{ row.septemberPrice }}
              </el-link>
            </template>

            <template #octoberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('10', row)"
                >{{ row.octoberPrice }}
              </el-link>
            </template>
            <template #novemberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('11', row)"
                >{{ row.novemberPrice }}
              </el-link>
            </template>
            <template #decemberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 1 ? 'primary' : ''"
                @click="handlePriceClick('12', row)"
                >{{ row.decemberPrice }}
              </el-link>
            </template>
            <template #businessUserName="{ row }">
              <el-link
                type="primary"
                @click="
                  currentBusinessUserName = '';
                  currentBusinessUserName = row.businessUserName;
                  currentUserId = row.businessUser;
                "
                >{{ row.businessUserName }}</el-link
              >
            </template>
            <template #businessUserName-search>
              <userSelect v-model="params.userId"></userSelect>
            </template>
          </avue-crud>
          <avue-crud
            :option="{
              ...option,
              height:null
            }"
            v-else
            :data="tableData"
            v-model:page="page"
            v-model:search="params"
            @on-load="onLoad"
            :table-loading="loading"
            ref="crud"
            @keyup.enter="onLoad"
            @row-del="rowDel"
            @search-reset="reset"
            @search-change="searchChange"
            @refresh-change="onLoad"
            @current-change="onLoad"
            :cell-style="cellStyle"
            @size-change="onLoad"
            v-model="form"
          >
            <template #januaryPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('01', row)"
                >{{ row.januaryPrice }}
              </el-link>
            </template>
            <template #februaryPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('02', row)"
                >{{ row.februaryPrice }}
              </el-link>
            </template>

            <template #marchPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('03', row)"
                >{{ row.marchPrice }}
              </el-link>
            </template>
            <template #aprilPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('04', row)"
                >{{ row.aprilPrice }}
              </el-link>
            </template>
            <template #mayPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('05', row)"
                >{{ row.mayPrice }}
              </el-link>
            </template>
            <template #junePrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('06', row)"
                >{{ row.junePrice }}
              </el-link>
            </template>
            <template #julyPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('07', row)"
                >{{ row.julyPrice }}
              </el-link>
            </template>
            <template #augustPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('08', row)"
                >{{ row.augustPrice }}
              </el-link>
            </template>
            <template #septemberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('09', row)"
                >{{ row.septemberPrice }}
              </el-link>
            </template>

            <template #octoberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('10', row)"
                >{{ row.octoberPrice }}
              </el-link>
            </template>
            <template #novemberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('11', row)"
                >{{ row.novemberPrice }}
              </el-link>
            </template>
            <template #decemberPrice="{ row }">
              <el-link
                :type="params.searchType === '' && row.$index == 0 ? 'primary' : ''"
                @click="handlePriceClick('12', row)"
                >{{ row.decemberPrice }}
              </el-link>
            </template>
            <template #businessUserName="{ row }">
              <el-link
                type="primary"
                @click="
                  currentBusinessUserName = '';
                  currentBusinessUserName = row.businessUserName;
                  currentUserId = row.businessUser;
                "
                >{{ row.businessUserName }}</el-link
              >
            </template>
            <template #businessUserName-search>
              <userSelect v-model="params.userId"></userSelect>
            </template>
          </avue-crud>
        </el-card>
      </el-col>

      <el-col :span="16">
        <el-card style="height: 100%" body-style="padding:5px" shadow="never">
          <template #header v-if="params.searchType === 0">
            {{ currentBusinessUserName }}详情
          </template>
          <div v-if="params.searchType !== 0">
            <div ref="chartsRefBar" style="width: 100%; height: 400px"></div>
          </div>
          <div v-else>
            <el-empty v-if="!currentUserId" description="请点击业务员查看详细情况"> </el-empty>
            <businessUserDetail
              v-else
              :business-user-name="currentBusinessUserName"
              :user-id="currentUserId"
              :year="params.year"
            ></businessUserDetail>
          </div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card style="height: 100%" body-style="padding:5px" shadow="never">
          <template #header>
            <div style="display: flex; justify-content: space-between">
              <span>收入总计</span>
              <el-select size="small" @change="initChartPie" v-model="monthIndex" placeholder="">
                <el-option
                  v-for="item in monthData"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </div>
          </template>
          <div>
            <div ref="chartsRefPie" style="width: 100%; height: 400px"></div>
          </div>
          <div></div>
        </el-card>
      </el-col>
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';
import businessUserDetail from './businessUserDetail.vue';
import userSelect from '@/views/desk/components/userSelect.vue';
let crud = ref()
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  size: 'small',
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  height: 300,
  menu: false,
  highlightCurrentRow: true,
  header: false,
  showSummary: true,
  sumColumnList: [
    { name: 'januaryPrice', type: 'sum', decimals: 2 },
    { name: 'februaryPrice', type: 'sum', decimals: 2 },
    { name: 'marchPrice', type: 'sum', decimals: 2 },
    { name: 'aprilPrice', type: 'sum', decimals: 2 },
    { name: 'mayPrice', type: 'sum', decimals: 2 },
    { name: 'junePrice', type: 'sum', decimals: 2 },
    { name: 'julyPrice', type: 'sum', decimals: 2 },
    { name: 'augustPrice', type: 'sum', decimals: 2 },
    { name: 'septemberPrice', type: 'sum', decimals: 2 },
    { name: 'octoberPrice', type: 'sum', decimals: 2 },
    { name: 'novemberPrice', type: 'sum', decimals: 2 },
    { name: 'decemberPrice', type: 'sum', decimals: 2 },
    { name: 'totalPrice', type: 'sum', decimals: 2 },
    { name: 'lastYearTotalPrice', type: 'sum', decimals: 2 },
  ],
  border: true,
  searchSpan: 6,
  searchMenuSpan: 4,
  column: {
    type: {
      label: '类目',
      type: 'select',
      overHidden: true,
      dicData: [
        {
          value: 0,
          label: '营业外收入',
        },
        {
          value: 1,
          label: '主营收入',
        },
      ],
      fixed: 'left',
    },
    businessUserName: {
      label: '业务员',
      hide: true,
      component: 'wf-user-drop',
      width: 80,

      searchSpan: 4,
      fixed: 'left',
    },

    businessTypeName: {
      label: '业务板块',
      hide: true,
      fixed: 'left',
      //   component: 'wf-user-drop',
      overHidden: true,
      //   search: true,
      searchSpan: 4,
      width: 150,
    },
    marketPointName: {
      label: '经营体',
      hide: true,
      //   component: 'wf-user-drop',
      overHidden: true,
      //   search: true,
      searchSpan: 4,
      fixed: 'left',
    },
    incomeTypeStr: {
      label: '收入类型',
      hide: true,
      //   component: 'wf-user-drop',

      //   search: true,
      searchSpan: 4,
    },
    januaryPrice: {
      label: '一月',
      width: 90,
    },
    februaryPrice: {
      label: '二月',
      width: 95,
    },
    marchPrice: {
      label: '三月',
      width: 95,
    },
    aprilPrice: {
      label: '四月',
      width: 95,
    },
    mayPrice: {
      label: '五月',
      width: 95,
    },
    junePrice: {
      label: '六月',
      width: 95,
    },
    julyPrice: {
      label: '七月',
      width: 95,
    },
    augustPrice: {
      label: '八月',
      width: 95,
    },
    septemberPrice: {
      label: '九月',
      width: 95,
    },
    octoberPrice: {
      label: '十月',
      width: 95,
    },
    novemberPrice: {
      label: '十一月',
      width: 95,
    },
    decemberPrice: {
      label: '十二月',
      width: 95,
    },
    totalPrice: {
      label: '本年累计',
      width: 95,
      fixed: 'right',
    },
    lastYearTotalPrice: {
      label: '上年同期累计',
      width: 95,
      fixed: 'right',
    },
    growthPrice: {
      label: '同期增长额',
      fixed: 'right',
      width: 95,
      html: true,
      formatter: row => {
        return `<div style='color:${row.growthPrice * 1 <= 0 ? 'green' : 'red'}'>${
          row.growthPrice ? row.growthPrice : '0'
        }</div>`;
      },
    },
    growthRate: {
      label: '同期增长率',
      html: true,
      width: 95,
      formatter: row => {
        return `<div style='color:${row.growthRate * 1 <= 0 ? 'green' : 'red'}'>${
          row.growthRate ? row.growthRate : '0'
        }%</div>`;
      },
      fixed: 'right',
    },
    // businessType: {
    //   label: '业务板块',
    //   type: 'select',
    //   dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //   },
    //   label: '业务类型',
    //   // multiple: true,
    //   span: 12,
    //   width: 250,
    //   searchSpan: 4,
    //   overHidden: true,
    //   parent: true,
    //   hide: true,
    //   search: true,
    // },
    // name: {
    //   label: '经营体名字',
    //   hide: true,
    //   search: true,
    //   searchLabelWidth: 95,
    //   searchSpan: 4,
    // },
    searchType: {
      label: '查询类型',
      hide: true,
      search: false,
      type: 'select',
      searchSpan: 4,
      group: true,
      size: 'small',
      dicData: [
        {
          label: '全部收入',
          groups: [
            {
              value: '',
              label: '全部',
            },
          ],
        },
        {
          label: '主营业务收入',
          groups: [
            {
              value: 0,
              label: '业务员',
            },
            {
              value: 1,
              label: '业务板块',
            },
            {
              value: 2,
              label: '经营体',
            },
          ],
        },
        {
          label: '营业外收入',
          groups: [
            {
              value: 3,
              label: '营业外',
            },
          ],
        },
      ],
    },
    year: {
      label: '搜索日期',
      hide: true,
      search: true,
      type: 'year',
      searchSpan: 4,
      format: 'YYYY',
      valueFormat: 'YYYY',
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '';
const urlObj = {
  ['']: '/api/vt-admin/statistics/annualSalesStatistics',
  0: '/api/vt-admin/statistics/annualBusinessPersonList',
  1: '/api/vt-admin/statistics/annualBusinessTypeList',
  2: '/api/vt-admin/statistics/annualMarketPointList',
  3: '/api/vt-admin/statistics/annualSecondaryIncomeList',
};
let params = ref({
  searchType: '',
  year: '' + new Date().getFullYear(),
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
const emits = defineEmits(['priceClick', 'updateDate']);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  option.value.column.businessUserName.hide = params.value.searchType !== 0;
  option.value.column.businessUserName.search = params.value.searchType === 0;
  option.value.column.incomeTypeStr.hide = params.value.searchType !== 3;
  option.value.column.businessTypeName.hide = params.value.searchType !== 1;
  option.value.column.marketPointName.hide = params.value.searchType !== 2;
 
 
  axios
    .get(urlObj[params.value.searchType], {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.map(item => {
        return {
          ...item,
          type: item.type == 0 ? '营业外收入' : '主营业务收入',
        };
      });
      if (params.value.searchType === 0) {
       
        currentBusinessUserName.value = tableData.value[0].businessUserName;
        currentUserId.value = tableData.value[0].businessUser;
      }
     
      initChartPie();
      initChartBar();
    });
}
let router = useRouter();

function reset() {
  params.value.year = '' + new Date().getFullYear();
  emits('updateDate', `${params.value.year}-01`);
  onLoad();
}
function searchChange(a, done) {
  if (params.value.businessUserName) {
    currentBusinessUserName.value = params.value.businessUserName;
  }
  emits('updateDate', `${params.value.year}-01`);
  onLoad();
  done();
}
onMounted(() => {});
let chartsRefPie = ref();
let obj = [
  'totalPrice',
  'januaryPrice',
  'februaryPrice',
  'marchPrice',
  'aprilPrice',
  'mayPrice',
  'junePrice',
  'julyPrice',
  'augustPrice',
  'septemberPrice',
  'octoberPrice',
  'novemberPrice',
  'decemberPrice',
];
function initChartPie() {
  var chartDom = chartsRefPie.value;
  var myChart = echarts.init(chartDom);
  var option;

  option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],
    tooltip: {
      trigger: 'item',
    },
    grid: {
      height: '70%',
      containLabel: true,
    },
    legend: {
      show: false,
      left: 'center',
      top: 'bottom',
      data: tableData.value.map(item => {
        return params.value.searchType === ''
          ? item.type
          : params.value.searchType === 3
          ? item.incomeTypeStr
          : params.value.searchType === 0
          ? item.businessUserName
          : params.value.searchType === 1
          ? item.businessTypeName
          : params.value.searchType == 2
          ? item.marketPointName
          : businessUserName;
      }),
    },
    // toolbox: {
    //   show: true,
    //   feature: {
    //     mark: { show: true },
    //     dataView: { show: true, readOnly: false },
    //     restore: { show: true },
    //     saveAsImage: { show: true },
    //   },
    // },
    series: [
      {
        name: '销售额',
        type: 'pie',
        radius: [0, 100],
        itemStyle: {
          // borderRadius: 5,
        },
        data: tableData.value.sort((a,b) =>  b[obj[monthIndex.value]] -a[obj[monthIndex.value]]).map(item => {
          return {
            value: item[obj[monthIndex.value]],
            name:
              params.value.searchType === ''
                ? item.type
                : params.value.searchType === 3
                ? item.incomeTypeStr
                : params.value.searchType === 0
                ? item.businessUserName
                : params.value.searchType === 1
                ? item.businessTypeName
                : params.value.searchType == 2
                ? item.marketPointName
                : businessUserName,
          };
        }),
      },
    ],
  };

  option && myChart.setOption(option);
}
let chartsRefBar = ref();
let chartsBar = '';
function initChartBar() {
  if (chartsBar) {
    chartsBar.dispose();
  }
  chartsBar = echarts.init(chartsRefBar.value);
  const data = tableData.value.map(item => {
    return [
      item.januaryPrice,
      item.februaryPrice,
      item.marchPrice,
      item.aprilPrice,
      item.mayPrice,
      item.junePrice,
      item.julyPrice,
      item.augustPrice,
      item.septemberPrice,
      item.octoberPrice,
      item.novemberPrice,
      item.decemberPrice,
    ];
  });
  let option = {
    grid: {
      bottom: 20,
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      show: true,
      right: 'center',
      width: '100%',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      type: 'scroll',
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: [
          '一月',
          '二月',
          '三月',
          '四月',
          '五月',
          '六月',
          '七月',
          '八月',
          '九月',
          '十月',
          '十一月',
          '十二月',
        ], //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '销售额', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: data.map((item, index) => {
      return {
        name:
          params.value.searchType === ''
            ? tableData.value[index].type
            : params.value.searchType === 3
            ? tableData.value[index].incomeTypeStr
            : params.value.searchType === 0
            ? tableData.value[index].businessUserName
            : params.value.searchType === 1
            ? tableData.value[index].businessTypeName
            : params.value.searchType == 2
            ? tableData.value[index].marketPointName
            : businessUserName, //名称
        type: 'bar', //类型
        barWidth: 18, //宽度
        stack: 'sales',
        data: item, //数值
        z: 1,
        barGap: 20,
        emphasis: {
          focus: 'series',
        },
        barWidth: 20,
        label: {
          show: index === tableData.value.length - 1,
          position: 'top',
          fontSize: 10,
          formatter: val => {
            console.log(val);

            const price = data.reduce((pre, cur) => {
              
              pre += cur[val.dataIndex] * 1;
              return pre;
            }, 0);
            return price * 1 == 0 ? '' : ((price * 1) / 10000).toFixed(2) + '万';
          },
        },
      };
    }),
  };
  chartsBar.setOption(option);
}

function handlePriceClick(date, row) {
  if (params.value.searchType !== '' || row.type !== '主营业务收入') return;
  emits('priceClick', {
    date: `${params.value.year}-${date}`,
  });
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  if (
    column.property == 'totalPrice' ||
    column.property == 'lastYearTotalPrice' ||
    column.property == 'type' ||
    column.property == 'businessUserName' ||
    column.property == 'businessTypeName' ||
    column.property == 'businessTypeName' ||
    column.property == 'incomeTypeStr' ||
    column.property == 'marketPointName'
  ) {
    return {
      fontWeight: 'bolder',
      fontSize:
        column.property == 'type' ||
        column.property == 'businessUserName' ||
        column.property == 'businessTypeName' ||
        column.property == 'businessTypeName' ||
        column.property == 'incomeTypeStr' ||
        column.property == 'marketPointName'
          ? '14px'
          : '12px',
    };
  }
}
let tabs = ref([
  {
    value: '',
    label: '全部',
  },
  {
    value: 0,
    label: '业务员',
  },
  // {
  //   value: 1,
  //   label: '业务板块',
  // },
  // {
  //   value: 2,
  //   label: '经营体',
  // },
  // {
  //   value: 3,
  //   label: '营业外',
  // },
]);
let tabIndex = ref(0);
function handleTabClick(item, index) {
  if (params.value.searchType === index) {
    return;
  }
  params.value.searchType = index;
  onLoad();
}
let currentBusinessUserName = ref('');
let currentUserId = ref('');

let monthData = [
  {
    value: 0,
    label: '全部',
  },
  {
    value: 1,
    label: '一月',
  },
  {
    value: 2,
    label: '二月',
  },
  {
    value: 3,
    label: '三月',
  },
  {
    value: 4,
    label: '四月',
  },
  {
    value: 5,
    label: '五月',
  },
  {
    value: 6,
    label: '六月',
  },
  {
    value: 7,
    label: '七月',
  },
  {
    value: 8,
    label: '八月',
  },
  {
    value: 9,
    label: '九月',
  },
  {
    value: 10,
    label: '十月',
  },
  {
    value: 11,
    label: '十一月',
  },
  {
    value: 12,
    label: '十二月',
  },
];
let monthIndex = ref(0);
</script>

<style lang="scss" scoped>
.el-link {
  font-weight: 400;
}
.project-main {
  // width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    // margin: 0 6px 0 7px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: var(--el-color-info-light-7);
      margin-bottom: -6px;
      border-radius: 5px 5px 0px 0px;

      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
:deep(.cell){
  padding: 0 0!important;
}
</style>
