<template>
  <div
    class="virtual-table-container"
    ref="containerRef"
    @scroll="handleScroll"
    
  >
    <!-- 独立的固定表头 -->
    <div class="fixed-header-container">
      <table class="fixed-header-table" border="0">
        <colgroup>
          <col width="100px" />
          <col width="150px" />
          <col width="80px" />
          <col width="200px" />
          <col width="150px" />
          <col width="80px" class="center" />
          <col width="80px" />
          <col
            v-for="item in columnHideData.filter(item => item.isShow)"
            :key="item.value"
            :width="`${item.width}px`"
          />
        </colgroup>
        <thead class="modern-table-header">
          <tr class="header-row">
            <td class="header-cell">
              <div class="header-content">
                <span class="header-text">序号</span>
              </div>
            </td>
            <td class="header-cell">
              <div class="header-content">
                <span class="header-text">产品名称</span>
              </div>
            </td>
            <td class="header-cell">
              <div class="header-content">
                <span class="header-text">品牌</span>
              </div>
            </td>
            <td class="header-cell">
              <div class="header-content">
                <span class="header-text">规格型号</span>
              </div>
            </td>
            <td class="header-cell">
              <div class="header-content">
                <span class="header-text">产品描述</span>
              </div>
            </td>
            <td class="header-cell center">
              <div class="header-content">
                <span class="header-text">单位</span>
              </div>
            </td>
            <td class="header-cell center">
              <div class="header-content">
                <span class="header-text">数量</span>
              </div>
            </td>
            <td
              :class="['header-cell', item.align || 'right']"
              v-for="item in columnHideData.filter(item => item.isShow)"
              :key="item.value"
            >
              <div class="header-content">
                <span class="header-text">{{ item.value }}</span>
              </div>
            </td>
          </tr>
        </thead>
      </table>
    </div>

    <div class="virtual-table-content" :style="{ height: totalHeight + 'px' }">
      <table class="sticky-table" border="1" :key="`table-${tableKey}`">
        <colgroup>
          <col width="100px" />
          <col width="150px" />
          <col width="80px" />
          <col width="200px" />
          <col width="150px" />
          <col width="80px" class="center" />
          <col width="80px" />
          <col
            v-for="item in columnHideData.filter(item => item.isShow)"
            :key="item.value"
            :width="`${item.width}px`"
          />
        </colgroup>

        <tbody :style="{ transform: `translateY(${offsetY}px)` }">
          <!-- 分类标题行、产品行、操作按钮行 -->
          <template v-for="(item, itemIndex) in visibleData" :key="`item-${item.uuid || item.classify || itemIndex}-${itemIndex}`">
            <!-- 分类标题行 -->
            <tr class="category" v-if="item.isCategory">
              <td :colspan="7 + columnHideData.filter(col => col.isShow).length" class="category-content">
                <div class="category-header">
                  <span v-if="item.classify" class="category-name">{{ item.classify }}</span>
                  <!-- 分类标题行的操作按钮（编辑、删除等） -->
                  <div class="category-title-actions">
                    <slot name="category-title-actions" :category="item"></slot>
                  </div>
                </div>
              </td>
            </tr>
            <!-- 产品行 -->
            <tr
              v-else-if="!item.isActionRow"
              :class="[
                'cell_hover',
                'allow_td',
                {
                  'current-editing': isCurrentEditingProduct && isCurrentEditingProduct(item),
                  'row-selected': isCurrentEditingProduct && isCurrentEditingProduct(item)
                }
              ]"
              @click="$emit('product-click', item)"
              :id="`tr_${item.uuid}`"
              :data-category-index="item.categoryIndex"
              :data-product-index="item.productIndex"
              :data-uuid="item.uuid"
            >
              <slot name="product-row" :product="item" :index="item.displayIndex"></slot>
            </tr>
            <!-- 操作按钮行 -->
            <tr v-else-if="item.isActionRow && $route.query.type != 'detail'">
              <td :colspan="7 + columnHideData.filter(col => col.isShow).length" class="action-row-content">
                <div class="action-buttons">
                  <slot name="category-actions" :category="item"></slot>
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import Sortable from 'sortablejs'

const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  itemHeight: {
    type: Number,
    default: 30
  },
  categoryHeight: {
    type: Number,
    default: 30
  },
  columnHideData: {
    type: Array,
    default: () => []
  },
  isCurrentEditingProduct: {
    type: Function,
    default: null
  },
  enableDrag: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['product-click', 'drag-end'])

const containerRef = ref(null)
const scrollTop = ref(0)
const containerHeight = ref(500)
const tableKey = ref(0)
const dynamicHeight = ref('calc(100vh - 250px)')

// 将表格数据扁平化，包含分类、产品和操作按钮行
const flattenedData = computed(() => {
  const result = []
  let globalIndex = 0

  props.tableData.forEach((category, categoryIndex) => {
    // 添加分类行
    result.push({
      ...category,
      isCategory: true,
      index: globalIndex++,
      height: props.categoryHeight
    })

    // 添加产品行
    const products = category.productList?.filter(product => product.detailType === 0) || []
    products.forEach((product, productIndex) => {
      result.push({
        ...product,
        isCategory: false,
        isActionRow: false,
        index: globalIndex++,
        height: props.itemHeight,
        categoryIndex,
        productIndex
      })
    })

    // 在每个分类的最后添加操作按钮行
    result.push({
      ...category,
      isCategory: false,
      isActionRow: true,
      index: globalIndex++,
      height: props.itemHeight,
      categoryIndex
    })
  })

  return result
})

// 计算总高度
const totalHeight = computed(() => {
  return flattenedData.value.reduce((total, item) => total + item.height, 0)
})

// 虚拟滚动配置 - 暂时禁用以确保数据完整性
const VIRTUAL_SCROLL_CONFIG = {
  enabled: false, // 暂时禁用虚拟滚动
  bufferSize: 5,
  minItemsForVirtual: 100, // 提高阈值，减少启用频率
  estimatedItemHeight: 35
}

// 计算可见区域的数据 - 修复数据显示问题
const visibleData = computed(() => {
  if (!flattenedData.value.length) return []

  // 暂时禁用虚拟滚动，确保所有数据正确显示
  // 虚拟滚动在复杂的混合数据结构下容易出现索引错误
  console.log('显示所有数据，总数量:', flattenedData.value.length)
  return flattenedData.value

  // 注释掉有问题的虚拟滚动逻辑
  /*
  // 如果禁用虚拟滚动或数据量较少，直接显示所有数据
  if (!VIRTUAL_SCROLL_CONFIG.enabled || flattenedData.value.length < VIRTUAL_SCROLL_CONFIG.minItemsForVirtual) {
    console.log('虚拟滚动未启用或数据量较少，显示所有数据')
    return flattenedData.value
  }

  // 启用虚拟滚动的逻辑需要重新设计
  // 当前实现在混合数据结构下有索引计算错误
  */
})

// 计算偏移量 - 暂时禁用
const offsetY = computed(() => {
  // 暂时返回0，确保数据显示正确
  return 0

  // 注释掉有问题的偏移计算
  /*
  if (!VIRTUAL_SCROLL_CONFIG.enabled || !flattenedData.value.length || flattenedData.value.length < VIRTUAL_SCROLL_CONFIG.minItemsForVirtual) {
    return 0
  }
  // ... 偏移计算逻辑
  */
})

// 优化的滚动处理，支持虚拟滚动
let scrollTimer = null
const handleScroll = (event) => {
  const newScrollTop = event.target.scrollTop

  // 立即更新scrollTop，确保虚拟滚动响应及时
  scrollTop.value = newScrollTop

  // 使用requestAnimationFrame优化性能
  if (scrollTimer) {
    cancelAnimationFrame(scrollTimer)
  }

  scrollTimer = requestAnimationFrame(() => {
    // 在这里可以添加额外的滚动处理逻辑
    console.log('虚拟滚动更新:', {
      scrollTop: newScrollTop,
      visibleItemsCount: visibleData.value.length
    })
  })
}

const updateContainerHeight = () => {
  if (containerRef.value) {
    containerHeight.value = containerRef.value.clientHeight
  }
}

// 计算动态高度
const calculateDynamicHeight = () => {
  try {
    let totalOffset = 0

    // 1. 顶部导航栏高度（通常是固定的）
    const headerHeight = 60
    totalOffset += headerHeight

    // 2. 汇总区域高度（动态变化）
    const summaryElement = document.querySelector('.wrap > div:first-child')
    if (summaryElement) {
      const summaryRect = summaryElement.getBoundingClientRect()
      const summaryHeight = summaryRect.height
      totalOffset += summaryHeight
      console.log('汇总区域高度:', summaryHeight)
    }

    // 3. 模块标签栏高度
    const moduleTabsElement = document.querySelector('.wrap .el-radio-group')
    if (moduleTabsElement) {
      const moduleTabsRect = moduleTabsElement.getBoundingClientRect()
      const moduleTabsHeight = moduleTabsRect.height + 10 // 加上一些边距
      totalOffset += moduleTabsHeight
      console.log('模块标签栏高度:', moduleTabsHeight)
    } else {
      totalOffset += 60 // 默认高度
    }

    // 4. 折叠按钮高度
    const foldButtonElement = document.querySelector('#table_box')
    if (foldButtonElement) {
      const foldButtonRect = foldButtonElement.getBoundingClientRect()
      const foldButtonHeight = foldButtonRect.height
      totalOffset += foldButtonHeight
      console.log('折叠按钮高度:', foldButtonHeight)
    } else {
      totalOffset += 30 // 默认高度
    }

    // 5. 额外的内边距和边距
    const padding = 40
    totalOffset += padding

    console.log('总偏移量:', totalOffset)

    const newHeight = `calc(100vh - ${totalOffset}px)`
    console.log('计算的新高度:', newHeight)

    dynamicHeight.value = newHeight
    return newHeight

  } catch (error) {
    console.error('计算动态高度时出错:', error)
    // 出错时使用默认高度
    const defaultHeight = 'calc(100vh - 250px)'
    dynamicHeight.value = defaultHeight
    return defaultHeight
  }
}

// 更新表格高度
const updateHeight = () => {
  calculateDynamicHeight()
  updateContainerHeight()
}

// 拖拽相关
let sortableInstance = null

const initDrag = () => {
  if (!props.enableDrag) {
    console.log('拖拽功能已禁用')
    return
  }

  nextTick(() => {
    const tbody = containerRef.value?.querySelector('tbody')
    console.log('初始化拖拽，tbody:', tbody, 'sortableInstance:', sortableInstance)

    if (tbody) {
      // 检查是否有可拖拽的元素
      const draggableElements = tbody.querySelectorAll('.allow_td:not(.category)')
      const sortHandles = tbody.querySelectorAll('.sort')
      console.log('可拖拽元素数量:', draggableElements.length)
      console.log('拖拽句柄数量:', sortHandles.length)

      // 强制销毁现有实例，确保重新创建
      if (sortableInstance) {
        console.log('销毁现有拖拽实例')
        sortableInstance.destroy()
        sortableInstance = null
      }

      // 总是创建新的拖拽实例
      console.log('创建新的拖拽实例')
      sortableInstance = new Sortable(tbody, {
        handle: '.sort', // 拖拽句柄
        draggable: '.allow_td:not(.category)', // 只允许产品行拖拽，排除分类行
        filter: '.category, .action-row-content', // 过滤掉分类行和操作按钮行
        animation: 180,
        delay: 0,
        onStart: (evt) => {
          console.log('拖拽开始:', evt)
          console.log('拖拽元素:', evt.item)
          console.log('拖拽句柄:', evt.item.querySelector('.sort'))

          // 虚拟滚动环境下的拖拽优化
          if (VIRTUAL_SCROLL_CONFIG.enabled && flattenedData.value.length >= VIRTUAL_SCROLL_CONFIG.minItemsForVirtual) {
            console.log('虚拟滚动环境下的拖拽开始')
            // 可以在这里添加特殊处理，比如临时禁用虚拟滚动或扩大可见区域
          }
        },
        onEnd: (evt) => {
          console.log('拖拽结束:', evt)
          handleDragEnd(evt)

          // 虚拟滚动环境下的拖拽结束处理
          if (VIRTUAL_SCROLL_CONFIG.enabled && flattenedData.value.length >= VIRTUAL_SCROLL_CONFIG.minItemsForVirtual) {
            console.log('虚拟滚动环境下的拖拽结束')
            // 拖拽结束后可能需要重新计算可见区域
            nextTick(() => {
              updateContainerHeight()
            })
          }
        }
      })
      console.log('拖拽实例创建完成:', sortableInstance)

      // 验证拖拽是否正确初始化
      if (sortHandles.length === 0) {
        console.warn('警告：未找到拖拽句柄(.sort)，拖拽功能可能无法正常工作')
      }
      if (draggableElements.length === 0) {
        console.warn('警告：未找到可拖拽元素(.allow_td)，拖拽功能可能无法正常工作')
      }

      // 虚拟滚动提示
      if (VIRTUAL_SCROLL_CONFIG.enabled && flattenedData.value.length >= VIRTUAL_SCROLL_CONFIG.minItemsForVirtual) {
        console.log('当前启用虚拟滚动，拖拽功能已优化适配')
      }
    } else {
      console.log('未找到 tbody 元素')
    }
  })
}

const handleDragEnd = (evt) => {
  const { oldIndex, newIndex } = evt
  if (oldIndex === newIndex) return

  console.log('虚拟表格拖拽事件 - 原始索引:', { oldIndex, newIndex })

  // 获取被拖拽元素的信息
  const draggedElement = evt.item
  const categoryIndex = parseInt(draggedElement.dataset.categoryIndex)
  const productIndex = parseInt(draggedElement.dataset.productIndex)
  const uuid = draggedElement.dataset.uuid

  console.log('拖拽元素信息:', { categoryIndex, productIndex, uuid })

  // 直接处理拖拽，不使用防抖，避免变量作用域问题
  processDragEnd(evt, { categoryIndex, productIndex, uuid, draggedElement })
}

// 处理拖拽结束的核心逻辑
const processDragEnd = (evt, dragInfo) => {
  const { oldIndex, newIndex } = evt
  const { categoryIndex, productIndex, uuid, draggedElement } = dragInfo

  // 获取所有行（包括分类行、产品行、操作按钮行）
  const allRows = Array.from(evt.to.children)
  console.log('所有行数量:', allRows.length)

  // 计算正确的产品行索引
  // 需要将 tbody 中的行索引转换为当前分类内的产品行索引

  // 首先找到拖拽的产品属于哪个分类
  const draggedCategoryIndex = categoryIndex
  console.log('拖拽产品所属分类索引:', draggedCategoryIndex)

  // 计算当前分类内的产品行索引
  let categoryProductRowOldIndex = -1
  let categoryProductRowNewIndex = -1
  let currentCategoryProductCount = 0
  let globalProductRowCount = 0

  // 遍历所有行，找到当前分类的产品行
  allRows.forEach((row, index) => {
    const isProductRow = row.classList.contains('allow_td') && !row.classList.contains('category')

    if (isProductRow) {
      const rowCategoryIndex = parseInt(row.dataset.categoryIndex)

      // 如果是当前分类的产品行
      if (rowCategoryIndex === draggedCategoryIndex) {
        if (index === oldIndex) {
          categoryProductRowOldIndex = currentCategoryProductCount
        }
        if (index === newIndex) {
          categoryProductRowNewIndex = currentCategoryProductCount
        }
        currentCategoryProductCount++
      }

      globalProductRowCount++
    }
  })

  console.log('转换后的分类内产品行索引:', {
    categoryProductRowOldIndex,
    categoryProductRowNewIndex,
    currentCategoryProductCount,
    globalProductRowCount
  })

  // 验证索引有效性
  if (categoryProductRowOldIndex === -1 || categoryProductRowNewIndex === -1) {
    console.log('无法计算有效的分类内产品行索引')
    return
  }

  // 检查是否是跨分类拖拽
  let targetCategoryIndex = -1

  // 找到目标位置的分类
  for (let i = newIndex; i >= 0; i--) {
    const row = allRows[i]
    if (row && row.classList.contains('allow_td') && !row.classList.contains('category')) {
      targetCategoryIndex = parseInt(row.dataset.categoryIndex)
      break
    } else if (row && row.classList.contains('category')) {
      // 如果目标位置是分类行，则查看该分类的索引
      // 这里需要从扁平化数据中找到对应的分类索引
      break
    }
  }

  // 如果没找到，尝试向下查找
  if (targetCategoryIndex === -1) {
    for (let i = newIndex; i < allRows.length; i++) {
      const row = allRows[i]
      if (row && row.classList.contains('allow_td') && !row.classList.contains('category')) {
        targetCategoryIndex = parseInt(row.dataset.categoryIndex)
        break
      }
    }
  }

  console.log('目标分类索引:', targetCategoryIndex, '源分类索引:', draggedCategoryIndex)

  if (targetCategoryIndex !== -1 && targetCategoryIndex !== draggedCategoryIndex) {
    console.log('检测到跨分类拖拽，暂不支持')
    console.log('源分类:', draggedCategoryIndex, '目标分类:', targetCategoryIndex)
    return
  }

  // 触发拖拽结束事件，让父组件处理数据更新
  emit('drag-end', {
    oldIndex: categoryProductRowOldIndex,  // 使用转换后的分类内产品行索引
    newIndex: categoryProductRowNewIndex,  // 使用转换后的分类内产品行索引
    categoryIndex,
    productIndex,
    uuid,
    draggedElement,
    totalProductRows: currentCategoryProductCount,
    originalOldIndex: oldIndex,    // 保留原始索引用于调试
    originalNewIndex: newIndex     // 保留原始索引用于调试
  })
}

// 监听数据变化，重新初始化拖拽
watch(
  () => [props.tableData, props.enableDrag],
  () => {
    console.log('表格数据或拖拽状态变化，重新初始化拖拽')
    nextTick(() => {
      // 销毁现有实例
      if (sortableInstance) {
        sortableInstance.destroy()
        sortableInstance = null
      }
      // 重新初始化
      initDrag()

      // 数据变化后重新设置表头固定
      setTimeout(() => {
        initStickyHeader()
      }, 100)
    })
  },
  { deep: true, flush: 'post' }
)

onMounted(() => {
  // 初始化高度计算
  nextTick(() => {
    calculateDynamicHeight()
    updateContainerHeight()

    // 强制设置表头固定
    initStickyHeader()
  })

  // 监听窗口大小变化
  window.addEventListener('resize', () => {
    calculateDynamicHeight()
    updateContainerHeight()
  })

  initDrag()
})

// 初始化表头固定
const initStickyHeader = () => {
  // 固定表头现在通过CSS实现，这个函数保留用于未来扩展
  console.log('固定表头已通过CSS实现')
}

onUnmounted(() => {
  window.removeEventListener('resize', updateContainerHeight)
  if (sortableInstance) {
    sortableInstance.destroy()
    sortableInstance = null
  }
})

// 强制刷新数据的方法
const forceRefresh = () => {
  console.log('虚拟表格强制刷新')

  // 销毁现有的拖拽实例
  if (sortableInstance) {
    console.log('销毁现有拖拽实例')
    sortableInstance.destroy()
    sortableInstance = null
  }

  // 方法1：更新表格key强制重新渲染
  tableKey.value = Date.now()

  // 方法2：通过修改容器高度来强制重新计算
  const originalHeight = containerHeight.value
  containerHeight.value = originalHeight + 1

  nextTick(() => {
    containerHeight.value = originalHeight
    updateContainerHeight()

    // 重新初始化拖拽
    console.log('重新初始化拖拽')
    initDrag()

    console.log('虚拟表格强制刷新完成')
  })
}

// 测试拖拽功能
const testDrag = () => {
  console.log('=== 拖拽功能测试 ===')
  console.log('enableDrag:', props.enableDrag)
  console.log('containerRef:', containerRef.value)

  if (containerRef.value) {
    const tbody = containerRef.value.querySelector('tbody')
    console.log('tbody:', tbody)

    if (tbody) {
      const draggableElements = tbody.querySelectorAll('.allow_td:not(.category)')
      const sortHandles = tbody.querySelectorAll('.sort')
      console.log('可拖拽元素数量:', draggableElements.length)
      console.log('拖拽句柄数量:', sortHandles.length)
      console.log('sortableInstance:', sortableInstance)

      // 检查第一个拖拽句柄
      if (sortHandles.length > 0) {
        console.log('第一个拖拽句柄:', sortHandles[0])
        console.log('拖拽句柄样式:', window.getComputedStyle(sortHandles[0]))
      }
    }
  }
  console.log('=== 测试结束 ===')
}

// 测试虚拟滚动功能
const testVirtualScroll = () => {
  console.log('=== 虚拟滚动测试 ===')
  console.log('虚拟滚动配置:', VIRTUAL_SCROLL_CONFIG)
  console.log('扁平化数据总量:', flattenedData.value.length)
  console.log('可见数据量:', visibleData.value.length)
  console.log('当前滚动位置:', scrollTop.value)
  console.log('容器高度:', containerHeight.value)
  console.log('偏移量:', offsetY.value)

  // 分析数据结构
  let categoryCount = 0
  let productCount = 0
  let actionRowCount = 0

  flattenedData.value.forEach(item => {
    if (item.isCategory) categoryCount++
    else if (item.isActionRow) actionRowCount++
    else productCount++
  })

  console.log('数据结构分析:', {
    分类行: categoryCount,
    产品行: productCount,
    操作按钮行: actionRowCount,
    总计: flattenedData.value.length
  })

  console.log('虚拟滚动状态:', VIRTUAL_SCROLL_CONFIG.enabled ? '已启用' : '已禁用')
  console.log('=== 测试结束 ===')
}

// 动态调整虚拟滚动配置
const updateVirtualScrollConfig = (config) => {
  Object.assign(VIRTUAL_SCROLL_CONFIG, config)
  console.log('虚拟滚动配置已更新:', VIRTUAL_SCROLL_CONFIG)

  // 强制重新计算
  nextTick(() => {
    updateContainerHeight()
  })
}

// 暴露方法给父组件
defineExpose({
  scrollToTop: () => {
    if (containerRef.value) {
      containerRef.value.scrollTop = 0
    }
  },
  initDrag, // 暴露拖拽初始化方法
  forceRefresh, // 暴露强制刷新方法
  testDrag, // 暴露拖拽测试方法
  testVirtualScroll, // 暴露虚拟滚动测试方法
  updateVirtualScrollConfig, // 暴露虚拟滚动配置更新方法
  scrollToItem: (index) => {
    console.log('VirtualTable scrollToItem 调用，索引:', index)
    console.log('扁平化数据长度:', flattenedData.value.length)

    if (!containerRef.value) {
      console.log('容器引用不存在')
      return
    }

    if (index < 0 || index >= flattenedData.value.length) {
      console.log('索引超出范围:', index, '有效范围: 0 -', flattenedData.value.length - 1)
      return
    }

    const targetItem = flattenedData.value[index]
    console.log('目标项:', targetItem)

    if (targetItem) {
      const targetScrollTop = flattenedData.value
        .slice(0, index)
        .reduce((total, item) => total + item.height, 0)

      console.log('计算的滚动位置:', targetScrollTop)
      console.log('当前滚动位置:', containerRef.value.scrollTop)

      containerRef.value.scrollTop = targetScrollTop

      // 使用 scrollIntoView 作为备选方案
      nextTick(() => {
        const targetElement = containerRef.value.querySelector(`tr.category:nth-child(${index + 1})`)
        if (targetElement) {
          console.log('找到目标元素，使用 scrollIntoView')
          targetElement.scrollIntoView({
            behavior: 'smooth',
            block: 'start',
            inline: 'nearest'
          })
        } else {
          console.log('未找到目标元素，尝试查找所有分类行')
          const allCategoryRows = containerRef.value.querySelectorAll('tr.category')
          console.log('所有分类行数量:', allCategoryRows.length)

          // 计算这是第几个分类行
          let categoryRowIndex = 0
          for (let i = 0; i <= index; i++) {
            if (flattenedData.value[i] && flattenedData.value[i].isCategory) {
              if (i === index) {
                break
              }
              categoryRowIndex++
            }
          }

          console.log('目标分类行索引:', categoryRowIndex)
          if (allCategoryRows[categoryRowIndex]) {
            console.log('通过分类行索引找到目标元素')
            allCategoryRows[categoryRowIndex].scrollIntoView({
              behavior: 'smooth',
              block: 'start',
              inline: 'nearest'
            })
          }
        }
      })
    }
  },
  scrollToCategoryByName: (categoryName) => {
    console.log('VirtualTable scrollToCategoryByName 调用，分类名:', categoryName)

    if (!containerRef.value) {
      console.log('容器引用不存在')
      return
    }

    // 查找分类名称对应的元素
    const categoryElements = containerRef.value.querySelectorAll('tr.category .category-name')
    const targetElement = Array.from(categoryElements).find(el =>
      el.textContent.trim() === categoryName
    )

    if (targetElement) {
      console.log('找到分类元素:', targetElement)
      const categoryRow = targetElement.closest('tr.category')
      if (categoryRow) {
        categoryRow.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
      }
    } else {
      console.log('未找到分类元素，分类名:', categoryName)
      console.log('可用分类:', Array.from(categoryElements).map(el => el.textContent.trim()))
    }
  },
  forceRefresh,
  updateHeight
})
</script>

<style scoped>
.virtual-table-container {
  /* 高度通过动态计算设置，不再使用固定值 */
  overflow: auto;
  position: relative;
  min-height: 400px; /* 设置最小高度 */
  transition: height 0.3s ease; /* 添加高度变化的过渡动画 */
  height: 100%;
  border-radius: 8px;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  /* 确保sticky定位的容器设置正确 */
  contain: layout style paint;
}

/* 固定表头容器样式 */
.fixed-header-container {
  position: sticky;
  top: 0;
  z-index: 1001;
  background: #fff;
  /* border-bottom: 1px solid #e4e7ed; */
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
}

.fixed-header-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  text-align: center;
  table-layout: fixed;
  font-size: 14px;
  background: #fff;
  margin: 0;
}

/* 自定义滚动条样式 */
.virtual-table-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.virtual-table-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.virtual-table-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
  transition: background 0.2s ease;
}

.virtual-table-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.virtual-table-content {
  position: relative;
}

/* 现代化表格编辑器样式 */
table,
.sticky-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  text-align: center;
  table-layout: fixed; /* 固定表格布局，确保列宽度固定 */
  font-size: 14px; /* 设置表格字体大小为14px */
  background: #fff;
  border-radius: 6px;
  overflow: visible; /* 改为visible以支持sticky */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  /* 确保表格支持sticky定位 */
  position: relative !important;
}

tr {
  height: 36px; /* 增加行高，提升视觉舒适度 */
  min-height: 36px;
  max-height: 36px;
  transition: background-color 0.2s ease;
}

td {
  padding: 8px 12px; /* 增加内边距 */
  border: 1px solid #e4e7ed; /* 使用更柔和的边框颜色 */
  border-top: none;
  border-left: none;
  height: 36px;
  min-height: 36px;
  max-height: 36px;
  line-height: 20px;
  vertical-align: middle;
  font-size: 14px;
  /* 确保内容超出时隐藏 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  background: #fff;
  position: relative;
}

/* 第一列左边框 */
td:first-child {
  border-left: 1px solid #e4e7ed;
}

/* 最后一行底边框 */
tr:last-child td {
  border-bottom: 1px solid #e4e7ed;
}

/* 当单元格处于编辑状态时，保持固定行高 */
td.active {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 36px;
  min-height: 36px;
  max-height: 36px;
  line-height: 20px;
  background-color: #f0f9ff !important;
  border: 2px solid #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
}

/* 现代化表头样式 - 强制固定定位 */
.modern-table-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 1001 !important; /* 增加z-index确保在最上层 */
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  /* 确保表头完全不透明 */
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.header-row {
  background: transparent;
  height: 36px; /* 降低表头高度 */
  min-height: 36px;
  max-height: 36px;
}

.header-cell {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  font-weight: 600;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%); /* 确保表头单元格有背景 */
  color: #374151;
  font-size: 14px;
  border-bottom: 3px solid #e5e7eb !important;
  border-right: 1px solid #e5e7eb;
  height: 36px;
  min-height: 36px;
  max-height: 36px;
  padding: 8px 16px; /* 减少上下内边距 */
  position: sticky; /* 每个表头单元格也设置sticky */
  top: 0;
  z-index: 999;
}

.header-cell:first-child {
  border-left: 1px solid #e5e7eb;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  position: relative;
}

.header-text {
  font-weight: 600;
  color: #374151;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
  letter-spacing: 0.025em;
}

/* 表头悬停效果 */
.header-cell:hover {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  transition: all 0.2s ease;
}

/* 表头不需要编辑状态，所以不应用active样式 */
thead .header-cell {
  white-space: nowrap !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
}

/* 确保表头在所有情况下都能固定 */
thead {
  position: sticky;
  top: 0;
  z-index: 1001;
}

/* 修复可能的浏览器兼容性问题 */
.virtual-table-container thead th,
.virtual-table-container thead td {
  position: sticky;
  top: 0;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  z-index: 1000;
}

/* 确保表头在滚动时保持可见 */
.virtual-table-container {
  scroll-behavior: smooth;
}

/* 为了确保在某些浏览器中正常工作 */
@supports (position: sticky) {
  .modern-table-header {
    position: sticky;
    top: 0;
  }

  .header-cell {
    position: sticky;
    top: 0;
  }
}

/* 强制表头固定 - 最高优先级 */
.virtual-table-container thead,
.virtual-table-container .modern-table-header {
  position: sticky !important;
  top: 0 !important;
  z-index: 1001 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

.virtual-table-container .header-cell {
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

/* 确保表头内容不会被遮挡 */
.header-content {
  position: relative;
  z-index: 1002;
}

/* 调试样式 - 可以临时添加来验证表头固定 */
.debug-sticky-header {
  position: sticky !important;
  top: 0 !important;
  background: red !important; /* 临时红色背景用于调试 */
  z-index: 9999 !important;
}

/* 最终的表头固定解决方案 */
.virtual-table-container {
  /* 确保容器有明确的滚动上下文 */
  overflow-y: auto !important;
  overflow-x: auto !important;
  max-height: 100% !important;
  position: relative !important;
}

.virtual-table-content {
  /* 虚拟内容容器不能影响sticky定位 */
  position: relative !important;
}

.virtual-table-container table {
  /* 确保表格不会影响sticky定位 */
  position: relative !important;
  width: 100% !important;
}

.virtual-table-container thead,
.virtual-table-container .modern-table-header {
  /* 最强制的表头固定 */
  position: sticky !important;
  top: 0 !important;
  z-index: 1001 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.virtual-table-container .header-cell {
  /* 表头单元格也需要sticky */
  position: sticky !important;
  top: 0 !important;
  z-index: 1000 !important;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%) !important;
}

/* 终极解决方案 - 覆盖所有可能的冲突样式 */
.virtual-table-container * {
  /* 确保没有transform影响sticky */
  transform: none !important;
}

.virtual-table-container {
  /* 确保容器本身不影响sticky */
  transform: none !important;
  will-change: auto !important;
}

.virtual-table-content {
  /* 确保内容容器不影响sticky */
  transform: none !important;
  will-change: auto !important;
}

/* 最强制的表头固定 - 覆盖一切 */
.virtual-table-container thead,
.virtual-table-container .modern-table-header,
.virtual-table-container .header-row {
  position: sticky !important;
  top: 0 !important;
  z-index: 9999 !important;
  background: #f8fafc !important;
  transform: none !important;
  will-change: auto !important;
}

.virtual-table-container .header-cell {
  position: sticky !important;
  top: 0 !important;
  z-index: 9998 !important;
  background: #f8fafc !important;
  transform: none !important;
  will-change: auto !important;
}

/* 分类行样式 - 现代化设计 */
.category {
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
  height: 40px; /* 分类行稍高一些 */
  min-height: 40px;
  max-height: 40px;
  border-left: 4px solid #0288d1 !important;
  position: relative;
}

.category:hover {
  background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
}

.category .delete_category {
  display: none;
  transition: all 0.2s ease;
}

.category:hover .delete_category {
  display: inline-block;
}

/* 分类标题行内容样式 */
.category-content {
  text-align: left !important;
  padding: 8px 16px;
  height: 40px;
  min-height: 40px;
  max-height: 40px;
  line-height: 24px;
  border: none !important;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
}

.category-name {
  font-weight: 600;
  font-size: 15px;
  color: #0277bd;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.category-title-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 操作按钮行样式 - 现代化设计 */
.action-row-content {
  text-align: left !important;
  padding: 16px 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0 !important;
  border-bottom: 2px solid #cbd5e1 !important;
  border-left: 1px solid #e4e7ed;
  border-right: 1px solid #e4e7ed;
  position: relative;
}

.action-row-content::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border-radius: 0 2px 2px 0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

/* 优化操作按钮样式 */
.action-buttons :deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.action-buttons :deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-buttons :deep(.el-button--primary) {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border: none;
}

.action-buttons :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
}

/* 产品行悬停效果 - 明显的黄色背景 */
.cell_hover:hover {
  background-color: #fef3c7 !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
}

/* 选中的数据行背景颜色 - 淡蓝色背景，黑色字体 */
.row-selected {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-left: 4px solid #409eff !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
  position: relative;
}

/* 选中行内的所有单元格 */
.row-selected td {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-color: #d4edda !important;
}

/* 选中行的悬停效果 - 稍深一点的淡蓝色 */
.row-selected:hover {
  background-color: #cce7ff !important;
  color: #000 !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

/* 选中行悬停时的所有单元格 */
.row-selected:hover td {
  background-color: #cce7ff !important;
  color: #000 !important;
}

/* 当前编辑产品高亮（保持向后兼容） - 淡蓝色背景，黑色字体 */
.current-editing {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-left: 4px solid #409eff !important;
  box-shadow: 0 2px 12px rgba(64, 158, 255, 0.2);
}

/* 当前编辑行内的所有单元格 */
.current-editing td {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-color: #d4edda !important;
}

.current-editing:hover {
  background-color: #cce7ff !important;
  color: #000 !important;
  box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  transform: translateY(-1px);
}

/* 当前编辑行悬停时的所有单元格 */
.current-editing:hover td {
  background-color: #cce7ff !important;
  color: #000 !important;
}

.left {
  text-align: left;
}

.center {
  text-align: center;
}

.right {
  text-align: right;
}

.error {
  background-color: var(--el-color-danger-light-8);
}

.index_product:hover .delete_product {
  display: inline-block;
}

.index_product:hover .index_product_span {
  display: none;
}

.index_product .delete_product {
  display: none;
  cursor: pointer;
}

.index_product .source {
  display: inline-block;
  height: 20px;
  width: 20px;
  text-align: center;
  line-height: 20px;
  border-radius: 15px;
  border: 1px solid var(--el-color-success);
  color: var(--el-color-success);
}
</style>
