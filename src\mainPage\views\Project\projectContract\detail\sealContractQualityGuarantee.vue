<template>
  <div>
    <div style="display: flex; justify-content: flex-end; margin-bottom: 20px">
      <el-button icon="edit" @click="dialogVisible = true">编辑</el-button>
    </div>
    <div>
      <el-result icon="info" title="提示" v-if="!form.id">
        <template #sub-title>
          <p>未完善质保服务</p>
        </template>
        <template #extra>
          <el-button type="primary" @click="edit">完善</el-button>
        </template>
      </el-result>
      <avue-form v-else :option="option" v-model="form"> </avue-form>
    </div>
    <el-dialog title="编辑质保信息" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <avue-form
        v-if="dialogVisible"
        ref="dialogForm"
        :option="addOption"
        @submit="submit"
        v-model="form"
      >
        <template #cycle>
          <div style="width: 100%; display: flex; align-items: center">
            <el-input
              v-model.number="form.cycleNumber"
              placeholder="请输入"
              style="width: 20%; margin-right: 5px"
            />
            <el-radio v-model="form.cycleType" :label="0">天</el-radio>
            <el-radio v-model="form.cycleType" :label="1">月</el-radio>
            <el-radio v-model="form.cycleType" :label="2">年</el-radio>
          </div>
        </template>
      </avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const { proxy } = getCurrentInstance();
let option = ref({
  labelWidth: 120,
  detail: true,
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'date',
      label: '开始质保日期',
      span: 24,
      width: 130,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },

    {
      type: 'input',
      label: '质保周期',
      width: 110,
      span: 24,
      display: true,
      prop: 'cycleTime',
    },
    {
      type: 'date',
      label: '质保到期时间',
      span: 24,
      width: 130,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'overDate',
    },
    {
      type: 'select',
      label: '提醒时间',
      width: 110,
      span: 24,
      display: true,
      props: {
        value: 'value',
        label: 'label',
      },
      dicData: (() => {
        let arr = [];
        for (let i = 1; i <= 31; i++) {
          arr.push({ label: '到期' + i + '天前', value: i });
        }
        return arr;
      })(),
      prop: 'beforeDays',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
    },
  ],
});
let addOption = ref({
  labelWidth: 120,

  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      type: 'date',
      label: '开始质保日期',
      span: 24,
      width: 130,
      rules: [{ required: true, message: '请选择开始质保日期', trigger: 'change' }],
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },

    {
      type: 'input',
      label: '质保周期',
      width: 110,
      span: 24,
      display: true,
      prop: 'cycle',
    },
    {
      label: '是否提醒',
      prop: 'isRemind',
      type: 'switch',
      value: 1,
      dicData: [
        { label: '否', value: 0 },
        { label: '是', value: 1 },
      ],
      control: val => {
        return {
          beforeDays: {
            display: !!val,
          },
        };
      },
    },
    {
      type: 'select',
      label: '提醒时间',
      width: 110,
      span: 24,
      display: true,
      props: {
        value: 'value',
        label: 'label',
      },
      dicData: (() => {
        let arr = [];
        for (let i = 1; i <= 31; i++) {
          arr.push({ label: '到期' + i + '天前', value: i });
        }
        return arr;
      })(),
      prop: 'beforeDays',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
    },
  ],
});
let form = ref({
    id:null
});
watch(() => {
  if (route.query.id) {
    getDetail();
  }
});
function getDetail() {
  axios
    .get('/api/vt-admin/sealContractQualityGuarantee/detail', {
      params: {
        sealContractId: route.query.id,
      },
    })
    .then(res => {
      form.value = res.data.data;
      form.value.cycleTime = form.value.cycleNumber + `${['天', '月', '年'][form.value.cycleType]}`;
    });
}
let dialogVisible = ref(false);
function edit() {
  dialogVisible.value = true;
}
function submit(form, done, loading) {
  form.createTime = null;
let url = form.id?
  '/api/vt-admin/sealContractQualityGuarantee/update':
  '/api/vt-admin/sealContractQualityGuarantee/save';
  axios
    .post(url, {
      ...form,
      sealContractId: route.query.id,
    })
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        done();
        getDetail();
        dialogVisible.value = false;
      }
    });
}
</script>

<style lang="scss" scoped></style>
