<template>
  <div style="height: calc(100vh - 170px)" :class="{ isFullSreen: isFullSreen }">
    <Title style="margin: 0 15px; background-color: #fff"
      >{{ isEdit ? '编辑方案' : `${form.name || ''}方案详情` }}({{ form.optionName }})
      <template #foot>
        <div v-if="isDeep">
          <el-button type="primary" @click="baseDrawer = true">基本信息</el-button>
          <el-button
            type="primary"
            @click="draftDeep"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >保存草稿</el-button
          >
          <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
          >保存草稿并生成报价单</el-button
        > -->

          <el-button
            type="primary"
            @click="draftDeep('confirm')"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            >保存并提交</el-button
          >

          <el-button
            type="primary"
            @click="deepAdminSubmit"
            v-if="isEdit && route.query.isAdmin == 'admin'"
            >保存</el-button
          >
          <el-button
            type="primary"
            icon="check"
            v-if="form.auditStatus == 1 && route.query.isAdmin == 'admin'"
            @click="deepConfirm"
            >确认</el-button
          >
          <el-button
            @click="
              $router.$avueRouter.closeTag();
              $router.back();
            "
            v-if="!props.businessPerson"
            >关闭</el-button
          >
        </div>
        <div v-else>
          <el-button
            icon="FullScreen"
            :title="isFullSreen ? '取消全屏' : '全屏'"
            @click="handleScreen"
          ></el-button>
          <el-button
            type="primary"
            @click="exportProgramme()"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >导入</el-button
          >
          <el-button type="primary" @click="baseDrawer = true">基本信息</el-button>
          <el-button
            type="primary"
            @click="submit()"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            plain
            >保存草稿</el-button
          >
          <!-- <el-button type="primary" @click="draft" v-if="isEdit && !props.businessPerson"
            >保存草稿并生成报价单</el-button
            > -->
          <el-button
            type="primary"
            @click="draft('confirm')"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            >保存并提交</el-button
          >

          <el-button
            type="warning"
            @click="savehistory"
            v-if="isEdit && !props.businessPerson && route.query.isAdmin != 'admin'"
            >保存为历史版本</el-button
          >
          <el-button
            type="primary"
            @click="adminSubmit"
            v-if="isEdit && route.query.isAdmin == 'admin'"
            >保存</el-button
          >
          <el-button
            type="primary"
            icon="check"
            v-if="form.auditStatus == 1 && route.query.isAdmin == 'admin'"
            @click="normalConfirm"
            >确认</el-button
          >
          <el-button
            @click="
              $router.$avueRouter.closeTag();
              $router.back();
            "
            v-if="!props.businessPerson"
            >关闭</el-button
          >
        </div>
      </template></Title
    >
    <div style="height: calc(100% - 90px)">
      <Sheet :option="sheetOption" ref="sheet"></Sheet>
    </div>
  </div>
  <el-drawer title="基本信息" v-model="baseDrawer">
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
      <template #baseInfo-header="column">
        <span class="avue-group__title" style="margin-right: 10px">基本信息</span>
        <el-button @click.stop="viewBusiness" type="primary" size="small">商机详情</el-button>
      </template>
      <template #distributionOption-header="column">
        <span class="avue-group__title" style="margin-right: 10px; color: var(--el-color-warning)"
          >主管备注</span
        >
      </template>
      <template #optionFiles v-if="option.detail">
        <File :fileList="form.optionFileList"></File>
      </template>
    </avue-form>
    <template #footer>
      <div style="flex: auto">
        <el-button type="primary" @click="baseDrawer = false">确认</el-button>
      </div>
    </template>
  </el-drawer>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-drawer v-model="drawer" size="60%">
    <Title>{{ businessForm.name }}</Title>
    <BusinessDetail :form="businessForm" :isEdit="false"></BusinessDetail>
  </el-drawer>
  <div class="programmeSelect" :class="{ active: isExport && route.query.isView != 1 }">
    <Title style="margin-bottom: 10px"
      >历史方案
      <template #foot
        ><el-icon @click="isExport = false" style="cursor: pointer" size="large"
          ><CircleCloseFilled /></el-icon></template
    ></Title>
    <el-input
      v-model="optionName"
      placeholder="请输入商机名称"
      style="width: 100%"
      @input="handleInput"
    ></el-input>
    <div
      v-if="!isHistoryProgramme"
      style="height: 420px; overflow-y: auto"
      :infinite-scroll-disabled="programmeList.length < 7"
      v-infinite-scroll="loadMore"
    >
      <div
        class="content"
        style="box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); margin: 5px; border-radius: 5px"
        v-for="(i, index) in programmeList"
      >
        <div class="title">
          <el-text size="large" type="primary">{{ i.name }}</el-text>
          <!-- <el-text type="warning" size="small">方案场景</el-text> -->
        </div>
        <!-- <span style="font-size: 12px" class="random-color-example">服务器</span> -->
        <div class="btn_box" style="display: flex; justify-content: space-between; height: 31px">
          <div class="left_btn">
            <el-button type="primary" size="small" @click="handleView(i)" plain round
              >预览</el-button
            >
          </div>
          <div class="right_btn">
            <el-button
              type="success"
              @click="useOption(i.id, !i.isHasDataJson)"
              plain
              size="small"
              round
              >使用</el-button
            >
          </div>
          <div class="right_btn">
            <el-button plain size="small" @click="viewHistory(i)" round>查看历史</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 历史 -->
    <div v-else style="height: 420px; overflow-y: auto">
      <div
        class="content"
        style="box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.3); margin: 5px; border-radius: 5px"
        v-for="(i, index) in programmeHistoryList"
      >
        <div class="title">
          <el-text size="large" type="primary">{{ i.optionName }}</el-text>
          <el-text type="warning" size="small">v${{ row.version }}.0</el-text>
        </div>
        <!-- <span style="font-size: 12px" class="random-color-example">服务器</span> -->
        <div class="btn_box" style="display: flex; justify-content: space-between; height: 31px">
          <div class="left_btn">
            <el-button type="primary" size="small" plain round>预览</el-button>
          </div>
          <div class="right_btn">
            <el-button type="success" @click="useOption(i.id, !i.dataJson)" plain size="small" round
              >使用</el-button
            >
          </div>
          <div class="right_btn">
            <!-- <el-button  plain size="small"  round>查看历史</el-button> -->
          </div>
        </div>
      </div>
      <el-empty v-if="programmeHistoryList.length == 0"></el-empty>
      <div style="text-align: center">
        <el-button @click="isHistoryProgramme = false" type="">返回</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance, onBeforeUnmount, onMounted, onUnmounted, handleError } from 'vue';
import { useRouter, useRoute, onBeforeRouteLeave } from 'vue-router';

import BusinessDetail from '@/views/CRM/businessOpportunity/detail/baseInfo.vue';

import { randomLenNum } from '@/utils/util';
import { loadFile } from '@/utils/file.js';
import { columnHideData as columnHideDataCopy } from '../../compoents/normal';

const route = useRoute();
const router = useRouter();
let isEdit = ref(route.query.type == 'edit' || route.query.type == 'add');
let form = ref({
  moduleDTOList: [
    {
      moduleName: '汇总',
    },
  ],
});
const props = defineProps({
  stageStatus: {
    type: Number,
    default: -1,
  },
  businessPerson: {
    type: String,
    default: '',
  },
});
let loading = ref(false);
const isDeep = ref(route.query.deep == '1');
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
  isEdit.value = route.query.type == 'edit' || route.query.type == 'add';
});
onMounted(() => {
  // if (route.query.id) {
  //   getDetail();
  // }
});

let option = ref({
  submitBtn: false,
  labelWidth: 100,
  detail: !isEdit.value || isDeep.value,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      column: [
        {
          label: '方案名称',
          prop: 'optionName',
          span: 24,
          rules: [
            {
              required: true,
              message: '请填写方案名称',
            },
            {
              validator: (rule, value, callback) => {
                const reg = /^[^/\\?？\[\]]*$/;
                console.log(value, rule, reg);
                if (!reg.test(value)) {
                  callback(new Error('不能包含特殊字符"/\?？[]"'));
                } else {
                  callback();
                }
              },
              trigger: 'change',
            },
          ],
        },
        {
          label: '客户名称',
          prop: 'customerName',
          readonly: true,
          span: 24,
        },
        {
          label: '关联联系人',
          prop: 'concatName',
          span: 24,
          readonly: true,
        },
        // {
        //   label: '人工费',
        //   prop: 'isNeedLabor',
        //   span:24,
        //   readonly: true,
        //   type: 'switch',

        //   dicData: [
        //     {
        //       value: 0,
        //       label: '否',
        //     },
        //     {
        //       value: 1,
        //       label: '是',
        //     },
        //   ],
        // },
        // {
        //   label: '自定义',
        //   prop: 'isHasCustom',
        //   readonly: true,
        //   type: 'switch',
        //   labelTip: '打开后点击文字即可编辑',
        //   span: 4,
        //   dicData: [
        //     {
        //       value: 0,
        //       label: '否',
        //     },
        //     {
        //       value: 1,
        //       label: '是',
        //     },
        //   ],
        // },
        // {
        //   label: '专项报价',
        //   prop: 'isHasSpecialPrice',
        //   type: 'radio',
        //   span: 24,
        //   value: 0,
        //   dicData: [
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //   ],
        // },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,

          // format: 'YYYY-MM-DD',
          // valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '上传附件',
          prop: 'optionFiles',
          type: 'upload',
          span: 24,
          dragFile: true,
          // rules: [
          //   {
          //     required: true,
          //     validator: validatorPath,
          //     trigger: "change",
          //   },
          // ],
          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',
          hide: true,
          viewDisplay: false,
          uploadPreview: (file, data) => {
            console.log(file, data);
            const { originalName, link } =
              form.value.optionFileList.find(item => item.id == file.url) || {};
            loadFile(link, originalName);
          },
        },
      ],
    },
    {
      label: '主管备注',
      prop: 'distributionOption',
      arrow: true,
      column: [
        {
          label: '',
          type: 'textarea',
          span: 24,
          readonly: true,
          prop: 'distributionOptionRemark',
        },
      ],
    },
  ],
});
let { proxy } = getCurrentInstance();

const editableTabsValue = ref(0);
// 是否是深化设计

let sheetOption = ref({
  option: '', //JSON数据
  detailList: [], //详情数据
  isRefresh: false, //是否刷新表格中的数据
  type: 2, //1 报价 2 方案
});
function getDetail() {
  let url = '/api/vt-admin/businessOpportunityOption/detailByOptionId';
  if (isDeep.value) {
    url = '/api/vt-admin/businessOpportunityOptionHistory/detailByOptionId';
  }
  if (route.query.isHistory) {
    url = '/api/vt-admin/businessOpportunityOptionHistory/detail';
  }
  if (route.query.isTranslate) {
    url = '/api/vt-admin/businessOpportunityOption/detailByHistoryOptionId';
  }

  loading.value = true;
  axios
    .get(url, {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      const data = res.data.data;
      sheetOption.value.option = data.dataJson;
      sheetOption.value.detailList =
        data.moduleVOList &&
        data.moduleVOList.reduce((pre, cur) => {
          pre.push(...cur.detailVOList);
          return pre;
        }, []);
      // sheetOption.value.isRefresh =res.data.auditStatus != 1
      sheetOption.value.isRefresh = res.data.auditStatus != 1;
      console.log(sheetOption.value.detailList);
      form.value = data;

      if (!form.value.optionName) {
        form.value.optionName = route.query.name + '的方案';
      }
      if (form.value.optionFileList) {
        form.value.optionFiles = form.value.optionFileList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        });
      } else {
        form.value.optionFiles = [];
      }

      let { productRate, labourRate, otherRate, warrantyRate, configuredJson } = form.value;
      if (!configuredJson) {
        configuredJson = '{}';
      }

      const {
        isFullSreen: newIsFullSreen = false,
        isFold = false,
        columnHideData = columnHideDataCopy(),
        isLock = false,
        tax = '',
      } = JSON.parse(configuredJson);
      isFullSreen.value = newIsFullSreen;
      sheetOption.value.form = {
        productRate: productRate || 0.13,
        labourRate: labourRate || 0.06,
        otherRate: otherRate || 0.06,
        warrantyRate: warrantyRate || 0.06,
        isLock,
        tax,
        columnHideData,
        isFold,
      };
      proxy.$refs.sheet.renderSheet();

      if (isDeep.value) return;
    });
}

function formatData(type = '') {
  const sheetData = proxy.$refs.sheet.getSheetData();
  let moduleKey = type == '' ? 'moduleDTOList' : 'moduleHistoryDTOList';
  let detailKey = type == '' ? 'detailDTOList' : 'detailHistoryDTOS';
  const { configuredJson } = sheetData;
  const data = {
    ...form.value,
    ...sheetData,
    optionFiles: form.value.optionFiles && form.value.optionFiles.map(item => item.value).join(','),
    configuredJson: JSON.stringify({
      ...configuredJson,
      isFullSreen: isFullSreen.value,
    }),
    [moduleKey]: sheetData.modelData.map(item => {
      return {
        moduleName: item.name,
        remark: item.remark,
        sort: item.sort,
        [detailKey]: item.productList.map(item => {
          return {
            ...item,
            customProductName: item.productName,
            customProductSpecification: item.productSpecification,
            customProductDescription: item.description,
          };
        }),
      };
    }),
    modelData: null,
  };

  return data;
}
let isSubmit = ref(false);
// 非深化设计提交
function draft(type) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        submit(type, done);
      });
    } else {
      baseDrawer.value = true;
    }
  });
}
function submit(type, done = () => {}) {
  let data = formatData();
  console.log(data);
  return new Promise((resolve, reject) => {
    if (data.id) {
      axios
        .post('/api/vt-admin/businessOpportunityOption/update', {
          ...data,
          optionStatus: type == 'confirm' ? 2 : 0,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          // getDetail();
          done();
          resolve(data.id);
          if (type == 'confirm') {
            isEdit.value = false;
            proxy.$router.$avueRouter.closeTag();
            proxy.$router.back();
          }

          // option.value.detail = true;
        });
    } else {
      data = {
        ...data,
        businessOpportunityId: route.query.id,
        optionStatus: type == 'confirm' ? 2 : 0,
      };
      axios.post('/api/vt-admin/businessOpportunityOption/save', data).then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();
        done();
        resolve(res.data.data);
        if (type == 'confirm') {
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }

        // option.value.detail = true;
      });
    }
  });
}
function adminSubmit(params) {
  return new Promise((resolve, reject) => {
    let data = formatData();
    axios
      .post('/api/vt-admin/businessOpportunityOption/updateOption', {
        ...data,
      })
      .then(res => {
        proxy.$message.success('保存成功');
        resolve();
      })
      .catch(() => {
        reject();
      });
  });
}

// 深化设计提交
function draftDeep(type) {
  // let res = [];
  // let logList = [];
  // form.value.moduleDTOList.forEach((item, index) => {
  //   console.log(item);
  //   if (!item.detailDTOList) return;
  //   item.detailDTOList.forEach((item1, index1) => {
  //     item1.productList.forEach((item2, index2) => {
  //       proxy.$refs[`sealPrice-${index}-${index1}-${index2}`][0].validate(valid => {
  //         if (valid) {
  //           res.push(true);
  //         } else {
  //           logList.push(`${item.moduleName}-${item1.classify}-${item2.productName}单价未填`);
  //           res.push(false);
  //         }
  //         console.log(res);
  //       });
  //     });
  //   });
  // });

  // setTimeout(() => {
  //   if (!res.some(i => !i)) {
  //     proxy.$refs.addForm.validate((valid, done) => {
  //       if (valid) {
  //         proxy.$confirm('确认此次操作吗？', '提示').then(() => {
  //           deepSubmit(type, done);
  //         });
  //       }
  //     });
  //   } else {
  //     proxy.$message.warning(logList.join('\n'));
  //   }
  // }, 0);
  deepSubmit(type);
}
function deepSubmit(type) {
  let data = formatData('deep');
  if (data.id) {
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/editDeepenDesign', {
        ...data,
        optionStatus: type == 'confirm' ? 1 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();

        if (type == 'confirm') {
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }
        // isEdit.value = false;
        // option.value.detail = true;
      });
  } else {
    data = {
      ...data,
      businessOpportunityId: route.query.id,
      optionStatus: type == 'confirm' ? 1 : 0,
    };
    axios
      .post('/api/vt-admin/businessOpportunityOptionHistory/saveDeepenDesign', data)
      .then(res => {
        proxy.$message.success(res.data.msg);
        getDetail();

        if (type == 'confirm') {
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        }
        // isEdit.value = false;
        // option.value.detail = true;
      });
  }
}
function deepAdminSubmit(params) {
  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      proxy.$confirm('确认此次操作吗？', '提示').then(() => {
        let data = formatData();

        axios
          .post('/api/vt-admin/businessOpportunityOption/updateOption', {
            ...data,
          })
          .then(res => {
            proxy.$message.success(res.data.msg);
            getDetail();
            done();
            // isEdit.value = false;
            // option.value.detail = true;
          });
      });
    }
  });
}
// 深化提交over

let drawer = ref(false);
let businessForm = ref({});
function viewBusiness(params) {
  drawer.value = true;
  getBusinessDetail();
}
function getBusinessDetail() {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id: route.query.businessOpportunityId || form.value.businessOpportunityId,
      },
    })
    .then(res => {
      const { provinceCode, cityCode, areaCode } = res.data.data;
      businessForm.value = {
        ...res.data.data,
        province_city_area: [provinceCode, cityCode, areaCode],
        isProduct: res.data.data.productVOList && res.data.data.productVOList.length == 0 ? 0 : 1,
        productVOList:
          res.data.data.productVOList &&
          res.data.data.productVOList.map(item => {
            return {
              ...item,
              ...item.productVO,
            };
          }),
        // registeredCapital: Number(res.data.data.registeredCapital),
      };
    });
}

function normalConfirm(row) {
  adminSubmit()
    .then(() => {
      proxy.$refs.dialogForm.show({
        title: '审核',
        option: {
          column: [
            {
              label: '审核结果',
              type: 'radio',
              value: 2,
              dicData: [
                {
                  value: 2,
                  label: '通过',
                },
                {
                  value: 3,
                  label: '不通过',
                },
              ],
              prop: 'auditStatus',
              control: val => {
                return {
                  auditReason: {
                    display: val == 3,
                  },
                };
              },
            },
            {
              label: '审核原因',
              prop: 'auditReason',
              type: 'textarea',
              span: 24,
            },
          ],
        },
        callback(res) {
          console.log(form.value);
          axios
            .post('/api/vt-admin/businessOpportunityOption/audit', {
              id: form.value.id,
              ...res.data,
            })
            .then(e => {
              proxy.$message.success('操作成功');
              res.close();

              proxy.$router.$avueRouter.closeTag();
              proxy.$router.back();
            });
        },
      });
    })
    .catch(() => {
      proxy.$message.warning('保存草稿失败，请重试');
    });
}
function deepConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type: 'radio',
          value: 2,
          dicData: [
            {
              value: 2,
              label: '通过',
            },
            {
              value: 3,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control: val => {
            return {
              auditReason: {
                display: val == 3,
              },
            };
          },
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunityOptionHistory/auditDeepenDesign', {
          id: form.value.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
          isEdit.value = false;
          proxy.$router.$avueRouter.closeTag();
          proxy.$router.back();
        });
    },
  });
}
function findValueByLabel(lable, arr) {
  return Boolean(arr.find(item => item.label === lable).value);
}

let baseDrawer = ref(true);

let isFullSreen = ref(false);
function handleScreen() {
  isFullSreen.value = !isFullSreen.value;
  proxy.$nextTick(() => {
    luckysheet.resize();
  });
}
function savehistory() {
  proxy.$refs.dialogForm.show({
    title: '保存历史版本',
    option: {
      column: [
        {
          label: '方案名称',
          type: 'input',
          prop: 'optionName',
          value: form.value.optionName,
          rules: [
            {
              required: true,
              message: '请输入方案名称',
              trigger: 'blur',
            },
          ],
          span: 24,
        },
        {
          label: '备注',
          type: 'textarea',
          prop: 'remark',
          span: 24,
        },
      ],
    },
    callback(res) {
      // 先保存草稿
      submit().then(r => {
        axios
          .post('/api/vt-admin/businessOpportunityOption/saveHistoryOption', {
            ...res.data,
            id: r,
          })
          .then(r => {
            proxy.$message.success(r.data.msg);
            res.close();
          });
      });
    },
  });
}

//  导入
let isExport = ref(false);
let currentSelectProgramme = ref(0);
function exportProgramme() {
  isExport.value = !isExport.value;
  programmeList.value = [];
  page.value.currentPage = 1;
  queryProgramme();
}
function handleClick(index) {
  currentSelectProgramme.value = index;
}
function loadMore() {
  console.log(2222);
  page.value.currentPage += 1;
  queryProgramme();
}
let page = ref({
  currentPage: 1,
  pageSize: 10,
});
let optionName = ref('');
let programmeList = ref([]);
let currentId = ref('');
let programmeHistoryList = ref([]);
let isHistoryProgramme = ref(false);
function queryProgramme(params) {
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/businessOpportunity/needOptionPage', {
      params: {
        size,
        current,
        selectType: 0,
        name: optionName.value,
        optionStatus: 2,
      },
    })
    .then(res => {
      programmeList.value.push(...res.data.data.records);
    });
}
function queryHistoryList(params) {
  axios
    .get('/api/vt-admin/businessOpportunityOptionHistory/page', {
      params: {
        optionId: currentId.value,
        size: 5000,
        current: 1,
      },
    })
    .then(res => {
      programmeHistoryList.value = res.data.data.records;
    });
}
function handleInput(value) {
  optionName.value = value;
  page.value.currentPage = 1;
  programmeList.value = [];
  queryProgramme();
}
function viewHistory(row) {
  console.log(1111);
  currentId.value = row.optionId;
  isHistoryProgramme.value = true;
  queryHistoryList();
}
function useOption(id, bol) {
  if (bol) {
    return proxy.$message.warning('未查询到方案');
  }
  proxy
    .$confirm('是否确认使用该方案,将会覆盖当前数据?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      let url;
      if (isHistoryProgramme.value) {
        url = '/api/vt-admin/businessOpportunityOptionHistory/detail';
      } else {
        url = '/api/vt-admin/businessOpportunityOption/detailByOptionId';
      }
      axios
        .get(url, {
          params: {
            id,
          },
        })
        .then(res => {
          const data = res.data.data;
          sheetOption.value.option = data.dataJson;
          sheetOption.value.detailList =
            data.moduleVOList &&
            data.moduleVOList.reduce((pre, cur) => {
              pre.push(...cur.detailVOList);
              return pre;
            }, []);
          let { productRate, labourRate, otherRate, warrantyRate, configuredJson } = form.value;
          if (!configuredJson) {
            configuredJson = '{}';
          }
          const {
            isFullSreen: newIsFullSreen = false,
            isFold = false,
            columnHideData = columnHideDataCopy(),
          } = JSON.parse(configuredJson);
          isFullSreen.value = newIsFullSreen;
          sheetOption.value.form = {
            productRate: productRate || 0.13,
            labourRate: labourRate || 0.06,
            otherRate: otherRate || 0.06,
            warrantyRate: warrantyRate || 0.06,
            isLock: false,
            columnHideData,
            isFold,
          };
          proxy.$refs.sheet.renderSheet(proxy.$refs.sheet.refreshData);
        });
    });
}
function handleView(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        name: `预览-${row.name}`,
        type: 'detail',
        isView: 1,
      },
    });
  } else if (row.isHasDataJson == 0) {
    proxy.$message.warning('未查询到方案');
  } else {
    proxy.$message.error('未查询到方案');
  }
}
onBeforeRouteLeave((to, from) => {
  if (isEdit.value) {
    const answer = window.confirm('确定离开当前页面吗？');
    // 取消导航并停留在同一页面上
    if (!answer) {
      proxy.$store.commit('ADD_TAG', {
        name: from.name || from.name,
        path: from.path,
        fullPath: from.fullPath,
        params: from.params,
        query: from.query,
        meta: from.meta,
      });
      return false;
    }
  }
});
</script>

<style scoped lang="scss">
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
::v-deep .el-table .el-table__cell {
  padding: 0;
}
::v-deep .el-collapse-item__header {
  line-height: 33px;
}
::v-deep .el-form-item {
  /* margin-bottom: 5px; */
}
::v-deep .el-tabs__header {
  margin-bottom: 5px;
}
.el-collapse-item__content {
  padding-bottom: 8px;
}
.el-table .el-form-item {
  margin-bottom: 0;
}
.fullScreen {
  position: fixed;

  box-sizing: border-box;
  width: 100vw;
  height: 100vh;
  overflow-y: auto;
  left: 0;
  top: 0;
  z-index: 999;
  background-color: #fff;
  padding: 15px;
}
::v-deep .affix .el-table .el-table__empty-block {
  display: none !important;
}
::v-deep .affix .el-table__body-wrapper {
  display: none !important;
}
.tab_box .move1 {
  display: none;
}
.tab_box:hover .move1 {
  display: inline-block;
}
.warningInput {
  /* border: 1px solid var(--el-color-warning) !important; */
}
::v-deep .warningInput .el-input__wrapper {
  box-shadow: 0 0 0 1px var(--el-color-danger) inset !important;
}
.warningInput {
  color: var(--el-color-danger);
}
.isFullSreen {
  position: fixed;
  height: 100vh !important;
  width: 100vw;
  background-color: #fff;
  top: 0;
  left: 0;
  z-index: 1000;
}
.programmeSelect {
  position: fixed;
  padding: 5px;
  top: calc(50% - 250px);
  right: -500px;
  border-radius: 5px;
  height: 500px;
  width: 300px;
  transition: all 0.3s;
  z-index: 99999;
  // border: 1px solid #ccc;
  box-shadow: 2px 2px 10px rgba(0, 0, 0, 0.3);
  background-color: #fff;
  .content {
    padding: 15px;
    padding-bottom: 0px;
    overflow: hidden;
    .title {
      display: flex;
      justify-content: space-between;
      border-bottom: 1px solid #ccc;
    }
    .btn_box {
      // background-color: red;
      height: 25px;
      transition: all 0.2s;
      line-height: 25px;
      div {
        width: 50%;
        text-align: center;
      }
    }
  }
}
.active {
  right: 20px;
}
.select {
  border: 1px solid $color-primary;
  .btn {
    height: 25px !important;
    margin-bottom: 5px;
  }
}
</style>
