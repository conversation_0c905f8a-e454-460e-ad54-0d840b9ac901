<template>
  <div>
    <myquation v-if="props.info.type == 0" :customerId="props.info.customerId"></myquation>
    <subordinatequation
      v-if="props.info.type == 1"
      :customerId="props.info.customerId"
    ></subordinatequation>
    <assistquation v-if="props.info.type == 2" :customerId="props.info.customerId"></assistquation>
    <allquation
      v-if="props.info.type == 3 || props.info.type == 4 || props.info.type == 5"
      :customerId="props.info.customerId"
    ></allquation>
  </div>
</template>

<script setup>
import myquation from '../../quotation/myquation.vue';
import subordinatequation from '../../quotation/subordinatequation.vue';
import assistquation from '../../quotation/assistquation.vue';
import allquation from '../../quotation/allquation.vue';
const props = defineProps(['info']);

</script>

<style lang="scss" scoped></style>
