<template>
  <div>
    <avue-form :option="option" :modelValue="props.form">
      <template #files> <File :fileList="form.attachList"></File></template>
    </avue-form>
  </div>
</template>

<script setup>
const props = defineProps({
  form: {
    type: Object,
    default: () => {},
  },
});

let option = ref({
  detail: true,
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  group: [
    {
      label: '基本信息',
      prop: 'base',
      arrow: false,
      column: [
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '项目板块',
          prop: 'businessTypeName',
        },
        {
          label: '项目属性',
          prop: 'projectAttribute',
          overHidden: true,
          dicData: [
            {
              label: '总包',
              value: 0,
            },
            {
              label: '分包',
              value: 1,
            },
          ],
          type: 'radio',
          hide: true,
        },
        {
      label: '开工时间',
      prop: 'startDate',
      type: 'date',
      span: 12,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
        {
      label: '交付时间',
      prop: 'deliveryDate',
    
      type:'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
        {
          label: '项目地址',
          prop: 'projectAddress',
        },
        {
          label: '项目总额',
          prop: 'projectPrice',
        },
        {
          label: '项目附件',
          prop: 'files',
        },
        {
          label: '项目备注',
          prop: 'projectRemark',
          span: 24,
          type: 'textarea',
        },
      ],
    },
    {
      label: '客户信息',
      prop: 'customerInfo',
      arrow: false,
      column: [
        {
          label: '客户名称',
          span: 12,
          display: true,
          prop: 'customerName',
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
      ],
    },
  ],
});
</script>

<style lang="scss" scoped></style>
