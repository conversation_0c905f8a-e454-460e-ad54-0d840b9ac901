<template>
  <div style="height: 100%">
 
    <div v-for="(item, listIndex) in listData">
      <h3 style="color: var(--el-color-primary);">{{ item.clueName }}</h3>
      <el-row style="height: 100%" :gutter="20">
        <el-col :span="5" style="height: 100%">
          <el-divider>采集目录</el-divider>

          <el-tree
            style="max-width: 600px"
            :data="treeData"
            ref="tree"
            node-key="id"
            check-strictly
            @check="(a, b) => handleChange(a, b, listIndex)"
            :props="{
              label: 'categoryName',
              children: 'children',
              disabled: 'disabled',
            }"
            show-checkbox
          />
        </el-col>

        <el-col :span="19" style="height: 100%">
          <!-- <Title>{{ categoryName || '----' }}</Title> -->

          <div style="margin: 20px 0">
            <el-form>
              <el-divider>采集配置</el-divider>

              <div v-for="(item, index) in item.allData">
                <el-divider content-position="left"
                  >{{ index + 1 }}. {{ item.categoryName }}</el-divider
                >
                <el-table class="avue-crud" :row-key="row => row.id" :data="item.tableData" border>
                  <el-table-column type="index" width="50" />
                  <el-table-column label="#" width="50" type="expand">
                    <template #default="scope">
                      <div style="margin-left: 100px; display: flex">
                        <el-tag
                          effect="plain"
                          style="margin-right: 5px; margin-bottom: 5px"
                          v-for="element in scope.row.valuesVOList"
                          :key="element.id"
                        >
                          {{ element.value }}
                        </el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="所属分类" prop="categoryName" width="100" /> -->
                  <el-table-column label="采集内容" prop="collectionContent" width="500" />
                  <el-table-column label="备注" prop="remark" width=""> </el-table-column>
                  <!-- <el-table-column label="选择项" >
                      <template #default="scope">
                        <div >
                          <draggable
                            v-model="scope.row.valuesVOList"
                            :animation="100"
                            @sort="
                              a => {
                                onMoveCallback(a, scope.row);
                              }
                            "
                          >
                            <transition-group>
                             <div   v-for="element in scope.row.valuesVOList">
                              <el-tag effect='plain'
                                style="margin-right: 5px;margin-bottom: 5px"
                               
                                :key="element.id"
                                closable
                                @close="handleClose(element)"
                              >
                                {{ element.value }}
                              </el-tag>
                             </div>
                            </transition-group>
                          </draggable>
    
                          <el-input
                            class="input-new-tag"
                            v-if="scope.row.inputVisible"
                            style="width: 100%"
                            v-model="scope.row.inputValue"
                            :ref="'saveTagInput' + scope.$index"
                            size="small"
                            @keyup.enter.native="addTag(scope.row)"
                            @blur="addTag(scope.row)"
                          >
                          </el-input>
    
                          <el-button
                            v-else-if="scope.row.type != 2"
                            class="button-new-tag"
                            size="small"
                            @click="showInput(scope.row, scope.$index)"
                            >+ 添加可选项</el-button
                          >
                        </div>
                      </template>
                    </el-table-column> -->
                  <el-table-column label="回答方式" prop="type" width="100">
                    <template #default="scope">
                      <el-tag effect="plain" type="success" v-if="scope.row.type == 0">单选</el-tag>
                      <el-tag effect="plain" type="success" v-else-if="scope.row.type == 1"
                        >多选</el-tag
                      >
                      <el-tag effect="plain" type="success" v-else>文本输入</el-tag>
                    </template>
                  </el-table-column>
                  <!-- <el-table-column label="是否使用" prop="isUse" width="100">
                      <template #default="scope">
                        <el-switch
                          :active-value="1"
                          :inactive-value="0"
                          disabled
                          v-model="scope.row.isUse"
                        ></el-switch
                      ></template>
                    </el-table-column> -->

                  <el-table-column label="操作" align="center" width="150">
                    <template #default="scope">
                      <!-- <el-button
                      text
                      icon="edit"
                      size="small"
                      type="primary"
                      @click="editParams(scope.row)"
                      >编辑</el-button
                    > -->
                      <el-button
                        text
                        icon="delete"
                        size="small"
                        type="danger"
                        @click="delParams(scope.row, index, listIndex)"
                        >删除</el-button
                      >
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </el-form>
          </div>
        </el-col>
      </el-row>
    </div>
    <dialogForm ref="dialogForm"></dialogForm>
  </div>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, reactive, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let form = ref({
  hasChildren: true,
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  defaultExpandAll: true,
  menu: false,
  multiple: true,
  checkStrictly: true,
  filter: true,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    children: 'children',
    value: 'id',
  },
  formOption: {
    column: {
      categoryName: {
        label: '分类名称',
        rules: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur',
          },
        ],
      },
    },
  },
});
onMounted(() => {
  getTreeData();
});
const updateUrl = '/api/vt-admin/product/update';
const props = defineProps(['list']);
let { proxy } = getCurrentInstance();
let tableData = ref([]);
let listData = ref([]);
let allData = ref([]);
watch(
  () => props.list,
  val => {
   
    if(!val || val.length == 0) return;
    listData.value = val.map(item => {
        return {
            ...item,
            allData:[]
        }
    });
  },
  {
    deep: true,
    immediate: true,
    
  }
);
let params = ref({});

let route = useRoute();

let loading = ref(false);

let router = useRouter();
let treeData = ref([]);
let isLock = ref(null);
let expandArr = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/requirementCategory/tree', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          disabled: true,
          leaf: !item.hasChildren,
        };
      });
      // console.log(props.data.keys, proxy.$refs.tree);
      // expandArr.value = props.data.keys;
      // proxy.$nextTick(() => {
      //   proxy.$refs.tree.setCheckedKeys(props.data.keys);
      // });
    });
}

let categoryId = ref('');
let categoryName = ref('');
let hasChildren = ref(true);

function getTableData(id, categoryName, index) {
  const { current, size } = page.value;
  axios
    .get('/api/vt-admin/requirementProperty/page', {
      params: {
        categoryId: id,
        current,
        size: 5000,
      },
    })
    .then(res => {
      ;
      listData.value[index].allData.push({
        id,
        categoryName,
        tableData: res.data.data.records,
      });
    });
}
function delParams(row, index, listIndex) {
  proxy
    .$confirm('确定删除此参数吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      listData.value[listIndex].allData[index].tableData = listData.value[listIndex].allData[
        index
      ].tableData.filter(item => item.id != row.id);
    });
}
let drag = ref(false);
let topList = ref([]);

function handleChange(a, b, index) {
  ;
  const bol = b.checkedKeys.includes(a.id);
  if (bol) {
    getTableData(a.id, a.categoryName, index);
  } else {
    listData.value[index].allData = listData.value[index].allData.filter(item => item.id != a.id);
  }
}
function getData() {
  console.log(
    allData.value.reduce((pre, cur) => {
      pre.concat(cur.tableData);
      return pre;
    }, [])
  );

  return listData.value.map(item => {
  
    return {
        ...item,
        discoverPcId:item.id,
        id:null,
        propertyDTOList: item.allData.reduce((pre, cur) => {
            return  pre.concat(cur.tableData);
        
        }, []).map(item => {
            return {
                requirementPropertyId: item.id,
            }
        }),
    }
  });
}
defineExpose({
  getData,
});
</script>

<style lang="scss" scoped></style>
