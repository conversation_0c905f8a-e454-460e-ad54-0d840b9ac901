<template>
  <Title v-if="!props.offerId"
    >{{ isDetail ? '报价详情' : '编辑报价' }}
    <template #foot>
      <!-- <el-button
          type="primary"
          @click="submit"
          v-if="isDetail && form.offerStatus == 0 && $store.getters.permission.quation_confirm_qua"
          >确认报价</el-button
        > -->
      <el-button
        type="primary"
        @click="draft"
        v-if="
          !isDetail &&
          form.offerStatus == 0 &&
          $store.getters.permission.quation_confirm_qua &&
          props.stageStatus == -1
        "
        plain
        >保存</el-button
      >
      <el-button
        type="primary"
        @click="submitValidate"
        v-if="
          !isDetail &&
          form.offerStatus == 0 &&
          $store.getters.permission.quation_confirm_qua &&
          props.stageStatus == -1
        "
        >保存并生成报价单</el-button
      >
      <el-button
        @click="
          $router.$avueRouter.closeTag();
          $router.back();
        "
        v-if="props.stageStatus == -1"
        >关闭</el-button
      ></template
    ></Title
  >
  <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form"></avue-form>
  <div style="margin-top: 20px">
    <el-table
      :data="form.detailList"
      :row-class-name="tableRowClassName"
      border
      class="avue-crud"
      show-summary
      :summary-method="productSum"
    >
      <el-table-column label="基本信息" align="center">
        <el-table-column label="设备名称" show-overflow-tooltip prop="productName">
          <template #default="{ row }">
            <el-popover
              placement="top"
              width="600"
              effect="light"
              trigger="click"
              :content="row.productName"
            >
              <template #default>
                <Title>原信息:</Title>
                <el-form>
                  <el-form-item label="产品名称:">
                    <span>{{ row.productName }}</span>
                  </el-form-item>
                  <el-form-item label="规格型号:">
                    <span>{{ row.productSpecification }}</span>
                  </el-form-item>
                  <el-form-item label="产品描述:">
                    <span>{{ row.description }}</span>
                  </el-form-item>
                </el-form>
              </template>
              <template #reference>
                <el-text style="cursor: pointer" type="primary">{{
                  row.customProductName
                }}</el-text>
              </template>
            </el-popover></template
          >
        </el-table-column>
        <el-table-column
          label="规格型号"
          prop="customProductSpecification"
          show-overflow-tooltip
        ></el-table-column>
        <el-table-column
          label="产品描述"
          show-overflow-tooltip
          width="200"
          prop="customProductDescription"
        ></el-table-column>
        <el-table-column label="产品图片" #default="{ row }">
          <el-image
            style="width: 80px"
            :z-index="1000000"
            :preview-src-list="[row.coverUrl]"
            :src="row.coverUrl"
          ></el-image>
        </el-table-column>

        <el-table-column label="品牌" prop="productBrand"></el-table-column>
        <el-table-column label="单位" prop="unitName"></el-table-column>
      </el-table-column>

      <el-table-column label="参考信息" align="center">
        <!-- <el-table-column
          label="采购价"
          v-if="$store.getters.permission.quation_price_view"
          #default="{ row }"
          prop="unitPrice"
        >
          <span>{{
            parseFloat(row.unitPrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.unitPrice).toLocaleString()
          }}</span>
        </el-table-column>
        <el-table-column
          label="参考采购价"
          v-if="$store.getters.permission.quation_price_view"
          #default="{ row }"
          prop="referPurchasePrice"
        >
          <span>{{
            parseFloat(row.referPurchasePrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.referPurchasePrice).toLocaleString()
          }}</span>
        </el-table-column> -->
        <el-table-column label="最近销售价" #default="{ row }" prop="preSealPrice">
          <span>{{
            parseFloat(row.preSealPrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.preSealPrice).toLocaleString()
          }}</span>
        </el-table-column>
        <el-table-column label="成本价" #default="{ row }" prop="costPrice">
          <span>{{
            parseFloat(row.costPrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.costPrice).toLocaleString()
          }}</span>
        </el-table-column>
        <el-table-column label="最低销售价" #default="{ row }" prop="minSealPrice">
          <span>{{
            parseFloat(row.minSealPrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.minSealPrice).toLocaleString()
          }}</span>
        </el-table-column>
        <el-table-column label="参考销售价" #default="{ row }" prop="referSealPrice">
          <span>{{
            parseFloat(row.referSealPrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.referSealPrice).toLocaleString()
          }}</span>
        </el-table-column>

        <el-table-column label="市场价" #default="{ row }" prop="marketPrice">
          <span>{{
            parseFloat(row.marketPrice).toLocaleString() == 'NaN'
              ? '--'
              : parseFloat(row.marketPrice).toLocaleString()
          }}</span>
        </el-table-column>
      </el-table-column>
      <!-- <el-table-column label="成本价" v-if="form.offerPeople == $store.getters.userInfo.user_id" #default="{ row }" prop="costPrice"> </el-table-column> -->
      <!-- <el-table-column
          label="是否含税"
          v-if="form.offerPeople == $store.getters.userInfo.user_id || $store.getters.permission.quation_price_view"
          #default="{ row }"
          prop="isHasTax"
        >
          <el-switch
            v-model="row.isHasTax"
            v-if="form.offerPeople == $store.getters.userInfo.user_id || $store.getters.permission.quation_price_view"
            inline-prompt
            active-text="是"
            :active-value="1"
            :inactive-value="0"
            inactive-text="否"
          />
        </el-table-column> -->
      <el-table-column label="报价信息" align="center">
        <el-table-column label="数量" #default="{ row }" prop="number"> </el-table-column>
        <el-table-column label="单价" #default="{ row, $index }" prop="sealPrice">
          <span v-if="isDetail">{{ parseFloat(row.sealPrice).toLocaleString() }}</span>
          <el-form :ref="'priceForm' + $index" v-else :model="row">
            <el-form-item
              prop="sealPrice"
              :rules="{
                required: true,
                message: '请填写单价',
                trigger: 'blur',
              }"
            >
              <el-input size="small" placeholder="请输入单价" v-model="row.unitPrice"></el-input>
            </el-form-item>
          </el-form>
        </el-table-column>
        <el-table-column label="金额" #default="{ row, $index }" prop="preTotalPrice">
          {{ parseFloat(row.number * row.sealPrice).toLocaleString() }}
        </el-table-column>
        <el-table-column
          label="专项成本"
          #default="{ row, $index }"
          width="120"
          v-if="form.isHasSpecialPrice"
          prop="specialCostPrice"
        >
          <el-input
            v-if="isEdit"
            v-model="row.specialCostPrice"
            placeholder="请输入专项成本"
            size="small"
            style="width: 100%"
          ></el-input>
          <span v-else>{{ row.specialCostPrice }}</span>
        </el-table-column>
        <el-table-column
          label="专项供应商"
          #default="{ row, $index }"
          width="120"
          v-if="form.isHasSpecialPrice"
          prop="specialSupplierId"
        >
          <WfSupplierSelect
            v-if="isEdit"
            v-model="row.specialSupplierId"
            placeholder="请选择专项供应商"
            size="small"
            style="width: 100%"
          ></WfSupplierSelect>
          <span v-else>{{ row.specialSupplierName }}</span>
        </el-table-column>
      </el-table-column>
      <!-- <el-table-column
          label="供应商"
          v-if="form.offerStatus == 2"
          #default="{ row, $index }"
          width="300"
          prop="supplier"
        >
          <span v-if="isDetail">{{ row.supplierName }}</span>
        </el-table-column> -->
      <!-- <el-table-column
        width="200"
        label="操作"
        #default="{ row }"
        align="center"
        v-if="form.auditStatus == 0 && $store.getters.permission.confirmEdit"
      >
        <el-button
          text
          type="primary"
          icon="Folder"
          v-if="row.isNew == 1"
          @click="handleClick(row)"
        >
          录入
        </el-button>
        <el-button text type="primary" icon="edit" v-else> 修改 </el-button>
      </el-table-column> -->
    </el-table>
    <el-dialog title="录入产品库" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <avue-form :option="addOption" @submit="addSubmit" ref="addFormRef" v-model="addForm">
        <template #productProperty>
          <div>
            <div
              class="item"
              style="display: flex; justify-content: flex-start; align-items: center"
              v-for="item in propertyList"
            >
              <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
              <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
              <span style="margin-left: 15px" v-if="item.type == 1">
                <el-checkbox-group v-model="item.selectList">
                  <el-checkbox
                    v-for="i in item.valuesEntityList"
                    :label="i.id + `-` + i.value"
                    size="large"
                    >{{ i.value }}</el-checkbox
                  >
                </el-checkbox-group>
              </span>
              <span style="margin-left: 15px" v-else-if="item.type == 0">
                <el-radio
                  v-model="item.radioSelect"
                  v-for="i in item.valuesEntityList"
                  :label="i.id + `-` + i.value"
                  size="large"
                  >{{ i.value }}</el-radio
                >
              </span>
              <span style="margin-left: 15px" v-else-if="item.type == 2">
                <el-input v-model="item.value" size="small"></el-input>
              </span>
            </div>
          </div>
        </template>
      </avue-form>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="$refs.addFormRef.addSubmit()" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import suppliyerSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import { dateFormat } from '@/utils/date';
import func from '@/utils/func';
let route = useRoute();
let router = useRouter();
let form = ref({
  offerDate: dateFormat(new Date(), 'yyyy-MM-dd'),
  detailList: [],
});
const props = defineProps({
  offerId: {
    type: String,
    default: '',
  },
  stageStatus: {
    type: Number,
    default: -1,
  },
});
let { proxy } = getCurrentInstance();
let isDetail = ref(route.query.type == 'detail');
let option = ref({
  submitBtn: false,
  labelWidth: 140,
  detail: false,
  emptyBtn: false,
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      disabled: true,
      rules: [
        {
          required: true,
          message: '请填写方案名称',
        },
      ],
    },
    {
      label: '关联商机',
      prop: 'businessOpportunityId',
      component: 'wf-business-select',
      disabled: true,
      // change: val => {
      //   if (val.value) {
      //     getDetail(val.value);
      //   }
      // },
    },
    {
      label: '对应客户',
      prop: 'customerId',
      disabled: true,
      component: 'wf-customer-select',
      change: val => {
        const contactPerson = proxy.findObject(option.value.column, 'contactPerson');
        contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val.value;
        customerId.value = val.value;
        reportForm.value.customerId = val.value;
      },
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 12,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'businessTypeId',
      checkStrictly: true,
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      disabled: true,
      component: 'wf-contact-select',
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },
    {
      label: '报价日期',
      prop: 'offerDate',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
      type: 'number',
    },
    {
      label: '专项报价',
      prop: 'isHasSpecialPrice',
      type: 'radio',
      span: 4,
      value: 0,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '线下报价',
      prop: 'isOnline',
      type: 'radio',
      span: 4,

      value: 0,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '自定义',
      prop: 'isHasCustom',
      readonly: true,
      type: 'switch',
      labelTip: '打开后点击文字即可编辑',
      span: 4,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
  if (isDetail.value) {
    option.value.detail = true;
  }
});

function draft() {
  let res = [];
  let arr = ['supplierForm', 'priceForm'];
  console.log(proxy.$refs);
  form.value.detailList.forEach((item, index) => {
    arr.forEach(i => {
      proxy.$refs[i + index].validate(valid => {
        console.log(valid);
        res.push(valid);
      });
    });
  });
  axios
    .post('/api/vt-admin/offer/updateOffer', {
      ...form.value,
      id: route.query.id,
    })
    .then(res => {
      proxy.$message.success(res.data.msg);
      router.$avueRouter.closeTag();
      isDetail.value = true;
    });
}
function submitValidate() {
  let res = [];
  let arr = ['supplierForm', 'priceForm'];
  console.log(proxy.$refs);
  form.value.detailList.forEach((item, index) => {
    arr.forEach(i => {
      proxy.$refs[i + index].validate(valid => {
        console.log(valid);
        res.push(valid);
      });
    });
  });

  setTimeout(() => {
    if (!res.some(i => !i)) {
      submit();
    }
  }, 0);
}

function submit() {
  proxy
    .$confirm('提交之后将会不可修改,确认报价?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/offer/updateOffer', {
          ...form.value,
          id: route.query.id,
          offerStatus: 1,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          router.$avueRouter.closeTag();
          router.go(-1);
        });
    });
}

function getDetail(id) {
  axios
    .get(
      route.query.isHistory == 'history'
        ? '/vt-admin/offerHistory/detail'
        : '/api/vt-admin/offer/detail',
      {
        params: {
          id: props.offerId || id,
        },
      }
    )
    .then(res => {
      const {
        detailVOList,
        // id: businessOpportunityId,
        customerId,
        businessOpportunityId,
        contactPerson,
        offerName,
        offerValidity,
        offerStatus,
        createUser,
        offerPeople,
        offerDate,
        auditStatus,
        businessTypeId,
        isHasSpecialPrice,
        isHasCustom,
        isOnline
      } = res.data.data;
      form.value.detailList = detailVOList.map(item => {
        delete item.product.id;
        return {
          number: item.number,
          ...item.product,
          productId: item.productId,
          // prePrice: item.prePrice,
          unitPrice: item.unitPrice,
          // marketPrice: item.marketPrice,
          sealPrice: item.sealPrice,
          preSealPrice: item.product.sealPrice,
          supplierName: item.supplierName,
          specialCostPrice: item.specialCostPrice,
          specialSupplierName: item.specialSupplierName,
          // minSealPrice: item.minSealPrice,
          // referSealPrice: item.referSealPrice,
          costPrice: auditStatus == 1 || auditStatus == 2 ? item.costPrice : item.product.costPrice,
          preSealPrice:
            auditStatus == 1 || auditStatus == 2
              ? item.sealPrice
              : item.product && item.product.sealPrice,
          minSealPrice:
            auditStatus == 1 || auditStatus == 2
              ? item.minSealPrice
              : item.product && item.product.minSealPrice,
          referSealPrice:
            auditStatus == 1 || auditStatus == 2
              ? item.referSealPrice
              : item.product && item.product.referSealPrice,
          supplierId: item.supplierId == 0 ? null : item.supplierId,
          id: item.id,
          customProductName: item.customProductName,
          customProductSpecification: item.customProductSpecification,
          customProductDescription: item.customProductDescription,
        };
      });
      form.value.businessOpportunityId = businessOpportunityId;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = offerName;
      form.value.offerStatus = offerStatus;
      form.value.offerValidity = offerValidity;
      form.value.createUser = createUser;
      form.value.offerDate = offerDate;
      form.value.offerPeople = offerPeople;
      form.value.auditStatus = auditStatus;
      form.value.businessTypeId = businessTypeId;
      form.value.isHasSpecialPrice = isHasSpecialPrice;
      form.value.isHasCustom = isHasCustom;
      form.value.isOnline = isOnline;
    });
}
function productSum(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => Number(item[column.property]));

      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'prePrice') {
      // const values = data.map(item => Number(item[column.property]));
      // sums[index] = `￥ ${values.reduce((prev, curr) => {
      //   const value = Number(curr);
      //   if (!Number.isNaN(value)) {
      //     return prev + curr;
      //   } else {
      //     return prev;
      //   }
      // }, 0)}`
    } else if (column.property == 'totalPrice') {
      const values = data.map(item => Number(item.number * item.unitPrice));

      sums[index] = `￥ ${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'preTotalPrice') {
      const values = data.map(item => Number(item.number * item.sealPrice));

      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'costPrice') {
      const values = data.map(item =>
        Number(
          item.number *
            (form.value.isHasSpecialPrice == 1 && item.specialCostPrice
              ? item.specialCostPrice * 1
              : item.costPrice * 1)
        )
      );
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    }
  });

  return sums;
}
function tableRowClassName({ row }) {
  if (row.isNew === 1) {
    return 'warning-row';
  } else {
    return '';
  }
}

// 录入产品库
let dialogVisible = ref(false);
let propertyList = ref([]);
let addForm = ref({});
let addOption = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  tabs: true,
  delBtn: false,

  meun: false,
  submitBtn: false,
  emptyBtn: false,

  menuWidth: 270,
  border: true,

  group: [
    {
      arrow: true,
      label: '基本信息',
      prop: 'baseInfo',
      column: [
        {
          label: '产品名称',
          prop: 'productName',
          overHidden: true,
          rules: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
          search: true,
        },
        {
          label: '产品分类',
          prop: 'categoryId',
          search: true,
          hide: true,
          type: 'tree',
          rules: [
            {
              required: true,
              message: '请选择产品分类',
              trigger: 'change',
            },
          ],
          dicUrl: '/api/vt-admin/productCategory/tree',
          props: {
            value: 'id',
            label: 'categoryName',
          },
          children: 'hasChildren',
          //   lazy: true,
          //   treeLoad: function (node, resolve) {
          //     axios
          //       .get('/api/vt-admin/productCategory/list', {
          //         params: {
          //           parentId: node.data.id,
          //         },
          //       })
          //       .then(res => {
          //         resolve(
          //           res.data.data.map(item => {
          //             return {
          //               ...item,
          //               leaf: !item.hasChildren,
          //             };
          //           })
          //         );
          //       });
          //   },
          change: val => {
            console.log(val, form.value);
            if (!val.value || val.value == form.value.categoryId) return;
            getPropertyList(val.value);
          },
        },
        {
          label: '品牌',
          prop: 'productBrand',
          overHidden: true,
          search: true,
        },
        {
          label: '单位',
          type: 'select',
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择单位',
              trigger: 'blur',
            },
          ],
          prop: 'unit',
          dicUrl: '/blade-system/dict/dictionary?code=unit',
          remote: false,
        },

        {
          label: '规格型号',
          prop: 'productSpecification',
          overHidden: true,
          search: true,
          span: 24,
          type: 'input',
        },

        {
          label: '产品图片',
          prop: 'coverUrl',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            addForm.value.coverId = res.id;

            done();
          },
        },
        {
          label: '产品参数',
          prop: 'productProperty',
          type: 'input',
          hide: true,
          slot: true,
          span: 24,
        },
        {
          label: '商品描述',
          prop: 'description',
          overHidden: true,
          type: 'textarea',
          span: 24,
        },

        {
          label: '用途',
          prop: 'purpose',
          overHidden: true,
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '价格信息',
      arrow: true,
      prop: 'priceInfo',
      labelWidth: '100px',
      column: [
        {
          label: '是否含税',
          prop: 'isHasTax',
          type: 'radio',
          rules: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
          labelTip: '采购价是否含税',
          dicData: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
        },
        {
          label: '采购价',
          prop: 'purchasePrice',
          type: 'number',
          overHidden: true,
          rules: [{ required: true, message: '请输入采购价', trigger: 'blur' }],
        },

        {
          label: '市场价',
          prop: 'marketPrice',
          type: 'number',
          overHidden: true,
        },
      ],
    },
  ],
});
function handleClick(row) {
  dialogVisible.value = true;
  getProductDetail(row.productId);
}
function getProductDetail(productId) {
  axios.get('/api/vt-admin/product/detail?id=' + productId).then(res => {
    addForm.value = res.data.data;
    propertyList.value = res.data.data.productPropertyVoS.map(item => {
      let radioSelect = '';
      let selectList = [];
      let value;
      if (item.type == 0) {
        if (item.entityList.filter(item => item.isCheck == 1).length > 0) {
          const { id, value } = item.entityList.filter(item => item.isCheck == 1)[0];
          radioSelect = `${id}-${value}`;
        }
      } else if (item.type == 1) {
        selectList = item.entityList
          .filter(item => item.isCheck == 1)
          .map(item => {
            const { id, value } = item;
            return `${id}-${value}`;
          });
      } else {
        value = item.value;
      }
      return {
        ...item,
        valuesEntityList: item.entityList,
        selectList,
        radioSelect,
        value,
      };
    });
  });
}
function addSubmit(form, done) {}
</script>

<style lang="scss" scoped></style>
