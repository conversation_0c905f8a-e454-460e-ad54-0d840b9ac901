<template>
  <div>
    
    <settlementList
      :is-apply="isApply"
      :is-audit="isAudit"
      :form="props.form"
      :get-url="isApply?'/api/vt-admin/sealContractSettlement/page':'/api/vt-admin/sealContractSettlement/pageForSettlementPlatform'"

    ></settlementList>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import settlementList from '../settlementList.vue';
import { useStore } from 'vuex';
const store = useStore();
const isApply = computed(() => {
  return store.getters.userInfo.tenant_id == props.form.tenantId;
})
const isAudit = computed(() => {
  return store.getters.userInfo.tenant_id != props.form.tenantId;
})


const props = defineProps({
  form: Object,
});
</script>

<style lang="scss" scoped></style>
