<template>
  <!-- 基本信息 -->
  <div>
    <avue-form :option="detailOption" @submit="handleSubmit" :modelValue="props.form">
      <template #contractFiles> <File :fileList="form.attachList || []"></File> </template>
      <template #customerInvoiceId>
        <span v-if="!form.customerInvoiceInfoVO">暂无</span>
        <el-form v-else inline label-position="left">
          <el-form-item label="客户公司名称：">
            <el-tag effect='plain'>{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.invoiceCompanyName
            }}</el-tag>
          </el-form-item>
          <el-form-item label="纳税人识别号：">
            <el-tag effect='plain'>{{
              form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.ratepayerIdentifyNumber
            }}</el-tag>
          </el-form-item>
          <el-form-item label="开户银行：">
            <el-tag effect='plain'>{{ form.customerInvoiceInfoVO && form.customerInvoiceInfoVO.bankName }}</el-tag>
          </el-form-item>
          <el-form-item label="初期末开票余额：">
            <el-text type="primary">{{
              form.customerInvoiceInfoVO &&
              parseFloat(form.customerInvoiceInfoVO.endTermNoInvoiceAmount).toLocaleString()
            }}</el-text>
          </el-form-item>
        </el-form>
      </template>
    </avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { businessOpportunityData } from '@/const/const';
const props = defineProps({
  form: {
    type: Object,
    default: () => {
      return { customerInvoiceInfoVO: {} };
    },
  },

  isEdit: Boolean,
});
watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
  if (props.sealContractId) {
    getDetail();
    console.log('查询详情');
  }
});
const route = useRoute();
let detailOption = ref({
  labelWidth: 150,
  submitBtn: false,
  detail: true,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      prop: 'baseInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          type: 'input',
          label: '关联报价',
          display: true,
          component: 'wf-quotation-select',
          prop: 'offerId',
        },
        {
          type: 'input',
          label: '关联商机',
          display: true,
          disabled: true,
          prop: 'businessOpportunityName',
        },
        {
          label: '客户名称',
          span: 12,
          display: true,
          prop: 'customerName',
        },
        {
          type: 'input',
          label: '客户地址',
          span: 12,
          display: true,
          prop: 'customerAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'customerContact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'customerPhone',
        },
        {
          label:'最终用户',
          prop:'finalCustomer',
          span:12
        }
      ],
    },

    {
      label: '送货信息',
      prop: 'distributionInfo',
      arrow: true,
      collapse: true,
      display: true,
      column: [
        {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 12,
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
        },
        {
          type: 'date',
          label: '客户要求交付日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          prop: 'deliveryDate',
          required: true,
          rules: [
            {
              required: true,
              message: '交付日期必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '交付地址',
          span: 24,
          display: true,
          prop: 'deliveryAddress',
        },
        {
          label: '关联联系人',
          type: 'input',
          prop: 'contact',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
        },
        {
          type: 'input',
          label: '电话',
          span: 12,
          display: true,
          prop: 'contactPhone',
        },
        {
          type: 'input',
          label: '开票信息',
          span: 24,
          display: true,
          prop: 'customerInvoiceId',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    {
      label: '交付信息',
      prop: 'deliveryInfo',
      column: [
        {
          label: '实际交付日期',
          type: 'actualDeliveryDate',
        },
        {
          label: '交付附件',
          type: 'actualDeliveryFile',
        },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: route.query.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
function getDetail() {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: props.sealContractId,
      },
    })
    .then(res => {
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        distributionMethod: '' + res.data.data.distributionMethod,
      };
    });
}
</script>

<style lang="scss" scoped></style>
