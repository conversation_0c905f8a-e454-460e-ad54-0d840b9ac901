<template>
  <el-drawer title="商机详情" size="90%" v-model="drawer">
    <detail :customerId="customerId" :type="type" :id="id"></detail>
  </el-drawer>
</template>

<script setup>
import detail from './detail_body.vue';
const props = defineProps(['customerId', 'id', 'type']);

const drawer = ref(false);
function open() {
  drawer.value = true;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
