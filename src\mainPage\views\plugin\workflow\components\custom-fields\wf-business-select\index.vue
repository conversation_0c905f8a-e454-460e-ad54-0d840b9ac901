<template>
  <div>
    <div style="display: flex; align-items: center">
      <el-select
        v-model="name"
        :size="size"
        style="width: 100%"
        suffix-icon="el-icon-user"
        :placeholder="placeholder || '商机选择'"
        :disabled="disabled"
        clearable
        filterable
        remote
        @change="handleUserSelectConfirm"
        :remote-method="remoteMethod"
      >
        <el-option v-for="item in data" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-button type="primary" v-if="!disabled" @click="handleSelect" icon="more"></el-button>
    </div>
    <!-- 商机选择弹窗 -->
    <wf-business-select
      ref="business-select"
      :check-type="checkType"
      :isNew="isNew"
      :userUrl="Url"
      :default-checked="modelValue"
      @onConfirm="handleUserSelectConfirm"
    ></wf-business-select>
  </div>
</template>
<script>
import { getUser } from '../../../api/process/user';

import WfBusinessSelect from '@/components/Y-UI/wf-business-select.vue';

export default {
  name: 'business-select',
  components: { WfBusinessSelect },
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      // default: () => {
      //   return 'small'
      // }
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    Url: {
      type: String,
      default: () => {
        return '/vt-admin/businessOpportunity/page?selectType=0';
      },
    },
    change: Function,
    isNew: {
      type: Boolean,
      default: false,
    },
  },
  mounted() {
    console.log(this.isNew);
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          const name = [];
          const checks = (val + '').split(',');
          const asyncList = [];
          checks.forEach(c => {
            asyncList.push(this.getDetail(c));
          });
          Promise.all(asyncList).then(res => {
            res.forEach(r => {
              const data = r.data.data;
              if (data) name.push(data.name);
            });
            this.name = name.join(',');
          });
        } else this.name = '';
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      data: [],
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['business-select'].visible = true;
    },
    handleUserSelectConfirm(id) {
      this.$emit('update:modelValue', id);
      if (this.change && typeof this.change == 'function') this.change({ value: id });
    },
    getDetail(c) {
      return axios.get('/api/vt-admin/businessOpportunity/detail?id=' + c, {
        id: c,
      });
    },
    remoteMethod(text) {
      console.log(text);
      if (!text) return (this.data = []);
      axios.get('/api/vt-admin/businessOpportunity/page?stage=0&selectType=0&name=' + text).then(res => {
        this.data = res.data.data.records;
      });
    },
  },
};
</script>
<style lang="scss"></style>
