<template>
  <div style="margin-bottom: 10px">
    <div style="display: flex; justify-content: space-between; align-items: center">
      <div>历史价格统计</div>
      <div style="display: flex; gap: 20px">
        <el-date-picker
          v-model="query.year"
          :clearable="false"
          value-format="YYYY"
          @change="getDetail"
          type="year"
          placeholder="请选择年份"
        ></el-date-picker>
        <!-- <el-radio-group @change="getDetail" v-model="query.type1">
                    <el-radio-button label="0"  >月</el-radio-button>
                    <el-radio-button label="1" >季度</el-radio-button>
                    <el-radio-button label="2"  >年</el-radio-button>
                  </el-radio-group> -->
        <!-- <wfUserSelectDrop v-model="query.userId" @change="getDetail"  v-if="form.type != 0" style="width: 100px"></wfUserSelectDrop> -->
      </div>
    </div>

    <!-- 图表 -->
    <div style="height: 300px">
      <div ref="chartRef" style="height: 100%"></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import moment from 'moment';
import * as echarts from 'echarts';
const chartRef = ref(null);
const props = defineProps(['supplierId', 'productId']);
onMounted(() => {
  getDetail();
});

watch(
  () => props.productId,
  () => {
    getDetail();
  },
  {
    deep: true,
  }
);
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#303133'],

    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: false,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    grid: {
      top: '10%',
      left: '6%',
      right: 0,
      bottom: '15%',
    },
    dataZoom: [
   {
    type:'inside',
    maxValueSpan:180
   },
    {
      type: 'slider',
      bottom:0,
      maxValueSpan:180
    }
  ],
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: detailForm.value.dateList, //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
       
      },
    ],
    yAxis: [
      //y轴
      {
        name: '产品价格', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
         min:'dataMin',
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: [
      {
        name: '产品价格', //名称
        type: 'line', //类型
        barWidth: 18, //宽度
        smooth: true,
        data: detailForm.value.priceList, //数值
        z: 1,
        label: {
          show: true,
          position: 'top',
        },
        itemStyle: {
          color: {
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            type: 'linear',
            global: false,
            colorStops: [
              {
                offset: 0,
                color: '#E6A23C',
              },
              {
                offset: 1,
                color: '#fff',
              },
            ],
          },
        },
        // 添加 markLine 配置（平均值）
      },
    ],
  };

  chart.setOption(option);
}
let query = ref({
  year:'',

});
let detailForm = ref({ y: [] });
function getDetail() {
  axios
    .get('/api/vt-admin/supplierProduct/historyTrend', {
      params: {
        supplierId: props.supplierId,
        productId: props.productId,
        ...query.value,
      },
    })
    .then(res => {
      detailForm.value = {
        dateList:res.data.data.dateList.reverse(),
        priceList:res.data.data.priceList.reverse()
      }
      initChart();
    });
}
</script>

<style lang="scss" scoped>
.el-card {
  height: 300px;
}
</style>
