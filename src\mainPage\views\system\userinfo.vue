<template>
  <div>
    <basic-container>
      <avue-form
        :option="option"
        v-model="form"
        @tab-click="handleTabClick"
        @submit="handleSubmit"
      ></avue-form>
    </basic-container>
  </div>
</template>

<script>


import option from '@/option/user/info';
import { getUserInfo, updateInfo, updatePassword } from '@/api/system/user';
import md5 from 'js-md5';
import func from '@/utils/func';
import axios from 'axios';
import { computed } from 'vue';


export default {
  data() {
    return {
      index: 0,
      option: option,
      form: {},
    };
  },
  created() {
    this.handleWitch();
  },
  methods: {
    handleSubmit(form, done) {
      if (this.index === 0) {
        updateInfo(form).then(
          res => {
            if (res.data.success) {
              this.$message({
                type: 'success',
                message: '修改信息成功!',
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg,
              });
            }
            done();
          },
          error => {
            window.console.log(error);
            done();
          }
        );
      } else if (this.index == 1) {
        updatePassword(md5(form.oldPassword), md5(form.newPassword), md5(form.newPassword1)).then(
          res => {
            if (res.data.success) {
              this.$message({
                type: 'success',
                message: '修改密码成功!',
              });
            } else {
              this.$message({
                type: 'error',
                message: res.data.msg,
              });
            }
            done();
          },
          error => {
            window.console.log(error);
            done();
          }
        );
      } else {
        axios
          .post(
            `/api/vt-admin/electronicBusinessCard/${this.form.id ? 'update' : 'save'}`,
            this.form
          )
          .then(res => {
            this.$message({
              type: 'success',
              message: '操作成功!',
            });
            done()
          });
      }
    },
    handleWitch() {
      if (this.index === 0) {
        getUserInfo().then(res => {
          const user = res.data.data;
          this.form = {
            id: user.id,
            avatar: user.avatar,
            name: user.name,
            realName: user.realName,
            phone: user.phone,
            email: user.email,
            position: user.roleName,
          };
        });
      } else if (this.index == 2) {
        axios
          .get('/api/vt-admin/electronicBusinessCard/detail', {
            params: {
              userId: this.form.id || this.$store.getters.userInfo.user_id,
            },
          })
          .then(res => {
            this.form = res.data.data || {
              ...this.form,
              id: null,
            };
          
            
            axios
              .get('/api/blade-system/tenant/detail', {
                params: {
                 tenantId: this.$store.getters.userInfo.tenant_id,
                },
              })
              .then(res => {
                this.form.companyProfile =  res.data.data.description;
              });
          });
      }
    },
    handleTabClick(tabs) {
      if (!tabs.index) return;
      this.index = func.toInt(tabs.index);
      this.handleWitch();
    },
  },
};
</script>

<style></style>
