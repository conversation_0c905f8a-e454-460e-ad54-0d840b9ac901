<template>
  <Title style="padding-top: 10px">
    <div style="display: flex; align-items: center; justify-content: space-between">待办事项</div>
  </Title>
  <el-radio-group style="display: flex; margin-left: 5px" v-model="tabPosition">
    <el-badge
      style="margin-bottom: 5px; margin-right: 5px"
      v-for="item in realTab.filter(item => item.key)"
      :value="toDoNumebr[item.key]"
      :type="toDoNumebr[item.key] == 0? 'info': 'danger'"
      class="item"
    >
      <el-radio-button  round plain :label="item.key">{{ item.name }}</el-radio-button>
    </el-badge>
  </el-radio-group>
  <optionDistribution :path="findPath('optionDistribution')" v-if="tabPosition == 'optionDistribution'"></optionDistribution>
  <!-- <todoPurchaseObject :path="findPath('todoPurchaseObject')" v-if="tabPosition == 'todoPurchaseObject'"></todoPurchaseObject> -->
  <optionAudit :path="findPath('optionAudit')" v-if="tabPosition == 'optionAudit'"></optionAudit>
  <!-- <offerAudit :path="findPath('offerAudit')" v-if="tabPosition == 'offerAudit'"></offerAudit> -->
  <purchaseOrderAudit :path="findPath('purchaseOrderAudit')" v-if="tabPosition == 'purchaseOrderAudit'"></purchaseOrderAudit>
  <todoObject :path="findPath('todoObject')" v-if="tabPosition == 'todoObject'"></todoObject>
  <todoOption :path="findPath('myOptionDesign')" v-if="tabPosition == 'myOptionDesign'"></todoOption>
  <todoBusiness :path="findPath('myAddBusiness')" v-if="tabPosition == 'myAddBusiness'"></todoBusiness>
  <todoOffer :path="findPath('myAddOffer')" v-if="tabPosition == 'myAddOffer'"></todoOffer>
  <todoOffer :path="findPath('myDeepenDesign')" v-if="tabPosition == 'myDeepenDesign'"></todoOffer>
  <!-- <waitDistributionContract :path="findPath('waitDistributionContract')" v-if="tabPosition == 'waitDistributionContract'"></waitDistributionContract> -->
</template>

<script setup>
import { getCurrentInstance, ref, onMounted,onBeforeUnmount } from 'vue';
import optionDistribution from './optionDistribution.vue';
import todoPurchaseObject from './todoPurchaseObject.vue';
import optionAudit from './optionAudit.vue';
import offerAudit from './offerAudit.vue';
import purchaseOrderAudit from './purchaseOrderAudit.vue';
import todoObject from './todoObject.vue';
import todoOption from './todoOption.vue';
import todoBusiness from './toDOBusinesss.vue';
import todoOffer from './todoOffer.vue';
import todoDeep from './todoDeep.vue';
import waitDistributionContract from './waitDistributionContract.vue';
const { proxy } = getCurrentInstance();
const menu = proxy.$store.getters.menu;
let tabPosition = ref('optionDistribution');
let option = ref({
  menu: false,
  header: false,
  height: 'auto',
});
let timer = ref(null);
onMounted(() => {
  getRoutesNoButton();
  getToDoNumber();
  timer.value =  setInterval(() => {
    getToDoNumber()
  }, 1000 * 60 * 1);
});
const tabArr = [
  {
    path: '/CRM/programme/myProgramme',
    name: '待做方案',
    key:'myOptionDesign'
  },
  {
    path: '/CRM/businessOpportunity/myBusinessOpportunity',
    name: '待办商机',
    key:'myAddBusiness'
  },
  {
    path: '/CRM/quotation/myquation',
    name: '待办报价',
    key:'myAddOffer'
  },
  {
    path: '/Project/deepSign/myDeepSign',
    name: '待做深化设计',
    key:'myDeepenDesign'
  },
  {
    path: '/CRM/programme/allocatedprogramme',
    name: '待分配方案',
    key: 'optionDistribution',
  },
  {
    path: '/CRM/programme/confirmprogramme',
    name: '待确认方案',
    key: 'optionAudit',
  },
  {
    path: '/Project/deepSign/confirmDeepSign',
    name: '深化待确认',
  },
  // {
  //   path: '/CRM/quotation/confirmquation',
  //   name: '待确认报价',
  //   key: 'offerAudit',
  // },
  {
    path: '/CRM/reporting/index',
    name: '待报备',
  },
  {
    path: '/SRM/product/confirmproduct',
    name: '待确认产品',
  },
  {
    path: '/SRM/procure/order',
    name: '待采购订单',
  },
  {
    path: '/SRM/procure/puchasePlan',
    name: '询价待确认',
    key: 'purchaseOrderAudit',
  },
  // {
  //   path: '/Contract/customer/categoryContract',
  //   name: '合同待分配',
  //   key:'waitDistributionContract'
  // },
  // {
  //   path: '/Contract/mission/purchaseMission',
  //   name: '采购待办标的',
  //   key: 'todoPurchaseObject',
  // },
  {
    path: '/Contract/mission/todoMission',
    name: '待办任务',
    key: 'todoObject',
  },
  {
    path: '/Finance/invoice/invoice',
    name: '待开票',
  },
  {
    path: '/Finance/payment/goodsPayment',
    name: '待付款',
  },
  {
    path: '/Finance/Collection/projectCollection',
    name: '待收款',
  },
];
// 获取角色拥有的待办页面
const extractLastLevelItems = data => {
  const lastLevelItems = [];

  const extractLastLevel = items => {
    for (const item of items) {
      const { children, ...rest } = item;
      if (children && children.length > 0) {
        // 递归调用，处理子节点
        extractLastLevel(children);
      } else {
        // 将最后一级的内容添加到数组中
        lastLevelItems.push(rest);
      }
    }
  };

  extractLastLevel(data);

  return lastLevelItems;
};
let realTab = ref([]);
const getRoutesNoButton = () => {
  axios.get('/api/blade-system/menu/routesNoButton').then(res => {
    const routes = extractLastLevelItems(res.data.data);
    console.log(routes);
    // 获取真的页面
    realTab.value = tabArr.reduce((pre, item) => {
      routes.forEach(route => {
        if (route.path === item.path) {
          pre.push(item);
        }
      });
      return pre;
    }, []);
    console.log(realTab);
  });
};
let toDoNumebr = ref({});
const getToDoNumber = () => {
  axios.get('/vt-admin/statistics/pageForTodoNumber').then(res => {
    toDoNumebr.value = res.data.data;
  });
};
const findPath = (value) => {

  return tabArr.find(item => item.key === value)?.path;
}
onBeforeUnmount(() => {
  clearInterval(timer.value);
})
</script>

<style lang="scss" scoped></style>
