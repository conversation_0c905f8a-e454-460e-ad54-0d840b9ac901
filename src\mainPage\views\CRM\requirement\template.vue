<template>
  <basic-container style="height: 100%">
    <el-row style="height: 100%" :gutter="20">
      <el-col :span="5" style="height: 100%">
      
          <avue-tree
            :option="treeOption"
            @save="treeSave"
            ref="tree"
            v-model="form"
            @update="treeUpdate"
            @del="treeDel"
            style="max-height: 660px; overflow-y: auto"
            :data="treeData"
            @node-click="nodeClick"
          >
          </avue-tree>
        
      </el-col>

      <el-col :span="19" style="height: 100%">
     
          <Title>{{ categoryName || '----' }}</Title>
          <div style="margin: 20px 0">
            <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
              <el-tab-pane label="采集配置" name="first">
                <el-form>
                  <el-form-item>
                    <el-button icon="plus" v-if="categoryId" @click="addParams" type="primary"
                      >新增采集内容</el-button
                    >
                  </el-form-item>
                  <el-table class="avue-crud" :row-key="row => row.id" :data="tableData" border>
                    <el-table-column type="index" width="50" />
                    <el-table-column label="#" width="50" type="expand">
                      <template #default="scope">
                        <div style="margin-left: 100px; display: flex">
                          <draggable
                            v-model="scope.row.valuesVOList"
                            :animation="100"
                            @sort="
                              a => {
                                onMoveCallback(a, scope.row);
                              }
                            "
                          >
                            <transition-group>
                              <el-tag effect='plain'
                                style="margin-right: 5px; margin-bottom: 5px"
                                v-for="element in scope.row.valuesVOList"
                                :key="element.id"
                                closable
                                @close="handleClose(element)"
                              >
                                {{ element.value }}
                              </el-tag>
                            </transition-group>
                          </draggable>

                          <el-input
                            class="input-new-tag"
                            v-if="scope.row.inputVisible"
                            style="width: 50%"
                            v-model="scope.row.inputValue"
                            :ref="'saveTagInput' + scope.$index"
                            size="small"
                            @keyup.enter.native="addTag(scope.row)"
                            @blur="addTag(scope.row)"
                          >
                          </el-input>

                          <el-button
                            v-else-if="scope.row.type != 2"
                            class="button-new-tag"
                            size="small"
                            @click="showInput(scope.row, scope.$index)"
                            >+ 添加可选项</el-button
                          >
                        </div>
                      </template>
                    </el-table-column>
                    <el-table-column label="采集内容" prop="collectionContent" width="500" />
                    <el-table-column label="备注" prop="remark" width=""> </el-table-column>
                    <!-- <el-table-column label="选择项" >
                  <template #default="scope">
                    <div >
                      <draggable
                        v-model="scope.row.valuesVOList"
                        :animation="100"
                        @sort="
                          a => {
                            onMoveCallback(a, scope.row);
                          }
                        "
                      >
                        <transition-group>
                         <div   v-for="element in scope.row.valuesVOList">
                          <el-tag effect='plain'
                            style="margin-right: 5px;margin-bottom: 5px"
                           
                            :key="element.id"
                            closable
                            @close="handleClose(element)"
                          >
                            {{ element.value }}
                          </el-tag>
                         </div>
                        </transition-group>
                      </draggable>

                      <el-input
                        class="input-new-tag"
                        v-if="scope.row.inputVisible"
                        style="width: 100%"
                        v-model="scope.row.inputValue"
                        :ref="'saveTagInput' + scope.$index"
                        size="small"
                        @keyup.enter.native="addTag(scope.row)"
                        @blur="addTag(scope.row)"
                      >
                      </el-input>

                      <el-button
                        v-else-if="scope.row.type != 2"
                        class="button-new-tag"
                        size="small"
                        @click="showInput(scope.row, scope.$index)"
                        >+ 添加可选项</el-button
                      >
                    </div>
                  </template>
                </el-table-column> -->
                    <el-table-column label="回答方式" prop="type" width="100">
                      <template #default="scope">
                        <el-tag effect='plain' type="success" v-if="scope.row.type == 0">单选</el-tag>
                        <el-tag effect='plain' type="success" v-else-if="scope.row.type == 1">多选</el-tag>
                        <el-tag effect='plain' type="success" v-else>文本输入</el-tag>
                      </template>
                    </el-table-column>
                    <!-- <el-table-column label="是否使用" prop="isUse" width="100">
                  <template #default="scope">
                    <el-switch
                      :active-value="1"
                      :inactive-value="0"
                      disabled
                      v-model="scope.row.isUse"
                    ></el-switch
                  ></template>
                </el-table-column> -->

                    <el-table-column label="操作" align="center" width="150">
                      <template #default="scope">
                        <el-button
                          text
                          icon="edit"
                          size="small"
                          type="primary"
                          @click="editParams(scope.row)"
                          >编辑</el-button
                        >
                        <el-button
                          text
                          icon="delete"
                          size="small"
                          type="danger"
                          @click="delParams(scope.row)"
                          >删除</el-button
                        >
                      </template>
                    </el-table-column>
                  </el-table>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="话术参考" name="second">
                <avue-crud :option="{
                 
                }"></avue-crud>
              </el-tab-pane>
            </el-tabs>
          </div>
       
      </el-col>
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
    <questionSelect ref="questionSelect"></questionSelect>
    
  </basic-container>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import questionSelect from './components/questionSelect.vue';
import { VueDraggableNext as draggable } from 'vue-draggable-next';
let form = ref({
  hasChildren: true,
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  defaultExpandAll: true,
  menu: true,
  filter: true,
  addBtn: true,
  props: {
    labelText: '标题',
    label: 'categoryName',
    id: 'id',
    children: 'children',
  },
  formOption: {
    column: {
      categoryName: {
        label: '分类名称',
        rules: [
          {
            required: true,
            message: '请输入分类名称',
            trigger: 'blur',
          },
        ],
      },
    },
  },
});

const updateUrl = '/api/vt-admin/product/update';

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  getTreeData();
});
let loading = ref(false);
function treeSave(node, data, done, loading) {
  console.log(node, data);
  const value = {
    ...data,

    parentId: node.data?.id,
  };
  axios
    .post('/api/vt-admin/requirementCategory/save', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
      parentId.value = 0;
    });
}
function treeUpdate(node, data, done, loading) {
  const value = {
    ...data,
  };
  axios
    .post('/api/vt-admin/requirementCategory/update', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        // onLoad();
        getTreeData;
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function treeDel(data, done) {
  console.log(data);
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/requirementCategory/remove?ids=' + data.data.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getTreeData();
        done(form);
      });
    })
    .catch(() => {});
}
let router = useRouter();
let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/requirementCategory/tree', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren,
        };
      });
      setShowCategory();
    });
}

let categoryId = ref('');
let categoryName = ref('');
let hasChildren = ref(true);
function nodeClick(val, accountName) {
  categoryId.value = val.id;
  categoryName.value = val.categoryName;
  hasChildren.value = val.hasChildren;
  getTableData();
}
function getTableData() {
  const { current, size } = page.value;
  axios
    .get('/api/vt-admin/requirementProperty/page', {
      params: {
        categoryId: categoryId.value,
        current,
        size,
      },
    })
    .then(res => {
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}

function showInput(row, index) {
  row.inputVisible = true;
  // 输入框自动获得焦点 $nextTick 当页面上的元素发生刷新（重新渲染）时的回调函数
  proxy.$nextTick(_ => {
    setTimeout(() => {
      console.log(proxy.$refs);
      proxy.$refs['saveTagInput' + index].focus();
    }, 200);
  });
}
function addParams() {
  
  proxy.$refs.questionSelect.open()
}
function editParams(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑采集内容',
    option: {
      column: [
        {
          label: '采集内容',
          type: 'input',
          value: row.collectionContent,
          prop: 'collectionContent',
          // maxlength: 5,
          showWordLimit: true,
          rules: [{ required: true, message: '请输入采集内容' }],
        },
        {
          label: '回答方式',
          type: 'radio',
          prop: 'type',
          value: row.type,
          dicData: [
            {
              label: '单选',
              value: 0,
            },
            {
              label: '多选',
              value: 1,
            },
            {
              label: '文本输入',
              value: 2,
            },
          ],
        },
        // {
        //   label: '是否使用',
        //   type: 'switch',
        //   prop: 'isUse',
        //   value: row.isUse,
        //   dicData: [
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //   ],
        // },
        {
          label: '备注',
          prop: 'remark',
          value: row.remark,
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/requirementProperty/update', {
          id: row.id,
          type: res.data.type,
          isUse: res.data.isUse,
          collectionContent: res.data.collectionContent,
          remark: res.data.remark,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getTableData();
        });
    },
  });
}
function addTag(row) {
  // 新增tag
  if (!row.inputValue) {
    row.inputVisible = false;
    return;
  }
  axios
    .post('/api/vt-admin/requirementProperty/addValue', {
      propertyId: row.id,
      value: row.inputValue,
    })
    .then(res => {
      proxy.$message.success('操作成功');
      getTableData();
      // row.inputVisible = false;
    });
}
function handleClose(row) {
  // 删除标签
  proxy
    .$confirm('确定删除此标签吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios.post('/api/vt-admin/requirementProperty/deleteValue?id=' + row.id).then(res => {
        proxy.$message.success('删除成功');
        getTableData();
      });
    });
}
function delParams(row) {
  proxy
    .$confirm('确定删除此参数吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios.post('/api/vt-admin/requirementProperty/remove?ids=' + row.id).then(res => {
        proxy.$message.success('删除成功');
        getTableData();
      });
    });
}
let drag = ref(false);
let topList = ref([]);
function onMoveCallback(e, row) {
  
  const data = row.valuesVOList.map((item, index) => {
    return {
      ...item,
      sort: index,
    };
  });
  axios
    .post('/api/vt-admin/requirementProperty/updateSort', {
      id: row.id,
      valuesVOList: data,
    })
    .then(res => {
      proxy.$message.success('排序成功');
      getTableData();
    });
}

function setShowCategory() {
  
  proxy.$refs.tree.setCurrentKey(categoryId.value || treeData.value[0].id);
}

let activeName = ref('first')
function handleClick() {}
function open(params) {
  
}
</script>

<style lang="scss" scoped></style>
