<template>
  <el-dialog title="产品选择" class="avue-dialog avue-dialog--top" v-model="visible" append-to-body>
    <el-alert type="info" style="margin-bottom: 5px" v-if="tableData.length == 0"
      >如没有查询到产品，请确认深化设计是否完成</el-alert
    >
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @selection-change="selectionChange"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @row-click="rowClick"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #isInvoice="{ row }">
        <el-tag effect='plain' v-if="row.isInvoice === 1" type="success">是</el-tag>
        <el-tag effect='plain' v-else-if="row.isInvoice === 2" type="success">部分开票</el-tag>
        <el-tag effect='plain' v-else type="danger">否</el-tag>
      </template>
      <template #objectNumber="{ row }">
        <el-input-number
          @click.stop="void"
          v-model="row.needNumber"
          :min="1"
          
          v-show="
            selectList.find(item => {
              return item.id == row.id;
            })
          "
        ></el-input-number>
      </template>
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button @click="handleConfirm" type="primary">确 定</el-button>
    </div>
  </el-dialog>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const props = defineProps({
  sealContractId: {
    type: String,
    default: '',
  },
  id: {
    type: String,
    default: '',
  },
  offerId: {
    type: String,
    default: '',
  },
  url: {
    type: String,
    default: '/api/vt-admin/sealContract/productPage',
  },
});
console.log(props.id);
let option = ref({
  // height: 'auto',
  align: 'center',
  menu: false,
  addBtn: false,
  editBtn: true,
  header: false,
  delBtn: true,
  reserveSelection: true,
  // calcHeight: 30,
  searchMenuSpan: 8,
  selection: true,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '产品',
      prop: 'productVOproductId',
      formatter:(row) => {
        return row.customProductName ||  row.productVO.productName
      },
      overHidden:true,
    },

    {
      label: '规格型号',
      prop: 'productVOproductSpecification',
     
      overHidden: true,
      // search: true,
      formatter:(row) => {
        return row.customProductSpecification || row.productVO.productSpecification
      },
      span: 24,
      type: 'input',
    },
    {
      label: '产品',
      prop: 'productproductId',
      formatter:(row) => {
        return row.customProductName || row.productVO.productName
      },
    },

    {
      label: '规格型号',
      prop: 'productproductSpecification',
      formatter:(row) => {
        return row.customProductSpecification || row.productVO.productSpecification
    
      },
      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '合同数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '合同数量',
      prop: 'contractNumber',
      type: 'number',
      span: 12,
      cell: false,
      formatter: (row, column, cellValue) => {
        return `${row.number}`;
      },
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      span: 12,
      cell: false,
      dicData: [
        {
          value: 0,
          label: '未到货',
        },
        {
          value: 1,
          label: '部分到货',
        },
        {
          value: 2,
          label: '已到货',
        },
      ],
    },
    {
      label: '标的数量',
      prop: 'objectNumber',
      type: 'number',
      span: 12,
      width: 180,
      cell: false,
    },

    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',

      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,

      cell: false,
    },
    {
      label: '是否开票',
      prop: 'isInvoice',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const visible = ref(false);
watchEffect(() => {
  if (props.id) {
    option.value.column = option.value.column.filter(
      item =>
        item.prop !== 'sealPrice' &&
        item.prop !== 'totalPrice' &&
        item.prop !== 'isInvoice' &&
        item.prop != 'number' &&
        item.prop != 'productproductId' &&
        item.prop != 'productproductSpecification'
    );
   
  }
  if (props.sealContractId) {
    option.value.column = option.value.column.filter(
      item =>
        item.prop !== 'objectNumber' &&
        item.prop != 'contractNumber' &&
        item.prop != 'arriveStatus' &&
        item.prop != 'productVOproductId' &&
        item.prop != 'productVOproductSpecification'
    );
    option.value.selectable = (row, index) => row.isInvoice === 0
  }
});
const emits = defineEmits(['confirm']);

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();

let loading = ref(false);
function onLoad() {
  // if(!props.id){
  //   return
  // }
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(props.url, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
        offerId: props.offerId,
        id: props.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          needNumber: 1,
        };
      });
      page.value.total = res.data.data.total;
    });
}
function open() {
  visible.value = true;
  proxy.$nextTick(() => {
    proxy.$refs.crud.clearSelection();
    onLoad();
  });
}
function close(params) {
  visible.value = false;
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list.map(item => {
    return {
      ...item,
      // needNumber: item.needNumber || 1,
    };
  });
  emits('select', selectList.value);
}
let router = useRouter();
function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm() {
  visible.value = false;
  
  getSelectTableData();
  
  emits('confirm', getSelectTableData());
}
function getSelectTableData() {
  return selectList.value.reduce((pre, item) =>{
    let i 
    i = tableData.value.find(i => i.id == item.id)
    pre.push(i)
    return pre
  },[])
}
function reset() {
  proxy.$nextTick(() => {
    proxy.$refs.crud.toggleSelection(selectList.value);
  });
}
function rowClick(row) {
  console.log(row);
  if (row.isInvoice == 1) return;
  proxy.$refs.crud.toggleSelection([row]);
}
defineExpose({
  open,
  reset,
});
</script>

<style lang="scss" scoped></style>
