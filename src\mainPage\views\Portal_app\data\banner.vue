<template>
  <basic-container>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleTabChange">
      <el-tab-pane
        v-for="item in typeList"
        :label="item.label"
        :name="item.value"
      ></el-tab-pane>
    </el-tabs>  
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      
    >
    <template #isClick="{ row }">
            <el-switch
              v-model="row.isClick"
              :active-value="1"
              :inactive-value="0"
              @click="handleIsClickChange(row)"
            >
            </el-switch>
          </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let questionTypeData = ref([]);
let activeName = ref(0);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  dialogType:'drawer',
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '标题',
      prop: 'title',
     
      overHidden: true,
      //   search: true,
    },
    {
      label: '封面图',
      prop: 'homePictureId',
      type: 'upload',
      value: [],
      dataType: 'string',
      listType: 'picture-card',

      limit: 1,
      loadText: '附件上传中，请稍等',
      span: 24,
      accept: 'image/png, image/jpeg,image/jpg',
      // align: 'center',
      uploadExceed: err => {
        console.log(err);
        ElMessage.error('只能上传一张图片');
      },
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      tip:'图片比例2:1最佳',
      rules: [
        {
          required: true,
          message: '请上传封面图',
          trigger: 'blur',
        },
      ],
      action: '/blade-resource/attach/upload',
    },
    {
      label: '排序',
      prop: 'sort',
    },
    {
      label: '内容',
      prop: 'content',
      component: 'AvueUeditor',
      action: '/api/blade-resource/attach/upload',
      options: {
        action: '/api/blade-resource/attach/upload',
        accept: 'image/png, image/jpeg, image/jpg,.mp4',
      },
      propsHttp: {
        res: 'data',
        url: 'link',
      },

      
      hide: true,
      minRows: 6,
      span: 24,
    },
    {
      label: '是否能查看详情',
      prop: 'isClick',
      type: 'switch',
      width: 150,
      // display: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let typeList = ref([
  {
    label: 'IT集成',
    value: 0,
  },
  {
    label: 'IT服务',
    value: 1,
  },
  {
    label: 'IT采购',
    value: 2,
  },
]);
onMounted(() => {
    onLoad()
})
const addUrl = '/api/vt-admin/itSlideshow/save';
const delUrl = '/api/vt-admin/itSlideshow/remove?ids=';
const updateUrl = '/api/vt-admin/itSlideshow/update';
const tableUrl = '/api/vt-admin/itSlideshow/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        type: activeName.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    type: activeName.value,
    isClick :1
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function beforeOpen(done, type) {
  if (type == 'add') {
    form.value.sort = page.value.total + 1;
  }
  done();
}
function handleTabChange() {
  page.value.currentPage = 1;
  onLoad();
}
function handleIsClickChange(item) {
  axios
    .post('/api/vt-admin/itSlideshow/update', {
      isClick: item.isClick,
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
</script>

<style lang="scss" scoped></style>
