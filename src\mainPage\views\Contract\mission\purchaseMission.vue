<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #sealContractId="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
        </el-link>
      </template>
      <template #menu="{ row }">
        <!-- <el-button type="primary" text icon="tools" @click="distribution(row)">分配</el-button> -->
        <el-button type="primary" text icon="Aim" @click="sendInfo(row)" :disabled="row.arriveStatus != 2">配送信息</el-button>
        <!-- <el-button type="primary" text icon="CircleCheck" @click="complete(row)">完成</el-button> -->
        <!-- <el-button type="primary" text icon="CirclePlus" @click="addProduct(row)"
          >关联产品</el-button
        > -->
        <!-- <el-button type="primary" text icon="Promotion" @click="delay(row)">延期</el-button> -->
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.objectName }}</el-link>
      </template>
      <template #isComplete="{ row }">
        <div>配送：<span style="color: var(--el-color-warning)">未完成</span></div>
        <div>
          技术服务：<span v-if="row.isVisitingService == 1" style="color: var(--el-color-warning)"
            >未完成</span
          >
          <span v-if="row.isVisitingService == 1" style="color: var(--el-color-info)"
            >无需服务</span
          >
        </div>
      </template>
      <template #distributionUser="{ row }">
        <el-button @click="viewDetail(row, 2)">查看</el-button>
      </template>
      <template #productNumber="{ row }">
        <el-button @click="viewDetail(row, 3)">查看</el-button>
      </template>
      <template #delay="{ row }">
        <el-button @click="viewDetail(row, 5)">查看</el-button>
      </template>
    </avue-crud>

    <dialogForm ref="dialogForm"></dialogForm>
    <productSelect
      ref="productSelectRef"
      @confirm="handleConfirm"
      :id="sealContractId"
      url="/api/vt-admin/sealContract/productPageForObject"
    ></productSelect>
    <detail ref="missionDetail"></detail>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productSelect from '../customer/compoents/productSelect.vue';
import detail from '@/views/Order/salesOrder/compoents/missionDetail.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 150,
  border: true,
  column: [
    {
      type: 'input',
      label: '合同名称',
      span: 24,
      display: true,
      hide: true,
      overHidden: true,
      addDisplay: false,
      search: true,
      editDisplay: false,
      prop: 'contractName',
    },
    {
      type: 'input',
      label: '合同名称',
      span: 24,
      display: true,
      overHidden: true,
      component: 'wf-contract-select',
      prop: 'sealContractId',
      formatter: row => {
        return row.contractName;
      },
    },
    {
      type: 'input',
      label: '客户名称',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'customerName',
      search: true,
    },
    {
      type: 'input',
      label: '标的名称',
      span: 24,
      display: true,
      search: true,
      overHidden:true,
      prop: 'objectName',
    },
    {
      type: 'input',
      label: '任务描述',
      span: 24,
      display: true,
      overHidden:true,
      prop: 'durationNode',
    },
    {
      type: 'datetime',
      label: '时间要求',
      span: 12,
      display: true,
      width: 140,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'planTime',
    },
    {
      type: 'switch',
      label: '技术人员',
      span: 6,
      display: true,
      overHidden: true,
      value: '0',
      dicData: [
        {
          label: '否',
          value: 0,
        },
        {
          label: '是',
          value: 1,
        },
      ],
      prop: 'isNeedTechnology',
      formatter(row, column, cellValue, index) {
        return row.technologyUserName;
      },
    },
    {
      type: 'input',
      label: '送货信息',
      span: 24,
      editDisplay: false,
      addDisplay: false,
      prop: 'distributionUser',
    },
    {
      label: '关联产品',
      prop: 'productNumber',
      addDisplay: false,
      editDisplay: false,
    },
    {
      type: 'select',
      label: '是否完成',
      span: 6,

      editDisplay: false,
      addDisplay: false,
      value: '0',
      dicData: [
        {
          label: '未开始',
          value: 0,
        },
        {
          label: '进行中',
          value: 1,
        },
        {
          label: '已完成',
          value: 2,
        },
      ],
      prop: 'objectStatus',
    },
    {
      type: 'date',
      label: '完成时间',
      span: 12,
      editDisplay: false,
      addDisplay: false,
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD',
      prop: 'completeTime',
    },
    {
      type: 'textarea',
      label: '延期说明',
      span: 24,
      editDisplay: false,
      addDisplay: false,
      prop: 'delay',
    },
    {
      type: 'switch',
      label: '上门服务',
      span: 12,
      display: true,
      hide: true,
      value: '0',
      dicData: [
        {
          label: '否',
          value: 0,
        },
        {
          label: '是',
          value: 1,
        },
      ],
      prop: 'isVisitingService',
    },
    {
      type: 'switch',
      label: '是否需要配送',
      span: 6,
      display: true,
      hide: true,
      value: '0',
      dicData: [
        {
          label: '否',
          value: 0,
        },
        {
          label: '是',
          value: 1,
        },
      ],
      prop: 'isDistribution',
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      span: 12,
      cell: false,
      editDisplay: false,
      addDisplay: false,
      dicData: [
        {
          value: 0,
          label: '未到货',
        },
        {
          value: 1,
          label: '部分到货',
        },
        {
          value: 2,
          label: '已到货',
        },
        {
          value: 3,
          label: '未关联',
        },
      ],
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      overHidden:true,
      prop: 'remark',
    },
    {
      label: '创建人',
      prop: 'createName',
      editDisplay: false,
      addDisplay: false,
    },
    {
      type: 'datetime',
      label: '创建时间',
      editDisplay: false,
      addDisplay: false,
      span: 12,
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'createTime',
    },
  ],
});

let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractObject/save';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractObject/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 3,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function distribution(row) {
  proxy.$refs.dialogForm.show({
    title: '分配',
    option: {
      column: [
        {
          label: '技术人员',
          type: 'input',
          value: row.technologyUser,
          component: 'wf-user-select',
          params: {
            checkType: 'checkBox',
          },
          prop: 'technologyUser',
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractObject/distributionTechnology', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function sendInfo(row) {
  proxy.$refs.dialogForm.show({
    title: '配送信息',
    option: {
      column: [
        {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 24,
          value: '' + row.distributionMethod || '1',
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
          control: val => {
            console.log(val);
            return {
              distributionUser: {
                label: val == 3 ? '送货人' : val == 1 ? '发货人' : '交付人',
              },
              distributionDate: {
                label: val == 3 ? '送货日期' : val == 1 ? '发货日期' :val == 4 ?'交付日期':'自提日期',
              },
              distributionAddress: {
                label: val == 3 ? '收货地址' : val == 1 ? '收货地址' : val == 4 ? '交付地址' : '',
                display: val != 2 ? true : false,
              },
            };
          },
          rules: [
            {
              required: true,
              message: '请选择送货方式',
              trigger: 'change',
            },
          ],
        },

        {
          label: '送货人',
          component: 'wf-user-select',
          prop: 'distributionUser',
          value: row.distributionUser,
          span: 12,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请选择送货人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货日期',
          prop: 'distributionDate',
          span: 12,
          type: 'datetime',
          value: row.distributionDate,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rules: [
            {
              required: true,
              message: '请选择送货日期',
              trigger: 'change',
            },
          ],
        },
        {
          label: '送货地址',
          prop: 'distributionAddress',
          span: 24,
          display: true,
          value: row.distributionAddress,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入送货地址',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系人',
          prop: 'contact',
          span: 12,
          type: 'input',
          value: row.contact,
          rules: [
            {
              required: true,
              message: '请输入联系人',
              trigger: 'change',
            },
          ],
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          span: 12,
          value: row.contactPhone,
          type: 'input',
          rules: [
            {
              required: true,
              message: '请输入联系电话',
              trigger: 'change',
            },
          ],
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractObject/distributionDistributionUser', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function complete(row) {
  let coverId = '';
  proxy.$refs.dialogForm.show({
    title: '完成',
    option: {
      column: [
        {
          label: '完成时间',
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'completeTime',
          span: 24,
        },
        {
          label: '附件',
          prop: 'completeFiles',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            coverId = res.id;

            done();
          },
        },
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
        completeFiles: coverId,
      };
      axios.post('/api/vt-admin/sealContractObject/complete', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
function delay(row) {
  proxy.$refs.dialogForm.show({
    title: '延期',
    option: {
      column: [
        {
          label: '延期时间',
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'delayDate',
          rules: [
            {
              required: true,
              message: '请选择延期时间',
            },
          ],
          span: 24,
        },
        {
          label: '延期理由',
          type: 'textarea',
          span: 24,
          rules: [
            {
              required: true,
              message: '请输入延期理由',
            },
          ],
          prop: 'delayReason',
        },
      ],
    },
    callback(res) {
      const data = {
        objectId: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/vt-admin/sealContractObjectDelay/save', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}

function viewDetail(row, active = 1) {
  proxy.$refs.missionDetail.viewDetail(row, active);
}
let currentObjectId = ref(null);
function addProduct(row) {
  currentObjectId.value = row.id;
  proxy.$refs.productSelectRef.open();
  proxy.$refs.productSelectRef.reset();
}
function handleConfirm(list) {
  console.log(list);
  const data = {
    id: currentObjectId.value,
    productDTOList: list.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
      };
    }),
  };
  axios.post('/api/vt-admin/sealContractObject/relationProduct', data).then(res => {
    proxy.$message.success(res.data.msg);
  });
}
function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId,
    },
  });
}
</script>

<style lang="scss" scoped></style>
