<template>
  <div style="width: 100%">
    <el-autocomplete
      v-model="value"
      style="width: 100%;"
      :fetch-suggestions="querySearch"
      :trigger-on-focus="false"
      clearable
      v-if="dataType=='name'"
      value-key="supplierName"
      class="inline-input w-50"
      placeholder="请输入供应商名称"
      @select="handleUserSelectConfirm"
      @blur="handleBlur"
    />
    <el-select v-else style="width: 100%" filterable remote placeholder="请输入供应商" :remote-method="querySearch" v-model="value" clearable @change="handleBlur">
      <el-option :label="item.supplierName" :value="item.id" v-for="item in supplierData"></el-option>
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'supplier-drop',
};
</script>
<script setup>
import axios from 'axios';
import { watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
  },
  url: {
    type: String,
    default: '/api/vt-admin/supplier/page',
  },
  dataType:{
    type: String,
    default: 'name'
  }
});
watch(
  () => props.modelValue,
  val => {
  
    value.value = val;
  }
);
const emits = defineEmits(['update:modelValue']);
let value = ref('');
let supplierData = ref([])
function querySearch(value, cb) {
  if (!value) {
    supplierData.value = []
    return []
  } 
  axios.get(props.url, { params: { supplierName: value, size: 100 } }).then(res => {
    if(cb){
      cb(res.data.data.records);
    }
    supplierData.value = res.data.data.records
  });
}
function handleUserSelectConfirm(v) {
  console.log(v,value.value);
  
  emits('update:modelValue', v.supplierName);
}
function handleBlur() {
  emits('update:modelValue', value.value);
}
</script>

<style lang="scss" scoped></style>
