<template>
  <div style="height: 100%; display: flex">
    <div class="page-container" style="flex: 23">
      <div class="page-item">
        <el-empty description="暂无公告" v-if="r2list.length == 0"></el-empty>
        <el-carousel style="height: 100%" height="100%" v-else :interval="3000" arrow="always">
          <el-carousel-item v-for="(item, index) in r2list || [{}]" :key="index">
            <img
              @click="handleCommonClick(item)"
              class="carousel-img"
              style="height: 100%; width: 100%"
              :src="item.coverList[0].link"
            />
          </el-carousel-item>
        </el-carousel>
      </div>
      <div class="page-item" style="height: 100%" :class="{ 'page-item-active': isFull == 1 }">
        <div class="page-content" style="height: 100%">
          <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="我的周计划" name="first"></el-tab-pane>
            <el-tab-pane label="下属周计划" v-if="isSubPlan" name="first_sub"></el-tab-pane>
            <el-tab-pane
              v-if="
                $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                $store.getters.userInfo.role_name.indexOf('manager') > -1
              "
              label="全部周计划"
              name="first_all"
            ></el-tab-pane>
            <el-tab-pane label="我的日志" name="second"></el-tab-pane>
            <el-tab-pane label="下属日志" v-if="isSubPlan" name="second_sub"></el-tab-pane>
            <el-tab-pane
              label="全部日志"
              v-if="
                $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
                $store.getters.userInfo.role_name.indexOf('manager') > -1
              "
              name="second_all"
            ></el-tab-pane>
            <el-tab-pane label="我的日程" name="third"></el-tab-pane>
          </el-tabs>
          <div class="calendar-box" v-if="['first', 'first_sub', 'first_all'].includes(activeName)">
            <div class="calendar-box-left">
              <el-select
                v-if="activeName != 'first'"
                style="width: 100%; margin: 10px 0 10px 5px"
                v-model="userId"
                placeholder="请选择人员以查看"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <MCalendarWeek
                :type="activeName == 'first' ? 'self' : 'all'"
                :user-id="activeName == 'first' ? $store.getters.userInfo.user_id : userId"
                ref="calendarRef"
                @calendarChange="handlePlanChange"
              />
            </div>
            <div class="calendar-box-right">
              <MToDoListWeek
                :data="planList"
                @isFull="getFull"
                ref="todoListRef"
                :type="activeName == 'first' ? 'self' : 'all'"
                :date="planDate"
              />
            </div>
          </div>
          <div
            class="calendar-box"
            style="min-width: 300px"
            v-else-if="['second', 'second_sub', 'second_all'].includes(activeName)"
          >
            <div class="calendar-box-left">
              <el-select
                v-if="activeName != 'second'"
                style="width: 100%; margin: 10px 0 10px 5px"
                v-model="userId"
                placeholder="请选择人员以查看"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
              <m-calendar-log
                :logDetailForMonth="logDetailForMonth"
                ref="calendarRef"
                @calendarChange="handleCalendarChangeLog"
              />

              <m-statisic-log @change="userClick" v-if="activeName == 'second_all'" :log-date="logDate"></m-statisic-log>
            </div>
            <div class="calendar-box-right">
              <m-do-log
                :type="activeName == 'second' ? 'self' : 'all'"
                :data="logForm"
                :workLogList="workLogList"
                @get-detail="
                  () => {
                    queryLogForm();
                    queryMonthDetail();
                  }
                "
                :date="logDate"
              />
                         
            </div>
                     
          </div>
          <div class="calendar-box" v-else>
            <div class="calendar-box-left">
              <MCalendar ref="calendarRef" @calendarChange="handleCalendarChange" />
            </div>
            <div class="calendar-box-right">
              <m-to-do-list :data="todoList" :date="todoDate" />            
            </div>
                     
          </div>
        </div>
      </div>
      <div class="page-item" style="transform: translateY(-100%)">
        <toDolist></toDolist>
      </div>
      <!-- <div class="page-item">
        <reviewList></reviewList>
      </div> -->
      <!-- <AvueUeditor
        :options="{
          action: '/api/blade-resource/attach/upload',
          accept: 'image/png, image/jpeg, image/jpg,.mp4',
        }"
        :action="'/api/blade-resource/attach/upload'"
        :propsHttp="{
          res: 'data',
          url: 'link',
        }"
      ></AvueUeditor> -->
    </div>
    <!-- <div style="padding: 0 10px 10px 10px; flex: 1; width: 20%">
      <el-card shadow="never" style="border: none">
        <div class="add_container" style="display: flex; flex-direction: column">
          <div class="item">
            <div class="first">
              <div style="margin-bottom: 5px; color: var(--el-color-success); font-weight: bolder">
                客户
              </div>
              <div>
                <i
                  class="element-icons el-icon-kehu"
                  style="font-size: 50px; color: var(--el-color-primary)"
                ></i>
              </div>
            </div>
            <span style="height: 90%; width: 3px; background-color: var(--el-color-primary)"></span>
            <div class="last">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="text-align: center; color: #666; font-size: 14px">本月新增</div>
                <div style="text-align: center; font-weight: bolder; color: var(--el-color-danger)">
                  111
                </div>
                <div style="text-align: center; color: #666; font-size: 14px">昨日新增</div>
                <div style="text-align: center; font-weight: bolder; color: var(--el-color-danger)">
                  111
                </div>
              </div>
              <i
                @click="handleClick"
                class="element-icons el-icon-jia"
                style="font-size: 50px; color: var(--el-color-info); position: absolute"
              ></i>
            </div>
          </div>
          <div class="item">
            <div class="first">
              <div style="margin-bottom: 5px; color: var(--el-color-success); font-weight: bolder">
                商机
              </div>
              <div>
                <i
                  class="element-icons el-icon-shangji"
                  style="font-size: 50px; color: var(--el-color-primary)"
                ></i>
              </div>
            </div>
            <span style="height: 90%; width: 3px; background-color: var(--el-color-primary)"></span>
            <div class="last">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="text-align: center; color: #666; font-size: 14px">本月新增</div>
                <div style="text-align: center; font-weight: bolder; color: var(--el-color-danger)">
                  111
                </div>
                <div style="text-align: center; color: #666; font-size: 14px">昨日新增</div>
                <div style="text-align: center; font-weight: bolder; color: var(--el-color-danger)">
                  111
                </div>
              </div>
              <i
                @click="handleClick"
                class="element-icons el-icon-jia"
                style="font-size: 50px; color: var(--el-color-info); position: absolute"
              ></i>
            </div>
          </div>
          <div class="item">
            <div class="first">
              <div style="margin-bottom: 5px; color: var(--el-color-success); font-weight: bolder">
                报价
              </div>
              <div>
                <i
                  class="element-icons el-icon-baojiadan"
                  style="font-size: 50px; color: var(--el-color-primary)"
                ></i>
              </div>
            </div>
            <span style="height: 90%; width: 3px; background-color: var(--el-color-primary)"></span>
            <div class="last">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: center;
                  align-items: center;
                "
              >
                <div style="text-align: center; color: #666; font-size: 14px">本月新增</div>
                <div style="text-align: center; font-weight: bolder; color: var(--el-color-danger)">
                  111
                </div>
                <div style="text-align: center; color: #666; font-size: 14px">昨日新增</div>
                <div style="text-align: center; font-weight: bolder; color: var(--el-color-danger)">
                  111
                </div>
              </div>
              <i
                @click="handleClick"
                class="element-icons el-icon-jia"
                style="font-size: 50px; color: var(--el-color-info); position: absolute"
              ></i>
            </div>
          </div>
        </div>
      </el-card>
    </div> -->

    <!-- <el-calendar></el-calendar> -->
    <el-drawer
      :title="detailTitle"
      v-model="detailDialog"
      direction="rtl"
      size="70%"
      append-to-body
      :destroy-on-close="true"
      :show-close="true"
      :wrapperClosable="true"
    >
      <div class="announcement-detail">
        <div class="stick">
          <div class="m-title">{{ detailObj['title'] }}</div>
          <div class="m-publication">
            <div class="m-time">
              {{ detailObj['time'] }}
              <p>
                已阅读
                {{ detailObj['number'] }} 次
              </p>
            </div>
            <div class="m-person">{{ detailObj['releaseName'] }}</div>
          </div>
        </div>
        <div class="m-contentBox">
          <div class="m-content" v-html="detailObj['content']"></div>
        </div>
        <p>
          <File :fileList="detailObj.fileList || []"></File>
        </p>
      </div>
    </el-drawer>
  </div>
</template>

<script setup>
import MCalendarWeek from './mcalendarWeek.vue';
import MToDoListWeek from './mtodoListWeek.vue';
import MCalendar from '@/components/MCalendar/MCalendar.vue';
import MToDoList from '../../components/MToDoList/MToDoList.vue';
import MCalendarLog from './log/MCalendar.vue';
import MDoLog from './log/MToDoList.vue';
import toDolist from './compoents/toDolist.vue';
import reviewList from './compoents/reviewList.vue';
import { getCurrentInstance, onMounted, watch, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import Editor from '@/components/avue-ueditor/main.vue';
import axios from 'axios';
import MStatisicLog from './log/MStatisicLog.vue';
let route = useRoute();
let router = useRouter();
let { proxy } = getCurrentInstance();
const menu = computed(() => {
  return proxy.$store.getters.menu;
});
console.log(menu.value, 'menu');
// 定义一个递归函数来查找 code 为 'subPlan' 的数据
function findSubPlan(data) {
  for (let item of data) {
    if (item.code === 'subPlan') {
      return true;
    }
    if (item.children && item.children.length > 0) {
      if (findSubPlan(item.children)) {
        return true;
      }
    }
  }
  return false;
}

// 使用 computed 计算属性来判断 menu 中是否含有 code 为 'subPlan' 的数据
let isSubPlan = computed(() => {
  return findSubPlan(menu.value);
});

// function handleClick(params) {
//   proxy.$nextTick(() => {
//     router.push('/CRM/customer/compoents/customerAdd');
//   });
// }
let tabPosition = ref('top');
let option = ref({
  menu: false,
  header: false,
  height: 'auto',
});
onMounted(() => {
  getr2List();
  // renderSheet();
});
let r2list = ref([]);
let detailDialog = ref(false);
let detailTitle = ref('');
let detailObj = ref({});
function getr2List(params) {
  axios
    .get('/api/vt-admin/publishNotice/getList', {
      params: {
        current: 1,
        size: 10,
        status: 1,
        isTopMark: 1,
      },
    })
    .then(e => {
      r2list.value = e.data.data.records;
    });
}
function handleCommonClick(row) {
  // getCompanyDetailAndRead(row.id).then((e) => {
  axios
    .post('/api/vt-admin/publishNotice/getByIdRead', {
      id: row.id,
    })
    .then(e => {
      let data = e.data;

      // console.log(JSON.parse(data.data.path));
      detailObj.value = {
        title: data.data['title'],
        time: data.data['publishTime'],
        content: data.data['content'],
        number: row['number'],
        path: row['path'],
        fileList: data.data['fileList'],
      };
      detailTitle.value = data.data.articleTypeName;
      detailDialog.value = true;
    });
}

// 日程
let todoDate = ref();
let todoList = ref([]);
function handleCalendarChange(e) {
  todoDate.value = e;
  queryTodoList();
}
// 查询日历待办列表
function queryTodoList() {
  axios({
    url: '/api/vt-admin/schedule/findListByDate',
    method: 'get',
    params: {
      scheduleDate: todoDate.value,
    },
  }).then(e => {
    let data = e.data.data;
    todoList.value = data;
  });
}

// 计划
let planDate = ref([]);
let planList = ref([]);
let userList = ref([]);
let activeName = ref('first');
let userId = ref('');
// 检查发现原代码中使用了未定义的 active 变量，应使用 activeName 变量进行判断
// 同时添加错误处理逻辑，移除调试用的  语句
watch(
  () => activeName.value,
  async newValue => {
    userId.value = '';
    planList.value = [];
    try {
      if (newValue === 'first_sub' || newValue === 'second_sub') {
        const res = await axios.get('/api/blade-system/user/under-list', {
          params: {
            size: 5000,
          },
        });
        userList.value = res.data.data;
        userList.value.shift();
        userList.value = userList.value.filter(item => item.status != 2);
        userId.value = userList.value[0].id;
      } else if (newValue === 'first_all' || newValue === 'second_all') {
        const res = await axios.get('/api/blade-system/search/user', {
          params: {
            size: 5000,
          },
        });
        userList.value = res.data.data.records;

        userId.value = userList.value[0].id;
      } else {
        userList.value = [];
        queryPlanList();
      }
    } catch (error) {
      console.error('获取用户列表时出错:', error);
    }
  }
);
watch(
  () => userId.value,
  newValue => {
    if (activeName.value.includes('first')) {
      queryAllPlanList();
    } else if (activeName.value.includes('second')) {
      queryLogForm();
      generateWork();
      queryMonthDetail();
    }
  }
);
function handlePlanChange(e) {
  planDate.value = e;
  if (activeName.value === 'first') {
    queryPlanList();
  } else {
    queryAllPlanList();
  }
}
// 查询日历待办列表
function queryPlanList() {
  axios({
    url: '/api/vt-admin/weekPlan/myList',
    method: 'get',
    params: {
      startDate: planDate.value[0],
      endDate: planDate.value[1],
    },
  }).then(e => {
    let data = e.data.data;
    planList.value = data;
  });
}
// 查询日历待办列表
function queryAllPlanList() {
  if (!userId.value) {
    return;
  }
  axios({
    url: '/api/vt-admin/weekPlan/list',
    method: 'get',
    params: {
      startDate: planDate.value[0],
      endDate: planDate.value[1],
      userId: userId.value,
    },
  }).then(e => {
    let data = e.data.data;
    planList.value = data;
  });
}
let isFull = ref(0);
function getFull(value) {
  isFull.value = value;
}

// 日志
let logForm = ref({});
let logDate = ref();
let oldDate = ref();
let logDetailForMonth = ref({});
let workLogList = ref([]);
function handleCalendarChangeLog(e) {
  logDate.value = e;
  logForm.value = {};
  // logDetailForMonth.value = {};
  queryLogForm();
  generateWork();
  const newMonth = new Date(logDate.value).getMonth() + 1;
  const oldMonth = new Date(oldDate.value).getMonth() + 1;
  if (newMonth !== oldMonth) {
    queryMonthDetail();
    oldDate.value = logDate.value;
  }
}
function queryLogForm() {
  axios
    .get('/api/vt-admin/businessWorkLog/detail', {
      params: {
        date: logDate.value,
        userId: userId.value,
      },
    })
    .then(e => {
      logForm.value = e.data.data;
    });
}
function generateWork() {
  const data = {
    createUser: userId.value || proxy.$store.getters.userInfo.user_id,
    date: logDate.value,
    current: 1,
    size: 100000,
  };
  axios
    .get('/api/vt-admin/businessSystemWorkLogs/page', {
      params: data,
    })
    .then(res => {
      workLogList.value = res.data.data.records;
    });
}
// 获取每个月完成的情况
function queryMonthDetail() {
  axios
    .get('/api/vt-admin/businessWorkLog/detailByMonth', {
      params: {
        userId: userId.value,
        month: new Date(logDate.value).getMonth() + 1,
        year: new Date(logDate.value).getFullYear(),
      },
    })
    .then(res => {
      logDetailForMonth.value = res.data.data;
    });
}



function userClick(item) {
    userId.value = item.id;
    
}
</script>

<style lang="scss" scoped>
.page-container {
  box-sizing: border-box;
  padding: 0 10px 10px 10px;
  display: flex;
  height: 100%;
  flex-wrap: wrap;
  justify-content: space-between;
  align-content: space-between;
  .page-item {
    width: 49%;
    height: 50%;
    margin-bottom: 10px;
    margin-right: 10px;
    border-radius: 5px;
    background-color: rgba(255, 255, 255);
    box-shadow: 0px 0px 10px rgba(255, 255, 255, 0.5);
  }
  .page-item-active {
    position: fixed;
    width: 100vw;
    height: 100vh;
    top: 0;
    left: 0;
    z-index: 999;
    background-color: #fff;
  }
  .calendar-box {
    width: 100%;
    height: calc(100% - 40px);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .calendar-box-left {
      width: 325px;
      height: 50%;
    }
    .calendar-box-right {
      width: calc(100% - 325px);
      height: 100%;

      padding: 10px 0 10px 16px;
      box-sizing: border-box;
    }
  }
}
.add_container {
  // padding-top: 5px;
  margin-top: 5px;
  display: flex;
  box-sizing: border-box;
  justify-content: flex-start;

  .item {
    height: 100px;
    width: 15%;
    min-width: 200px;
    border-radius: 10px;
    display: flex;
    margin: 10px;
    align-items: center;

    box-shadow: var(--el-box-shadow);
    .first {
      width: 40%;
      display: flex;
      justify-content: center;
      flex-direction: column;
      align-items: center;
    }
    .last {
      width: 60%;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      i {
        transform: translateX(100px);
        transition: all 0.2s;
        opacity: 0;
      }
      div {
        transform: translateX(0px);
        transition: all 0.2s;
      }
    }
    .last:hover i {
      transform: translateX(0px);
      opacity: 1;
    }
    .last:hover div {
      transform: translateX(-100px);
    }
  }
}
:deep(.el-tabs__header) {
  margin-bottom: 0;
}

.avue-comment img {
  display: none;
}
</style>
