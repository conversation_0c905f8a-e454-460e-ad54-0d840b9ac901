<template>
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="reset"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      > <template #file="{ row }">
        <File
          :fileList="
            row.generatedFileName && [
              { originalName: row.generatedFileName, link: row.generatedFilePath },
            ]
          "
        ></File>
      </template>
      <template #fileFinal="{ row }">
        <File :fileList="row.attachList || []"></File>
      </template>
        <template #menu="{ row }">
          <el-button type="text" icon="document" @click="handle(row)">归档</el-button>
        </template>
         <template #offerName="{row}">
        <el-link type="primary" @click="toDetail(row)">
          
          {{ row.offerName }}
        
        </el-link>
      </template>
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: false,
    delBtn: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
     searchIcon: true,
  searchIndex: 5,
    menuWidth: 120,
      menu:false,
    border: true,
    column: [
      {
        label: '归档编号',
        prop: 'contractCode',
        width: 250,
        overHidden: true,
        // search: true,
      },
      {
        label: '客户名称',
        prop: 'customerName',
        search: true,
        component: 'wf-customer-drop',
      },
  
      {
        label: '合同名称',
        prop: 'offerName',
        search: true,
      },
      {
        label: '报价时间',
        prop: 'offerDate',width:120,
      },
      {
        label: '板块名称',
        prop: 'businessTypeName',width:120,
      },
  
      {
        label: '合同金额',
        prop: 'contractTotalPrice',width:120,
        type: 'select',
      },
  
      {
      label: '合同附件',
      prop: 'file',
      type: 'select',
    },
    {
      label: '归档附件',
      prop: 'fileFinal',
      type: 'select',
    },
      {
        label: '业务员',
        prop: 'businessName',
        component: 'wf-user-drop',
        search: true,
      },
        {
      label:'签订时间',
      prop:'signTime',
      component:'wf-daterange-search',
      search:true,
      searchSpan:6,
         formatter: (row, column) => {
        return row.signDate;
      },
    }
    ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  
  const addUrl = '';
  const delUrl = '';
  const updateUrl = '';
  const tableUrl = '/api/vt-admin/contractFiles/page';
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
          size,
          current,
          ...params.value,
          selectType:2,
             startTime:params.value.signTime?params.value.signTime[0] : null,
        endTime:params.value.signTime?params.value.signTime[1] : null,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        page.value.total = res.data.data.total;
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...form,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  function reset() {
    onLoad();
  }
  function searchChange(params, done) {
    onLoad();
    done();
  }
  function handle(row) {
    proxy.$refs.dialogForm.show({
      title: '归档',
      option: {
        column: [
          {
            label: '合同附件',
            prop: 'files',
            type: 'upload',
            dataType: 'object',
            span: 24,
            slot: true,
            limit: 1,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'link',
              name: 'originalName',
            },
            action: '/blade-resource/attach/upload',
          },
        ],
      },
      callback(res) {
        console.log(res.data);
  
        axios
          .post('/api/vt-admin/contractFiles/upload', {
            id: row.id,
            generatedFileName: res.data.files[0].label,
            generatedFilePath: res.data.files[0].value,
          })
          .then(e => {
            proxy.$message.success('操作成功');
            res.close();
            onLoad();
          });
      },
    });
  }
  function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.contractId,
      delBtn: 1,
       name:row.offerName,
      selectType: 0,
    },
  });
}
  </script>
  
  <style lang="scss" scoped></style>
  