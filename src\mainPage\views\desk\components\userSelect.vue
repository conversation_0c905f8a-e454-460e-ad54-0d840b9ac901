<template>
  <div>
    <el-select
      v-model="name"
      filterable
      remote
      clearable
      reserve-keyword
      :placeholder="placeholder || '请输入名字'"
      :remote-method="remoteMethod"
      :loading="loading"
      @blur="handleBlur"
      @change="handleUserSelectConfirm"
    >
      <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.id" />
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'user-drop',
  components: {},
  emits: ['update:modelValue', 'change'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      default: () => {
        return 'small';
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    userUrl: {
      type: String,
      default: () => {
        return '/blade-system/search/user';
      },
    },
    tenantId: String,
    change: Function,
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          this.name = val;
        } else {
          this.name = '';
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      nameList: '',
      loading: false,
    };
  },
  mounted(){
    this.remoteMethod('')
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['user-select'].visible = true;
    },
    remoteMethod(value) {
      
      this.loading = true;
      axios.get('/api/blade-system/search/user?functionKeys=dataSearch&size=10000&name=' + value).then(res => {
        this.nameList = res.data.data.records;
        this.loading = false;
      });
    },
    handleUserSelectConfirm(id) {
      console.log(id);
      this.$emit('update:modelValue', id);
      this.$emit('change', id);
      if (this.change && typeof this.change == 'function') this.change({ value: id });
    },
  },
};
</script>
<style lang="scss"></style>
