<template>
  <basic-container>
    <el-form
      inline
      :model="form"
      ref="form"
      :rules="rules"
      label-width="80px"
      :inline="false"
      size="normal"
    >
      <el-form-item label="">
        <el-date-picker v-model="params.year" type="year" placeholder="date"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="searchChange">搜索</el-button>
        <el-button @click="reset">清空</el-button>
      </el-form-item>
    </el-form>
    <el-tabs v-model="activeName" type="card" class="demo-tabs" >
      <el-tab-pane label="全部" name="all"></el-tab-pane>
      <el-tab-pane :label="item.userName" v-for="item in tableData" :key="item.uuid" :name="item.uuid"></el-tab-pane>
     
    </el-tabs>
    <el-row :gutter="10">
      <el-col v-for="item in currentTable" :key="item.uuid" :span="activeName != 'all' ? 24 : 12">
        <avue-crud
          :option="option"
          :data="item.children"
          v-model:page="page"
          v-model:search="params"
          :table-loading="loading"
          ref="crud"
          @keyup.enter="onLoad"
          @search-reset="reset"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          v-model="form"
          :span-method="spanMethod"
          :cell-style="cellStyle"
          :header-row-style="
            () => {
              return { 'font-weight': 'bolder' };
            }
          "
        >
        </avue-crud>
      </el-col>
    </el-row>
    <!-- <el-table :data="tableData"  :span-method="spanMethod" row-key="uuid" border style="width: 100%">
      <el-table-column prop="businessUserName" label="业务员" width="width"> </el-table-column>
      <el-table-column prop="year" label="年份" width="width"> </el-table-column>
      <el-table-column prop="quarter" label="季度" #default="{row}" width="width"> 
        <span>{{ setQuarter(row.month) }}</span>
      </el-table-column>
      <el-table-column prop="month" label="月份" width="width"> </el-table-column>
      <el-table-column prop="totalPrice" label="合同金额" width="width"> </el-table-column>
      <el-table-column prop="otherOutCome" label="其他支出" width="width"> </el-table-column>
      <el-table-column prop="sealsPrice" label="销售额" width="width"> </el-table-column>
    </el-table> -->
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { randomLenNum } from '@/utils/util';
import moment from 'moment';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 12,
  searchSpan: 12,
  menuWidth: 270,
  border: true,
  menu: false,
  header: false,
  rowKey: 'userId',
  size: 'small',
  showSummary: true,
  sumColumnList: [
    { name: 'totalPrice', type: 'sum', decimals: 2 },
    { name: 'otherOutCome', type: 'sum', decimals: 2 },
    { name: 'sealsPrice', type: 'sum', decimals: 2 },
    { name: 'expensePrice', type: 'sum', decimals: 2 },
  ],
  //   rowParentKey: 'userId',
  column: [
    {
      label: '业务员',
      prop: 'businessUserName',
      width: 100,
      component: 'wf-user-drop',
      overHidden: true,
    },
    {
      label: '年份',
      prop: 'year',
      width: 100,
      type: 'year',
      format: 'YYYY',
      valueFormat: 'YYYY',
    },
    {
      label: '季度',
      prop: 'quarter',
      formatter: row => {
        return setQuarter(row.month);
      },
      width: 50,
    },
    {
      label: '月份',
      prop: 'month',
      width: 50,
    },
    {
      label: '合同金额',
      prop: 'totalPrice',
    },
    {
      label: '其他支出',
      prop: 'otherOutCome',
    },
    {
      label: '销售额',
      prop: 'sealsPrice',
    },
    {
      label: '报销费用',
      prop: 'expensePrice',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/statistics/businessPersonComprehensiveStatistics';
let params = ref({
  year: '' + new Date().getFullYear(),
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
onMounted(() => {
  onLoad();
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        year: 2024,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.map(item => {
        return {
          businessUser: item.userId,
          businessUserName: item.userName,
          ...item,
          totalPrice: item.children
            ?.reduce((total, child) => {
              total += child.totalPrice * 1;
              return total;
            }, 0)
            .toFixed(2),
          otherOutCome: item.children
            ?.reduce((total, child) => {
              total += child.otherOutCome * 1;
              return total;
            }, 0)
            .toFixed(2),
          expensePrice: item.children
            ?.reduce((total, child) => {
              total += child.expensePrice * 1;
              return total;
            }, 0)
            .toFixed(2),
          uuid: randomLenNum(10, true),
          children: item.children?.map(child => {
            return {
              ...child,
              businessUserName: item.userName,
              year: '' + item.year,
              uuid: randomLenNum(10, true),
            };
          }),
        };
      });
      console.log(tableData.value);

      // page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function setQuarter(month) {
  if (month >= 1 && month <= 3) {
    return 1;
  } else if (month >= 4 && month <= 6) {
    return 2;
  } else if (month >= 7 && month <= 9) {
    return 3;
  } else if (month >= 10 && month <= 12) {
    return 4;
  } else {
    return '';
  }
}

function reset() {
  params.value.year = '' + new Date().getFullYear();
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function spanMethod({ row, column, rowIndex, columnIndex }) {
  if (column.property == 'businessUserName') {
    if (rowIndex == 0) {
      return { rowspan: 12, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }
  if (column.property == 'year') {
    if (rowIndex == 0) {
      return { rowspan: 12, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }
  if (column.property == 'quarter') {
    if (rowIndex % 3 == 0 && rowIndex != 12) {
      return { rowspan: 3, colspan: 1 };
    } else {
      return { rowspan: 0, colspan: 1 };
    }
  }
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  // if(column.property == 'totalPrice' || column.property == 'otherOutCome' || column.property == 'expensePrice' || column.property == 'businessUserName'){
  return {
    // color: 'red',
    fontSize: '16px',
    fontWeight: 'bold',
    // }
  };
}
let activeName = ref('all')
const currentTable = computed(() => {
    if(activeName.value == 'all'){
        return tableData.value
    }else{
        return tableData.value.filter(item => item.uuid == activeName.value)
    }
})
</script>

<style lang="scss" scoped>
:deep(.el-table__footer) {
  font-weight: bolder !important;
  font-size: 16px;

  .cell {
    color: var(--el-color-primary) !important;
  }
}
</style>
