# 拖拽排序调试指南

## 当前问题

拖拽后的排列顺序不对，控制台没有报错。

## 调试步骤

### 1. 检查拖拽事件日志

在浏览器控制台中，拖拽产品时应该看到以下日志：

```
=== 拖拽开始 ===
拖拽事件: {oldIndex: X, newIndex: Y, categoryIndex: Z, productIndex: W, uuid: "..."}
当前分类产品: ["0: 产品A", "1: 产品B", "2: 产品C", ...]
拖拽产品: 产品名称 从位置 W
使用productIndex作为源位置: W
全局到本地映射: [{categoryIndex: 0, localIndex: 0, globalIndex: 0}, ...]
oldIndex映射: {categoryIndex: Z, localIndex: X1, globalIndex: X}
newIndex映射: {categoryIndex: Z, localIndex: Y1, globalIndex: Y}
分类内拖拽: X1 -> Y1
重排前: ["产品A", "产品B", "产品C", ...]
重排后: ["产品A", "产品C", "产品B", ...]
=== 拖拽完成，表格重新渲染 ===
```

### 2. 验证关键数据

请检查以下关键信息：

#### A. 拖拽事件参数
- `oldIndex`: 拖拽前在所有产品中的全局索引
- `newIndex`: 拖拽后在所有产品中的全局索引
- `categoryIndex`: 当前分类的索引
- `productIndex`: 产品在当前分类中的索引
- `uuid`: 产品的唯一标识

#### B. 映射关系
- 全局索引到分类内索引的映射是否正确
- oldIndex和newIndex对应的分类是否一致
- 分类内的localIndex计算是否正确

#### C. 数组重排
- 重排前的产品顺序
- 重排后的产品顺序
- 是否按预期移动了产品

### 3. 常见问题排查

#### 问题1：索引计算错误
**症状**：拖拽后产品位置不对
**检查**：
- 全局索引映射是否正确构建
- 分类内索引转换是否准确
- 是否有跨分类拖拽的情况

#### 问题2：数据更新不生效
**症状**：控制台显示正确，但界面没有更新
**检查**：
- `updateModuleDTOListFromTableData()` 是否正确执行
- `setTableData()` 是否被调用
- Vue的响应式更新是否生效

#### 问题3：拖拽范围错误
**症状**：拖拽到错误的位置
**检查**：
- Sortable.js的配置是否正确
- 可拖拽元素的选择器是否准确
- 是否有其他元素干扰拖拽

### 4. 手动测试步骤

1. **准备测试数据**：
   - 确保至少有一个分类
   - 分类中至少有3个产品
   - 产品名称要容易区分

2. **执行拖拽测试**：
   - 将第1个产品拖拽到第3个位置
   - 将第3个产品拖拽到第1个位置
   - 将中间的产品拖拽到两端

3. **验证结果**：
   - 检查产品在表格中的实际位置
   - 检查产品序号是否正确更新
   - 刷新页面后位置是否保持

### 5. 可能的修复方案

#### 方案1：简化拖拽逻辑
如果当前逻辑过于复杂，可以考虑：
```javascript
// 直接使用DOM元素的位置信息
function handleVirtualTableDragEnd(dragInfo) {
  const { oldIndex, newIndex, categoryIndex } = dragInfo
  
  // 获取当前分类的产品列表
  const category = tableData.value[categoryIndex]
  const products = category.productList.filter(p => p.detailType === 0)
  
  // 直接使用oldIndex和newIndex在当前分类内重排
  const [movedProduct] = products.splice(oldIndex, 1)
  products.splice(newIndex, 0, movedProduct)
  
  // 更新数据
  category.productList = [
    ...category.productList.filter(p => p.detailType !== 0),
    ...products
  ]
  
  updateModuleDTOListFromTableData()
  setTableData()
}
```

#### 方案2：使用原生拖拽API
如果Sortable.js的索引计算有问题，可以考虑：
- 使用HTML5的原生拖拽API
- 自己计算拖拽的源位置和目标位置
- 更精确地控制拖拽行为

#### 方案3：调试Sortable.js配置
检查Sortable.js的配置是否正确：
```javascript
new Sortable(tbody, {
  handle: '.sort',
  draggable: '.allow_td:not(.category)',
  filter: '.category, .action-row-content',
  animation: 180,
  delay: 0,
  // 添加更多调试信息
  onStart: (evt) => {
    console.log('拖拽开始:', evt)
  },
  onMove: (evt) => {
    console.log('拖拽移动:', evt)
  },
  onEnd: (evt) => {
    console.log('拖拽结束:', evt)
    handleDragEnd(evt)
  }
})
```

### 6. 调试技巧

#### 使用浏览器开发者工具
1. 打开控制台查看日志
2. 在Network标签页检查数据请求
3. 在Elements标签页检查DOM结构

#### 添加断点调试
在关键位置添加 `debugger` 语句：
```javascript
function handleVirtualTableDragEnd(dragInfo) {
  debugger; // 在这里暂停执行
  // ... 拖拽处理逻辑
}
```

#### 使用Vue开发者工具
1. 检查组件的数据状态
2. 查看props和computed的值
3. 监控数据变化

### 7. 预期的正确行为

拖拽完成后应该：
1. 产品在表格中的位置立即更新
2. 产品序号重新计算
3. 数据保存后位置保持不变
4. 控制台显示正确的拖拽日志

### 8. 如果问题仍然存在

请提供以下信息：
1. 完整的控制台日志
2. 拖拽前后的产品顺序
3. 具体的拖拽操作步骤
4. 预期结果和实际结果的对比

这样可以帮助更准确地定位和解决问题。
