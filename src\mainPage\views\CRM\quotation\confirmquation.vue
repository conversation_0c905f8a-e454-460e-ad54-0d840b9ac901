<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button text type="primary" icon="el-icon-check" @click="confirm(row)">确认</el-button>
        <el-button text type="primary" icon="el-icon-view" @click="handleView(row)">详情</el-button>
        <el-button text type="primary" icon="el-icon-clock" @click="viewProcess(row)"
            >流程</el-button
          >
      </template>
      <template #auditStatus="{ row }">
        <div v-if="row.auditStatus == 1 || row.auditStatus == 2">
          <el-tag effect='plain' v-if="row.auditStatus == 1" size="small" type="success">审核成功</el-tag>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditReason"
            placement="top-start"
            v-if="row.auditStatus == 2"
          >
            <el-tag effect='plain' size="small" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
          <el-tag effect='plain' v-if="row.auditType == 1" size="small" type="info">待采购审核</el-tag>
          <el-tag effect='plain' v-if="row.auditType == 2" size="small" type="info">待主管审核</el-tag>
          <el-tag effect='plain' v-if="row.auditType == 3" size="small" type="info">待总经理审核</el-tag>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <process ref="processRef" :currentId="currentId" :type="1"></process>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { offerStatus,auditStatus } from '@/const/const.js';
import process from './compoents/process.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 300,
  searchIcon:true,
  searchIndex:4,
  border: true,
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      width: 250,
      overHidden: true,
      search: true,
    },

    {
      label: '关联商机',
      prop: 'businessOpportunityName',
      width: 250,
      overHidden: true,
      search: true,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      search: true,
    },
    {
      label: '业务员',
      prop:'businessPersonName',
      component: 'wf-user-drop',
      search: true,
      formatter(row, column, cellValue, index) {
        return row.createName;
      }
    },
    // {
    //   label: '关联联系人',
    //   prop: 'contactPersonName',
    // },
    {
      label: '报价状态',
      type: 'select',
      dicData: offerStatus,
      prop: 'offerStatus',
      width:120,
      search: true,
    },
    {
      label: '审核状态',
      type: 'select',
      dicData: auditStatus,
      prop: 'auditStatus',
      search: true,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type:'date',
      search: true,
      searchRange: true,
      searchSpan: 6,
    },
    {
      label: '总金额（元）',
      prop: 'offerPrice',
      formatter:(row) => {
        return parseFloat(row.offerPrice).toLocaleString()
      }
    },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'date',
      format: 'YYYY-MM-DD HH:mm',
      overHidden:true,
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/offer/waitAuditPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function searchChange(params, done) {
  onLoad();
  done();
}
function handleView(row) {
  
  if (row.dataJson && row.isHasOption != 1) {
    router.push({
      path: '/CRM/quotation/compoents/add',
      query: {
        id: row.id,
        name: row.offerName,
        type: 'detail',
      },
    });
  } else if (row.dataJson && row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.name,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  } else {
    router.push({
      path: '/CRM/quotation/compoents/editOffer',
      query: {
        id: row.id,
        type: 'detail',
        name: row.offerName,
        businessOpportunityId: row.isHasOption == 1 ? row.businessOpportunityId : null,
      },
    });
  }

  // }
}
function confirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type:'radio',
          value:1,
          dicData: [
            {
              value: 1,
              label: '通过',
            },
            {
              value: 2,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control:(val) => {
            return {
              auditReason:{
                display:val == 2
              }
            }
          }
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type:'textarea',
          span:24
        }
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/offer/audit',{
          id:row.id,
          ...res.data
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
// 流程
function viewProcess(row) {
  currentId.value = row.id;
  proxy.$refs.processRef.open();
}
</script>

<style lang="scss"></style>
