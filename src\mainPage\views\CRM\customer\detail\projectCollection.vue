<template>
  <div>
    <collectionPlanMy v-if="props.info.type == 0 || props.info.type == 2" :customerId="props.info.customerId"></collectionPlanMy>
    <contractCollectionPlan v-else :customerId="props.info.customerId"></contractCollectionPlan>
  </div>
</template>

<script setup>
import contractCollectionPlan from '@/views/Finance/Collection/contractCollectionPlan.vue';
import collectionPlanMy from '@/views/Finance/Collection/collectionPlanMy.vue';
const props = defineProps(['info']);
</script>

<style lang="scss" scoped></style>
