<template>
  <div class="wrap" v-loading="loading">
    <div style="height: 100%; display: flex; justify-content: flex-start">
      <!-- <div class="formBox" style="height: 100%;width: 800px;">
            <avue-form :option="formOption" v-model="form"></avue-form>
        </div> -->
      <div style="height: 100%; width: 100%">
        <div
          style="
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            padding: 5px;
            transition: all 0.3s;
          "
          class="barbox"
          :class="{ isFold: isFold }"
        >
          <!-- <el-button @click="addProduct">添加产品</el-button>
            <el-button @click="generateOffer">生成报价单</el-button>
        -->
          <!-- <el-button @click="getSheetData">获取表格数据</el-button> -->
          <!-- <el-button @click="downloadOffer">下载报价单</el-button> -->
          <div>
            <el-form inline size="small" :disabled="route.query.type == 'detail'">
              <el-form-item label="设备税率">
                <el-popconfirm
                  hide-icon
                  @confirm="setPriceBytax('设备含税单价', '设备未税单价')"
                  title="修改将会重新计算所有产品设备未税价，是否确认?"
                >
                  <template #reference>
                    <el-input v-model="form.productRate" style="width: 100px"></el-input>
                  </template>
                </el-popconfirm>
              </el-form-item>
              <el-form-item label="人工税率">
                <el-popconfirm
                  hide-icon
                  @confirm="setPriceBytax('人工含税单价', '人工未税单价')"
                  title="修改将会重新计算所有产品人工未税价，是否确认?"
                >
                  <template #reference>
                    <el-input v-model="form.labourRate" style="width: 100px"></el-input>
                  </template>
                </el-popconfirm>
              </el-form-item>
              <el-form-item label="其他税率">
                <el-popconfirm
                  hide-icon
                  @confirm="setPriceBytax('其他含税单价', '其他未税单价')"
                  title="修改将会重新计算所有产品其他未税价，是否确认?"
                >
                  <template #reference>
                    <el-input v-model="form.otherRate" style="width: 100px"></el-input>
                  </template>
                </el-popconfirm>
              </el-form-item>

              <el-form-item label="延保税率">
                <el-popconfirm
                  hide-icon
                  @confirm="setPriceBytax('延保含税单价', '延保未税单价')"
                  title="修改将会重新计算所有产品延保未税价，是否确认?"
                >
                  <template #reference>
                    <el-input v-model="form.warrantyRate" style="width: 100px"></el-input>
                  </template>
                </el-popconfirm>
              </el-form-item>
              <el-form-item label="" v-if="props.option.type == 2">
                <el-switch
                  v-model="form.isLock"
                  size="small"
                  active-text="锁定"
                  inactive-text="解锁"
                />
                <el-popconfirm
                  hide-icon
                  @confirm="setPrice"
                  title="修改将会重新计算所有产品含税价格，是否确认?"
                >
                  <template #reference>
                    <el-input
                      :disabled="form.isLock"
                      v-model="form.tax"
                      style="width: 100px; margin-left: 15px"
                      placeholder="请输入"
                    ></el-input>
                  </template>
                </el-popconfirm>
              </el-form-item>
              <el-form-item>
                <el-button
                  title="如遇到计算错误，可点击此处刷新计算公式"
                  icon="QuestionFilled"
                  size="small"
                  type="primary"
                  @click="refreshFoluma"
                  >刷新计算公式</el-button
                >
              </el-form-item>
              <el-form-item>
                <el-button
                  title="获取产品最新信息"
                  icon="Refresh"
                  size="small"
                  type="primary"
                  @click="refreshProductInfo"
                  >刷新产品</el-button
                >
              </el-form-item>
              <el-form-item label="">
                <el-button
                  type=""
                  plain
                  icon="el-icon-setting"
                  @click="colmunDrawer = !colmunDrawer"
                  v-if="props.option.type != 1"
                  >自定义显示</el-button
                >
              </el-form-item>
            </el-form>
          </div>
          <div style="display: flex; justify-content: flex-start; align-items: center">
            <div v-if="props.option.type == 2">
              <el-button
                size="small"
                :disabled="route.query.type == 'detail'"
                style="margin-right: 20px"
                type="primary"
                @click="addCategory"
                >新增子项</el-button
              >
            </div>
            <!-- <div>
              <el-checkbox-group
                :disabled="route.query.type == 'detail'"
                v-model="utilCheckBox"
                @change="hideColumn"
                size="small"
              >
      
                <el-checkbox label="延保" border>隐藏延保</el-checkbox>
                <el-checkbox label="其他" border>隐藏其他</el-checkbox>
                <el-checkbox label="人工" border>隐藏人工</el-checkbox>
                <el-checkbox label="综合" border>隐藏综合</el-checkbox>
                <el-checkbox label="专项" border>隐藏专项</el-checkbox>
                <el-checkbox label="未税" border>隐藏未税</el-checkbox>
               
              </el-checkbox-group>
            </div> -->
          </div>
        </div>
        <div
          style="display: flex; cursor: pointer; justify-content: center; background-color: #ccc"
          @click="handleFold"
        >
          <el-icon :style="{ transform: `rotate(${isFold ? '-90deg' : '90deg'})` }"
            ><DArrowLeft
          /></el-icon>
        </div>
        <div
          id="luckysheet"
          style="width: 100%"
          class="box"
          :style="{ height: isFold ? '103%' : 'calc(100% - 60px)' }"
        ></div>
      </div>
    </div>

    <!-- 产品选择弹窗 -->
    <wf-product-select ref="product-select" check-type="box" @onConfirm="handleUserSelectConfirm">
    </wf-product-select>
    <!-- 供应商选择弹窗 -->
    <wf-supplier-select ref="supplier-select" @onConfirm="handleSupplierSelectConfirm">
    </wf-supplier-select>
    <el-drawer title="列显隐" size="20%" v-model="colmunDrawer">
      <el-table border :data="columnHideData">
        <el-table-column label="列名称" prop="value"></el-table-column>
        <el-table-column label="隐藏/显示">
          <template #default="scope">
            <el-switch @change="handleShowAndHide($event, scope.row)" v-model="scope.row.isShow" />
          </template>
        </el-table-column>
      </el-table>
    </el-drawer>
  </div>
</template>

<script setup>
import { onMounted, getCurrentInstance, defineProps } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/components/Y-UI/wf-supplier-select.vue';
import { option } from './option';
import { randomLenNum } from '@/utils/util';
import { exportExcel } from './export';
import { normal, programme } from './normal.js';
import {
  getRowByHeaderValue,
  getColumnByHeaderValue,
  getAllRowLength,
  getAllColumnLength,
  getCurrentHeaderValue,
  getRowByValue,
  setValueByHeaderAndRow,
  getValueByHeaderAndRow,
  setHidecolumn,
  setShowcolumn,
  refreshRowByHeaderValue,
} from './util';
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  option: {
    type: Object,
    default: () => {
      return {
        form: {
          productRate: '0.13',
          labourRate: '0.06',
          otherRate: '0.06',
          warrantyRate: '0.06',
        },
      };
    },
    //  isRefresh : 是否刷新价格
    //  detailList : 产品列表
    //  option ： JSON 数据
    // form：税率数据
  },
});
let columnHideData = ref([]);
onMounted(() => {
  console.log(props.option);
  if (props.option.type !== 2 && route.query.type !== 'detail') {
    renderSheet();
  }

  window.addProduct = addProduct;
  window.addSupplier = addSupplier;
  window.setTaxPrice = setTaxPrice;
});

let form = ref({
  productRate: '0.13',
  labourRate: '0.06',
  otherRate: '0.06',
  warrantyRate: '0.06',
});

let taxOption = {
  设备含税单价: '设备未税单价',
  人工含税单价: '人工未税单价',
  其他含税单价: '其他未税单价',
  延保含税单价: '延保未税单价',
};
let taxOptionReverse = {
  设备未税单价: '设备含税单价',
  人工未税单价: '人工含税单价',
  其他未税单价: '其他含税单价',
  延保未税单价: '延保含税单价',
};
const tax = {
  设备含税单价: 'productRate',
  设备未税单价: 'productRate',
  人工含税单价: 'labourRate',
  人工未税单价: 'labourRate',
  其他含税单价: 'otherRate',
  其他未税单价: 'otherRate',
  延保含税单价: 'warrantyRate',
  延保未税单价: 'warrantyRate',
};
function renderSheet(callback = () => {}) {
  form.value = props.option.form;
  isFold.value = props.option.form.isFold;
  columnHideData.value = props.option.form.columnHideData;
  luckysheet.create({
    container: 'luckysheet',
    lang: 'zh',
    title: '111',
    showtoolbar: true,
    row: 10,
    column: 15,

    data: props.option.option
      ? JSON.parse(props.option.option)
      : props.option.type === 1
      ? normal()
      : programme(),
    userInfo: false,
    allowEdit: route.query.type != 'detail',
    showinfobar: false,
    enableAddRow: false,
    // forceCalculation: true,
    cellRightClickConfig: {
      deleteColumn: false,
    },
    hook: {
      cellUpdateBefore: (r, c, value, isRfresh) => {
        // const headerValue = getCurrentHeaderValue(c)
        // if (taxOption[headerValue]) {
        //   setTaxtPrice(value, taxOption[headerValue], r, '/');
        // } else if (taxOptionReverse[headerValue]) {
        //   setTaxtPrice(value, taxOptionReverse[headerValue], r, '*');
        // }
      },
      cellEditBefore: val => {
        position.value.or = position.value.cr;
        position.value.oc = position.value.cc;
        position.value.cr = val[0].row[0];
        position.value.cc = val[0].column[0];
      },
      cellMousedown: (cell, p, sheet, context) => {
        position.value.or = position.value.cr;
        position.value.oc = position.value.cc;
        position.value.cr = p.r;
        position.value.cc = p.c;
        const headerIndex = getRowByHeaderValue();
        // // return
        console.log(headerIndex, position.value.or);
        if (position.value.or <= headerIndex) return;
        if (position.value.or && position.value.oc) {
          const value = luckysheet.getCellValue(position.value.or, position.value.oc);
          const headerValue = getCurrentHeaderValue(position.value.oc);

          if (value == 'NaN' || !value) {
          } else {
            if (taxOption[headerValue]) {
              setTaxtPrice(value, taxOption[headerValue], position.value.or, '/');
            } else if (taxOptionReverse[headerValue]) {
              // setTaxtPrice(value, taxOptionReverse[headerValue], position.value.or, '*');
            }
          }
        }
      },
      workbookCreateAfter: o => {
        refreshRowByHeaderValue();
        if (props.option.isRefresh) {
          // refreshData();
        }
        callback('all');
        luckysheet.setRangeShow("A2",{show:false})
      },
      sheetActivate: (index, a, b) => {
        console.log(index, a, b);

        refreshRowByHeaderValue();
        //  刷线汇总数据
        refreshTotalData(index);

        // 设置显示隐藏
        if (index != 'Sheet_3l0oT36lkioo_1706066384054') {
          setTimeout(() => {
            hideColumn();
          }, 500);
        }
      },
    },
    showsheetbarConfig: {
      add: false,
    },
  });
  //   getRowByHeaderValue('产品名称');
}
function addProduct(params) {
  proxy.$refs['product-select'].visible = true;
  // handleUserSelectConfirm("1739092989822484482")
}
function addSupplier(params) {
  proxy.$refs['supplier-select'].visible = true;
}
function setTaxPrice(value) {
  const headerValue = getCurrentHeaderValue(position.value.cc);
  if (taxOption[headerValue]) {
    setTaxtPrice(value, taxOption[headerValue], position.value.cr, '/');
  } else if (taxOptionReverse[headerValue]) {
    setTaxtPrice(value, taxOptionReverse[headerValue], position.value.cr, '*');
  }
}
let position = ref({
  or: '', // 上一行
  oc: '', // 上一列
  cr: '', // 当前行
  cc: '', // 当前列
});

let loading = ref(false);
let productIds = ref([]);
function handleUserSelectConfirm(ids, type) {
  loading.value = true;
  console.log(type);
  if (type == true) {
    position.value.cr = getRowByHeaderValue() + 1;
    position.value.cc = 0;
  }
  //拿到汇总所在行
  const row = getRowByValue('汇总');
  productIds.value.push(...ids.split(','));
  if (ids.split(',').length > row - position.value.cr) {
    window.luckysheet.insertRow(position.value.cr, {
      number: ids.split(',').length - (row - position.value.cr) + 1,
    });
  } else {
    window.luckysheet.insertRow(position.value.cr, {
      number: 1,
    });
  }

  ids.split(',').forEach((item, index) => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      const data = {
        ...r.data.data,
        productId: r.data.data.id,
        preSealPrice: r.data.data.sealPrice,
        sealPrice: '',
        number: 1,
        costPrice: r.data.data.costPrice || 0,
        laborCost: r.data.data.laborCost,
        id: null,
        productName: r.data.data.productName,
        productSpecification: r.data.data.productSpecification,
        description: r.data.data.description,
      };
      if (index == ids.split(',').length - 1) {
        loading.value = false;
      }
      renderCell(data, index, index == ids.split(',').length - 1);
    });
  });

  //   setFc();
  //   setCostFc();
  setTimeout(() => {
    luckysheet.refresh();
  }, 1000);
}
function handleSupplierSelectConfirm(id) {
  axios
    .get('/api/vt-admin/supplier/detail', {
      params: {
        id,
      },
    })
    .then(r => {
      window.luckysheet.setCellValue(position.value.cr, position.value.cc, {
        specialSupplierId: r.data.data.id,
        v: r.data.data.supplierName,
      });
    });
}
function handleBusiness(detailList) {
  position.value.cr = getRowByHeaderValue() + 1;
  position.value.cc = 0;

  //拿到汇总所在行
  const row = getRowByValue('汇总');

  if (detailList.length > row - position.value.cr) {
    window.luckysheet.insertRow(position.value.cr, {
      number: detailList.length - (row - position.value.cr) + 1,
    });
  } else {
    window.luckysheet.insertRow(position.value.cr, {
      number: 1,
    });
  }

  detailList.forEach((item, index) => {
    renderCell(item, index);
  });
}
function renderCell(item, index, bol) {
  const sheetWidth = getAllColumnLength();
  // 循环sheetWidth
  for (let i = 0; i < sheetWidth; i++) {
    //  获取当前列的表头值
    const headerValue = getCurrentHeaderValue(i);
    if (!headerValue) break;
    // 渲染数据初始化
    if (headerValue == '成本设备单价') {
      console.log(item, option[headerValue], position.value.cr + index, item[option[headerValue]]);
      window.luckysheet.setCellValue(
        position.value.cr + index,
        i,
        {
          v: item[option[headerValue]],
          headerKey: option[headerValue],
          productId: item.productId,
          ff: '宋体',
          fc:
            item.priceWarnType == 1
              ? 'green'
              : item.priceWarnType == 2
              ? 'orange'
              : item.priceWarnType == 3
              ? 'red'
              : 'black',
        },
        {
          isRefresh: false,
        }
      );
    } else if (headerValue == '产品名称') {
      window.luckysheet.setCellValue(
        position.value.cr + index,
        i,
        {
          v: item[option[headerValue]],
          headerKey: option[headerValue],
          productId: item.productId,
          ff: '宋体',
          bg: item.isNew == 1 ? '#F56C6C' : '#fff',
        },
        {
          isRefresh: true,
        }
      );
    } else {
      window.luckysheet.setCellValue(
        position.value.cr + index,
        i,
        {
          v: item[option[headerValue]] || null,
          headerKey: option[headerValue],
          productId: item.productId,
          ff: '宋体',
          f: setRowFc(headerValue, position.value.cr + index, i),
        },
        {
          isRefresh: true,
        }
      );
    }
    if (bol && headerValue == '专项供应商') {
      luckysheet.refreshFormula();
    }
  }

  setCostFc();
  setFcNotax();
  setFc();
  setTimeout(() => {
    luckysheet.refresh();
  }, 1000);
}
// 设置汇总公式 含税
function setFc() {
  console.log(111);
  const row = getRowByValue('汇总');
  const number = getColumnByHeaderValue('数量');
  const sealPrice = getColumnByHeaderValue('设备含税金额');
  const artificialPrice = getColumnByHeaderValue('人工含税金额');
  const otherPrice = getColumnByHeaderValue('其他含税金额');
  const extendedPrice = getColumnByHeaderValue('延保含税金额');
  const allPrice = getColumnByHeaderValue('综合含税金额');
  const arr = [
    // { value: number, type: '含税数量' },
    { value: sealPrice, type: '设备金额(含税)', key: 'productRate' },
    { value: artificialPrice, type: '人工费(含税)', key: 'labourRate' },
    { value: otherPrice, type: '其他费(含税)', key: 'otherRate' },
    { value: extendedPrice, type: '延保费(含税)', key: 'warrantyRate' },
    { value: allPrice, type: '综合金额(含税)' },
  ];
  const headerRow = getRowByHeaderValue('产品名称');
  // console.log(number);
  // for (let index = row; index < 5 + row; index++) {

  //   const value  = luckysheet.getCellValue(index + 1 ,3);
  //   console.log(value);
  //   
  //   if(arr[index - row].type == '综合金额(含税)'){

  //     window.luckysheet.setCellValue(index + 1, 1, {
  //     f: `=SUM(B${row + 2}:B${row + 5})`,
  //     ff:'宋体',
  //     priceType: arr[index - row].type,
  //   });
  //   }else{
  //     window.luckysheet.setCellValue(index + 1, 1, {
  //     fa:'0.00',
  //     ff:'宋体',
  //     f: `=MULTIPLY(C${index + 2},${form.value[arr[index - row].key] * 1 + 1})`,
  //     priceType: arr[index - row].type,
  //   });
  //   }
  // }
  console.log(number);
  for (let index = row; index < 5 + row; index++) {
    window.luckysheet.setCellValue(index + 1, 1, {
      f: `=SUM(${arr[index - row].value}:${arr[index - row].value})`,
      priceType: arr[index - row].type,
    });
  }
  luckysheet.refreshFormula();
}
// 设置汇总公式 未税
function setFcNotax() {
  console.log(111);
  const row = getRowByValue('汇总');
  const number = getColumnByHeaderValue('数量');
  const sealPrice = getColumnByHeaderValue('设备未税金额');
  const artificialPrice = getColumnByHeaderValue('人工未税金额');
  const otherPrice = getColumnByHeaderValue('其他未税金额');
  const extendedPrice = getColumnByHeaderValue('延保未税金额');
  const allPrice = getColumnByHeaderValue('综合未税金额');
  const arr = [
    // { value: number, type: '未税数量' },
    { value: sealPrice, type: '设备金额(未税)' },
    { value: artificialPrice, type: '人工费(未税)' },
    { value: otherPrice, type: '其他费(未税)' },
    { value: extendedPrice, type: '延保费(未税)' },
    { value: allPrice, type: '综合金额(未税)' },
  ];
  const headerRow = getRowByHeaderValue('产品名称');
  console.log(number);
  for (let index = row; index < 5 + row; index++) {
    window.luckysheet.setCellValue(index + 1, 2, {
      f: `=SUM(${arr[index - row].value}:${arr[index - row].value})`,
      priceType: arr[index - row].type,
    });
  }
}
// 设置成本汇总公式
function setCostFc() {
  const row = getRowByValue('汇总');
  const number = getColumnByHeaderValue('数量');
  const sealPrice = getColumnByHeaderValue('成本设备金额');
  const artificialPrice = getColumnByHeaderValue('成本人工金额');
  const otherPrice = getColumnByHeaderValue('成本其他金额');
  const extendedPrice = getColumnByHeaderValue('成本延保金额/年');
  const allPrice = getColumnByHeaderValue('成本综合金额');
  const arr = [
    // { value: number, type: '成本数量' },
    { value: sealPrice, type: '设备成本金额' },
    { value: artificialPrice, type: '人工成本金额' },
    { value: otherPrice, type: '其他成本金额' },
    { value: extendedPrice, type: '延保成本金额' },
    { value: allPrice, type: '综合成本金额' },
  ];
  const headerRow = getRowByHeaderValue('产品名称');
  console.log(number);
  for (let index = row; index < 5 + row; index++) {
    window.luckysheet.setCellValue(index + 1, 3, {
      f: `=SUM(${arr[index - row].value}:${arr[index - row].value})`,
      priceType: arr[index - row].type,
    });
  }
}
let sumOption = {
  设备含税金额: '设备含税单价',
  设备未税金额: '设备未税单价',
  人工含税金额: '人工含税单价',
  人工未税金额: '人工未税单价',
  其他含税金额: '其他含税单价',
  其他未税金额: '其他未税单价',
  延保含税金额: '延保含税单价',
  延保未税金额: '延保未税单价',
  综合含税单价: '设备含税单价,人工含税单价,其他含税单价,延保含税单价',
  综合未税单价: '设备未税单价,人工未税单价,其他未税单价,延保未税单价',
  综合含税金额: '综合含税单价',
  综合未税金额: '综合未税单价',
  成本设备金额: '成本设备单价',
  成本人工金额: '成本人工单价',
  成本其他金额: '成本其他单价',
  '成本延保金额/年': '成本延保单价/年',
  成本综合单价: '成本人工单价,成本其他单价,成本延保单价/年',
  成本综合金额: '成本综合单价',
};
// 单行计算公式
function setRowFc(headerValue, row, colmun) {
  if (!sumOption[headerValue]) return null;
  const number = getColumnByHeaderValue('数量');
  const specialCost = getColumnByHeaderValue('专项成本');
  if (['综合含税单价', '综合未税单价'].includes(headerValue)) {
    const text = sumOption[headerValue]
      .split(',')
      .map(item => {
        const value = getColumnByHeaderValue(item);
        return `${value}${row + 1}`;
      })
      .join(',');
    return `=SUM(${text})`;
  } else if (['成本综合单价'].includes(headerValue)) {
    const text = `=SUM(IF(${specialCost}${row + 1},${specialCost}${
      row + 1
    },${getColumnByHeaderValue('成本设备单价')}${row + 1}),${sumOption[headerValue]
      .split(',')
      .map(item => {
        const value = getColumnByHeaderValue(item);
        return `${value}${row + 1}`;
      })
      .join(',')})`;
    return text;
  } else if (['成本设备金额'].includes(headerValue)) {
    const text = `=MULTIPLY(IF(${specialCost}${row + 1},${specialCost}${
      row + 1
    },${getColumnByHeaderValue('成本设备单价')}${row + 1}),${number}${row + 1})`;
    return text;
  } else {
    const value = getColumnByHeaderValue(sumOption[headerValue]);
    return `=MULTIPLY(${value}${row + 1},${number}${row + 1})`;
  }
}
// 计算未税价格或者含税价格
function setTaxtPrice(cell, value, row, type, option = undefined) {
  let price;
  console.log(cell, value, row, type);
  if(!cell) cell = 0
  if (type == '/') {
    price = cell / (1 + form.value[tax[value]] * 1);
  } else {
    price = cell * (1 + form.value[tax[value]] * 1);
    console.log(price, form.value[tax[value]], cell);
  }
  //  console.log(getValueByHeaderAndRow(value,row,option));
  //   if( getValueByHeaderAndRow(value,row,option) == price) return
  setValueByHeaderAndRow(value, row, price.toFixed(3), option);
  luckysheet.refreshFormula();
}
function setRowValue() {}
function getSheetData() {
  const modelData = window.luckysheet
    .getAllSheets()
    .filter(item => item.name !== '汇总')
    .map((item, index) => {
      return {
        ...item,
        sort: index,
        productList: formatData(item),
      };
    });
  const { productRate, labourRate, otherRate, warrantyRate, isLock, tax } = form.value;
  console.log(modelData);
  return {
    productRate,
    labourRate,
    otherRate,
    warrantyRate,
    configuredJson: {
      isFold: isFold.value,
      columnHideData: columnHideData.value,
      isLock,
      tax,
    },
    modelData: modelData,
    dataJson: JSON.stringify(window.luckysheet.getAllSheets()),
  };
}

function formatData({ data, order }) {
  // 获取整个表格行数数
  const sheetOption =
    props.option.type == 1
      ? null
      : {
          order: order,
        };

  const sheetWidth = getAllRowLength(sheetOption);
  // 过滤表头表尾
  const footRow = getRowByValue('汇总', sheetOption);
  const headerRow = getRowByHeaderValue('产品名称', sheetOption);
  console.log(footRow, headerRow);
  let productList = [];
  let classify = null;
  let classifySort = null;
  for (let index = headerRow + 1; index < footRow; index++) {
    if (data[index][0]) {
      // 有产品代表是产品，没有则代表是分类
      if (data[index][0].productId) {
        const productInfo = {
          productId: data[index][0].productId,
          classify,
          classifySort,
          sortNumber: index,
        };
        data[index].forEach(item => {
          if (!item) return;
          if (item.headerKey) {
            if (item.headerKey != 'specialSupplierId') {
              productInfo[item.headerKey] = item.v;
            } else {
              if (item.v) {
                productInfo[item.headerKey] = item.specialSupplierId;
              } else {
                productInfo[item.headerKey] = '';
              }
            }
          }
        });
        productList.push(productInfo);
      } else {
        classify = data[index][0].v;
        classifySort = index;
      }
    }
  }
  console.log(productList);
  return productList;
}
function downloadOffer() {
  //    window.luckysheet.deleteColumn(0,0)
  console.log(window.luckysheet.getAllSheets());

  exportExcel(window.luckysheet.getAllSheets(), '报价单');
}
function generateOffer() {}

let utilCheckBox = ref([]);
const hideOption = {
  人工: [
    '人工含税单价',
    '人工未税单价',
    '人工含税金额',
    '人工未税金额',
    '成本人工单价',
    '成本人工金额',
    '最近人工销售价',
  ],
  延保: [
    '延保含税单价',
    '延保未税单价',
    '延保含税金额',
    '延保未税金额',
    '成本延保单价/年',
    '成本延保金额/年',
    '最近延保销售价',
  ],
  其他: [
    '其他含税单价',
    '其他未税单价',
    '其他含税金额',
    '其他未税金额',
    '成本其他单价',
    '成本其他金额',
    '最近其他费销售价',
  ],
  综合: [
    '综合含税单价',
    '综合未税单价',
    '综合含税金额',
    '综合未税金额',
    '成本综合单价',
    '成本综合金额',
  ],
  未税: [
    '设备未税单价',
    '设备未税金额',
    '人工未税单价',
    '人工未税金额',
    '延保未税单价',
    '延保未税金额',
    '其他未税单价',
    '其他未税金额',
    '综合未税单价',
    '综合未税金额',
  ],
  // 参考: [
  //   '最近设备销售价',
  //   '最近人工销售价',
  //   '最近其他费销售价',
  //   '最近延保销售价',
  //   '最低销售价',
  //   '参考销售价',
  //   '市场价',
  // ],
  专项: ['专项成本', '专项供应商'],
  //   '未税': [
  //     '设备未税单价',
  //     '设备未税金额',
  //     '人工未税单价',
  //     '人工未税金额',
  //     '其他未税单价',
  //     '其他未税金额',
  //     '延保未税单价',
  //     '延保未税金额',
  //     '综合未税单价',
  //     '综合未税金额',
  //   ],
  // 成本: [
  //   '成本设备单价',
  //   '成本设备金额',
  //   '成本人工单价',
  //   '成本人工金额',
  //   '成本其他单价',
  //   '成本其他金额',
  //   '成本延保单价/年',
  //   '成本延保金额/年',
  //   '成本综合单价',
  //   '成本综合金额',
  // ],
};
function hideColumn() {
  const hideArr = [];
  const showArr = [];
  columnHideData.value.forEach(item => {
    if (item.isShow) {
      showArr.push(item.value);
    } else {
      hideArr.push(item.value);
    }
  });
  setShowcolumn(showArr);
  setHidecolumn(hideArr);
}
function refreshProductInfo() {
  proxy
    .$confirm('确认刷新产品信息？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      refreshData('some');
    });
}
// 刷新数据
const allUpdateArr = [
  '产品名称',
  '品牌',
  '规格型号',
  '详细描述',
  '最近设备销售价',
  '最近人工销售价',
  '最近其他费销售价',
  '最近延保销售价',
  '最低销售价',
  '参考销售价',
  '市场价',
  '成本设备单价',
];
const someUpdateArr = ['最近设备销售价', '最低销售价', '参考销售价', '市场价', '成本设备单价'];
let refresLoading = ref(null)
async function refreshData(type) {
  // type  all 刷新产品所有
  // type  some 刷新产品某些数据
  console.log(type, 'type');
  refresLoading.value =  proxy.$loading('刷新中')
  let renderArr = type === 'all' ? allUpdateArr : someUpdateArr;
  let newProductList = []; //新产品数据

  const prodcutIds = props.option.detailList.map(item => item.product && item.product.id);
  const allIds = [...new Set([...prodcutIds, ...productIds.value])];
  const res = await axios.get('/api/vt-admin/product/detailByIds', {
    params: {
      idList: allIds.join(','),
    },
  });
  console.log(allIds, 'allIds');
  console.log(res, 'res');
  newProductList = res.data.data;
  const allData = window.luckysheet.getAllSheets().filter(item => item.name !== '汇总');
  allData.forEach(item => {
    console.log(item);
    const sheetOption =
      props.option.type == 1
        ? null
        : {
            order: item.order,
          };
    // 获取表头所在行

    const rowHeader = getRowByHeaderValue(undefined, undefined, sheetOption);
    console.log(rowHeader);
    // 获取表尾所在行
    const rowFooter = getRowByValue('汇总', sheetOption);
    console.log(rowFooter);
    for (let index = rowHeader; index < rowFooter; index++) {
      let productId;
      if (props.option.type == 1) {
        productId = luckysheet.getCellValue(index, 1, { type: 'productId' });
      } else {
        productId = luckysheet.getCellValue(index, 1, { type: 'productId', order: item.order });
      }

      if (productId) {
        //  更新是否入库信息，最近设备销售价，最近人工销售价，最近其他费销售价，最近延保销售价，最低销售价，参考销售价，市场价，成本设备单价，成本人工单价，成本其他单价，成本延保单价/年，
        // const updateArr = ['产品名称','最近设备销售价','最近人工销售价','最近其他费销售价','最近延保销售价','最低销售价','参考销售价','市场价','成本设备单价','成本人工单价','成本其他单价','成本延保单价/年']

        renderArr.forEach(i => {
          const data = newProductList.find(item1 => item1.id == productId);
          const value = data[option[i]];

          if (i == '成本设备单价') {
            setValueByHeaderAndRow(
              i,
              index,
              {
                v: value || 0,
                fc:
                  data.priceWarnType == 1
                    ? 'green'
                    : data.priceWarnType == 2
                    ? 'orange'
                    : data.priceWarnType == 3
                    ? 'red'
                    : 'black',
              },
              sheetOption
            );
          } else if (i == '产品名称') {
            setValueByHeaderAndRow(
              i,
              index,
              {
                v: value,
                bg: data.isNew == 1 ? '#F56C6C' : '#fff',
              },
              sheetOption
            );
          } else {
            setValueByHeaderAndRow(i, index, value, sheetOption);
          }
        });
      }
    }
    refresLoading.value.close()
  });
  proxy.$message.success('刷新产品基本数据成功');
}
function refreshTotalData(index) {
  luckysheet.refreshFormula();
  if (index == 'Sheet_3l0oT36lkioo_1706066384054') {
    const loadingInstance = proxy.$loading({
      target: '.wrap',
      text: '计算中',
    });
    setTimeout(() => {
      const totalSheet = luckysheet.getAllSheets()[0];
      const allData = luckysheet
        .getAllSheets()
        .filter(item => item.index != 'Sheet_3l0oT36lkioo_1706066384054');

      let totalData = [];

      const A = getRowByValue('子系统名称', {
        order: 0,
      });
      const B = getRowByValue('总计', {
        order: 0,
      });

      if (B - A - 1 < allData.length) {
        setTimeout(() => {
          window.luckysheet.insertRow(getRowByValue('总计') - 1, {
            number: allData.length - (B - A - 1),
          });
        }, 0);
      }
      allData.forEach(item => {
        let arr = [];
        totalSheet.data[0].forEach((i, index) => {
          if (!i.v) return;
          const value = window.luckysheet.find(i.v, {
            type: 'priceType',
            order: item.order,
            isWholeWord: true,
          });
          if (index == 0) {
            arr.push(item.name);
          } else {
            if (value.length == 0) {
              arr.push(0);
            } else {
              arr.push(value[0].v);
            }
          }
        });
        console.log(arr);
        totalData.push(arr);
      });

      for (let ind = 0; ind < B - A - 1; ind++) {
        let i;
        if (totalData[ind]) {
          i = totalData[ind];
        } else {
          i = new Array(totalData[0].length).fill();
        }

        i.forEach((item, index) => {
          window.luckysheet.setCellValue(ind + 1, index, item, { order: 0 });
          if (ind == totalData.length - 1 && index == i.length - 1) {
            console.log('刷新');
            loadingInstance.close();
            window.luckysheet.refresh();
          }
        });
      }
      //     totalData.forEach((i,ind) =>{
      //     i.forEach((item,index) => {
      //         window.luckysheet.setCellValue(ind + 1,index ,item,{order:0})
      //         if(ind == totalData.length - 1 && index == i.length - 1){
      //             console.log('刷新');
      //             window.luckysheet.refresh()
      //         }
      //     })

      //    })

      // setTotalFc()
      luckysheet.refreshFormula();
    }, 100);
  }
}
function addCategory() {
  console.log(luckysheet.getAllSheets()[1]);
  luckysheet.setSheetAdd({
    sheetObject: programme()[1],
    order: luckysheet.getAllSheets().length,
  });
}
function setPriceBytax(key1, key2) {
  const allData = luckysheet
    .getAllSheets()
    .filter(item => item.index != 'Sheet_3l0oT36lkioo_1706066384054');
  allData.forEach(item => {
    const sheetOption =
      props.option.type == 1
        ? null
        : {
            order: item.order,
          };
    // 获取表头所在行
    const A = getRowByHeaderValue(undefined, undefined, sheetOption);
    // 获取表尾所在行
    const B = getRowByValue('汇总', sheetOption);
    const columnIndex = getColumnByHeaderValue(key1, 'number',sheetOption);
    console.log(B, A);
    for (let index = A; index < B; index++) {
      let productId;
      if (props.option.type == 1) {
        productId = luckysheet.getCellValue(index, 1, { type: 'productId' });
      } else {
        productId = luckysheet.getCellValue(index, 1, { type: 'productId', order: item.order });
      }
      if (productId) {
        
        let value;
        if (props.option.type == 1) {
          value = luckysheet.getCellValue(index, columnIndex);
        } else {
          value = luckysheet.getCellValue(index, columnIndex, sheetOption);
        }

        console.log(value);
        setTaxtPrice(value, key2, index, '/', sheetOption);
      }
    }
  });
}
function setPrice() {
  const allData = luckysheet
    .getAllSheets()
    .filter(item => item.index != 'Sheet_3l0oT36lkioo_1706066384054');
  allData.forEach(item => {
    const sheetOption =
      props.option.type == 1
        ? null
        : {
            order: item.order,
          };
    // 获取表头所在行
    const A = getRowByHeaderValue(undefined, undefined, sheetOption);
    // 获取表尾所在行
    const B = getRowByValue('汇总', sheetOption);
    console.log(B, A);
    for (let index = A; index < B; index++) {
      let productId;
      if (props.option.type == 1) {
        productId = luckysheet.getCellValue(index, 1, { type: 'productId' });
      } else {
        productId = luckysheet.getCellValue(index, 1, { type: 'productId', order: item.order });
      }
      if (productId) {
        const columnIndex = getColumnByHeaderValue('成本设备单价', 'number');
        const specialIndex = getColumnByHeaderValue('专项成本', 'number');
        let value;
        if (props.option.type == 1) {
          value =
            luckysheet.getCellValue(index, specialIndex) ||
            luckysheet.getCellValue(index, columnIndex);
        } else {
          value =
            luckysheet.getCellValue(index, specialIndex, sheetOption) ||
            luckysheet.getCellValue(index, columnIndex, sheetOption);
        }
        const price = value * (form.value.tax * 1);
        setValueByHeaderAndRow('设备含税单价', index, price.toFixed(2), sheetOption);
        setTaxtPrice(price, '设备未税单价', index, '/', sheetOption);
      }
    }
  });

  form.value.isLock = true;
}
let isFold = ref(false);
function handleFold() {
  isFold.value = !isFold.value;
  proxy.$nextTick(() => {
    luckysheet.resize();
  });
}
function refreshFoluma() {
  luckysheet.refreshFormula(() => {
    proxy.$message.success('刷新公式成功');
  });
}

// 列显隐藏
let colmunDrawer = ref(false);

function handleShowAndHide(value, row) {
  if (value) {
    setShowcolumn([row.value]);
  } else {
    setHidecolumn([row.value]);
  }
}
defineExpose({
  getSheetData,
  handleBusiness,
  renderSheet,
  refreshData,
});
</script>

<style lang="scss" scoped>
.wrap {
  height: calc(100% - 30px);
  width: calc(100% - 30px);
  background: #fff;
  box-sizing: border-box;
  margin: 15px;
  border-radius: 5px;
  .barbox {
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
  .box {
    height: calc(100% - 60px);
    width: 100%;
  }
}
.isFold {
  height: 0 !important;
  padding: 0 !important;
  overflow: hidden;
}
</style>
