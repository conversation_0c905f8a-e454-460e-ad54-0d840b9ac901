<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #isHasCollectionCode="{ row }">
        <el-switch
          v-model="row.isHasCollectionCode"
          :active-value="1"
          :inactive-value="0"
          @click="handleIsHasCollectionCodeChange(row)"
        >
        </el-switch>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  menu: false,
  column: [
    {
      label: '用户昵称',
      prop: 'name',

      overHidden: true,
      search: true,
    },
    {
      label: '真实姓名',
      prop: 'realName',

      overHidden: true,
      search: true,
    },
    {
      label: '用户头像',
      prop: 'avatar',
      type: 'upload',
      value: [],
      dataType: 'string',
      listType: 'picture-card',
      limit: 1,
      loadText: '附件上传中，请稍等',
      span: 24,
      accept: 'image/png, image/jpeg,image/jpg',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },

    {
      label: '手机号',
      prop: 'phone',
    },
    {
      label: '是否显示收款码',
      prop: 'isHasCollectionCode',
      type: 'switch',
      width: 150,
      // display: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/blade-system/user/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        userType: 2,
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleIsHasCollectionCodeChange(item) {
  axios
    .post('/api/blade-system/user/update-info', {
      isHasCollectionCode: item.isHasCollectionCode,
      id: item.id,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
</script>

<style lang="scss" scoped></style>
