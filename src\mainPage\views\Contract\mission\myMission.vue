<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #sealContractId="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
        </el-link>
      </template>
      <template #menu-left>
        <!-- <el-button type="primary" icon="plus" @click="addObject">新增</el-button> -->
      </template>
      <template #menu="{ row }">
        <!-- <el-button type="primary" text icon="tools" @click="distribution(row)">分配</el-button>
      <el-button type="primary" text icon="Aim" @click="sendInfo(row)">配送信息</el-button>
      <el-button type="primary" text icon="CircleCheck" @click="complete(row)">完成</el-button> -->
        <el-button
          type="primary"
          text
          icon="edit"
          @click="$refs.crud.rowEdit(row, $index)"
          v-if="row.objectStatus == 0"
          >编辑</el-button
        >
        <!-- <el-button type="primary" text icon="Printer" @click="printOrder(row)"
          >打印</el-button
        > -->
        <el-button
          type="primary"
          text
          icon="CirclePlus"
          @click="addProduct(row)"
          v-if="row.objectStatus != 2"
          >关联产品</el-button
        >
        <!-- <el-button type="primary" text icon="Promotion" @click="delay(row)">延期</el-button> -->
      </template>
      <template #objectName="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{
          row.$objectName || row.objectName
        }}</el-link>
      </template>
      <template #isComplete="{ row }">
        <div>配送：<span style="color: var(--el-color-warning)">未完成</span></div>
        <div>
          技术服务：<span v-if="row.isVisitingService == 1" style="color: var(--el-color-warning)"
            >未完成</span
          >
          <span v-if="row.isVisitingService == 1" style="color: var(--el-color-info)"
            >无需服务</span
          >
        </div>
      </template>
      <template #distributionUser="{ row }">
        <el-button @click="viewDetail(row, 2)">查看</el-button>
      </template>
      <template #productNumber="{ row }">
        <el-button
          circle
          type="primary"
          v-if="row.arriveStatus == 3 && row.objectStatus != 2"
          style="font-size: 20px"
          size="large"
          text
          icon="CirclePlus"
          @click="addProduct(row)"
        ></el-button>
        <el-button @click="viewDetail(row, 3)" v-else>查看</el-button>
      </template>
      <template #delay="{ row }">
        <el-button @click="viewDetail(row, 5)">查看</el-button>
      </template>
      <template #files="{ row }">
        <File :fileList="row.fileList"></File>
      </template>

    </avue-crud>

    <dialogForm ref="dialogForm"></dialogForm>
    <productSelect
      ref="productSelectRef"
      @confirm="handleConfirm"
      :id="currentSealContractId"
      url="/api/vt-admin/sealContract/productPageForObject"
    ></productSelect>
    <detail ref="missionDetail" :delBtn="true"></detail>
    <el-drawer title="新增标的" size="50%" v-model="addDrawer">
      <div style="display: flex; justify-content: flex-start; margin-bottom: 30px">
        <el-radio-group v-model="form.objectType">
          <el-radio :value="0" size="large" :label="0" border>收款任务</el-radio>
          <el-radio :value="1" size="large" :label="1" border>配送任务</el-radio>
          <el-radio :value="2" size="large" :label="2" border>技术上门</el-radio>
          <el-radio :value="3" size="large" :label="3" border>请购任务</el-radio>
          <el-radio :value="4" size="large" :label="4" border>其他任务</el-radio>
        </el-radio-group>
      </div>
      <avue-form :option="addoption" v-model="addForm"></avue-form>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productSelect from '../customer/compoents/productSelect.vue';
import detail from '@/views/Order/salesOrder/compoents/missionDetail.vue';
import { objectType } from '@/const/const';
let { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 180,
  border: true,
  column: [
    {
      type: 'input',
      label: '订单名称',
      span: 24,
      display: true,
      component: 'wf-contract-select',
      overHidden: true,
      prop: 'sealContractId',
      params: {
        Url: '/api/vt-admin/sealContract/pageForObject?selectType=5&contractType=0',
        checkType: 'checkbox',
      },
      formatter: (row, value) => {
        return row.contractName;
      },
      change: val => {
        handleChange(val.value);
      },
    },
    {
      label: '订单名称',
      hide: true,
      search: true,
      editDisplay: false,
      addDisplay: false,
      prop: 'contractName',
    },
    {
      type: 'checkbox',
      dicData: objectType,
      label: '任务名称',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'objectName',
      search: true,
      rules: [
        {
          required: true,
          message: '请选择任务名称',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'textarea',
      label: '任务描述',
      overHidden: true,
      span: 24,
      display: true,
      prop: 'durationNode',
    },
    {
      type: 'datetime',
      label: '完成时间要求',
      span: 12,
      display: true,
      span: 24,
      width: 140,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'planTime',
      rules: [
        {
          required: true,
          message: '请选择完成时间',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '关联产品',
      prop: 'productNumber',
      addDisplay: false,
      width: 90,
      editDisplay: false,
    },
    {
      label: '计划完成时间',
      type: 'date',
      prop: 'planDate',
      hide: true,
      addDisplay: false,
      editDisplay: false,
      component: 'wf-daterange-search',
      search: true,
      startPlaceholder: '开始时间',
      endPlaceholder: '结束时间',
      search: true,
      span: 24,
      searchSpan: 6,
      searchLabelWidth: 120,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
      width: 150,
      hide: true,
    },
    {
      type: 'select',
      label: '是否完成',
      span: 6,
      width: 90,
      editDisplay: false,
      addDisplay: false,
      value: '0',
      search: true,
      dicData: [
        {
          label: '未开始',
          value: 0,
        },
        {
          label: '进行中',
          value: 1,
        },
        {
          label: '已完成',
          value: 2,
        },
      ],
      prop: 'objectStatus',
    },
    {
      type: 'date',
      label: '完成时间',
      span: 12,
      editDisplay: false,
      addDisplay: false,
      overHidden: true,
      width: 110,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD',
      prop: 'completeTime',
    },
    // {
    //   type: 'textarea',
    //   label: '延期说明',
    //   span: 24,
    //   editDisplay: false,
    //   addDisplay: false,
    //   prop: 'delay',
    // },

    {
      label: '处理人',
      span: 12,
      component: 'wf-user-select',
      display: true,
      span: 24,
      overHidden: true,
      params: {
        checkType: 'checkbox',
        userUrl:
          '/api/blade-system/search/user?roleKeys=ywzy,swzy,business_leader,cgzy,purchase_leader,technology_leader,sales_person,technology_person',
        showTree: false,
      },
      // value:proxy.$store.getters.userInfo.user_id,
      prop: 'handleUser',
      formatter: row => {
        return row.handleUserName;
      },
      width: 80,
      rules: [
        {
          required: true,
          message: '请选择处理人',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '处理人',
      prop: 'handleUserName',
      editDisplay: false,
      addDisplay: false,
      hide: true,
      component: 'wf-user-drop',
      search: true,
    },

    // {
    //   type: 'select',
    //   label: '状态',
    //   span: 24,

    //   editDisplay: false,
    //   addDisplay: false,
    //   prop: 'status',
    // },
    {
      label: '附件',
      prop: 'files',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      overHidden: true,
      prop: 'remark',
      overHidden: true,
    },
    {
      label: '到货状态',
      prop: 'arriveStatus',
      type: 'select',
      span: 12,
      cell: false,
      width: 90,
      editDisplay: false,
      addDisplay: false,
      dicData: [
        {
          value: 0,
          label: '未到货',
        },
        {
          value: 1,
          label: '部分到货',
        },
        {
          value: 2,
          label: '已到货',
        },
        {
          value: 3,
          label: '未关联',
        },
      ],
    },
    {
      label: '派单人',
      prop: 'createName',
      editDisplay: false,
      width: 80,
      addDisplay: false,
    },
    {
      type: 'datetime',
      label: '派单时间',
      editDisplay: false,
      addDisplay: false,
      span: 12,
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'createTime',
    },
  ],
});

let form = ref({
  objectType: '',
});
// let objectType = ref(null);
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractObject/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/sealContractObject/update';
const tableUrl = '/api/vt-admin/sealContractObject/page';
let params = ref({});
let tableData = ref([]);

let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,

        selectType: 0,
        planStartDate: params.value.planDate && params.value.planDate[0],
        planEndDate: params.value.planDate && params.value.planDate[1],
        planDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    files: form.files && form.files.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

function viewDetail(row, active = 1) {
  proxy.$refs.missionDetail.viewDetail(row, active);
}

let currentObjectId = ref(null);
let currentSealContractId = ref(null);
function addProduct(row) {
  currentObjectId.value = row.id;
  currentSealContractId.value = row.sealContractId;
  proxy.$refs.productSelectRef.open();
  proxy.$refs.productSelectRef.reset();
  // proxy.$refs.productSelectRef.onLoad();
}
function handleConfirm(list) {
  console.log(list);
  const data = {
    id: currentObjectId.value,
    productDTOList: list.map(item => {
      return {
        ...item,
        orderDetailId: item.id,
        id: null,
        number: item.needNumber,
      };
    }),
  };
  axios.post('/api/vt-admin/sealContractObject/relationProduct', data).then(res => {
    proxy.$message.success(res.data.msg);
    // proxy.$refs.productSelectRef.onLoad();
    onLoad();
  });
}
function toDetail(row) {
  router.push({
    path: '/Order/salesOrder/compoents/detail',
    query: {
      id: row.sealContractId,
    },
  });
}
function setBaseInfo(value) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: value.split(',')[0],
      },
    })
    .then(res => {
      loading.value = false;
      // form.value = formatData(res.data.data)
      form.value = {
        ...res.data.data,
        id: null,
        distributionMethod: '',
        distributionAddress: res.data.data.deliveryAddress,
        distributionDate: res.data.data.deliveryDate
          ? res.data.data.deliveryDate + ' 00:00:00'
          : null,
      };
    });
}
let addDrawer = ref(false);

function addObject(params) {
  addDrawer.value = true;
}
let addForm = ref({});
let addoption = computed(() => {
  let option = {
    submitBtn: false,
    emptyBtn: false,
    labelPositon: 'left',
    column: [],
  };
  switch (form.value.objectType) {
    case 0:
      option.column = [
        {
          type: 'input',
          label: '合同名称',
          span: 24,
          display: true,
          overHidden: true,
          component: 'wf-contract-select',
          params: {
            Url: '/vt-admin/sealContract/page?selectType=5',
          },
          change: ({ value }) => {
            setBaseInfo(value);
          },
          prop: 'sealContractId',
          formatter: row => {
            return row.contractName;
          },
        },

        {
          type: 'input',
          label: '标的名称',
          span: 24,
          display: true,
          overHidden: true,
          search: true,
          prop: 'objectName',
        },
        {
          type: 'input',
          label: '任务描述',
          span: 24,
          display: true,
          overHidden: true,
          prop: 'durationNode',
        },
        {
          type: 'datetime',
          label: '时间要求',
          span: 12,
          display: true,
          width: 140,
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'planTime',
        },

        {
          label: '送货方式',
          type: 'radio',
          prop: 'distributionMethod',
          span: 12,
          // value:'1',
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'dictKey',
            label: 'dictValue',
          },
          control: val => {
            console.log(val);
            return {
              distributionUser: {
                label: val == 3 ? '送货人' : val == 1 ? '发货人' : '交付人',
              },
              distributionDate: {
                label:
                  val == 3
                    ? '送货日期'
                    : val == 1
                    ? '发货日期'
                    : val == 4
                    ? '交付日期'
                    : '自提日期',
              },
              distributionAddress: {
                label:
                  val == 3
                    ? '收货地址'
                    : val == 1
                    ? '收货地址'
                    : val == 4
                    ? '交付地址'
                    : '交付地址',
                display: val != 2 && form.value.isDistribution == 1 ? true : false,
              },
            };
          },
        },

        {
          label: '送货日期',
          prop: 'distributionDate',
          span: 12,
          type: 'date',
          hide: true,
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
        },
        {
          label: '送货地址',
          prop: 'distributionAddress',
          span: 24,
          hide: true,
          display: false,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'contact',
          span: 12,
          type: 'input',
          hide: true,
        },
        {
          label: '联系电话',
          prop: 'contactPhone',
          span: 12,
          hide: true,
          type: 'input',
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          overHidden: true,
          prop: 'remark',
        },
      ];
      break;

    default:
      break;
  }
  return option;
});
function handleChange(id) {
  axios
    .get('/api/vt-admin/sealContract/detail', {
      params: {
        id: id.split(',')[0],
      },
    })
    .then(res => {
      const { deliveryAddress, deliveryDate, distributionMethod, contact, contactPhone } =
        res.data.data;
      form.value = {
        ...form.value,
        distributionAddress: deliveryAddress,
        distributionMethod,
        contact,
        contactPhone,
      };
    });
}
</script>

<style lang="scss" scoped></style>
