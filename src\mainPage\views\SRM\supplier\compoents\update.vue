<template>
  <basic-container>
    <Title v-if="!props.isNew"
      >{{
        $route.query.type == 'edite' ? '编辑' : $route.query.type == 'detail' ? '查看' : '新增'
      }}
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" v-model="form" @submit="submit">
      <template #supplierName>
        <el-autocomplete
          style="width: 100%"
          v-model="form.supplierName"
          :fetch-suggestions="querySearch"
          :trigger-on-focus="false"
          value-key="supplierName"
          placeholder="请输入供应商名称"
        ></el-autocomplete>
      </template>
    </avue-form>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
  isNew: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['success']);
onMounted(() => {
  if (route.query.id) {
    getDetail();
  }
  if (route.query.type !== 'detail' && route.query.type !== 'edite') {
    // get_supplier_concat_type();
  }
});
const validator = (rules, value, callback) => {
  if (!value) return callback();
  const { supplierName, id = null } = form.value;
  axios
    .get('/api/vt-admin/supplier/isExist', {
      params: {
        supplierName,
        id,
      },
    })
    .then(res => {
      if (res.data.data == 1) {
        callback(new Error('系统已经存在此供应商'));
      } else {
        callback();
      }
    });
};
let addUrl = '/api/vt-admin/supplier/save';
let updateUrl = '/api/vt-admin/supplier/update';
let detailUrl = '/api/vt-admin/supplier/detail';
const router = useRouter();
const route = useRoute();
let option = ref({
  labelWidth: 150,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '供应商名称',
          prop: 'supplierName',
          rules: [
            {
              required: true,
              message: '请填写供应商名称',
              trigger: 'blur',
            },
            {
              validator: validator,
              trigger: 'blur',
            },
          ],
        },
        {
          label: '供应商地址',
          prop: 'address',
          rules: [
            {
              required: true,
              message: '请填写供应商地址',
            },
          ],
        },
        {
          type: 'select',
          label: '供应商类型',
          span: 12,
          // rules: [
          //   {
          //     required: true,
          //     message: '请选择供应商类型',
          //   },
          // ],
          display: true,
          prop: 'supplierClassify',
          dicUrl: '/blade-system/dict/dictionary?code=supplierClassify',
          props: {
            label: 'dictValue',
            value: 'id',
          },
        },
        {
          label: '主要供应产品',
          prop: 'mainProduct',
        },
       
        {
          label: '统一社会信用代码',
          prop: 'unifySocialCreditCode',
          // rules: [
          //   {
          //     required: true,
          //     message: '请填写供应商地址',
          //   },
          // ],
        },
        {
          label: '主要供应品牌',
          prop: 'brand',
        },
        {
          type: 'radio',
          label: '账期',
          cascader: [],
          // rules: [
          //   {
          //     required: true,
          //     message: '请选择账期',
          //   },
          // ],
          span: 12,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentMethod',
          dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
          remote: false,
        },
        {
          type: 'input',
          label: '供应商特色',
          span: 12,
          // rules: [
          //   {
          //     required: true,
          //     message: '请选择供应商级别',
          //   },
          // ],
          display: true,
          prop: 'supplierFeature',
        },
       
        {
          label: '是否是我们客户',
          prop: 'isMyCustomer',
          type: 'radio',
          span: 12,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          label: '供应商网址',
          prop: 'webUrl',
        },
      ],
    },
    {
      label: '联系人信息',
      prop: 'contact',
      column: [
        {
          label: '',
          prop: 'concatDTOList',
          type: 'dynamic',
          span: 24,
          labelWidth: 0,
          children: {
            align: 'center',
            headerAlign: 'center',
            rowAdd: done => {
              done();
            },
            rowDel: (row, done) => {
              done();
            },

            column: [
              {
                type: 'select',
                label: '联系人标签',

                span: 12,
                dataType: 'string',
                display: true,
                props: {
                  label: 'dictValue',
                  value: 'id',
                },
                rules: [
                  {
                    required: true,
                    message: '请选择联系人标签',
                  },
                ],
                width: 200,
                prop: 'concatType',

                dicUrl: '/blade-system/dict/dictionary?code=supplier_concat_type',
                remote: false,
              },
              {
                label: '姓名',
                prop: 'concatName',
              },
              {
                label: '手机',
                prop: 'concatPhone',
                type: 'number',
                controls: false,
              },

              {
                label: '微信',
                prop: 'wxCode',
              },
              {
                label: '邮箱',
                prop: 'mail',
              },
              {
                label: '性别',
                prop: 'sex',
                type: 'select',
                dicData: [
                  {
                    value: 1,
                    label: '男',
                  },
                  {
                    value: 2,
                    label: '女',
                  },
                ],
              },
              {
                label: '部门',
                prop: 'dept',
                type: 'select',
                props: {
                  label: 'dictValue',
                  value: 'id',
                  desc: 'desc',
                },
                dicUrl: '/blade-system/dict/dictionary?code=dept',
              },
              // {
              //   label: '职务',
              //   prop: 'post',
              //   type: 'select',
              //   props: {
              //     label: 'dictValue',
              //     value: 'id',
              //     desc: 'desc',
              //   },
              //   dicUrl: '/blade-system/dict/dictionary?code=position',

              // },

              {
                label: '备注',
                prop: 'remark',
                type: 'input',
                span: 24,
                rows: 2,
              },
            ],
          },
        },
      ],
    },
    {
      label: '银行信息',
      column: [
        {
          label: '对公账户名称',
          prop: 'bankAccount',
          span: 8,
        },
        {
          label: '对公开户行名称',
          prop: 'bankName',
          span: 8,
        },
        {
          label: '对公账号',
          prop: 'bankAccountNumber',
          span: 8,
        },
        {
          label: '对私账户名称',
          prop: 'privateBankAccount',
          span: 8,
        },
        {
          label: '对私开户行名称',
          prop: 'privateBankName',
          span: 8,
        },
        {
          label: '对私账号',
          prop: 'privateBankAccountNumber',
          span: 8,
        },
      ],
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
  if (route.query.type == 'detail') {
    option.value.detail = true;
    option.value.submitBtn = false;
    option.value.emptyBtn = false;
  } else {
    option.value.detail = false;
    option.value.submitBtn = true;
    option.value.emptyBtn = true;
  }
});

let form = ref({});

const { proxy } = getCurrentInstance();

function submit(form, done) {
  axios
    .post(route.query.id && !props.isNew ? updateUrl : addUrl, { ...form })
    .then(res => {
      if (!props.isNew) {
        proxy.$message.success(res.data.msg);
        router.back();
        router.$avueRouter.closeTag();
      } else {
        emit('success', res.data.data);
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function getDetail() {
  axios
    .get(detailUrl, {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      form.value = {
        ...res.data.data,
        concatDTOList: res.data.data.supplierConcatVOList,
      };
    });
}

function get_supplier_concat_type(params) {
  axios.get('/api/blade-system/dict/dictionary?code=supplier_concat_type').then(res => {
    form.value.concatDTOList = res.data.data.map(item => {
      const { id: concatType } = item;
      return {
        concatType,
      };
    });
  });
}
function querySearch(val, cb) {
  if(!val) return 
  axios
    .get('/api/vt-admin/supplier/list', {
      params: {
        size: 1000,
        supplierName: val,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
</script>

<style></style>
