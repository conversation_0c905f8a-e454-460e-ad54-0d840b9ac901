// 性能测试工具
// 用于测试右边面板产品数据的性能优化效果

/**
 * 生成测试数据
 * @param {number} categoryCount 分类数量
 * @param {number} productPerCategory 每个分类的产品数量
 * @returns {Array} 测试数据
 */
export function generateTestData(categoryCount = 10, productPerCategory = 100) {
  const testData = [];
  
  for (let i = 0; i < categoryCount; i++) {
    const category = {
      classify: `测试分类${i + 1}`,
      productList: []
    };
    
    for (let j = 0; j < productPerCategory; j++) {
      category.productList.push({
        uuid: `product_${i}_${j}`,
        customProductName: `测试产品${i + 1}-${j + 1}`,
        productBrand: `品牌${(j % 5) + 1}`,
        customProductSpecification: `规格${j + 1}`,
        customProductDescription: `这是测试产品${i + 1}-${j + 1}的描述信息`,
        customUnit: '台',
        number: Math.floor(Math.random() * 10) + 1,
        sealPrice: Math.floor(Math.random() * 10000) + 1000,
        laborCost: Math.floor(Math.random() * 500) + 100,
        ybhsdj: Math.floor(Math.random() * 200) + 50,
        qthsdj: Math.floor(Math.random() * 300) + 100,
        costPrice: Math.floor(Math.random() * 8000) + 800,
        rgcbdj: Math.floor(Math.random() * 400) + 80,
        ybcbdj: Math.floor(Math.random() * 150) + 40,
        qtcbdj: Math.floor(Math.random() * 250) + 80,
        specialCostPrice: null,
        specialSupplierId: null,
        remark: `备注信息${j + 1}`,
        detailType: 0,
        isCheck: 1,
        classify: `测试分类${i + 1}`,
        source: Math.floor(Math.random() * 5),
        categoryId: i + 1,
        productId: j + 1,
        minSealPrice: Math.floor(Math.random() * 500) + 500
      });
    }
    
    testData.push(category);
  }
  
  return testData;
}

/**
 * 性能测试函数
 * @param {Function} testFunction 要测试的函数
 * @param {Array} testData 测试数据
 * @param {string} testName 测试名称
 * @returns {Object} 测试结果
 */
export function performanceTest(testFunction, testData, testName = '性能测试') {
  console.log(`开始 ${testName}...`);
  
  const startTime = performance.now();
  const startMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
  
  // 执行测试函数
  const result = testFunction(testData);
  
  const endTime = performance.now();
  const endMemory = performance.memory ? performance.memory.usedJSHeapSize : 0;
  
  const executionTime = endTime - startTime;
  const memoryUsed = endMemory - startMemory;
  
  const testResult = {
    testName,
    executionTime: `${executionTime.toFixed(2)}ms`,
    memoryUsed: `${(memoryUsed / 1024 / 1024).toFixed(2)}MB`,
    result
  };
  
  console.log(`${testName} 完成:`, testResult);
  return testResult;
}

/**
 * 测试计算属性性能
 * @param {Array} testData 测试数据
 */
export function testCalculationPerformance(testData) {
  // 模拟原始的多次reduce计算
  function oldCalculation(data) {
    const list = data.reduce((pre, cur) => pre.concat(cur.productList), []);
    
    return {
      totalProductPrice: list.reduce((pre, cur) => pre + cur.number * cur.sealPrice, 0),
      totalLaborCost: list.reduce((pre, cur) => pre + cur.number * cur.laborCost, 0),
      totalYbPrice: list.reduce((pre, cur) => pre + cur.number * cur.ybhsdj, 0),
      totalQtPrice: list.reduce((pre, cur) => pre + cur.number * cur.qthsdj, 0),
      totalRgcbPrice: list.reduce((pre, cur) => pre + cur.number * cur.rgcbdj, 0),
      totalCostPrice: list.reduce((pre, cur) => pre + cur.number * (cur.specialCostPrice || cur.costPrice), 0),
      totalQtcbPrice: list.reduce((pre, cur) => pre + cur.number * cur.qtcbdj, 0),
      totalYbcbPrice: list.reduce((pre, cur) => pre + cur.number * cur.ybcbdj, 0),
    };
  }
  
  // 优化后的单次遍历计算
  function newCalculation(data) {
    const list = data.reduce((pre, cur) => pre.concat(cur.productList), []);
    
    return list.reduce((acc, cur) => {
      const number = cur.number || 0;
      acc.totalProductPrice += number * (cur.sealPrice || 0);
      acc.totalLaborCost += number * (cur.laborCost || 0);
      acc.totalYbPrice += number * (cur.ybhsdj || 0);
      acc.totalQtPrice += number * (cur.qthsdj || 0);
      acc.totalRgcbPrice += number * (cur.rgcbdj || 0);
      acc.totalCostPrice += number * (cur.specialCostPrice || cur.costPrice || 0);
      acc.totalQtcbPrice += number * (cur.qtcbdj || 0);
      acc.totalYbcbPrice += number * (cur.ybcbdj || 0);
      return acc;
    }, {
      totalProductPrice: 0,
      totalLaborCost: 0,
      totalYbPrice: 0,
      totalQtPrice: 0,
      totalRgcbPrice: 0,
      totalCostPrice: 0,
      totalQtcbPrice: 0,
      totalYbcbPrice: 0,
    });
  }
  
  const oldResult = performanceTest(oldCalculation, testData, '原始计算方法');
  const newResult = performanceTest(newCalculation, testData, '优化计算方法');
  
  const improvement = ((parseFloat(oldResult.executionTime) - parseFloat(newResult.executionTime)) / parseFloat(oldResult.executionTime) * 100).toFixed(2);
  
  console.log(`性能提升: ${improvement}%`);
  
  return {
    old: oldResult,
    new: newResult,
    improvement: `${improvement}%`
  };
}

/**
 * 测试数组操作性能
 * @param {Array} testData 测试数据
 */
export function testArrayOperationPerformance(testData) {
  const flatData = testData.reduce((pre, cur) => pre.concat(cur.productList), []);
  
  // 原始的findIndex方法
  function oldArrayOperation(data) {
    return data.reduce((pre, cur) => {
      if (pre.findIndex(item => item.classify === cur.classify) === -1) {
        pre.push({
          ...cur,
          classify: cur.classify,
          productList: [cur],
        });
      } else {
        pre.find(item => item.classify === cur.classify).productList.push(cur);
      }
      return pre;
    }, []);
  }
  
  // 优化的Map方法
  function newArrayOperation(data) {
    const classifyMap = new Map();
    
    return data.reduce((pre, cur) => {
      const classify = cur.classify;
      
      if (!classifyMap.has(classify)) {
        const newCategory = {
          ...cur,
          classify: classify,
          productList: [cur],
        };
        classifyMap.set(classify, newCategory);
        pre.push(newCategory);
      } else {
        classifyMap.get(classify).productList.push(cur);
      }
      return pre;
    }, []);
  }
  
  const oldResult = performanceTest(oldArrayOperation, flatData, '原始数组操作');
  const newResult = performanceTest(newArrayOperation, flatData, '优化数组操作');
  
  const improvement = ((parseFloat(oldResult.executionTime) - parseFloat(newResult.executionTime)) / parseFloat(oldResult.executionTime) * 100).toFixed(2);
  
  console.log(`数组操作性能提升: ${improvement}%`);
  
  return {
    old: oldResult,
    new: newResult,
    improvement: `${improvement}%`
  };
}

/**
 * 运行完整的性能测试套件
 * @param {number} categoryCount 分类数量
 * @param {number} productPerCategory 每个分类的产品数量
 */
export function runFullPerformanceTest(categoryCount = 10, productPerCategory = 100) {
  console.log('=== 开始性能测试 ===');
  console.log(`测试数据: ${categoryCount} 个分类，每个分类 ${productPerCategory} 个产品，总计 ${categoryCount * productPerCategory} 个产品`);
  
  const testData = generateTestData(categoryCount, productPerCategory);
  
  console.log('\n1. 测试计算属性性能:');
  const calculationResult = testCalculationPerformance(testData);
  
  console.log('\n2. 测试数组操作性能:');
  const arrayResult = testArrayOperationPerformance(testData);
  
  console.log('\n=== 性能测试完成 ===');
  console.log('总结:');
  console.log(`- 计算性能提升: ${calculationResult.improvement}`);
  console.log(`- 数组操作性能提升: ${arrayResult.improvement}`);
  
  return {
    calculation: calculationResult,
    arrayOperation: arrayResult,
    testDataSize: categoryCount * productPerCategory
  };
}

// 使用示例:
// import { runFullPerformanceTest } from './performance-test.js';
// runFullPerformanceTest(20, 200); // 测试20个分类，每个分类200个产品
