<template>
  <el-dialog
    title="选择模板"
    v-model="dialogVisible"
    style="width: 60%"
    draggable
    class="avue-dialog avue-dialog--top"
  >
    <el-row :gutter="20">
      <el-col :span="6">
        <avue-tree
          :option="treeOption"
          :data="treeData"
          @update="treeUpdate"
          @save="treeSave"
          node-key="id"
          ref="tree"
          @del="treeDel"
          @node-click="handleNodeClick"
          v-model="treeForm"
        >
          <template #default="{ node, data }">
            <span
              :style="{
                color: data.disabled ? '#ccc' : '',
                cursor: data.disabled ? 'not-allowed' : 'pointer',
              }"
              >{{ data.categoryName }}</span
            >
          </template>
        </avue-tree>
      </el-col>
      <el-col :span="18">
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          @keyup.enter="onLoad"
          @row-del="rowDel"
          @search-reset="onLoad"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          @row-click="rowClick"
          v-model="form"
        >
          <template #radio="{ row }">
            <el-radio v-model="selectRow" :label="row.id">{{}}</el-radio>
          </template>
          <template #menu="{ row }">
            <el-button type="text" icon="view" size="small" @click="handleView(row)"
              >查看</el-button
            >
          </template>
          <template #templateCategory="{ row }">
            <div v-if="row.templateCategory" style="display: flex; flex-wrap: wrap">
              <el-tag effect='plain'
                style="margin: 2px"
                size="small"
                v-for="item in row.$templateCategory?.split('|') || []"
                :key="item"
                >{{ item }}</el-tag
              >
            </div>
          </template>
          <!-- <template #templateCategory-search>
        <templateTagSelect v-model="params.templateCategory"></templateTagSelect>
      </template> -->
        </avue-crud>
      </el-col>
    </el-row>

    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="handleConfirm" type="primary">确 定</el-button>
    </div>
  </el-dialog>
  <el-drawer v-model="drawer" size="80%" :title="detailForm.templateName">
    <templateOne :data="detailForm" :detail="true"></templateOne>
  </el-drawer>
</template>

<script setup>
import templateOne from './template.vue';
import templateTagSelect from './templateTagSelect.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,

  searchMenuSpan: 6,
  searchSpan: 10,
  menuWidth: 100,
  header: false,
  menu: true,
  border: true,
  column: [
    {
      label: '',
      prop: 'radio',
      width: 60,
      hide: false,
    },
    {
      label: '模板名称',
      prop: 'templateName',
      search: true,
      width: 200,
      overHidden: true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入模板名称',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '模板类型',
      prop: 'templateType',
      type: 'select',
      width: 160,
      span: 24,

      dicData: [
        {
          value: 0,
          label: '方案模板',
        },

        {
          value: 1,
          label: '子系统模板',
        },
        {
          value: 2,
          label: '子分类模板',
        },
        {
          value: 3,
          label: '报价模板',
        },
      ],
      rules: [
        {
          required: true,
          message: '请选择模板类型',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务类型',
      // multiple: true,
      span: 12,
      width: 250,
      overHidden: true,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      search: true,
      display: true,
      filterable: true,
      prop: 'businessType',
      checkStrictly: true,
    },

    {
      label: '模板描述',
      prop: 'remark',
      search: true,
      type: 'textarea',
      width: 250,
      overHidden: true,
      span: 24,
    },
    {
      label: '创建人',
      display: false,
      prop: 'createUserName',
      width: 120,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      display: false,
      type: 'date',
      format: 'YYYY-MM-DD',
      width: 110,
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const tableUrl = '/api/vt-admin/optionTemplate/page';

let tableData = ref([{}]);
let treeData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let params = ref({
  // businessType: route.query.businessType,
});
let loading = ref(false);
const props = defineProps({
  templateType: {
    default: '',
  },
  businessType: {
    type: String,
  },
  level: {
    // 3 报价  其他  方案
    default: 0,
  },
});
watch(() => {
  // params.value.businessType = props.businessType;
});
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        templateType: props.templateType,
        templateCategory: currentNode.value.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
}
function getTree(id) {
  axios.get('/api/vt-admin/templateCategory/tree?categoryCode=' + id).then(res => {
    treeData.value = res.data.data;
  });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
let dialogVisible = ref(false);
let selectRow = ref(null);
function rowClick(row) {
  selectRow.value = row.id;
}
function open() {
  getTreeData();
  onLoad();
  dialogVisible.value = true;
}
function handleConfirm() {
  proxy.$emit('change', selectRow.value, () => {
    dialogVisible.value = false;
  });
}
let drawer = ref(false);
let detailForm = ref({});

function getTreeData() {
  axios.get('/api/vt-admin/templateCategory/tree?level=' + props.level).then(res => {
    treeData.value = handleData(res.data.data);
    
    console.log(treeData.value);
    proxy.$nextTick(() => {
      const data =
        props.level == 0
          ? treeData.value[0]
          : props.level == 1
          ? treeData.value[0].children[0]
          : treeData.value[0].children[0].children[0];

      proxy.$refs.tree.setCurrentKey(data.id);
      handleNodeClick(data);
    });
  });
}
function handleData(data) {
  if (!data || data.length < 1) {
    return [];
  }
  return data.map(item => {
    item.disabled = item.level != props.level;
    item.children = handleData(item.children);
    return item;
  });
}
let treeOption = ref({
  defaultExpandAll: true,
  menu: false,
  addBtn: false,
  formOption: {
    labelWidth: 100,
    column: [
      {
        label: '分类名称',
        prop: 'categoryName',
        // width: 250,
        overHidden: true,
        align: 'left',
        search: true,
        rules: [
          {
            required: true,
            message: '请填写分类名称',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '备注',
        prop: 'remark',
        // width: 250,
        overHidden: true,
        // search: true,
      },
    ],
  },
  props: {
    label: 'categoryName',
    children: 'children',
    value: 'id',
  },
});
function handleView(row) {
  let url = '/api/vt-admin/optionTemplate/detailForAdd';
  axios
    .get(url, {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      drawer.value = true;
      const data = res.data.data;
      proxy.$nextTick(() => {
        detailForm.value = data;
      });
    });
}
let currentNode = ref({});
let treeForm = ref({});
function handleNodeClick(node) {
  console.log(node);
  currentNode.value = node;
  onLoad();
  form.value.templateType = currentNode.value.level == 0 ? 0 : currentNode.value.level == 1 ? 2 : 3;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
