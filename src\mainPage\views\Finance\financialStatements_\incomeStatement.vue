<template>
  <basic-container block>
    <div style="display: flex; justify-content: space-between; margin-bottom: 10px">
      <el-breadcrumb style="margin-bottom: 10px" separator="/">
        <el-breadcrumb-item
          ><a style="cursor: pointer" href="#" @click="activeName = 'incomeStatemnetYear'"
            >{{ currentDate.split('-')[0] }}年度收入统计</a
          >
        </el-breadcrumb-item>
        <el-breadcrumb-item v-if="activeName == 'incomeStatemnetMonth'"
          ><a href="#" @click="activeName = 'incomeStatemnetMonth'"
            >{{ currentDate }}收入统计</a
          ></el-breadcrumb-item
        >
      </el-breadcrumb>
      <div>
        <el-button plain v-if="activeName == 'incomeStatemnetMonth'" @click="activeName = 'incomeStatemnetYear'" icon="back"></el-button>
      </div>
    </div>
    <!-- <incomeStatemnetMonth></incomeStatemnetMonth>
    <incomeStatemnetYear></incomeStatemnetYear> -->

    <transition name="fade" mode="out-in">
      <!-- <component :is="activeName"></component> -->
      
        <incomeStatemnetMonth
          :date="currentDate"
          v-if="activeName == 'incomeStatemnetMonth'"
        ></incomeStatemnetMonth>
        <incomeStatemnetYear
          @updateDate="updateDate"
          @priceClick="handleClick"
          v-else
        ></incomeStatemnetYear>
    
    </transition>
  </basic-container>
</template>

<script setup>
import { shallowRef } from 'vue';
import incomeStatemnetMonth from './incomeStatemnetMonth.vue';
import incomeStatemnetYear from './incomeStatemnetYear.vue';

let activeName = ref('incomeStatemnetYear');

let currentDate = ref(`${new Date().getFullYear()}-${new Date().getMonth() + 1}`);
function handleClick(val) {
  currentDate.value = val.date;
  activeName.value = 'incomeStatemnetMonth';
}

function updateDate(date) {
  currentDate.value = date;
}
</script>

<style lang="scss" scoped>
/* 下面我们会解释这些 class 是做什么的 */
.fade-enter-active,
.fade-leave-active {
  transition: all 0.5s ease;
  transform: translateX(0px);
}

.fade-enter-from,
.fade-leave-to {
  //   opacity: 0;
  transform: translateX(100px);
}
</style>
