<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="searchReset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @selection-change="selectionChange"
      @current-change="onLoad"
      @size-change="onLoad"
    >
      <template #menu-left="{}">
        <div style="display: flex; align-items: center">
          <span style="font-weight: bolder">总金额：</span>
          <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
        </div>
      </template>
      <!-- <template #menu="{ row }">
        <el-button
          type="primary"
          text
          icon="close"
          v-if="row.renewStatus !== 2"
          @click="closeRenew(row, $index)"
          >关闭</el-button
        >
        <el-button type="primary" text icon="Clock" @click="viewHistory(row, $index)"
          >历史</el-button
        >
      </template> -->
      <template #menu="{ row, index }">
        <!-- <el-button type="primary" text @click="handleEdit(row)" icon="Edit">编辑</el-button> -->
        <!-- <el-button
          type="primary"
          text
          @click="handleView(row)"
          icon="View"
          v-if="route.query.type == 0 || route.query.type == 2 || !route.query.type"
          >详情</el-button
        > -->

        <div v-if="row.stage != 5" style="display: flex; justify-content: center">
          <div>
            <el-button
              type="primary"
              text
              @click="follow(row)"
              icon="Service"
              v-if="row.stage != 4"
              >跟进</el-button
            >
          </div>
          <div >
            <el-button @click="handleEdit(row)" text icon="edit" type="primary" v-if="row.stage == 0"
              >编辑</el-button
            >
            <el-button
              @click="handleCommand(1, row)"
              text
              icon="DocumentAdd"
              type="primary"
              :command="1"
              v-if="row.stage == 0"
              >方案</el-button
            >

            <!-- <el-button
              :command="2"
              icon="Tickets"
              @click="handleCommand(2, row)"
              v-if="row.stage == 1 && row.optionStatus == 2"
              text
              type="primary"
              >报价</el-button
            > -->
            <!-- <el-dropdown-item :command="2">投标</el-dropdown-item> -->
            <el-button
              :command="5"
              @click="handleCommand(5, row)"
              text
              icon="Tickets"
              type="primary"
              v-if="row.stage == 0"
              >报价</el-button
            >
            <el-button :command="3" @click="handleCommand(3, row)" text icon="SetUp" type="primary"
              >登记</el-button
            >
            <el-button
              :command="3"
              @click="cancel(row)"
              text
              v-if="row.stage == 1"
              icon="back"
              type="primary"
              title="撤回到新增状态"
              >撤 回</el-button
            >
          
            <el-button type="primary" text icon="Clock" @click="viewHistory(row, $index)"
              >历史</el-button
            >
            <el-button
              :command="7"
              @click="$refs.crud.rowDel(row)"
              text
              icon="delete"
              type="primary"
              v-if="row.stage != 3"
              >删除</el-button
            >
          </div>
        </div>
        <el-button
          @click="handleCommand(8, row)"
          text
          icon="VideoPlay"
          type="primary"
          :command="8"
          v-else
          >重 启</el-button
        >
      </template>
      <template #overDate="{ row }">
        {{ row.overDate }}(<span
          style="font-size: 20px; font-weight: bolder"
          :style="{ color: `var(--el-color-${row.days > 7 ? 'success' : 'warning'})` }"
          >{{ row.days }}</span
        >天)
      </template>
      <template #sealContractName="{ row }">
        <el-link type="primary" @click="toDetail(row.sealContractId)">
          {{ row.sealContractName }}
        </el-link>
      </template>
      <template #overDate-search>
        <div style="display: flex">
          <el-date-picker
            v-model="params.overDateYear"
            value-format="YYYY"
            type="year"
            placeholder="请选择年份"
          />-
          <el-select
            v-model="params.overDateStrMonth"
            :disabled="!params.overDateYear"
            clearable
            placeholder="请选择月份"
          >
            <el-option
              v-for="item in [
                '01',
                '02',
                '03',
                '04',
                '05',
                '06',
                '07',
                '08',
                '09',
                '10',
                '11',
                '12',
              ]"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </div>
      </template>
      <template #stage="{ row }">
        <el-tooltip
          :content="row.failReason || row.sendBackReason || row.pauseReason"
          :disabled="
            !(
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
            )
          "
          placement=""
        >
          <el-tag
            effect="plain"
            :type="
              (row.stage == 0 && row.sendBackStatus == 1) ||
              row.$stage == '失单' ||
              row.$stage == '暂停'
                ? 'danger'
                : 'primary'
            "
            > <span style="font-size: 12px" v-if="row.stage != 2">
              {{
              row.$stage == '方案'
                ? `方案(${row.optionStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage == '报价'
                ? `报价(${row.offerStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage
            }}
           
            </span>
            <el-link v-else type="primary" size="small" @click="handleToOffer(row)">{{
              row.$stage == '方案'
                ? `方案(${row.optionStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage == '报价'
                ? `报价(${row.offerStatus == 1 ? '进行中' : '已完成'})`
                : row.$stage
            }}
           </el-link>
          </el-tag>
        </el-tooltip>
      </template>
      <template #name="{ row }">
        <el-link type="primary" @click="handleView(row)">{{ row.name }}</el-link>
      </template>
      <template #customProductName="{ row }">
        <el-button text type="primary" @click="viewProductList(row)">查看</el-button>
      </template>
    </avue-crud>

    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="historyVisible" size="70%" title="查看历史">
      <el-collapse v-model="activeName" @change="collapseChange">
        <el-collapse-item v-for="item in currentRow.contractVOS || []" :name="item.id">
          <template #title>
            <div style="display: flex; justify-content: center">
              <el-link type="primary" @click.stop="toDetail(item.id)">
                {{ item.contractName }}
              </el-link>
              <span style="color: var(--el-color-info); margin-left: 5px" title="签订时间">{{
                item.signDate
              }}</span>
            </div>
          </template>
          <el-table
            class="avue-crud"
            :data="item.isViewAll ? item.allDetailList : item.detailList"
            border
            align="center"
          >
            <el-table-column
              label="产品名称"
              show-overflow-tooltip
              #default="{ row }"
              prop="customProductName"
            >
              {{ row.customProductName || row.product?.productName }}
            </el-table-column>
            <el-table-column
              label="规格型号"
              show-overflow-tooltip
              #default="{ row }"
              prop="customProductSpecification"
            >
              {{ row.customProductSpecification || row.product?.productSpecification }}
            </el-table-column>
            <el-table-column label="产品图片" #default="{ row }">
              <el-image
                style="width: 80px"
                :preview-src-list="[row.product.coverUrl]"
                :src="row.product.coverUrl"
              ></el-image>
            </el-table-column>
            <el-table-column
              label="产品描述"
              show-overflow-tooltip
              width="200"
              #default="{ row }"
              prop="customProductDescription"
            >
              {{ row.customProductDescription || row.product?.description }}
            </el-table-column>
            <el-table-column label="品牌" #default="{ row }" prop="product.productBrand">
              {{ row.productBrand || row.product?.productBrand }}
            </el-table-column>
            <el-table-column label="单位" prop="product.unitName" #default="{ row }" align="center">
              {{ row.customUnit || row.product?.unitName }}
            </el-table-column>
            <el-table-column label="数量" #default="{ row }" prop="number" align="center">
            </el-table-column>

            <el-table-column label="单价" #default="{ row }" align="center" prop="sealPrice">
              <span>{{ row.sealPrice }}</span>
            </el-table-column>
            <el-table-column label="金额" #default="{ row }" align="center" prop="sealPrice">
              <span>{{ (row.sealPrice * row.number).toLocaleString() }}</span>
            </el-table-column>
          </el-table>
          <el-divider v-if="!item.isViewAll">
            <el-button type="primary" @click="item.isViewAll = true" text>查看更多</el-button>
          </el-divider>
        </el-collapse-item>
      </el-collapse>
    </el-drawer>
    <productList ref="productListRef"></productList>
    <detail_drawer
      :type="currentType"
      :id="currentId"
      :customer-id="customerId"
      ref="detailRef"
    ></detail_drawer>
    <el-dialog title="编辑" v-model="dialogVisible" class="avue-dialog" width="30%">
      <el-form ref="editFormRef" v-model="editForm"  label-width="120px">
        <el-form-item label-width="120px" label="开始使用时间：">
          <el-date-picker
            v-model="editForm.useDate"
            type="date"
            value-format="YYYY-MM-DD"
            placeholder="请输入开始使用时间"
          ></el-date-picker>
        </el-form-item>
      </el-form>
      <el-form-item label-width="120px" label="使用周期：">
        <el-input
          v-model.number="editForm.cycleNumber"
          placeholder="请输入"
          style="width: 30%; margin-right: 5px"
        />
        <el-radio v-model="editForm.cycleType" :label="0">天</el-radio>
        <el-radio v-model="editForm.cycleType" :label="1">月</el-radio>
        <el-radio v-model="editForm.cycleType" :label="2">年</el-radio>
      </el-form-item>
      <template #footer>
        <span class="avue-dialog__footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleEditComfirm">确认</el-button>
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import template from './../programme/compoents/template.vue';
import productList from './compoents/productList.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated, nextTick,watch } from 'vue';
import { businessOpportunityData, bidStatus } from '@/const/const.js';
import { useRoute, useRouter } from 'vue-router';
import { ElMessageBox, ElDatePicker } from 'element-plus';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date.js';
import detail_drawer from './compoents/detail_drawer.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  menu: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  // selection: true,
  selectable: (row, index) => {
    return row.renewStatus == 0 || row.renewStatus == 1;
  },
  searchMenuSpan: 4,
  labelWidth: 130,
  searchSpan: 4,
  menuWidth: 330,
  border: true,
  column: [
    {
      label: '客户名称',
      prop: 'customerName',
      editDisplay: false,
      component: 'wf-customer-drop',
      overHidden: true,
      search: true,
    },
    {
      label: '商机名称',
      prop: 'name',
      component: 'wf-bussiness-drop',
      // editDisplay: false,
      overHidden: true,
      search: true,
    },
    {
      label: '产品',
      prop: 'customProductName',
      editDisplay: false,
      overHidden: true,
      width: 80,
    },
    {
      label: '总金额',
      prop: 'totalPrice',
      editDisplay: false,
      overHidden: true,
      width: 120,
    },

    {
      type: 'date',
      label: '开始使用日期',
      span: 24,
      width: 130,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },
    {
      type: 'date',
      label: '到期时间',
      span: 24,
      width: 170,
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'overDate',
      search: true,
      searchSpan: 6,
    },
    {
      type: 'input',
      label: '使用周期',
      width: 110,
      span: 24,
      display: true,
      prop: 'cycle',
      formatter: row => {
        return row.cycleNumber + `${['天', '月', '年'][row.cycleType]}`;
      },
    },
    {
      type: 'select',
      label: '提醒时间',
      width: 110,
      span: 24,
      display: true,
      props: {
        value: 'value',
        label: 'label',
      },
      dicData: (() => {
        let arr = [];
        for (let i = 1; i <= 31; i++) {
          arr.push({ label: '到期' + i + '天前', value: i });
        }
        return arr;
      })(),
      prop: 'beforeDays',
    },
    {
      label: '业务员',
      prop: 'businessPersonName',
      component: 'wf-user-drop',
      search: true,
      width: 80,
      // hide: true,
      formatter: (row, column, cellValue) => {
        return row.businessPersonName;
      }
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      overHidden: true,
      type: 'textarea',
    },
    {
      label: '商机阶段',
      prop: 'stage',
      width: 100,
      slot: true,
      type: 'select',
      search: true,
      dicData: businessOpportunityData,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/businessOpportunity/remove?ids=';
const updateUrl = '/api//vt-admin/vt-admin/customerProductRenew/update';
const tableUrl = '/api/vt-admin/businessOpportunity/pageForRenew';
let route = useRoute();
let params = ref({
  
});
watch(
  () => route.query,
  () => {
    
    // 检查是否有查询参数
    if (Object.keys(route.query).length > 0&& route.path === '/CRM/businessOpportunity/renewBusinessOpportunity') {
      params.value = {
        ...params.value,
        ...route.query,
      };

      onLoad();
    } else {
      
    }
  },
  {
    deep: true,
    immediate: true,
  }
);
console.log(params.value);
let tableData = ref([]);
let { proxy } = getCurrentInstance();

let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,

        businessSourceType: 1,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  axios
    .get('/api/vt-admin/businessOpportunity/pageForRenewStatistics', {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
        businessSourceType: 1,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

onActivated(() => {
  onLoad();
});
function searchChange(params, done) {
  onLoad();
  done();
}

function toDetail(id) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id,
      delBtn: 0,
    },
  });
}

function searchReset() {
  params.value = {};
  onLoad();
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
}
function addBussiness() {
  if (selectList.value.length == 0) {
    proxy.$message.warning('请选择要续约的数据');
    return;
  }
  const res = selectList.value.every(item => {
    return item.sealContractId == selectList.value[0].sealContractId;
  });
  if (!res) {
    proxy.$message.warning('请选择相同合同的数据');
    return;
  }

  router.push({
    path: '/CRM/businessOpportunity/compoents/update',
    query: {
      renewIds: selectList.value.map(item => item.id).join(','),
      businessOpportunityId: selectList.value[0].businessOpportunityId,
      offerId: selectList.value[0].offerId,
    },
  });
  proxy.$refs.crud.toggleSelection();
}
function closeRenew(row) {
  proxy
    .$confirm('确定关闭续约吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/customerProductRenew/close', {
          id: row.id,
        })
        .then(res => {
          if (res.data.code == 200) {
            proxy.$message.success('关闭成功');
            onLoad();
          }
        });
    });
}
// 历史
let historyVisible = ref(false);
let activeName = ref([]);
let historyList = ref([{ time: '2020-12-03', contractName: '测试合同' }]);
let currentRow = ref(null);
let detailOption = ref({
  align: 'center',
  addBtn: false,
  size: 'small',
  menu: false,
  editBtn: false,
  delBtn: false,
  searchMenuSpan: 4,
  labelWidth: 130,
  searchSpan: 4,
  header: false,
  menuWidth: 100,
  border: true,
  showSummary: true,
  sumColumnList: [
    {
      name: 'hsze',
      type: 'sum',
    },
  ],
  column: [
    // {
    //   label: '合同名称',
    //   prop: 'sealContractName',
    //   // editDisplay: false,
    //   overHidden: true,
    // },
    {
      label: '产品',
      prop: 'customProductName',
      editDisplay: false,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',

      overHidden: true,
      // search: true,
      span: 24,

      type: 'input',
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      editDisplay: false,
      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      editDisplay: false,
      type: 'number',
      span: 12,
      width: 80,
      cell: false,
    },

    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      editDisplay: false,
      span: 12,
      width: 100,
      cell: false,
    },
    {
      label: '金额',
      prop: 'hsze',
      type: 'number',
      span: 12,
      width: 100,
      editDisplay: false,
      cell: false,
    },
    {
      type: 'date',
      label: '开始使用日期',
      span: 24,
      width: 130,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },
    {
      type: 'date',
      label: '到期时间',
      span: 24,
      width: 170,
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'overDate',

      searchSpan: 6,
    },
    {
      type: 'input',
      label: '使用周期',
      width: 110,
      span: 24,
      display: true,
      prop: 'cycle',
      formatter: row => {
        return row.cycleNumber + `${['天', '月', '年'][row.cycleType]}`;
      },
    },

    {
      label: '备注',
      prop: 'remark',
      span: 24,
      overHidden: true,
      type: 'textarea',
    },
    {
      label: '状态',
      prop: 'renewStatus',
      span: 24,
      overHidden: true,
      type: 'select',
      dicData: [
        { label: '待续费', value: 0 },
        { label: '已到期', value: 1 },
        { label: '已关闭', value: 2 },
      ],
      span: 24,
      overHidden: true,
      type: 'textarea',
    },
  ],
});
function viewHistory(row, index) {
  currentRow.value = row;
  axios.get('/api/vt-admin/businessOpportunity/renewForHistory?id=' + row.id).then(res => {
    currentRow.value.contractVOS = res.data.data.reverse();
    historyVisible.value = true;
    activeName.value = currentRow.value.contractVOS.map(item => item.id);
    currentRow.value.contractVOS.forEach(item => {
      axios
        .get('/api/vt-admin/sealContract/productPage?size=50000&offerId=' + item.offerId)
        .then(res => {
          if (res.data.data.records.length > 5) {
            item.detailList = res.data.data.records.slice(0, 4);
            item.isViewAll = false;
          } else {
            item.isViewAll = true;
          }
          item.allDetailList = res.data.data.records;
        });
    });
  });
}
function rowStyle({ row, rowIndex }) {
  const {
    customProductName,
    productVO: { id, productName },
  } = row;
  const {
    customProductName: currentCustomProductName,
    productVO: { id: currentId, productName: currentProductName },
  } = currentRow.value;
  if (
    customProductName == currentCustomProductName ||
    customProductName == currentProductName ||
    productName == currentCustomProductName ||
    productName == currentProductName ||
    id == currentId
  ) {
    return {
      backgroundColor: 'var(--el-color-success-light-8)',
    };
  }
}

function cancel(row) {
  proxy
    .$confirm('是否撤回到新增状态?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/businessOpportunity/sendBackBusinessOpportunity', {
          id: row.id,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          onLoad();
        });
    });
}

// 跟进
function follow(row) {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      type: 1,
      customerId: row.customerId,
      logicId: row.id,
    },
  });
}
function handleCommand(val, row) {
  switch (val) {
    case 1:
      needProgramme(row);
      break;
    case 2:
      needQuotation(row);
      break;
    case 3:
      registration(row);
      break;
    case 5:
      toQuotation(row);
      break;
    case 6:
      toBid(row);
      break;
    case 7:
      bidResult(row);
      break;
    case 8:
      start(row);
      break;
    default:
      break;
  }
}
let optionRequireTime = ref('');
function needProgramme(row) {
  axios.get('/api/vt-admin/businessTypeLeader/getByBusinessId?businessId=' + row.id).then(res => {
    // proxy
    //   .$confirm(
    //     `<div>确定需要技术人员提供方案吗？</div><div>本次方案技术负责人：<i style="color:var(--el-color-primary)">${res.data.data.userName}</i></div>`,
    //     '提示',
    //     {
    //       confirmButtonText: '确认',
    //       cancelButtonText: '取消',
    //       dangerouslyUseHTMLString: true,
    //       type: 'info',
    //     }
    //   )
    //   .then(() => {
    //
    //   });

    ElMessageBox({
      title: '提示',
      message: () => [
        h('p', null, [
          h('div', null, '确定需要技术人员提供方案吗？'),
          h('div', null, [
            h('span', null, '本次技术负责:'),
            h('span', { style: 'color:var(--el-color-primary)' }, res.data.data.userName),
          ]),
          h('div', { style: 'margin-top:5px' }, [
            h('span', null, '要求完成时间：'),
            h(ElDatePicker, {
              modelValue: optionRequireTime.value,
              valueFormat: 'YYYY-MM-DD HH:mm:ss',
              placeholder: '请选择要求完成时间',
              'onUpdate:modelValue': val => {
                optionRequireTime.value = val;
              },
            }),
          ]),
        ]),
      ],
    }).then(res => {
      axios
        .post('/api/vt-admin/businessOpportunity/applyMakeOption', null, {
          params: {
            id: row.id,
            optionRequireTime: optionRequireTime.value,
          },
        })
        .then(e => {
          proxy.$message.success('已通知售前技术主管');
          onLoad();
        });
    });
  });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function needQuotation(row) {
  console.log(222);
  proxy.$refs.dialogForm.show({
    title: row.name,
    option: {
      column: [
        {
          label: '报价人员',
          component: 'wf-user-select',
          prop: 'offerPerson',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/businessOpportunity/transferOffer', null, {
          params: {
            id: row.id,
            ...res.data,
          },
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function toQuotation(row) {
  router.push({
    path: '/CRM/quotation/compoents/addVersion3',
    query: {
      businessId: row.id,
      type: 'add',
    },
  });
}
function toBid(row) {
  router.push({
    path: '/businessOpportunity/bid',
    query: {
      businessId: row.id,
    },
  });
}
// 登记
let dialogRef = '';
function registration(row) {
  proxy.$refs.dialogForm.show({
    title: '登记',
    option: {
      column: [
        {
          label: '登记结果',
          prop: 'stage',
          type: 'radio',
          rules: [
            {
              required: true,
              message: '请选择登记结果',
              trigger: 'change',
            },
          ],
          dicData: [
            {
              label: '成交',
              value: 3,
              disabled: row.stage != 2,
            },
            {
              label: '暂停',
              value: 5,
            },
            {
              label: '失单',
              value: 4,
              disabled: row.stage == 3,
            },
          ],
          control: val => {
            return {
              failReason: {
                display: val == 4,
              },
              isSplit: {
                display: val == 2 && row.isIntelligentizeProject != 1,
              },
              pauseReason: {
                display: val == 5,
              },
            };
          },
          span: 24,
        },
        // {
        //   label: '拆解产品',
        //   prop: 'isSplit',
        //   type: 'switch',
        //   dicData: [
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //   ],
        //   span: 12,
        //   display: false,
        // },
        {
          label: '失单原因',
          prop: 'failReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
        {
          label: '暂停原因',
          prop: 'pauseReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
      ],
    },
    callback(res) {
      console.log(res);
      dialogRef = res;
      if (res.data.isSplit == 1) {
        customerConfirmAndDecompose(row);
      } else {
        if (res.data.stage == 5) {
          axios
            .post('/api/vt-admin/businessOpportunity/pause', {
              id: row.id,
              pauseReason: res.data.pauseReason,
            })
            .then(r => {
              proxy.$message.success(r.data.msg);
              res.close();
              onLoad();
            });
        } else if (res.data.stage == 4) {
          axios
            .post('/api/vt-admin/businessOpportunity/setDown', {
              id: row.id,
              ...res.data,
            })
            .then(e => {
              proxy.$message.success(e.data.msg);
              res.close();

              onLoad();
            });
        } else {
          if (row.isIntelligentizeProject == 1) {
            res.close();
            router.push({
              path: '/Project/add',
              query: {
                offerId: row.offerId,
              },
            });
          } else {
            router.push({
              path: '/Order/salesOrder/compoents/addOrder',
              query: {
                offerId: row.offerId,
              },
            });
          }
        }
      }
    },
  });
}
let currentBusinessRow = ref({});
function viewProductList(row) {
  currentBusinessRow.value = row;

  proxy.$refs.productListRef.businessOpportunityId = currentBusinessRow.value.id;
  proxy.$refs.productListRef.open();
}
let customerId = ref(null);
let currentType = ref(null);
let currentId = ref(null);
let detailRef = ref(null);
function handleView(row) {
  router.push({
    path: '/CRM/businessOpportunity/compoents/detail',
    query: {
      type: 'detail',
      businessId: row.id,
      customerId: row.customerId,
    },
  });
}
let dialogVisible = ref(false);
let editForm = ref({});
let editFormRef = ref(null);
function handleEdit(row) {
  
  dialogVisible.value = true;
  nextTick(() => {
   
   
   
    setTimeout(() => {
       
        editFormRef.value.resetFields()
        editForm.value = row;
    },100)
  });
}
function handleEditComfirm() {
  axios.post('/api/vt-admin/businessOpportunity/editRenew', editForm.value).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg); 
    }
    onLoad();
    dialogVisible.value = false;
  })
}
function handleToOffer(row) {
  const path = proxy.$store.getters.userInfo.user_id == row.createUser ? '/CRM/quotation/myquation' : '/CRM/quotation/assistquation';
  router.push({
    path:path,
    query: {
      ids: row.offerId,
    },
  });
}
</script>

<style lang="scss" scoped>
.hilight {
  background-color: var(--el-color-success);
}
</style>
