<template>
  <div>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      @row-update="rowUpdate"
      :table-loading="loading"
      @refresh-change="onLoad"
      @row-save="rowSave"
      @row-del="rowDel"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: inline-block; margin-left: 20px">
          <el-form>
            <el-form-item label="网络拓扑图:">
              <el-image
                v-for="item in props.networkTopologyList"
                style="width: 80px"
                :preview-src-list="networkTopologyList.map(item => item.link)"
                :key="item.id"
                :src="item.link"
              ></el-image>
              <el-tag effect="plain" type="info" v-if="props.networkTopologyList.length == 0"
                >暂无</el-tag
              >
              <el-button
                text
                type="primary"
                @click="upload"
                v-if="props.type == 0 || props.type == 2"
              >
                上传</el-button
              >
            </el-form-item>
          </el-form>
        </div>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          text
          icon="Bell"
          @click="customerItRemind(row)"
          v-if="props.type == 0 || props.type == 2"
          >提醒</el-button
        >
        <el-button
          type="primary"
          text
          icon="Clock"
          @click="remindCords(row)"
          v-if="props.type == 0 || props.type == 2"
          >提醒记录</el-button
        >
      </template>
    </avue-crud>
    <RemindCords ref="remindCordsRef" :itCurrentId="currentId"></RemindCords>
    <dialogForm ref="dialogForm"></dialogForm>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import RemindCords from './remindCords.vue';
import { useRoute } from 'vue-router';
let route = useRoute();
let option = ref({
  // height:'auto',
  // calcHeight:30,
  menuWidth: 310,
  addBtn: props.type == 0 || props.type == 2,
  editBtn: props.type == 0 || props.type == 2,
  delBtn: props.type == 0 || props.type == 2,
  border: true,
  labelWidth: 120,
  column: [
    {
      label: '设备名称',
      prop: 'equipmentName',
      overHidden: true,
    },

    {
      label: '数量',
      prop: 'quantity',
      type: 'number',
      overHidden: true,
      width: 80,
    },
    {
      label: '品牌/型号',
      prop: 'brandModel',
      overHidden: true,
    },
    {
      label: '序列号(SN码)',
      prop: 'snCode',
      overHidden: true,
      width: 150,
    },
    {
      label: '使用场景',
      prop: 'usageScenario',
      overHidden: true,
      span: 24,
    },
    {
      label: '购买日期',
      prop: 'buyDate',
      span: 12,
      overHidden: true,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },

    {
      label: '产品维保',
      prop: 'isHasMaintenance',
      type: 'radio',
      width: 100,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },

    {
      label: '版本升级',
      prop: 'isHasVersionUpgrade',
      type: 'radio',
      width: 100,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      overHidden: true,
      type: 'textarea',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let loading = ref(false);
let props = defineProps({
  networkTopologyList: Array,
  id: String,
  type: {},
});
watchEffect(
  () => {
    if (props.id) {
      onLoad();
    }
  },
  {
    flush: 'post',
  }
);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/customerItInfo/page', {
      params: {
        customerId: props.id,
        size,
        current,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
    customerId: props.id,
  };
  axios
    .post('/api/vt-admin/customerItInfo/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/customerItInfo/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/customerItInfo/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function tip() {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          label: '提醒时间',
          component: 'wf-user-select',
          prop: 'assistant',
          value: form.value.assistant,
        },
        {
          label: '提醒内容',
          prop: 'referrer',
          value: form.value.referrer,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customer/update', {
          ...res.data,
          id: props.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
function customerItRemind(row) {
  proxy.$refs.dialogForm.show({
    title: '新建提醒',
    option: {
      column: [
        {
          label: '提醒时间',
          prop: 'remindTime',
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          rules: [{ required: true, message: '请选择提醒时间', trigger: 'blur' }],
        },
        {
          label: '提醒人',
          component: 'wf-user-select',
          prop: 'remindPerson',
          span: 12,
          rules: [{ required: true, message: '请选择提醒人', trigger: 'blur' }],
        },
        {
          label: '提醒内容',
          type: 'textarea',
          span: 24,
          prop: 'remindContent',
          rules: [{ required: true, message: '请输入提醒内容', trigger: 'blur' }],
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customerItRemind/save', {
          ...res.data,
          itId: row.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
let currentId = ref('');
function remindCords(row) {
  currentId.value = row.id;
  proxy.$refs.remindCordsRef.open();
}
function upload() {
  let form = {};
  proxy.$refs.dialogForm.show({
    title: '上传拓扑图',
    option: {
      column: [
        {
          label: '拓扑图',
          prop: 'networkTopology',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          value: props.networkTopologyList[0] && props.networkTopologyList[0].link,
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            form.networkTopology = res.id;
            done();
          },
          uploadExceed: () => {
            proxy.$message.warning('请先删除当前图片');
          },
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/customer/update', {
          networkTopology: form.networkTopology,
          id: props.id,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getDetail();
        });
    },
  });
}
</script>

<style lang="scss" scoped></style>
