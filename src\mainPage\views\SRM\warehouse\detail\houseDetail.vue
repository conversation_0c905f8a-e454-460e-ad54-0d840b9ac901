<template>
  <basic-container style="height: 100%">
    <Title
      >库存详情
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <!-- <h3 style="margin-bottom: 10px" class="title">{{ route.query.productName }}</h3> -->
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="产品名称">
            <el-tag effect='plain' size="large">{{ route.query.productName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="品牌">
            <el-tag effect='plain' size="large">{{ form.productVO.productBrand
 || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="规格型号">
            <el-tag effect='plain' size="large">{{ form.productVO.productSpecification
 || '---' }}</el-tag>
          </el-form-item>
          
        </el-form>
      </div>
    </div>
    <!-- <div style="display: flex">
          <div class="left_content">
            <div class="main_box">
              <div
                class="item"
                v-for="(item, index) in tabArr"
                :class="{ active: currentIndex == index }"
                @click="handleClick(index)"
              >
                <div class="arrow"></div>
                {{ item }}
              </div>
            </div>
          </div>
          <div style="width: calc(100% - 100px)">
            <component
              :is="currentCompoent"
              :form="form"
              :isEdit="isEdit"
              @getDetail="getDetail"
            ></component>
          </div>
        </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component :is="tabArray.find(item => item.name == activeName).component"></component>

    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';

// 入库记录
import wareHouseHistory from './inhouseHistory.vue';
// 出库记录
import outHouseHistory from './outhouseHistory.vue';
// 出库记录
import productListHistory from './productList.vue';
// 操作记录
import editHistory from './editHistory.vue';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
onMounted(() => {
  getDetail();
});
watchEffect(() => {
  if (route.query.id) {
    getDetail();
  }
});
const conpletArr = ['wareHouse', 'outHouse', 'productList','editHistory'];
const tabArray = [
  // {
  //   label: '库存列表',
  //   name: 'productList',
  //   component: productListHistory,
  // },
  {
    label: '入库记录',
    name: 'wareHouse',
    component: wareHouseHistory,
  },
  {
    label: '出库记录',
    name: 'outHouse',
    component: outHouseHistory,
  },
  // {
  //   label: '操作记录',
  //   name: 'editHistory',
  //   component: editHistory,
  // },
];

function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/inventory/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;

      form.value = res.data.data;
    });
}

let { proxy } = getCurrentInstance();

const store = useStore();
const tag = computed(() => store.getters.tag);
const activeName = ref('wareHouse');
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
