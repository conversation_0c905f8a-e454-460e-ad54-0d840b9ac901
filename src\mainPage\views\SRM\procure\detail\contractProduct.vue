<template>
  <div>
    <el-table
      class="avue-crud"
      :data="productListBf"
      border
      align="center"
      :row-style="rowStyle"
      @selection-change="handleSelectionChange"
    >
      <el-table-column
        label="设备名称"
        type="selection"
        width="55"
        :selectable="selectable"
      ></el-table-column>
      <el-table-column label="产品名称" prop="productName" show-overflow-tooltip></el-table-column>
      <el-table-column
        label="规格型号"
        prop="productSpecification"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="产品图片" #default="{ row }">
        <el-image
          style="width: 80px"
          :preview-src-list="[row.coverUrl]"
          :src="row.coverUrl"
        ></el-image>
      </el-table-column>
      <el-table-column
        label="产品描述"
        show-overflow-tooltip
        width="200"
        prop="description"
      ></el-table-column>
      <el-table-column label="品牌" align="center" prop="productBrand"></el-table-column>
      <el-table-column label="单位" align="center" width="100" prop="unitName"></el-table-column>
      <el-table-column label="数量" align="center" width="100" prop="number">
        <template #default="{ row }">
          <span>{{ row.number }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="已开票数量"
        align="center"
        width="100"
        prop="invoiceNumber"
      >
        <template #default="{ row }">
          <span>{{ row.invoiceNumber }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column label="入库数量" #default="{ row }" prop="arriveNumber"> </el-table-column> -->
      <el-table-column label="单价" align="center" width="100" prop="unitPrice">
        <template #default="{ row }">
          <span>{{ row.unitPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="金额" align="center" width="100" prop="totalPrice">
        <template #default="{ row }">
          <span>{{ (row.number * row.unitPrice).toFixed(2) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发票单价" align="center" width="100" prop="realCostPrice">
        <template #default="{ row }">
          <span>{{ row.realCostPrice }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发票状态" width="100" #default="{ row }" prop="isInvoice">
        <el-tag
          effect="plain"
          :type="row.isInvoice == 0 ? 'danger' : row.isInvoice == 1 ? 'success' : 'warning'"
          >{{ ['未收票', '已收票', '部分收票', '无需收票'][row.isInvoice] }}</el-tag
        >
      </el-table-column>
      <el-table-column
        label="采购类型"
        prop="purchaseType"
        show-overflow-tooltip
        width="120"

      >
      <template #default="{row}">
        <el-tag type="primary" v-if="row.purchaseType == 1" effect="plain">库存采购</el-tag>
      <el-tag type="success" v-else effect="plain">报价采购</el-tag>
      </template>

      </el-table-column>
      <el-table-column label="操作" width="100"  v-if="permission['purchaseContract:edit']" align="center">
        <template #default="{ row }">
          <el-button
            type="primary"
            text
            size="small"
            @click="editProduct(row)"
            icon="Edit"
          >
            编辑
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-drawer v-model="drawer" size="90%" title="新增收票">
      <el-row :gutter="10">
        <el-col style="height: 100%" :span="24">
          <el-divider content-position="left"
            ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
              >关联产品</span
            ></el-divider
          >
          <el-card class="box-card" shadow="never" style="height: 100%">
            <avue-crud
              :option="selectProductOption"
              @row-del="productRowDel"  :summary-method="summaryMethod"
              :data="selectProductList"
            >
              <template #menu-left>
                <!-- <el-button type="primary" icon="plus" @click="addProduct">添加产品</el-button> -->
              </template>
              <template #number="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  size="small"
                  style="width: 80%"
                  v-model="row.number"
                ></el-input-number>
                
              </template>
               <template #realCostPrice="{ row }">
                <el-input-number
                  @change="setTotalAmount"
                  :min="0"
                  controls-position="right"
                  :size="size"
                  style="width: 80%"
                  v-model="row.realCostPrice"
                ></el-input-number>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20">
        <el-col :span="24">
          <el-divider content-position="left"
            ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
              >发票信息</span
            ></el-divider
          >
          <el-card shadow="never" class="box-card" style="height: 100%">
            <avue-form :option="formOption" ref="addFormRef" @submit="submit" v-model="form">
            </avue-form>
          </el-card>
        </el-col>
      </el-row>

      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="$refs.addFormRef.submit()">确认 收票</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 编辑产品对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑产品"
      class="avue-dialog avue-dialog--top"
      width="500px"
      :before-close="handleEditDialogClose"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="100px"
      >
        <el-form-item label="产品名称">
          <el-input v-model="editForm.productName" disabled />
        </el-form-item>
        <el-form-item label="规格型号">
          <el-input v-model="editForm.productSpecification" disabled />
        </el-form-item>
        <el-form-item label="单位">
          <el-input v-model="editForm.unitName" disabled />
        </el-form-item>
        <el-form-item label="数量" prop="number">
          <el-input-number
            v-model="editForm.number"
            :min="0"
            disabled
            :precision="2"
            style="width: 100%"
            @change="calculateTotalPrice"
          />
        </el-form-item>
        <el-form-item label="单价" prop="unitPrice">
          <el-input-number
            v-model="editForm.unitPrice"
            :min="0"
            :precision="2"
            style="width: 100%"
            @change="calculateTotalPrice"
          />
        </el-form-item>
        <el-form-item label="发票单价" prop="realCostPrice">
          <el-input-number
            v-model="editForm.realCostPrice"
            :min="0"
            :precision="2"
            style="width: 100%"
           
          />
        </el-form-item>
        <el-form-item label="金额">
          <el-input v-model="editForm.totalPrice" disabled />
        </el-form-item>
      </el-form>
       <div class="avue-dialog__footer">
        <span class="dialog-footer">
          <el-button @click="handleEditDialogClose">取消</el-button>
          <el-button type="primary" @click="handleEditConfirm">确认</el-button>
        </span>
     </div>
    </el-dialog>

    <div class="bottom_box" :class="{ active: selectList && selectList.length > 0 }">
      <div class="pagenation_box"></div>
      <div class="confirm_box" style="display: flex">
        <div class="confirm_box" style="display: flex; align-items: center">
          <el-text
            >已选<el-button
              @click="selectListDrawer = true"
              text
              size="small"
              style="font-size: 20px"
              type="primary"
              >{{ selectList.length }}</el-button
            >项</el-text
          ><el-text
            type="primary"
            style="cursor: pointer"
            @click="
              selectList = [];
              setChecked();
            "
            >清空</el-text
          >
        </div>

        <el-button style="margin-left: 5px" type="primary" @click="receview">收票</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue';
import { useStore } from 'vuex';
import { dateFormat } from '@/utils/date';
import axios from 'axios';
let store = useStore();
const props = defineProps({
  // 父组件传递过来的值
  productList: Array,
  contractId:String,
  supplierId:String
});
const emits = defineEmits(['success'])
const { proxy } = getCurrentInstance();
const productListBf = computed(() => {
  return props.productList.map(item => {
    return {
      ...item.productVO,
      ...item,
    };
  });
});
let permission = computed(() => store.getters.permission);
function selectable(row) {
  return row.isInvoice == 0 || row.isInvoice == 2;
}

// 编辑产品相关方法
function editProduct(row) {
  editForm.value = {
    id: row.id,
    productName: row.productName,
    productSpecification: row.productSpecification,
    unitName: row.unitName,
    number: row.number * 1,
    unitPrice: row.unitPrice * 1,
    realCostPrice: row.realCostPrice * 1,
    totalPrice: row.totalPrice * 1
  };
  calculateTotalPrice();
  editDialogVisible.value = true;
}

function calculateTotalPrice() {
  if (editForm.value.number && editForm.value.unitPrice) {
    editForm.value.totalPrice = (editForm.value.number * editForm.value.unitPrice).toFixed(2);
  } else {
    editForm.value.totalPrice = '0.00';
  }
}

function handleEditDialogClose() {
  editDialogVisible.value = false;
  editFormRef.value?.resetFields();
}

function handleEditConfirm() {
  editFormRef.value?.validate((valid) => {
    if (valid) {
      // 调用编辑接口更新产品信息
      const updateData = {
        id: editForm.value.id,
        number: editForm.value.number,
        unitPrice: editForm.value.unitPrice,
        totalPrice: editForm.value.totalPrice,
        realCostPrice:editForm.value.realCostPrice
      };

      axios.post('/api/vt-admin/purchaseContractDetail/update', updateData)
        .then(res => {
          if (res.data.code === 200) {
            proxy.$message.success('产品信息更新成功');
            handleEditDialogClose();
            emits('success'); // 通知父组件刷新数据
          } else {
            proxy.$message.error(res.data.msg || '更新失败');
          }
        })
        .catch(error => {
          console.error('更新产品信息失败:', error);
          proxy.$message.error('更新失败，请稍后重试');
        });
    }
  });
}
let selectList = ref([]);
let form = ref({});

// 编辑产品相关变量
let editDialogVisible = ref(false);
let editForm = ref({
  id: '',
  productName: '',
  productSpecification: '',
  unitName: '',
  number: 0,
  unitPrice: 0,
  realCostPrice: 0,
  totalPrice: 0
});
let editFormRef = ref(null);
let editRules = ref({
  number: [
    { required: true, message: '请输入数量', trigger: 'blur' },
    { type: 'number', min: 0, message: '数量不能小于0', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '单价不能小于0', trigger: 'blur' }
  ],
  realCostPrice: [
    { required: true, message: '请输入发票单价', trigger: 'blur' },
    { type: 'number', min: 0, message: '发票单价不能小于0', trigger: 'blur' }
  ]
});
function handleSelectionChange(list) {
  selectList.value = list
}
function handleSelectChange(e, row, item) {
  if (e) {
    selectList.value.push({
      ...row,
      supplierName: item.supplierName,
      supplierId: item.supplierId,
    });
  } else {
    selectList.value = selectList.value.filter(item => item.id !== row.id);
  }
}
let selectListDrawer = ref(false);
function productRowDelSelect(row, done) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectList.value = selectList.value.filter(item => item.id !== row.id);
      setChecked();
    })
    .catch(() => {});
}
//   收票申请
let drawer = ref(false);

let selectProductList = ref([]);
let formOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '上传发票',
      prop: 'invoiceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      width: 120,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },

      tip: '先上传发票可以自动填充上面部分类容',
      uploadAfter: (res, done) => {
        console.log(res);
        done();
        const { id } = res;
        axios
          .get('/api/vt-admin/purchaseContractInvoiceDetail/analysisInvoice', {
            params: {
              id,
            },
          })
          .then(res => {
            const {
              date: invoiceDate,
              buyerName: supplierName,
              totalAmount: invoicePrice,
              number: invoiceNumber,
            } = res.data.data;
            const list = res.data.data.detailList.map(item => {
              const {
                totalPrice,
                amount: productTotalPrice,
                taxAmount: taxPrice,
                taxRate,
                count: number,
                name: productName,
                model: specification,
                unit: unitName,
                price: price,
              } = item;
              return {
                totalPrice,
                productTotalPrice,
                taxPrice,
                taxRate,
                number,
                productName,
                specification,
                unitName,
                price,
                disabled: true,
              };
            });

            if (form.value.detailEntityList) {
              form.value.detailEntityList.push(...list);
            } else {
              form.value.detailEntityList = list;
            }
            form.value.invoiceDate = invoiceDate;

            form.value.invoicePrice = invoicePrice;
            form.value.invoiceNumber = invoiceNumber;
          });
      },
      uploadPreview: file => {
        return;
      },

      action: '/blade-resource/attach/upload',
    },
    {
      type: 'input',
      label: '供应商名称',
      span: 12,
      width: 120,

      placeholder: '',
      prop: 'supplierName',
      search: true,
      disabled: true,
      overHidden: true,
    },

    {
      type: 'date',
      label: '登记日期',
      span: 12,
      width: 120,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      prop: 'createTimeBf',
      formatter(row, column, cellValue, index) {
        return dateFormat(new Date(row.createTime), 'yyyy-MM-dd');
      },
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      display: true,
      width: 120,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },
    {
      type: 'input',
      label: '发票号码',
      span: 12,
      display: true,
      width: 180,
      overHidden: true,
      prop: 'invoiceNumber',
    },
    // {
    //   type: 'textarea',
    //   label: '发票内容',
    //   span: 24,
    //   overHidden: true,
    //   display: true,
    //   prop: 'invoiceContent',
    // },

    {
      type: 'input',
      label: '发票总额',
      span: 12,
      overHidden: true,
      display: true,
      width: 100,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '发票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      search: true,
      width: 120,
      display: true,
      overHidden: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [
        {
          required: true,
          message: '请选择发票类型',
        },
      ],
    },
    
    {
      type: 'input',
      labelPosition: 'left',
      label: '明细信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'detailEntityList',
      type: 'dynamic',
      rules: [
        {
          required: true,
          message: '请输入明细信息',
        },
      ],
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width: 80,
          },
          {
            label: '数量',
            prop: 'number',
            width: 80,
            change: (a, b, c) => {
              const { row } = a;
              if (row.productTotalPrice && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '单价',
            prop: 'price',
            width: 100,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.number) {
              //   row.productTotalPrice = (row.number * 1 * row.price * 1).toFixed(2);
              // }
            },
          },
          {
            label: '金额',
            prop: 'productTotalPrice',
            width: 120,
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.taxRate) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.number && !row.disabled) {
                row.price = ((row.productTotalPrice * 1) / row.number) * 1;
              }
            },
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width: 100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
            change: (a, b, c) => {
              // const { row } = a;
              // if (row.productTotalPrice) {
              //   row.taxPrice = ((row.productTotalPrice * 1 * (row.taxRate * 1)) / 100).toFixed(2);

              //   row.totalPrice = (row.productTotalPrice * 1 + row.taxPrice * 1).toFixed(2);
              // }
              const { row } = a;
              if (row.totalPrice && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width: 120,
          },
          {
            label: '小计',
            prop: 'totalPrice',
            width: 120,
            change: a => {
              form.value.invoicePrice = form.value.detailEntityList
                .reduce((pre, cur) => {
                  return (pre += cur.totalPrice * 1);
                }, 0)
                .toFixed(2);
              const { row } = a;
              if (row.taxRate && !row.disabled) {
                row.productTotalPrice = (row.totalPrice / (1 + (row.taxRate * 1) / 100)).toFixed(2);

                row.taxPrice = (row.totalPrice - row.productTotalPrice).toFixed(2);
              }
            },
          },
        ],
      },
    },

    {
      type: 'textarea',
      label: '备注',
      overHidden: true,
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});

let selectProductOption = ref({
  header: true,
  menu: true,
  editBtn: false,
  viewBtn: false,
  addBtn: false,
  header: false,
  showSummary: true,
  //   sumColumnList: [{ name: 'totalPrice', type: 'sum' }],
  menuWidth: 100,
  border: true,
  selection: false,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      bind: 'product.productName',
      cell: false,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      bind: 'product.productBrand',
      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      bind: 'product.productSpecification',
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 150,
      span: 12,
      cell: true,
    },
    {
      label: '单位',
      prop: 'unitName',
      bind: 'product.unitName',
      span: 12,
      width: 80,
    },
    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',
      span: 12,
      cell: false,
      width: 80,
    },
     {
      label: '发票单价',
      prop: 'realCostPrice',
      type: 'number',
      span: 12,
      cell: true,
      width: 150,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      width: 100,
      cell: false,
      formatter: row => {
        return (row.number * row.realCostPrice).toFixed(2);
      },
    },
  ],
});

function receview() {
  const bol = [...new Set(selectList.value.map(item => item.supplierId))].length > 1;

  if (bol) {
    proxy.$message.warning('请选择同一个供应商');
    return;
  }

  drawer.value = true;
  proxy.$nextTick(() => {
    clearAll();

    selectProductList.value.push(
      ...selectList.value.map(i => {
        return {
          ...i,
          number: i.number * 1 - i.invoiceNumber * 1,
          product: {
            ...i.productVO,
          },
        };
      })
    );

    setTotalAmount();
    form.value.supplierId = selectProductList.value[0].supplierId;
    form.value.supplierName = selectProductList.value[0].supplierName;
  });
}

function productRowDel(row) {
  proxy
    .$confirm('确认删除此产品吗?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      selectProductList.value = selectProductList.value.filter(item => item.id !== row.id);
      setTotalAmount();
      setChecked();
    })
    .catch(() => {});
}
function setTotalAmount() {
  const totalPrice = selectProductList.value.reduce(
    (total, item) => (total += item.number * 1 * (item.unitPrice * 1)),
    0
  );
  form.value.invoicePrice = totalPrice.toFixed(2);
}
function submit(form, done, loading) {
  if (selectProductList.value.length == 0) {
    proxy.$message.error('请选择产品');
    done();
    return;
  }
  form.productDTOList = selectProductList.value.map(item => {
    return {
      purchaseContractDetailId: item.id,
      ...item,
      id: null,
    };
  });
  form.purchaseContractId = props.contractId
  form.supplierId = props.supplierId
  form.createTime = null;
  form.invoiceFiles = form.invoiceFiles.map(item => item.value).join(',');

  axios.post('/api/vt-admin/purchaseContractInvoice/save', form).then(res => {
    if (res.data.code == 200) {
      proxy.$message.success(res.data.msg);
      done();
      drawer.value = false;
      clearAll();
      emits('success')
    }
  });
}
function clearAll() {
  proxy.$refs.addFormRef.resetForm();
  selectProductList.value = [];
  form.value.createName = proxy.$store.getters.userInfo.nick_name;
  form.value.createTime = dateFormat(new Date(), 'yyyy-MM-dd');
}
function rowStyle({row,index}) {
    if(row.purchaseType == 1){
      return {
        background: 'var(--el-color-primary-light-9)'
      }
    }
}
const summaryMethod = ({ columns, data }) => {
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '合计';
      return;
    }
    if (column.property === 'totalPrice') {
      const values = data.map(item => {
        const num = Number(item.number) || 0;
        const price = Number(item.realCostPrice) || 0;
        return num * price;
      });
      sums[index] = values.reduce((a, b) => a + b, 0).toFixed(2);
    } else {
      sums[index] = '';
    }
  });
  return sums;
};
</script>

<style lang="scss" scoped>
.bottom_box {
  height: 0px;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  position: fixed;
  z-index: 2000;
  bottom: 0;
  left: 0;
  transition: height 0.2s linear;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom_box.active {
  height: 60px;
}
</style>
