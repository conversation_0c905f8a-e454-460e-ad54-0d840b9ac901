<template>
  <div class="publicity">
    <el-container class="p_container">
      <el-aside class="p_aside" style="width: 250px">
        <!-- <div class="p_aside_tree">
          <avue-tree
            :loading="treeLoading"
            :permission="getPermission"
            :option="treeOption"
            :data="treeData"
            @node-click="nodeClick"
            @update="treeUpdate"
            @save="treeSave"
            @del="treeDel"
            v-model="treeForm"
          >
          </avue-tree>
        </div> -->
        <div class="p_aside_tree">
          <div style="display: flex; justify-content: space-between; align-items: center">
            <el-input
              placeholder="输入关键字进行过滤"
              v-model="filterText"
              style="margin-bottom: 5px"
            >
            </el-input>
            <el-dropdown @command="addCommand" trigger="click">
              <el-button type="primary" size="small" icon="more" text></el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item
                    command="add"
                    v-if="$store.getters.permission['repository:add'] || true"
                    >新增</el-dropdown-item
                  >
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          <el-tree
            :data="treeData"
            :props="{
              label: 'categoryName',
              children: 'children',
            }"
            v-modal="false"
            ref="tree"
            node-key="id"
            :expand-on-click-node="false"
            @node-click="nodeClick"
            :filter-node-method="filterNode"
          >
            <template #default="{ node, data }">
              <span
                class="custom-tree-node"
                slot-scope="{ node, data }"
                @mouseenter="mouseenter(data)"
                @mouseleave="mouseleave(data)"
              >
                <span @mouseenter="mouseTootip($event)" style="font-size: 14px">{{
                  node.label
                }}</span>
                <span class="right_action">
                  <el-dropdown
                    @command="
                      item => {
                        command(item, data);
                      }
                    "
                    trigger="click"
                  >
                    <el-button type="primary" size="small" icon="more" text></el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item
                          command="add"
                          v-if="$store.getters.permission['repository:add'] || true"
                          >新增</el-dropdown-item
                        >
                        <el-dropdown-item
                          command="edit"
                          v-if="$store.getters.permission['repository:edit'] || true"
                          >编辑</el-dropdown-item
                        >
                        <el-dropdown-item
                          command="delete"
                          v-if="$store.getters.permission['repository:delete'] || true"
                          >删除</el-dropdown-item
                        >
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </span>
              </span>
            </template>
          </el-tree>
          <el-button
            type="primary"
            @click="addCommand()"
            size="small"
            style="width: 100%; margin-top: 10px"
            plain
            v-if="$store.getters.permission['repository:add'] || true"
            icon="plus"
          ></el-button>
        </div>
      </el-aside>
      <el-container class="p_container_right">
        <avue-crud
          :option="option"
          :data="data"
          v-model:page="page"
          ref="crud"
          v-model:search="query"
          @row-del="rowDel"
          v-model="form"
          :table-loading="loading"
          :permission="permissionList"
          @row-update="rowUpdate"
          @row-save="rowSave"
          @refresh-change="onLoadFile"
          @search-change="searchChange"
          @search-reset="searchReset"
          :upload-exceed="uploadExceed"
          :upload-after="uploadAfter"
          :before-open="beforeOpenDialog"
          @size-change="onLoadFile"
          @current-change="onLoadFile"
          :upload-before="uploadBefore"
          @selection-change="selectionChange"
        >
          <template #menu-left>
            <!-- <el-button type="primary"  @click="downLoadFile()">打包下载</el-button> -->
          </template>
          <template #size="{ row }">
            <span>{{ sizeFilter(row.size) }}</span>
          </template>
          <template #menu="{ row }">
            <!-- <el-button
              icon="folder"
             text
              type="primary"
              @click="onViewFile(row)"
              >浏览文件</el-button
            > -->
            <!-- <el-button
              icon="download"
              text
              type="primary"
              v-if="$store.getters.permission['repository:download']"
              @click="onDownloadFile(row)"
              >下载
            </el-button>
            <el-button icon="download" text type="primary" @click="preview(row)">预览 </el-button> -->
            <el-button
              icon="upload"
              text
              type="primary"
              v-if="row.isPush == 0"
              @click="push(row, 1)"
              >推送</el-button
            >
            <el-button
              icon="download"
              text
              type="primary"
              v-if="row.isPush == 1"
              @click="push(row, 0)"
              >取消推送</el-button
            >
            <!-- <el-button
              icon="delete"
              text
              type="primary"
              v-if="$store.getters.permission['repository:delete'] || true"
              @click="$refs.crud.rowDel(row)"
              >删除
            </el-button> -->
          </template>
          <template #fileIds="{ row }">
            <File :file-list="row.attachList"></File>
          </template>
          <template #keywordValues="{ row }">
            <div>
              <el-tag effect='plain'
                style="margin-right: 5px; margin-bottom: 5px"
                size="mini"
                v-for="item in row.keywordValues && row.keywordValues.split('；').join(';').split(';')"
                type="primary"
                >{{ item }}</el-tag
              >
            </div>
          </template>
          <template #keywordValues-search="{ disabled, size }">
            <el-input v-model="query.keywordValues" placeholder="请输入关键词" clearable />
          </template>
        </avue-crud>
      </el-container>
    </el-container>
    <el-dialog
      :destroy-on-close="true"
      append-to-body
      :title="title"
      v-model="dialogVisible"
      class="avue-dialog avue-dialog--top"
    >
      <el-form :model="treeItem" ref="form" :rules="rules">
        <el-form-item label="分类名" prop="categoryName" label-width="80px">
          <el-input placeholder="请输入分类名" v-model="treeItem.categoryName"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="avue-dialog__footer">
        <el-button size="small" @click="dialogVisible = false">取 消</el-button>
        <el-button size="small" type="primary" @click="handleSubmit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { loadFile } from '@/utils/file';
import { Base64, encode } from 'js-base64';
import { downloadOwn } from '@/utils/util';
import template from './../CRM/programme/compoents/template.vue';
import config from '@/config/website';
export default {
  name: 'repository',

  components: { template },
  data() {
    return {
      isShowTooltip: false,
      // 分页
      page: {
        total: 0,
        currentPage: 1,
        pageSize: 10,
      },
      query: {},
      form: {},
      filterText: '',
      loading: false,
      publishKnowledgeId: null,
      data: [],
      imgItem: {},
      treeData: [],
      treeForm: {},
      treeLoading: false,
      dialogVisible: false,

      title: '新增分类',
      treeItem: {
        categoryName: null,
        parentId: 0,
        path: '',
        remarks: '',
        type: 0,
      },
      rules: {
        categoryName: [{ required: true, message: '请输入分类名称', trigger: 'blur' }],
      },
      currentNode: '',
      treeOption: {
        title: '新增文件夹',
        filterText: '输入搜索关键字',
        defaultExpandAll: true,
        indexFixed: false,
        selectionFixed: false,
        menuFixed: false,
        formOption: {
          labelWidth: 100,
          column: [
            {
              label: '文件名',
              prop: 'name',
              id: 'id',
              rules: [
                {
                  required: true,
                  message: '请输入用户名',
                  trigger: 'blur',
                },
              ],
            },
          ],
        },
        props: {
          label: 'name',
          children: 'childrens',
        },
      },
      selectList: [],
    };
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  computed: {
    ...mapGetters(['permission']),
    permissionList() {
      return {
        addBtn: !!this.$store.getters.permission['repository:add'] || true,
        editBtn: !!this.permission['repository:edit'] || true,
        delBtn: !!this.permission['repository:del'] || true,
      };
    },
    option() {
      var validatorPath = (rule, value, callback) => {
        // console.log(this.form.path);
        if (!this.form.path) {
          callback('请上传附件');
        } else {
          callback();
        }
      };
      return {
        menuWidth: 300,
        height: 'auto',
        calcHeight: 30,
        dialogWidth: '40%',
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: true,
        delBtn: true,
        excelBtn: false,
        columnBtn: true,
        selection: false,
        reserveSelection: true,
        dialogClickModal: true,
        indexFixed: false,
        selectionFixed: false,
        menuFixed: false,
        labelWidth: 120,
        column: [
          {
            label: '复盘主题',
            prop: 'reviewName',
            search: true,
            searchLabelWidth: 120,
            span: 24,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入文件名称",
            //     trigger: "change",
            //   },
            // ],
            overHidden: true,
          },
          {
            label: '关键词',
            prop: 'keywordValues',
            search: true,
            searchLabelWidth: 120,
            span: 24,
            dataType: 'input',
     
            placeholder:'请输入关键词，用中文分号隔开',
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入文件名称",
            //     trigger: "change",
            //   },
            // ],
           
            // overHidden: true,
          },

          {
            label: '复盘描述',
            prop: 'reviewDescription',
            type: 'textarea',
            span: 24,
            // rules: [
            //   {
            //     required: true,
            //     message: "请输入文件名称",
            //     trigger: "change",
            //   },
            // ],
            overHidden: true,
          },
          {
            label: '复盘日期',
            prop: 'reviewDate',
            type: 'date',
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            overHidden: true,
            width: 120,
            span: 24,
            align: 'center',
          },
          {
            label: '复盘附件',
            prop: 'fileIds',
            type: 'upload',
            span: 24,
            dragFile: true,

            value: [],
            dataType: 'object',
            // rules: [
            //   {
            //     required: true,
            //     validator: validatorPath,
            //     trigger: "change",
            //   },
            // ],
            formatter: row => {
              return row.attachList;
            },
            propsHttp: {
              res: 'data',
              url: 'id',
              name: 'originalName',
            },
            // tip: "只能上传图片和PDF 文件不超过100M",
            // tip: '文件不超过100M',
            action: '/api/blade-resource/attach/upload',
            // hide: true,
            viewDisplay: false,
          },
          {
            label: '创建者',
            prop: 'createName',
            width: 120,
            align: 'center',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入创建人',
                trigger: 'blur',
              },
            ],
          },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'date',
            format: 'YYYY-MM-DD HH:mm',
            valueFormat: 'YYYY-MM-DD HH:mm',
            addDisplay: false,
            editDisplay: false,
            rules: [
              {
                required: true,
                message: '请输入修改时间',
                trigger: 'blur',
              },
            ],
            width: 150,
            align: 'center',
          },
        ],
      };
    },
  },

  mounted() {
    this.onLoadTree().then(() => {
      if (this.$route.params.id) {
        this.$refs.tree.setCurrentKey(this.$route.params.id);
      }
    });
    // 加载列表
    this.onLoadFile();
  },

  methods: {
    getPermission(key, data) {
      if (key == 'addBtn' && data.value == 0) {
        return false;
      }
      return true;
    },
    filterNode(value, data) {
      // console.log(value, data, "-------------");
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    onLoadTree() {
      return new Promise(resolve => {
        this.treeLoading = true;
        axios.get('/api/vt-admin/reviewCategory/tree').then(resp => {
          let d = resp.data.data;
          this.treeData = d;
          this.treeLoading = false;
          resolve();
        });
      });
    },

    treeDel(data, done) {
      this.$confirm('此操作将永久删除该文件, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios.post('/api/vt-admin/reviewCategory/remove?ids=' + this.currentNode.id).then(resp => {
          this.onLoadTree();
          this.$message.success(resp.data.msg);
        });
      });
    },

    treeUpdate(node, data, done, loading) {
      let p = {};
      p.id = this.currentNode.id;
      p.categoryName = this.treeItem.categoryName;
      p.parentId = this.currentNode.parentId;
      axios.post('/api/vt-admin/reviewCategory/update', p).then(resp => {
        this.onLoadTree();
        this.$message.success(resp.data.msg);

        this.dialogVisible = false;
      });
    },

    treeSave(node, data, done, loading) {
      if (this.currentNode) this.treeItem.parentId = this.currentNode.id;
      else this.treeItem.parentId = 0;
      // this.treeItem.name = data.name;
      axios.post('/api/vt-admin/reviewCategory/save', this.treeItem).then(resp => {
        this.onLoadTree();
        this.$message.success(resp.data.msg);

        this.dialogVisible = false;
      });
    },

    nodeClick(data) {
      this.publishKnowledgeId = data.id;
      this.currentNode = data;
      this.onLoadFile();
    },

    uploadExceed() {
      this.$message.warning('请先移除原有文件！');
    },

    onLoadFile() {
      this.loading = true;
      // 列表数据
      console.log(this.currentNode);
      axios
        .get('/api/vt-admin/review/page', {
          params: {
            reviewType: this.currentNode.id,
            ...this.query,
            size: this.page.pageSize,
            current: this.page.currentPage,
          },
        })
        .then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.loading = false;

            this.data = data.data.records;
            this.page['total'] = data.data.total;
          }
        });
    },

    rowSave(row, done, loading) {
      console.log(row);
      if (!this.currentNode) {
        this.$message.warning('请选择分类！');
        return;
      }
      row.fileIds = row.fileIds.map(item => item.value).join(',');
      row.reviewType = this.currentNode.id;
     
      axios.post('/api/vt-admin/review/save', row).then(resp => {
        this.onLoadFile();
        this.$message.success(resp.data.msg);
        loading();
        done();
      });
    },

    rowUpdate(row, index, done, loading) {
      row.fileIds = row.fileIds.map(item => item.value).join(',');

      
      axios.post('/api/vt-admin/review/update', row).then(resp => {
        this.imgItem = {};
        this.onLoadFile();
        this.$message.success(resp.data.msg);
        loading();
        done();
      });
    },
    push(row, type) {
      axios
        .post('/api/vt-admin/review/push', {
          ...row,
          isPush: type,
        })
        .then(res => {
          this.$message.success(res.data.msg);
          this.onLoadFile();
        });
    },
    uploadAfter(res, done) {
      this.form.type = res.type;
      this.form.size = res.size;
      this.form.name = res.originalName;
      done();
    },

    rowDel(row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        axios.post('/api/vt-admin/review/remove?ids=' + row.id).then(resp => {
          this.onLoadFile();
          this.$message.success(resp.data.msg);
        });
      });
    },

    searchReset() {
      this.onLoadFile();
    },

    searchChange(params, done) {
      this.onLoadFile(params.name);
      done();
    },
    // 预览文件
    onViewFile(e) {
      this.$file.previewFile(e.path, '', '预览');
    },
    // 下载文件
    onDownloadFile(e) {
      if (e.onlineUrl) {
        axios
          .post('/api/vt-admin/publishKnowledgeFile/addDownloadNumber?id=' + e.id, {})
          .then(res => {
            window.open(e.onlineUrl);
            e.downloadNumber++;
          });
      } else {
        axios
          .post('/api/vt-admin/publishKnowledgeFile/addDownloadNumber?id=' + e.id, {})
          .then(res => {
            loadFile(e.path, e.name);
            e.downloadNumber++;
          });
      }
    },
    // 预览
    preview(e) {
      if (!e['path'] && !e.onlineUrl) {
        return this.$message.error('未发现文件');
      }
      window.open(
        config.previewFileUrl +
          'onlinePreview?url=' +
          encodeURIComponent(encode(e.onlineUrl || e.path))
      );
    },
    handleNodeClick(data) {
      this.publishKnowledgeId = data.id;
      this.removeTreeId = data.id;

      console.log(data);
      this.onLoadFile();
    },
    // 鼠标移入判断长短
    mouseTootip(event) {
      const ev = event.target;
      const ev_weight = ev.scrollWidth; // 文本的实际宽度   scrollWidth：对象的实际内容的宽度，不包边线宽度，会随对象中内容超过可视区后而变大。
      const content_weight = ev.clientWidth; // 文本的可视宽度 clientWidth：对象内容的可视区的宽度，不包滚动条等边线，会随对象显示大小的变化而改变。
      // const content_weight = this.$refs.tlp.$el.parentNode.clientWidth; // 文本容器宽度(父节点)
      if (ev_weight > content_weight) {
        // 实际宽度 > 可视宽度  文字溢出
        this.isShowTooltip = false;
      } else {
        // 否则为不溢出
        this.isShowTooltip = true;
      }
    },
    mouseenter(data) {
      // console.log(data);
      this.$set(data, 'show', true);
    },
    mouseleave(data) {
      this.$set(data, 'show', false);
    },
    command(item, node) {
      this.currentNode = node;
      if (item == 'add') {
        this.dialogVisible = true;
        this.treeItem.categoryName = '';
        this.title = '新增分类';
      } else if (item == 'edit') {
        // console.log(node);
        this.title = '修改分类';
        this.dialogVisible = true;

        this.treeItem.categoryName = node.categoryName;
      } else {
        // console.log(111);
        this.treeDel();
      }
    },
    handleSubmit() {
      // console.log(this.$refs);
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.title == '修改分类') {
            this.treeUpdate();
          } else {
            this.treeSave();
          }
        }
      });
    },
    addCommand() {
      this.currentNode = '';
      this.dialogVisible = true;
      this.treeItem = {
        ...this.treeItem,
        name: '',
      };
      this.title = '新增分类';
    },
    // 弹窗打开之前
    beforeOpenDialog(done, type) {
      console.log(this.form);
      if (!['view', 'edit'].includes(type)) {
        // 新增检验
        if (!this.publishKnowledgeId) {
          return this.$message.error('请先在左边选择分类');
        }
      } else {
        if (this.form.fileIds) {
          this.form.fileIds = this.form.attachList.map(item => {
            return {
              value: item.id,
              label: item.originalName,
            };
          });
        } else {
          this.form.fileIds = [];
        }
        
      }
      done();
    },
    // 图片上传前
    uploadBefore(file, done, loading) {
      let { type, size } = file;
      let isFile = true;
      let max = false;
      // 2023 03 16 放开限制
      // if (
      //   type == "application/pdf" ||
      //   type == "image/jpeg" ||
      //   type == "image/png" ||
      //   type == "image/jpg"
      // ) {
      //   isFile = true;
      // }
      if (size / 1024 / 1024 < 100) {
        max = true;
      }

      if (isFile && max) {
        done();
      } else {
        loading();
        this.$message.warning('只能上传图片和PDF 文件不超过100M');
      }
    },
    findNode(tree, func) {
      for (const node of tree) {
        if (func(node)) return node;
        if (node.childrens) {
          const res = this.findNode(node.childrens, func);
          if (res) return res;
        }
      }
      return null;
    },
    selectionChange(list) {
      console.log(list);
      this.selectList = list;
    },
    downLoadFile() {
      if (this.selectList.length == 0) {
        this.$message.warning('请至少选择一条数据');
        return;
      }
      axios
        .get(
          '/api/vt-admin/publishKnowledgeFile/download',
          {
            params: {
              ids: this.selectList.map(item => item.id).join(','),
            },
          },
          {
            responseType: 'blob',
          }
        )
        .then(res => {
          let name = decodeURI(res.headers['content-disposition']).split('=');
          downloadOwn(res.data, name[1], 'zip');
          this.$refs.crud.toggleSelection();
        });
    },
    sizeFilter(size) {
      if (size) {
        size *= 1000;
        if (size < 0.1 * 1024) {
          //小于0.1KB，则转化成B
          size = size.toFixed(2) + 'B';
        } else if (size < 0.1 * 1024 * 1024) {
          // 小于0.1MB，则转化成KB
          size = (size / 1024).toFixed(2) + 'KB';
        } else if (size < 0.1 * 1024 * 1024 * 1024) {
          // 小于0.1GB，则转化成MB
          size = (size / (1024 * 1024)).toFixed(2) + 'MB';
        } else {
          // 其他转化成GB
          size = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
        }
        // 转成字符串
        let sizeStr = size + '',
          // 获取小数点处的索引
          index = sizeStr.indexOf('.'),
          // 获取小数点后两位的值
          dou = sizeStr.substr(index + 1, 2);

        // 判断后两位是否为00，如果是则删除00
        if (dou == '00') return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);

        return size;
      }
      return '0KB';
    },
  },
};
</script>

<style lang="scss" scoped>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .item {
    display: inline-block;
    width: calc(100% - 20px);
  }
}
.right_action {
  position: absolute;
  right: -15px;
  background: #fff;
}
// ::v-deep .el-tree {
//   overflow: auto;
//   scrollbar-width: 4px; /* firefox */
//   -ms-overflow-style: none; /* IE 10+ */
// }
// ::v-deep .el-tree::-webkit-scrollbar {
//   height: 4px;
// }
.publicity {
  height: calc(100% - 11px);
  margin-top: 14px;
  margin-left: 5px;
  // padding: 0 16px 16px 16px !important;

  .p_container {
    height: 100%;
    overflow: hidden auto;
    border: 1px solid #eee;
    padding-bottom: 10px;

    .p_aside {
      height: 100%;
      background-color: #ffffff;
      border-radius: 5px;
      margin-right: 16px;
      padding: 16px;
      overflow: auto;
      .p_aside_tree {
        // margin-top: 16px;
      }
    }

    .p_container_right {
      height: 100%;
      border-radius: 5px;
      background-color: #ffffff;
      padding: 16px;
      margin-right: 5px;

      .p_header {
        background-color: #ffffff;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding: 0 !important;

        // /deep/ .el-tabs__header {
        //   padding: 16px 0;
        //   margin: 0 !important;
        // }
      }

      .p_main {
        background-color: #ffffff;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;

        .p_pagination {
          text-align: right;
          margin-top: 32px;
        }
      }
    }
  }
}
</style>
