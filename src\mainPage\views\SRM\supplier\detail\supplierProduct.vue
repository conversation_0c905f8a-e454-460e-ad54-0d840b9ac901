<template>
  <div>
    <!-- <el-form>
      <el-form-item label="">
        <el-button type="primary" size="normal" icon="plus" @click="dialogVisible = true">
          新增
        </el-button>
      </el-form-item>
    </el-form>
    <el-table :data="productListBf" border align="center">
      <el-table-column label="设备名称" prop="productName" align="center"></el-table-column>
      <el-table-column
        label="规格型号"
        prop="productSpecification"
        align="center"
      ></el-table-column>
      <el-table-column label="产品图片" #default="{ row }" align="center">
        <el-image
          style="width: 80px"
          :preview-src-list="[row.coverUrl]"
          :src="row.coverUrl"
        ></el-image>
      </el-table-column>
      <el-table-column
        align="center"
        label="产品描述"
        show-overflow-tooltip
        width="200"
        prop="description"
      ></el-table-column>
      <el-table-column label="品牌" align="center" prop="productBrand"></el-table-column>
      <el-table-column label="单位" align="center" prop="unitName"></el-table-column>
      <el-table-column label="价格" align="center" #default="{ row }" prop="unitPrice">
        <span>{{ row.unitPrice }}</span>
      </el-table-column>
      <el-table-column label="操作" align="center" #default="{ row }" prop="unitPrice">
        <el-button type="text" icon="edit">编辑</el-button>
      </el-table-column>
    </el-table> -->
    <el-button type="primary" size="normal" icon="plus" @click="handleAdd()"> 新增 </el-button>
    <ProductList :supplierId="props.id" ref="ProductListRef"></ProductList>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      :userUrl="`/api/vt-admin/product/pageForSupplier?supplierId=${props.id}`"
      @onConfirm="handleUserSelectConfirm"
    ></wf-product-select>

    <el-dialog title="关联产品" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <avue-form
        :option="addOption"
        ref="dialogForm"
        v-model="addForm"
        @submit="submit"
      ></avue-form>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import axios from 'axios';
import ProductList from './productList.vue';
import { dateFormat } from '@/utils/date';
const props = defineProps({
  // 父组件传递过来的值
  productList: Array,
  id: String,
});

const { proxy } = getCurrentInstance();
const productListBf = computed(() => {
  return props.productList.map(item => {
    return {
      ...item.productVO,
      ...item,
    };
  });
});
let dialogVisible = ref(false);
let addForm = ref({
  supplierProductDTOList: [],
});
const addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '',
      prop: 'supplierProductDTOList',
      type: 'dynamic',
      span: 24,
      labelWidth: 0,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          addProduct();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品',
            prop: 'productId',
            component: 'wf-product-select',
          },
          {
            label: '报价时间',
            prop: 'offerDate',
            type: 'date',
            span: 12,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
          },
          {
            label: '产品质保期（年）',
            prop: 'warrantyPeriod',
            type: 'number',
            span: 12,
          },
          {
            label: '是否含税',
            prop: 'isHasTax',
            type: 'switch',
            value: 1,
            dicData: [
              {
                value: 0,
                label: '否',
              },
              {
                value: 1,
                label: '是',
              },
            ],
            span: 12,
          },
          {
            label: '税率',
            type: 'select',
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
          },
          {
            label: '单价',
            prop: 'unitPrice',
            type: 'number',
            span: 12,
          },
        ],
      },
    },
  ],
});
function addProduct() {
  proxy.$refs['product-select'].visible = true;
}

function handleUserSelectConfirm(ids) {
  addForm.value = {
    supplierProductDTOList: ids.split(',').map(item => {
      return {
        productId: item,
        isHasTax: 1,
        offerDate: dateFormat(new Date(), 'yyyy-MM-dd'),
      };
    }),
  };
}
function submit(form, done) {
  const data = {
    id: props.id,
    ...form,
  };
  axios.post('/api/vt-admin/supplier/relationProduct', data).then(res => {
    proxy.$message.success('添加成功');
    proxy.$refs.ProductListRef.onLoad();
    done();
    form.value = {};
    dialogVisible.value = false;
  });
}

function handleAdd() {
  dialogVisible.value = true;
  addForm.value.supplierProductDTOList = [];
}
</script>

<style lang="scss" scoped></style>
