<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @selection-change="selectionChange"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex">
          <el-button type="primary"  @click="allPay" v-if="permission.payment_pay"
            > <el-icon size="22" style="margin-right: 5px;transform:rotate(180deg);color: var(--el-color-danger);font-weight: bolder;"><Pointer /></el-icon> 合并付款</el-button
          >
          <div style="display: flex; align-items: center; gap: 20px">
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (contractTotalPrice * 1).toLocaleString() }}</el-text
            >
            <span style="font-weight: bolder">已付款总额：</span>
            <el-text type="primary" size="large"
              >￥{{ (hasPaymentPrice * 1).toLocaleString() }}</el-text
            >
            <span style="font-weight: bolder">未付款总额：</span>
            <el-text type="primary" size="large"
              >￥{{ ((contractTotalPrice - hasPaymentPrice) * 1).toLocaleString() }}</el-text
            >
          </div>
        </div>
        <!-- <el-text size="large" style="font-weight: bolder; margin-left: 10px">计划付款总额：</el-text> -->
        <!-- <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text> -->
        <!-- <el-text size="large" style="font-weight: bolder; margin-left: 10px"
          >实际付款总额：</el-text
        >
        <el-text type="primary" size="large">￥{{ (actualPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">未付款总额：</el-text>
        <el-text type="primary" size="large">￥{{ (noPayPrice * 1).toLocaleString() }}</el-text> -->
      </template>
      <template #purchaseContractId="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.contractCode }}</el-link>
      </template>
      <template #invoiceStatus="{ row }">
      <el-tag
          effect="plain"
          size="large"
            v-if="row.isNeedInvoice == 1"
          :type="row.invoiceStatus == 0 ? 'success' : row.invoiceStatus == 1 ? 'danger' : 'warning'"
          >{{ row.$invoiceStatus }}</el-tag
        >
          <el-tag size="large" v-else type="info">无需开票</el-tag>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          text
          icon="Pointer"
          @click="pay(row)"
          v-if="row.paymentStatus != 1 && permission.payment_pay"
          >付款</el-button
        >
        <!-- <el-button
          type="primary"
          text
          icon="edit"
          v-if="row.paymentStatus == 0"
          @click="$refs.crud.rowEdit(row, $index)"
          >编辑</el-button
        > -->

        <el-button type="primary" text icon="Clock" @click="payListRecord(row)">付款记录</el-button>
        <el-button type="primary" text icon="view" @click="viewDetail(row)" v-if="row.paymentStatus == 1">明细</el-button>
      </template>
      <template #paymentStatus="{ row }">
        <el-tag effect="plain" v-if="row.paymentStatus == 0" type="danger">未付款</el-tag>
        <el-tag effect="plain" v-if="row.paymentStatus == 2" type="warning">部分付款</el-tag>
        <el-tag effect="plain" v-if="row.paymentStatus == 1" type="success">已付款</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="付款记录" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <!-- <slot ></slot> -->
      <payList :purchaseContractId="currentId"></payList>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
      </div>
    </el-dialog>
    <el-drawer title="明细" size="80%" v-model="detailDrawer">
      <ContractProduct :id="currentId"></ContractProduct>
    </el-drawer>
    <el-drawer title="付款" size="80%" v-model="payDrawer">
      <div v-if="payType == 0">
         <ContractProduct :id="currentId"></ContractProduct>
      </div>
      <div v-else>
        <div v-for="item in selectList" :key="item.id">
          <h3>{{ item.contractCode }}-{{ item.purchaseDate.split(' ')[0] }}</h3>
           <ContractProduct :id="item.id"></ContractProduct>
        </div>
      </div>
      <template #footer>
        <div style="border-top: 1px dashed #ccc;">
          <avue-form v-model="payForm" ref="payConfirmRef" :option="payOption" @submit="payConfirm"></avue-form>
        </div>
      </template>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import template from './../../CRM/programme/template.vue';
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfInvoiceDrop from '@/views/SRM/procure/compoents/wf-invoice-drop.vue';
import { dateFormat } from '@/utils/date';
import payList from './compoents/payList.vue';
import { useStore } from 'vuex';
import { ElMessage } from 'element-plus';
import ContractProduct from './contractProduct.vue';
import { Pointer } from '@element-plus/icons-vue';
const props = defineProps({
  contractCode: String,
  supplierName: String,
  id: String,
});
let store = useStore();
let permission = computed(() => store.getters.permission);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  tip:false,
  labelWidth: 120,
  searchLabelWidth: 120,
  searchSpan: 4,
  selection: true,
  reserveSelection: true,
  menuWidth: 250,
  border: true,
  selectable: (row, index) => {
    return row.paymentStatus != 1;
  },
  column: [
    {
      type: 'input',
      label: '合同编号',
      span: 12,
      component: 'wf-purchaseContract-select',
      display: true,

      prop: 'purchaseContractId',
      formatter(row, column, cellValue, index) {
        return row.contractCode;
      },
    },
    {
      type: 'input',
      label: '供应商名称',
      span: 12,
      addDisplay: false,
      editDisplay: false,
      search: true,
      overHidden: true,
      component: 'wf-supplier-drop',
      prop: 'supplierName',
    },
    // {
    //   type: 'input',
    //   label: '关联发票',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'invoiceId',
    // },
    // {
    //   type: 'input',
    //   label: '供应商名称',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'supplierName',
    //   disabled: true
    // },
    // {
    //   type: 'input',
    //   label: '产品状态',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'arriveStatus',
    //   disabled: true
    // },
    // {
    //   type: 'input',
    //   label: '计划名称',
    //   span: 12,
    //   display: true,
    //   prop: 'planName',
    // },
    // {
    //   type: 'date',
    //   label: '计划付款时间',
    //   span: 12,

    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',

    //   component: 'wf-daterange-search',
    //   search: true,
    //   searchSpan: 6,
    //   hide: true,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'planPaymentDateSearch',
    // },
    // {
    //   type: 'date',
    //   label: '计划付款时间',
    //   span: 12,
    //   display: true,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    //   prop: 'planPaymentDate',
    // },
    // {
    //   type: 'number',
    //   label: '计划付款金额',
    //   controls: true,
    //   span: 24,
    //   display: true,
    //   prop: 'planPaymentPrice',
    // },
    // {
    //   type: 'textarea',
    //   label: '备注',
    //   span: 24,
    //   display: true,
    //   prop: 'remark',
    // },
    {
      label: '合同总额',
      prop: 'contractPrice',
      addDisplay: false,
    },
    {
      type: 'input',
      label: '已付款金额',
      span: 12,
      addDisplay: false,
      editDisplay: false,

      prop: 'paymentPrice',
    },
    {
      label: '已收票金额',
      prop: 'hasInvoice',
      addDisplay: false,
      width: 150,
      formatter: row => {
        return (row.hasInvoice * 1).toFixed(2);
      },
    },
    // {
    //   type: 'input',
    //   label: '付款时间',
    //   span: 12,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'actualPaymentDate',
    // },
    // {
    //   type: 'input',
    //   label: '付款人',
    //   span: 12,
    //   addDisplay: false,
    //   editDisplay: false,
    //   prop: 'paymentUserName',
    // },
    {
      type: 'select',
      label: '付款状态',
      span: 12,
      search: true,
      multiple: true,
      searchSpan: 5,
      dicData: [
        {
          value: '0',
          label: '未付款',
        },
        {
          value: '1',
          label: '已付款',
        },
        {
          value: '2',
          label: '部分付款',
        },
      ],
      addDisplay: false,
      editDisplay: false,
      prop: 'paymentStatusList',
      formatter: row => {
        return ['未付款', '已付款', '部分付款'][row.paymentStatus];
      },
    },
    {
      type: 'select',
      label: '发票状态',
      span: 12,
      addDisplay: false,
      // search: true,
      editDisplay: false,
      prop: 'invoiceStatus',
      dicData: [
        {
          label: '已收票',
          value: 0,
        },
        {
          label: '未收票',
          value: 1,
        },
        {
          label: '部分收票',
          value: 2,
        },
      ],
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
    {
      label: '签订日期',
      type: 'date',
      prop: 'purchaseDate',
      // hide: true,
      search: true,
      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
  ],
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContractPlanPayment/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseContractPlanPayment/update';
const tableUrl = '/api/vt-admin/purchaseContract/page';
let params = ref({
  // paymentStatusList: '0,2',
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
let contractTotalPrice = ref(0);
let hasInvoicePrice = ref(0);
let hasPaymentPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.purchaseDate ? params.value.purchaseDate[0] : null,
        endTime: params.value.purchaseDate ? params.value.purchaseDate[1] : null,
        purchaseDate: null,

        paymentStatus: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        startTime: params.value.purchaseDate ? params.value.purchaseDate[0] : null,
        endTime: params.value.purchaseDate ? params.value.purchaseDate[1] : null,
        purchaseDate: null,

        paymentStatus: null,
      },
    })
    .then(res => {
      contractTotalPrice.value = res.data.data.contractTotalPrice;
      hasInvoicePrice.value = res.data.data.hasInvoicePrice;
      hasPaymentPrice.value = res.data.data.hasPaymentPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function toDetail(row) {
  router.push({
    path: '/procure/contractDetail',
    query: {
      id: row.id,
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
let imgId = ref('');
let payDrawer = ref(false);
let payForm = ref({});
let payOption = ref({
  column: [
    {
      type: 'number',
      label: '付款金额',
      span: 8,
      display: true,

      prop: 'actualPaymentPriceList',
    },
    {
      type: 'datetime',
      label: '付款日期',
      span: 8,
      display: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
      prop: 'actualPaymentDate',
    },
    {
      type: 'select',
      label: '付款账号',

      cascader: [],
      span: 8,
      display: true,

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'paymentAccount',
      rules: [
        {
          required: true,
          message: '请选择付款账号',
          trigger: 'change',
        },
      ],
    },
    {
      label: '付款凭证',
      prop: 'paymentFiles',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 12,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        imgId.value = res.id;
        done();
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 12,
      display: true,
      prop: 'remark',
    },
  ],
});
let payConfirmRef = ref(null)
let payType = ref(0)  // 0 单项支付 1 合并支付
function pay(row) {
  const value = row.contractPrice * 1 - row.paymentPrice * 1;
  payType.value = 0
  payForm.value.actualPaymentPriceList = value 
  payForm.value.purchaseContractId = row.id
  payForm.value.actualPaymentDate = dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss')
   
  payDrawer.value = true
  currentId.value = row.id
}
function payConfirm(form, done, loading) {
  axios
    .post('/api/vt-admin/purchaseContractActualPayment/save', {
      // purchaseContractId: row.id,
      // actualPaymentPriceList: row.actualPaymentPriceList,
      ...form,
      paymentFiles: imgId.value,
    })
    .then(e => {
      proxy.$message.success('操作成功');
     
      imgId.value = '';
      onLoad();
      proxy.$refs.crud.toggleSelection();
      done()
      payDrawer.value = false;
      payConfirmRef.value.resetForm()
    });
}
let dialogVisible = ref(false);
let currentId = ref('');
function payListRecord(row) {
  currentId.value = row.id;
  dialogVisible.value = true;
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
}
function allPay() {
  if (selectList.value.length === 0) {
    proxy.$message.warning('请选择需要付款的合同');
    return;
  }
  const bol = [...new Set(selectList.value.map(item => item.supplierId))];

  if (bol.length > 1) {
    proxy.$message.warning('请选择同一供应商下的合同');
    return;
  }
  selectList.value.forEach(item => {
    item.actualPaymentPrice = (item.contractPrice * 1 - item.paymentPrice * 1 || 0).toFixed(2) * 1;
  });
  const value = selectList.value.reduce((pre, cur) => {
    return pre + cur.contractPrice * 1 - cur.paymentPrice * 1;
  }, 0);
  proxy.$refs.dialogForm.show({
    title: '付款',
    option: {
      column: [
        {
          label: '',
          prop: 'paymentList',
          type: 'dynamic',
          labelWidth: 0,
          span: 24,
          index: false,
          value: selectList.value.map(item => {
            return {
              ...item,
            };
          }),
          children: {
            align: 'center',
            addBtn: false,
            delBtn: false,
            headerAlign: 'center',
            showSummary: true,
            sumColumnList: [{ name: 'actualPaymentPrice', type: 'sum' }],
            column: [
              {
                label: '关联合同编号',
                prop: 'contractCode',
                overHidden: true,
                cell: false,

                //   component: 'wf-contract-select',
              },
              {
                label: '签订时间',
                prop: 'purchaseDate',
                overHidden: true,
                type:'date',
                format: 'YYYY-MM-DD',
                cell: false,
              },
              {
                type: 'number',
                label: '合同金额',
                controls: true,
                span: 24,
                cell: false,
                display: true,
                prop: 'contractPrice',
              },
              {
                type: 'number',
                label: '实际已付金额',
                controls: true,
                span: 24,
                cell: false,
                addDisplay: false,
                editDisplay: false,
                prop: 'paymentPrice',
              },
              {
                label: '本次付款',
                prop: 'actualPaymentPrice',
                type: 'number',
                span: 24,
              },
            ],
          },
        },
        // {
        //   type: 'number',
        //   label: '付款金额',
        //   span: 12,
        //   // max:value,
        //   value,
        //   display: true,
        //   prop: 'actualPaymentPrice',
        // },
        {
          type: 'datetime',
          label: '付款日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          prop: 'actualPaymentDate',
        },
        {
          type: 'select',
          label: '付款账号',

          cascader: [],
          span: 12,
          display: true,

          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentAccount',
          rules: [
            {
              required: true,
              message: '请选择付款账号',
              trigger: 'change',
            },
          ],
        },
        {
          label: '付款凭证',
          prop: 'paymentFiles',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            imgId.value = res.id;
            done();
          },
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'remark',
        },
      ],
    },
    callback(res) {
      const bol = res.data.paymentList.some(item => {
        return item.actualPaymentPrice > item.contractPrice * 1 - item.paymentPrice * 1;
      });
      if (bol) return ElMessage.warning('付款金额不可大于剩余可付款金额');

      axios
        .post('/api/vt-admin/purchaseContractActualPayment/save', {
          purchaseContractId: res.data.paymentList.map(item => item.id).join(','),
          actualPaymentPriceList: res.data.paymentList
            .map(item => item.actualPaymentPrice)
            .join(','),
          ...res.data,
          paymentFiles: imgId.value,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          imgId.value = '';
          onLoad();
          proxy.$refs.crud.toggleSelection();
        });
    },
  });
}

let detailDrawer = ref(false);
function viewDetail(row) {
  currentId.value = row.id;
  detailDrawer.value = true;
}
</script>

<style lang="scss" scoped>

:deep(.avue-upload){
  text-align: left;
}
</style>
