<template>
  <div>
    <el-table class="avue-crud" :data="productList" border align="center">
      <el-table-column
        label="产品名称"
        show-overflow-tooltip
        #default="{ row }"
        prop="customProductName"
      >
        {{ row.customProductName || row.product?.productName }}
      </el-table-column>
      <el-table-column
        label="规格型号"
        show-overflow-tooltip
        #default="{ row }"
        prop="customProductSpecification"
      >
        {{ row.customProductSpecification || row.product?.productSpecification }}
      </el-table-column>
      <el-table-column label="产品图片" #default="{ row }">
        <el-image
          style="width: 80px"
          :preview-src-list="[row.product.coverUrl]"
          :src="row.product.coverUrl"
        ></el-image>
      </el-table-column>
      <el-table-column
        label="产品描述"
        show-overflow-tooltip
        width="200"
        #default="{ row }"
        prop="customProductDescription"
      >
        {{ row.customProductDescription || row.product?.description }}
      </el-table-column>
      <el-table-column label="品牌" #default="{ row }" prop="product.productBrand">
        {{ row.productBrand || row.product?.productBrand }}
      </el-table-column>
      <el-table-column label="单位" prop="product.unitName" #default="{ row }" align="center">
        {{ row.customUnit || row.product?.unitName }}
      </el-table-column>
      <el-table-column label="数量" #default="{ row }" prop="number" align="center">
      </el-table-column>
      <el-table-column label="已开票数量" #default="{ row }" prop="invoiceNumber" align="center">
      </el-table-column>
      <el-table-column label="单价" #default="{ row }" align="center" prop="zhhsdj">
        <span>{{ row.zhhsdj }}</span>
      </el-table-column>
      <el-table-column label="金额" #default="{ row }" align="center" prop="zhhsdj">
        <span>{{ (row.zhhsdj * row.number).toLocaleString() }}</span>
      </el-table-column>
      <el-table-column label="是否开票" align="center">
        <template #default="{ row }">
          <el-tag effect="plain" v-if="row.isInvoice === 1" type="success">是</el-tag>
          <el-tag effect="plain" v-else-if="row.isInvoice === 2" type="success">部分开票</el-tag>
          <el-tag effect="plain" v-else type="danger">否</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { onMounted, watch } from 'vue';
const props = defineProps({
    offerId: String,
});
onMounted(() => {
  getProductList();
})
watch(
  () => props.offerId,
  () => {
    getProductList();
  }
);
let productList = ref([]);
function getProductList(params) {
  axios
    .get('/api/vt-admin/sealContract/productPage', {
      params: {
        current: 1,
        size: 5000,
        offerId: props.offerId,
      },
    })
    .then(res => {
      productList.value = res.data.data.records;
    });
}
</script>

<style lang="scss" scoped></style>
