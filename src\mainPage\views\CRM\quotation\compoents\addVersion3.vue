<template>
  <div style="height: calc(100vh - 160px)" :class="{ isFullSreen: isFullSreen }">
    <Title style="margin: 0 15px; background-color: #fff"
      >{{
        $route.query.type == 'add'
          ? '新增报价'
          : $route.query.type == 'edit'
          ? `编辑${$route.query.name}`
          : `查看${$route.query.name}`
      }}
      <el-text type="primary">{{ form.customerName }}</el-text>
      <template #foot>
        <el-button icon="FullScreen" @click="handleScreen"></el-button>
        <el-button type="primary" @click="handleAddInquiryForm">询价({{ selectList.length }})</el-button>
        <el-button type="primary" @click="handleInquiry">询价历史</el-button>
        <el-button
          type=""
          title="从模板库选择"
          v-if="$route.query.type != 'detail' && !$route.query.copy"
          @click="addTemplate(2)"
          >引入模板</el-button
        >
        <el-button type="primary" @click="drawer = true">基本信息</el-button>
        <el-button
          type="warning"
          @click="savehistory"
          v-if="form.isOnline == 0 && $route.query.type != 'detail' && !$route.query.copy"
          >保存为历史版本</el-button
        >
        <el-button
          type="primary"
          @click="submit(null)"
          v-if="$route.query.type != 'detail' && !$route.query.copy"
          plain
          >保存草稿</el-button
        >
        <el-button type="primary" @click="submit('confirm')" v-if="$route.query.type != 'detail'"
          >保存并提交</el-button
        >

        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >

    <!-- <input type="file" @change="handleChange" ref="file" /> -->

    <div style="height: calc(100% - 90px)">
      <edit @addInquiry="handleAddInquiry" :repairId="route.query.repairId" :data="form" ref="sheet"></edit>
    </div>
    <el-drawer title="基本信息" v-model="drawer">
      <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form">
        <template #filesDetail>
          <File :fileList="form.fileList"></File>
        </template>
      </avue-form>
      <template #footer>
        <div style="flex: auto">
          <el-button type="primary" @click="drawer = false">确认</el-button>
        </div>
      </template>
    </el-drawer>

    <!-- 询价详情抽屉 -->
    <el-drawer title="询价详情" v-model="detailDrawer" size="70%">
      <div class="inquiry-detail-content" v-if="inquiryDetail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="报价时间">
              {{ inquiryDetail.offerDate }}
            </el-descriptions-item>
            <el-descriptions-item label="报价有效期">
              {{ inquiryDetail.overDays }}
            </el-descriptions-item>
            <el-descriptions-item label="报价人">
              {{ inquiryDetail.offerPersonName }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ inquiryDetail.contactPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="备注">
              {{ inquiryDetail.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 产品列表 -->
        <div class="detail-section">
          <h3>产品列表</h3>
          <el-table :data="inquiryDetail.offerDetailVOList" border style="width: 100%">
            <el-table-column prop="customProductName" label="产品名称" width="200">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.customProductName || '原产品名称' }}
                  </div>
                  <div>{{ scope.row.customProductName }}</div>
                </div>
                <div v-else>
                  {{ scope.row.customProductName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="productBrand" label="品牌" width="120">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.productBrand || '原品牌' }}
                  </div>
                  <div>{{ scope.row.productBrand }}</div>
                </div>
                <div v-else>
                  {{ scope.row.productBrand }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="customProductSpecification" label="型号" width="150">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.customProductSpecification || '原型号' }}
                  </div>
                  <div>{{ scope.row.customProductSpecification }}</div>
                </div>
                <div v-else>
                  {{ scope.row.customProductSpecification }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="number" label="数量" width="80" align="center">
            </el-table-column>
            <el-table-column prop="customUnit" label="单位" width="80" align="center">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.customUnit || '原单位' }}
                  </div>
                  <div>{{ scope.row.customUnit }}</div>
                </div>
                <div v-else>
                  {{ scope.row.customUnit }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="inquiryPrice" label="单价" width="100" align="right">
            </el-table-column>
            <el-table-column prop="totalPrice" label="金额" width="120" align="right">
              <template #default="{ row }">
                <div>￥{{ (row.number * row.inquiryPrice).toFixed(2) }}</div>
              </template>
            </el-table-column>
            <!-- 是否含税 -->
            <el-table-column prop="isTax" label="含税" width="80" align="center">
              <template #default="scope">
                {{ scope.row.isHasTax === 1 ? '是' : '否' }}
              </template>
            </el-table-column>
            <!-- 税率 -->
            <el-table-column prop="taxRate" label="税率" width="80" align="center">
              <template #default="scope"> {{ scope.row.taxRate || 0 }}% </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="150">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div
                    style="text-decoration: line-through; color: #999; font-size: 12px"
                    v-if="scope.row.originalProduct?.remark"
                  >
                    {{ scope.row.originalProduct?.remark }}
                  </div>
                  <div>{{ scope.row.remark }}</div>
                </div>
                <div v-else>
                  {{ scope.row.remark }}
                </div>
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <el-button type="primary" text size="mini" @click="handleImport(scope.row)"
                  >引入</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>
    <div style="position: fixed"></div>
    <dialogForm ref="dialogForm"></dialogForm>
    <templateSelect
      ref="templateDialog"
      :templateType="3"
      :level="3"
      :businessType="form.businessTypeId"
      @change="handleTemplateChange"
    ></templateSelect>

    <!-- 询价管理组件 -->
    <InquiryManagement
        ref="inquiryManagement"
        v-model:visible="inquiryDrawer"
        :business-id="form.businessOpportunityId"
        :offer-id="form.id"
        :offer-name="form.offerName"
        v-model:selectList="selectList"
        @import="handleImportProducts"
        @update:visible="inquiryDrawer = $event"
      />


  </div>
</template>

<script setup>
import { getCurrentInstance, onBeforeUnmount, onMounted, watch } from 'vue';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import Sortable from 'sortablejs';
import { randomLenNum } from '@/utils/util';
import edit from './edit.vue';
import templateSelect from '@/views/CRM/programme/compoents/templateSelect.vue';
import InquiryManagement from './InquiryManagement.vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { Plus, Delete, RefreshLeft } from '@element-plus/icons-vue';

// import sheet from '@/views/CRM/compoents/sheet.vue';
let { proxy } = getCurrentInstance();
// 设置拖拽排序
let route = useRoute();
onMounted(() => {
  if (route.query.roleType == 'assist') {
    console.log(111);
    setBusinessUrl();
    setCustomerUrl();
  }
});
watch(
  () => route.query,
  () => {
    if (route.query.repairId) {
      handleRepair(route.query.repairId);
    }
  },
  {
    deep: true,
    immediate: true,
  }
);

let router = useRouter();
let form = ref({
  detailList: [],
});
let isEdit = ref(true);
let option = ref({
  submitBtn: false,
  labelWidth: 140,
  detail: route.query.type == 'detail',
  emptyBtn: false,
  column: [
    {
      label: '关联商机',
      prop: 'businessOpportunityId',
      component: 'wf-business-select',
      span: 24,
      params: {
        isNew: true,
      },
      change: val => {
        if (val.value) {
          getBusinessDetail(val.value);
        }
      },
      control: val => {
        return {
          businessTypeId: {
            display: !val,
          },
        };
      },
    },
    {
      label: '报价名称',
      prop: 'offerName',
      span: 24,
      rules: [
        {
          required: true,
          message: '请填写方案名称',
        },
        {
          validator: (rule, value, callback) => {
            const reg = /^[^/\\?？\[\]]*$/;
            console.log(value, rule, reg);
            if (!reg.test(value)) {
              callback(new Error('不能包含特殊字符"/\?？[]"'));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ],
    },

    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 24,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'businessTypeId',
      checkStrictly: true,
    },
    {
      label: '对应客户',
      prop: 'customerId',
      span: 24,
      component: 'wf-customer-select',
      change: val => {
        const contactPerson = proxy.findObject(option.value.column, 'contactPerson');

        if (!val.value) {
          contactPerson.disabled = true;
          return;
        }

        contactPerson.disabled = false;
        contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val.value;
        setBaseInfo(val.value);
      },
      params: {},
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      label: '协作业务员',
      placeholder: '请选择协作业务员',
      type: 'select',
      span: 24,
      dicData: [],
      display: false,
      prop: 'businessPerson',
      rules: [
        {
          required: true,
          message: '请选择协作业务员',
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      span: 24,
      component: 'wf-contact-select',
      disabled: true,
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        // checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },

    // {
    //   label: '报价人员',
    //   component: 'wf-user-select',
    //   prop: 'offerPeople',
    // },
    {
      label: '报价公司',
      type: 'select',
      // value: '深圳市非常聚成科技有限公司',
      prop: 'offerCompany',
      span: 24,
      overHidden: true,
      cell: false,
      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        //  form.value.offerCompany = res.data.records[0]?.id
        return res.data.records;
      },
      dicUrl: '/api/vt-admin/company/page?size=1000&current=1',
    },
    {
      label: '报价日期',
      prop: 'offerDate',
      type: 'date',
      span: 24,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
      type: 'number',
      span: 24,
    },
    {
      label: '设备保修期(年)',
      prop: 'freeWarrantyYear',
      span: 24,
      type: 'number',
    },
    // {
    //   label: '线下报价',
    //   prop: 'isOnline',
    //   type: 'radio',
    //   span: 24,
    //   // display: !route.query.id,
    //   value: 0,
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
    // {
    //   label: '专项报价',
    //   prop: 'isHasSpecialPrice',
    //   type: 'radio',
    //   span: 24,
    //   value: 0,
    //   dicData: [
    //     {
    //       label: '是',
    //       value: 1,
    //     },
    //     {
    //       label: '否',
    //       value: 0,
    //     },
    //   ],
    // },
    {
      label: '附件',
      prop: 'filesDetail',
      display: route.query.type == 'detail',
      span: 24,
    },
    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      display: route.query.type != 'detail',
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
  if (route.query.businessId) {
    getBusinessDetail(route.query.businessId);
  }
});
let drawer = ref(route.query.type != 'detail');
let inquiryDrawer = ref(false);


function submit(type) {
  // if (form.value.detailList.length == 0) {
  //   return proxy.$message.warning('请至少添加一个产品');
  // }

  proxy.$refs.addForm.validate((valid, done) => {
    if (valid) {
      if (!type) {
        sumitForm(type, done);
      } else {
        proxy
          .$confirm('确认此次操作吗？', '提示')
          .then(() => {
            sumitForm(type, done);
          })
          .catch(err => {
            done();
          });
      }
    } else {
      drawer.value = true;
    }
  });
}

function sumitForm(type, done) {
  return new Promise((resolve, reject) => {
    const { columnHideData, isHasTax, productRate } = proxy.$refs.sheet.getData();
    form.value = {
      ...form.value,
      fileIds: form.value.fileIds && form.value.fileIds.map(item => item.value).join(','),
      configuredJson: JSON.stringify({
        isFullSreen: isFullSreen.value,
        columnHideData: columnHideData,
      }),
      isHasTax,
      productRate,
      detailList: proxy.$refs.sheet.getData().moduleDTOList.detailDTOList.map((item, index) => {
        return {
          ...item,
          sortNumber: index,
          sourceType: null,
          invoiceNumber: null,
          isInvoice: null,
        };
      }),
    };

    if (type == 'confirm') {
      const isNoPass = form.value.detailList.some(item => !item.customUnit || !item.number);
      if (isNoPass) {
        ElMessage.warning('单位或者数量必填');
        return done();
      }
    }
    if (form.value.id && !route.query.copy) {
      axios
        .post('/api/vt-admin/offer/businessUpdateOffer', {
          ...form.value,
          detailList: form.value.detailList.map((i, index) => {
            return {
              ...i,
              sortNumber: index,
            };
          }),
          id: form.value.id,
          offerPrice: offerPrice(),
          offerStatus: type == 'confirm' ? 1 : 0,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          proxy.$store.dispatch('getMessageList');
          resolve(form.value.id);
          if (type == 'confirm') {
            isEdit.value = false;
            isAutoSave.value = false;
            router.$avueRouter.closeTag();
            router.go(-1);
            proxy.$refs.addForm.resetForm();
            form.value.detailList = [];
          }
          getDetail(form.value.id);
          done();
        });
    } else {
      let url;
      if (form.value.businessOpportunityId) {
        url = '/api/vt-admin/businessOpportunity/noOptionAddOffer';
      } else {
        url = '/api/vt-admin/offer/noBusinessAddOffer';
      }
      axios
        .post(url, {
          ...form.value,
          id: null,
          repairId: route.query.repairId || null,
          offerType: route.query.repairId ? 1 : 0,
          detailList: form.value.detailList.map((i, index) => {
            return {
              ...i,
              sortNumber: index,
              id: null,
            };
          }),
          offerPeople: proxy.$store.getters.userInfo.user_id,
          offerStatus: type == 'confirm' ? 1 : 0,
          offerPrice: offerPrice(),
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          proxy.$store.dispatch('getMessageList');
          if (type == 'confirm') {
            isEdit.value = false;
            isAutoSave.value = false;
            router.$avueRouter.closeTag();
            router.go(-1);
            proxy.$refs.addForm.resetForm();
            form.value.detailList = [];
          } else {
            getDetail(res.data.data);
            resolve(res.data.data);
          }
          done();
        });
    }
  });
}
function offerPrice() {
  return form.value.detailList.reduce((prev, curr) => {
    const value = Number(curr.sealPrice) * Number(curr.number);
    if (!Number.isNaN(value)) {
      return prev + value;
    } else {
      return prev;
    }
  }, 0);
}
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const mainPerson = res.data.data.customerContactVO?.id;
      form.value.contactPerson = mainPerson;
      const businessPersonRef = proxy.findObject(option.value.column, 'businessPerson');
      if (res.data.data.businessPerson.split(',').length > 1 && route.query.roleType == 'assist') {
        businessPersonRef.display = true;
        const dicData = res.data.data.businessPerson.split(',').map((item, index) => {
          return {
            value: item,
            label: res.data.data.businessPersonName.split(',')[index],
          };
        });
        businessPersonRef.dicData = dicData;
      } else if (
        res.data.data.businessPerson.split(',').length == 1 &&
        route.query.roleType == 'assist'
      ) {
        form.value.businessPerson = res.data.data.businessPerson;
        businessPersonRef.display = false;
      } else {
        businessPersonRef.display = false;
      }
    });
}
let sheetOption = ref({
  option: '', //JSON数据
  detailList: [], //详情数据
  isRefresh: false, //是否刷新表格中的数据
  type: 1, //1 报价 2 方案
  form: {
    productRate: '0.13',
    labourRate: '0.06',
    otherRate: '0.06',
    warrantyRate: '0.06',
  },
});
function getDetail(id) {
  let url = '';
  if (route.query.isHistory == '1') {
    url = '/api/vt-admin/offerHistory/detail';
  } else if (route.query.isTranslate) {
    url = '/api/vt-admin/offer/detailByHistoryId';
  } else {
    url = '/api/vt-admin/offer/detail';
  }
  axios
    .get(url, {
      params: {
        id: id,
      },
    })
    .then(res => {
      let {
        detailVOList,
        id,
        customerId,
        customerName,
        businessOpportunityId,
        contactPerson,
        offerName,
        isHasOption,
        offerValidity,
        freeWarrantyYear,
        offerDate,
        businessTypeId,
        isHasSpecialPrice,
        isHasCustom,
        isOnline,
        auditStatus,
        dataJson,
        configuredJson,
        productRate,
        isHasTax,
        labourRate,
        otherRate,
        remark,
        repairId,
        warrantyRate,
        discountsPrice,
        fileList,
        offerCompany,
      } = res.data.data;
      form.value.detailList = detailVOList;
      form.value.discountsPrice = discountsPrice * 1;
      form.value.productRate = productRate;
      form.value.isHasTax = isHasTax;
      if (route.query.copy != '1') {
        form.value.businessOpportunityId = businessOpportunityId;
        form.value.customerId = customerId;
        form.value.customerName = customerName;
        form.value.contactPerson = contactPerson;
        form.value.offerName = offerName;
      }
      form.value.isHasOption = isHasOption;
      form.value.offerValidity = offerValidity;
      form.value.freeWarrantyYear = freeWarrantyYear;
      form.value.offerDate = offerDate;
      form.value.businessTypeId = businessTypeId;
      form.value.isHasSpecialPrice = isHasSpecialPrice;
      form.value.isHasCustom = isHasCustom;
      form.value.id = id;
      form.value.isOnline = isOnline;
      form.value.remark = remark;
      form.value.offerCompany = offerCompany;
      form.value.fileIds =
        fileList &&
        fileList.map(item => {
          return {
            value: item.id,
            label: item.originalName,
          };
        });
      form.value.fileList = fileList;
      sheetOption.value.option = dataJson;
      sheetOption.value.detailList = form.value.detailList;
      sheetOption.value.isRefresh = auditStatus != 1;
      // form.value.repairId = repairId;

      if (!configuredJson) {
        configuredJson = '{}';
      }

      const {
        isFullSreen: newIsFullSreen = false,
        isFold = false,
        utilCheckBox = [],
        columnHideData = null,
      } = JSON.parse(configuredJson);
      isFullSreen.value = newIsFullSreen;
      form.value.columnHideData = columnHideData;
    });
}
function getBusinessDetail(id) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        id: businessOpportunityId,
        contactPerson,
        name,
        isCooperation,
        type,
        cooperationTenantId,
      } = res.data.data;

      if (productVOList && productVOList.length > 0) {
        console.log(productVOList);
        const detailList = productVOList.map(item => {
          return {
            ...item.productVO,
            number: item.number || item.productVO?.number,
            customProductName: item.productVO?.productName,
            customProductSpecification: item.productVO?.productSpecification,
            customProductDescription: item.productVO?.description,
            customUnit: item.productVO?.unitName,
            productId: item.productVO?.id,
            preSealPrice: item.productVO?.sealPrice,

            detailType: 0,
            source: 0,
            sealPrice: item.sealPrice,
            rgcbdj: '',
            ybcbdj: '',
            qtcbdj: '',
            ybhsdj: '',
            qthsdj: '',

            laborCost: '',
            id: null,
          };
        });
        form.value.detailList = detailList;
      }

      form.value.businessOpportunityId = businessOpportunityId;
      form.value.businessTypeId = type;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = name;
      const contactPersonRef = proxy.findObject(option.value.column, 'contactPerson');

      contactPersonRef.disabled = false;
      contactPersonRef.params.Url = '/vt-admin/customerContact/page?customerId=' + customerId;
      setBaseInfo(customerId);
      // 合作项目修改 报价公司查询接口
      const offerCompanyRef = proxy.findObject(option.value.column, 'offerCompany');
      if (isCooperation == 1) {
        axios
          .get(`/api/vt-admin/company/page?size=1000&current=1&tenantId=${cooperationTenantId}`)
          .then(res => {
            offerCompanyRef.dicData = res.data.data.records;
            if (!form.value.offerCompany) {
              form.value.offerCompany = res.data.data.records[0]?.id;
            }
          });
      } else {
        axios.get(`/api/vt-admin/company/page?size=1000&current=1`).then(res => {
          offerCompanyRef.dicData = res.data.data.records;
          if (!form.value.offerCompany) {
            form.value.offerCompany = res.data.data.records[0]?.id;
          }
        });
      }
    });
}

function setBusinessUrl(params) {
  const businessOpportunity = proxy.findObject(option.value.column, 'businessOpportunityId');
  businessOpportunity.params.Url = '/vt-admin/businessOpportunity/page?selectType=3';
}
function setCustomerUrl() {
  const customer = proxy.findObject(option.value.column, 'customerId');
  customer.params.Url = '/vt-admin/customer/page?type=2';
}
onBeforeUnmount(() => {});
function handleFocus(row, value) {
  row[value] = true;
  console.log(proxy.$refs);
  proxy.$nextTick(() => {
    proxy.$refs[`${value}${row.uuid}`].focus();
  });
}

let isFullSreen = ref(false);
function handleScreen() {
  isFullSreen.value = !isFullSreen.value;
  proxy.$nextTick(() => {
    luckysheet.resize();
  });
}
function savehistory() {
  if (!form.value.id) {
    proxy.$refs.addForm.validate((valid, done) => {
      if (valid) {
        savehistorySubmit();
      } else {
        drawer.value = true;
      }
    });
  } else {
    proxy.$refs.dialogForm.show({
      title: '保存历史版本',
      option: {
        column: [
          {
            label: '报价名称',
            type: 'input',
            prop: 'offerName',
            value: form.value.offerName,
            rules: [
              {
                required: true,
                message: '请输入报价名称',
                trigger: 'blur',
              },
            ],
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin//vt-admin/offer/saveHistoryOffer', {
            ...res.data,
            id: form.value.id,
          })
          .then(r => {
            proxy.$message.success(r.data.msg);
            res.close();
          });
      },
    });
  }
}
function savehistorySubmit(params) {
  sumitForm().then(r => {
    proxy.$refs.dialogForm.show({
      title: '保存历史版本',
      option: {
        column: [
          {
            label: '报价名称',
            type: 'input',
            prop: 'offerName',
            value: form.value.offerName,
            rules: [
              {
                required: true,
                message: '请输入报价名称',
                trigger: 'blur',
              },
            ],
            span: 24,
          },
          {
            label: '备注',
            type: 'textarea',
            prop: 'remark',
            span: 24,
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin//vt-admin/offer/saveHistoryOffer', {
            ...res.data,
            id: r,
          })
          .then(r => {
            proxy.$message.success(r.data.msg);
            res.close();
          });
      },
    });
  });
}
let isAutoSave = ref(true);
// 添加模板
function handleTemplateChange(id, done) {
  proxy
    .$confirm('引入模板将会替换所有内容，是否确定引入模板?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(async () => {
      const res = await axios.get('/api/vt-admin/optionTemplate/detailForAdd', {
        params: {
          id,
        },
      });
      console.log(res);
      const { detailTemplateVOS } = res.data.data;
      form.value = {
        ...form.value,
        detailList: detailTemplateVOS.map(item => {
          return {
            ...item,
            ...(item.productVO || {}),
            productBrand: item.productBrand,
            detailType: 0,
            source: 1,
            sealPrice: '',
            rgcbdj: '',
            ybcbdj: '',
            qtcbdj: '',
            ybhsdj: '',
            qthsdj: '',
            sealPrice: '',
            laborCost: '',
            id: null,
          };
        }),
      };
      done();
    });
}
function addTemplate(val, done) {
  proxy.$nextTick(() => {
    proxy.$refs.templateDialog.open();
  });
}
function handleRepair(id) {
  axios.get('/api/vt-admin/repair/detail', { params: { id: id } }).then(res => {
    const { customerId } = res.data.data;
    form.value.customerId = customerId;
    const contactPerson = proxy.findObject(option.value.column, 'contactPerson');
    contactPerson.disabled = false;
    contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + customerId;
    form.value.offerName = route.query.repairCode + '的维修报价';
    setBaseInfo(customerId);
  });
}
// 处理询价按钮点击事件
function handleInquiry() {
  if (!form.value.id) {
    ElMessage.warning('请先保存报价单');
    return;
  }
  // 直接打开新增询价抽屉
  inquiryDrawer.value = true;
 
}
 function handleAddInquiryForm() {
   // 通过nextTick确保组件已渲染，然后调用新增询价方法
  proxy.$nextTick(() => {
    const inquiryComponent = proxy.$refs.inquiryManagement;
    if (inquiryComponent && inquiryComponent.handleAddInquiry) {
      inquiryComponent.handleAddInquiry();
    }
  });
}
// 处理从询价组件传来的产品导入事件
function handleImportProducts(products) {
  debugger
  const {detailId,isReplaceProduct,inquiryPrice} = products[0]
  if(detailId){
    if(isReplaceProduct == 1){
      ['customProductName','customProductSpecification','productBrand','customProductDescription','customUnit'].forEach(item => {
       proxy.$refs.sheet.setData({key:item,id:detailId,value:products[0][item]})
      })
    }
     proxy.$refs.sheet.setData({key:'costPrice',id:detailId,value:inquiryPrice})
     ElMessage.success('引入成功')
  }else{
   proxy.$confirm('该产品未在报价单中存在，是否添加到报价单中？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
       proxy.$refs.sheet.setData({data:{
        ...products[0],
        source:2,
        costPrice:inquiryPrice,
        number:parseFloat(products[0].number)
       }})
   })

  }
  

}
let selectList = ref([])
function handleAddInquiry(row) {
  const {id} = row
  if(selectList.value.find(item => item.id == id)){
    ElMessage.warning('该产品已添加')
    return
  }else{
    selectList.value.push(row)
    ElMessage.success('添加成功')
  }
}


onBeforeRouteLeave((to, from) => {
  // if (route.query.type != 'detail' && isAutoSave.value && form.value.id) {
  //   submit();
  //   proxy.$message.success('自动保存草稿成功');
  // }
});
</script>

<style scoped>
.el-table .el-form-item {
  margin-bottom: 0;
}
.isFullSreen {
  position: fixed;
  height: 100vh !important;
  width: 100vw;
  background-color: #fff;
  top: 0;
  left: 0;
  z-index: 1000;
}


</style>
