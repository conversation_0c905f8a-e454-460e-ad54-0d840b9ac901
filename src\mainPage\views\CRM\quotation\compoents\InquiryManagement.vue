<template>
  <div>
    <!-- 询价抽屉 -->
    <el-drawer title="询价列表" v-model="inquiryDrawer" size="80%">
       
      <div class="inquiry-cards">
         <!-- 新增按钮卡片 -->
        <div class="inquiry-card add-card">
          <el-card shadow="hover" class="add-inquiry-card" @click="handleAddInquiry">
            <div class="add-content">
              <el-icon size="40" color="#409EFF">
                <Plus />
              </el-icon>
              <p>新增询价</p>
            </div>
          </el-card>
        </div>
        <!-- 询价卡片列表 -->
        <div v-for="inquiry in inquiryList" :key="inquiry.id" class="inquiry-card">
          <el-card shadow="hover">
            <div class="card-content">
              <div class="inquiry-info">
                <h4>{{ inquiry.inquiryCode }}</h4>
                <p class="inquiry-time">供应商：{{ inquiry.supplierName }}</p>
                <p class="inquiry-time">询价时间：{{ inquiry.createTime }}</p>

                <!-- 多个报价版本时的展示 -->
                <template
                  v-if="inquiry.offerVersionVOList && inquiry.offerVersionVOList.length > 0"
                >
                  <div class="multiple-offers">
                    <p class="offer-count">共{{ inquiry.offerVersionVOList.length }}个报价版本：</p>
                    <div class="offer-list">
                      <div
                        v-for="(offer, index) in inquiry.offerVersionVOList"
                        :key="offer.id"
                        class="offer-item"
                      >
                        <div class="offer-info">
                          <span class="offer-name">{{
                            offer.offerName || `报价版本${index + 1}`
                          }}</span>
                          <span class="offer-time">{{ offer.offerDate }}</span>
                        </div>
                        <div class="offer-actions">
                          <el-tag
                            :type="getStatusType(offer.offerStatus)"
                            size="small"
                            class="offer-status"
                          >
                            {{ getStatusText(offer.offerStatus) }}
                          </el-tag>
                          <div class="offer-buttons">
                            <el-button
                              type="text"
                              size="small"
                              @click="handleViewDetail({ ...inquiry, offerVersionVOList: [offer] })"
                            >
                              查看详情
                            </el-button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </el-card>
        </div>

      
      </div>
    </el-drawer>

    <!-- 询价详情抽屉 -->
    <el-drawer title="询价详情" v-model="detailDrawer" size="70%">
      <div class="inquiry-detail-content" v-if="inquiryDetail">
        <!-- 基本信息 -->
        <div class="detail-section">
          <h3>基本信息</h3>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="报价时间">
              {{ inquiryDetail.offerDate }}
            </el-descriptions-item>
            <el-descriptions-item label="报价有效期">
              {{ inquiryDetail.overDays }}
            </el-descriptions-item>
            <el-descriptions-item label="报价人">
              {{ inquiryDetail.offerPersonName }}
            </el-descriptions-item>
            <el-descriptions-item label="联系电话">
              {{ inquiryDetail.contactPhone }}
            </el-descriptions-item>
            <el-descriptions-item label="备注">
              {{ inquiryDetail.remark }}
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 产品列表 -->
        <div class="detail-section">
          <h3>产品列表</h3>
          <el-table :data="inquiryDetail.offerDetailVOList" border style="width: 100%">
            <el-table-column prop="customProductName" label="产品名称" width="200">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.customProductName || '原产品名称' }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.customProductName }}
                    <el-tag v-if="scope.row.isStop == 1" type="danger" size="small" style="margin-left: 5px;">停产</el-tag>
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.customProductName }}
                  <el-tag v-if="scope.row.isStop == 1" type="danger" size="small" style="margin-left: 5px;">停产</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="productBrand" label="品牌" width="120">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.productBrand || '原品牌' }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.productBrand }}
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.productBrand }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="customProductSpecification" label="型号" width="150">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.customProductSpecification || '原型号' }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.customProductSpecification }}
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.customProductSpecification }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="number" label="数量" width="80" align="center">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.number || '原数量' }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.number }}
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.number }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="customUnit" label="单位" width="80" align="center">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.customUnit || '原单位' }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.customUnit }}
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.customUnit }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="inquiryPrice" label="单价" width="100" align="right">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    {{ scope.row.originalProduct?.inquiryPrice || '原单价' }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.inquiryPrice }}
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.inquiryPrice }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="totalPrice" label="金额" width="120" align="right">
              <template #default="{ row }">
                <div v-if="row.isReplaceProduct == 1">
                  <div style="text-decoration: line-through; color: #999; font-size: 12px">
                    ￥{{ (row.originalProduct?.number * row.originalProduct?.inquiryPrice || 0).toFixed(2) }}
                  </div>
                  <div :style="row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    ￥{{ (row.number * row.inquiryPrice).toFixed(2) }}
                  </div>
                </div>
                <div v-else :style="row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  ￥{{ (row.number * row.inquiryPrice).toFixed(2) }}
                </div>
              </template>
            </el-table-column>
            <!-- 是否含税 -->
            <el-table-column prop="isTax" label="含税" width="80" align="center">
              <template #default="scope">
                <span :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.isHasTax === 1 ? '是' : '否' }}
                </span>
              </template>
            </el-table-column>
            <!-- 税率 -->
            <el-table-column prop="taxRate" label="税率" width="80" align="center">
              <template #default="scope">
                <span :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.taxRate || 0 }}%
                </span>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="备注" min-width="150">
              <template #default="scope">
                <div v-if="scope.row.isReplaceProduct == 1">
                  <div
                    style="text-decoration: line-through; color: #999; font-size: 12px"
                    v-if="scope.row.originalProduct?.remark"
                  >
                    {{ scope.row.originalProduct?.remark }}
                  </div>
                  <div :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                    {{ scope.row.remark }}
                  </div>
                </div>
                <div v-else :style="scope.row.isStop == 1 ? 'text-decoration: line-through; color: #999;' : ''">
                  {{ scope.row.remark }}
                </div>
              </template>
            </el-table-column>
            <!-- 操作 -->
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <el-button type="primary" :disabled="scope.row.isStop == 1 && scope.row.isReplaceProduct == 0" text size="small" @click="handleImport(scope.row)"
                  >引入</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </el-drawer>

    <!-- 新增询价抽屉 -->
    <el-drawer title="新增询价" v-model="addInquiryDrawer" size="70%">
      <div class="add-inquiry-content">
        <!-- 基本信息表单 -->
        <div class="inquiry-form-section">
          <h3>基本信息</h3>
          <avue-form :option="inquiryOption" ref="inquiryFormRef" v-model="inquiryForm">
          </avue-form>
        </div>

        <!-- 产品列表 -->
        <div class="inquiry-products-section">
          <div class="products-header">
            <h3>询价产品列表</h3>
            <div class="batch-actions">
              <el-button
                type="danger"
                size="small"
                icon="Delete"
                :disabled="selectedProductIds.length === 0"
                @click="handleBatchDelete"
              >
                批量删除 ({{ selectedProductIds.length }})
              </el-button>
              <el-button
                type="warning"
                size="small"
                icon="RefreshLeft"
                @click="handleInverseSelection"
              >
                反选
              </el-button>
            </div>
          </div>
          <el-table
            :data="selectedProducts"
            border
            style="width: 100%"
            align="center"
            ref="productTableRef"
            row-key="id"
            @selection-change="handleProductSelection"
          >
            <el-table-column type="selection" width="55" align="center"></el-table-column>
            <el-table-column
              prop="customProductName"
              label="产品名称"
              align="center"
              min-width="150"
            ></el-table-column>
            <el-table-column
              prop="customProductSpecification"
              align="center"
              label="规格型号"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="productBrand"
              align="center"
              label="品牌"
              min-width="120"
            ></el-table-column>
            <el-table-column
              prop="customUnit"
              align="center"
              label="单位"
              width="80"
            ></el-table-column>
            <el-table-column prop="number" align="center" label="数量" width="150">
              <template #default="scope">
                <el-input-number
                  style="width: 100%"
                  controls-position="right"
                  v-model="scope.row.number"
                  :min="1"
                  size="small"
                ></el-input-number>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center">
              <template #default="scope">
                <el-button
                  type="danger"
                  size="small"
                  icon="Delete"
                  @click="handleDeleteProduct(scope.$index)"
                  title="删除产品"
                ></el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>

      <template #footer>
        <div style="flex: auto">
          <el-button @click="addInquiryDrawer = false">取消</el-button>
          <el-button type="primary" @click="submitInquiry">提交询价</el-button>
        </div>
      </template>
    </el-drawer>
  </div>
</template>

<script setup>
import { ref, getCurrentInstance } from 'vue';
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { Plus, Delete, RefreshLeft } from '@element-plus/icons-vue';

const { proxy } = getCurrentInstance();

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  offerId: {
    type: [String, Number],
  
  },
  optionId: {
    type: [String, Number],
    
  },
  businessOpportunityId: {
    type: [String, Number],
    
  },
  offerName: {
    type: String,
    default: ''
  },
  selectList: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:visible', 'import', 'update:selectList']);

// 响应式数据
const inquiryDrawer = ref(false);
const inquiryList = ref([]);
const addInquiryDrawer = ref(false);
const inquiryForm = ref({});
const selectedProducts = ref([]);
const selectedProductIds = ref([]);
const detailDrawer = ref(false);
const inquiryDetail = ref(null);

// 询价表单配置
const inquiryOption = ref({
  submitBtn: false,
  labelWidth: 120,
  emptyBtn: false,
  column: [
    {
      label: '询价标题',
      prop: 'inquiryTitle',
      span: 12,
      rules: [
        {
          required: true,
          message: '请输入询价标题',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '询价厂家',
      type: 'select',
      dicUrl: '/api/vt-admin/supplier/pageForPlatformSupplier',
      prop: 'supplierId',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        value: 'id',
        label: 'supplierName',
      },
    },
    {
      label: '询价人',
      prop: 'inquiryPerson',
    },
    {
      label: '询价人电话',
      prop: 'inquiryPersonPhone',
    },
    {
      label: '询价备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
  ],
});

// 监听visible变化
watch(() => props.visible, (newVal) => {
  inquiryDrawer.value = newVal;
  if (newVal) {
    getInquiryList();
  }
});

watch(inquiryDrawer, (newVal) => {
  emit('update:visible', newVal);
});

// 方法
// 获取询价列表
function getInquiryList() {
  
  
  axios
    .get('/api/vt-admin/supplierOffer/page', {
      params: {
        offerId: props.offerId,
        optionId:props.optionId,
        businessOpportunityId: props.businessOpportunityId,
        current: 1,
        size: 100,
      },
    })
    .then(res => {
      inquiryList.value = res.data.data.records || [];
    })
    .catch(err => {
      console.error('获取询价列表失败:', err);
      inquiryList.value = [];
    });
}

// 查看询价详情
function handleViewDetail(inquiry) {
  // 获取报价单详情
  const offerVersionId =
    inquiry.offerVersionVOList && inquiry.offerVersionVOList[0]
      ? inquiry.offerVersionVOList[0].id
      : null;
  if (!offerVersionId) {
    ElMessage.warning('该询价没有报价版本信息');
    return;
  }

  axios
    .get('/api/vt-admin/supplierOffer/offerVersionDetail', {
      params: { id: offerVersionId },
    })
    .then(res => {
      if (res.data.data.offerStatus == 0) return ElMessage.warning('请等待供应商报价完成');
      inquiryDetail.value = {
        ...res.data.data,
        offerDetailVOList: res.data.data.offerDetailVOList.map(item => {
          return {
            ...item,
            originalProduct: {
              customProductName: item.replaceCustomProductName,
              customProductSpecification: item.replaceCustomProductSpecification,
              customProductDescription: item.replaceCustomProductDescription,
              productBrand: item.replaceProductBrand,
              customUnit: item.replaceCustomUnit,
              number: item.replaceNumber,
              inquiryPrice: item.replaceInquiryPrice,
              remark: item.replaceRemark,
            },
          };
        }),
      };
      detailDrawer.value = true;
    })
    .catch(err => {
      console.error('获取询价详情失败:', err);
      ElMessage.error('获取详情失败，请重试');
    });
}

// 引入产品
function handleImport(row) {
  emit('import', [row]);
}

// 新增询价
function handleAddInquiry() {
 
  
  // 初始化询价表单
  inquiryForm.value = {
    inquiryTitle: props.offerName + '的询价',
    inquiryDeadline: '',
    inquiryPerson: proxy.$store.getters.userInfo.real_name,
    inquiryDescription: '',
  };

  // 初始化产品列表（从报价单中获取）
  selectedProducts.value = props.selectList.map(item => {
    const {
      customProductName,
      customProductSpecification,
      customUnit,
      number,
      productBrand,
      id,
    } = item;
    return {
      customProductName,
      customProductSpecification,
      customUnit,
      productBrand,
      number,
      detailId: id,
    };
  });

  addInquiryDrawer.value = true;
}

// 处理产品选择
function handleProductSelection(selection) {
  selectedProductIds.value = selection.map(item => item.id);
}

// 删除产品
function handleDeleteProduct(index) {
  proxy
    .$confirm('确定要删除该产品吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const deletedProduct = selectedProducts.value[index];
      selectedProducts.value.splice(index, 1);
      
      // 同步更新外部selectList
      const updatedSelectList = props.selectList.filter(item => item.id !== deletedProduct.detailId);
      emit('update:selectList', updatedSelectList);
      
      ElMessage.success('产品删除成功');
    })
    .catch(() => {
      // 用户取消操作
    });
}

// 批量删除产品
function handleBatchDelete() {
  if (selectedProductIds.value.length === 0) {
    ElMessage.warning('请先选择要删除的产品');
    return;
  }

  proxy
    .$confirm(`确定要删除选中的 ${selectedProductIds.value.length} 个产品吗？`, '批量删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      // 根据选中的id删除产品
      const selectedIds = new Set(selectedProductIds.value);
      const deletedProducts = selectedProducts.value.filter(product => selectedIds.has(product.id));
      selectedProducts.value = selectedProducts.value.filter(
        product => !selectedIds.has(product.id)
      );

      // 同步更新外部selectList
      const deletedDetailIds = new Set(deletedProducts.map(p => p.detailId));
      const updatedSelectList = props.selectList.filter(item => !deletedDetailIds.has(item.id));
      emit('update:selectList', updatedSelectList);

      // 清空选择
      selectedProductIds.value = [];
      proxy.$refs.productTableRef.clearSelection();

      ElMessage.success(`成功删除 ${selectedIds.size} 个产品`);
    })
    .catch(() => {
      // 用户取消操作
    });
}

// 反选
function handleInverseSelection() {
  const tableRef = proxy.$refs.productTableRef;
  if (!tableRef) return;

  // 获取当前选中的产品id
  const currentSelectedIds = new Set(selectedProductIds.value);

  // 清空当前选择
  tableRef.clearSelection();

  // 选中未选中的行
  selectedProducts.value.forEach(row => {
    if (!currentSelectedIds.has(row.id)) {
      tableRef.toggleRowSelection(row, true);
    }
  });
}

// 提交询价
function submitInquiry() {
  proxy.$refs.inquiryFormRef.validate((valid, done) => {
    if (valid) {
      // 检查是否选择了产品
      const selectedProductList = selectedProducts.value;

      if (selectedProductList.length === 0) {
        ElMessage.warning('请至少选择一个产品进行询价');
        return done();
      }

      const inquiryData = {
        ...inquiryForm.value,
        offerId: props.offerId,
        businessOpportunityId:props.businessOpportunityId,
        optionId: props.optionId,
        inquiryPerson: null,
        type: props.offerId?2:props.optionId?1:null,
        supplierOfferDetailDTOList: selectedProductList,
      };

      axios
        .post('/api/vt-admin/supplierOffer/add', inquiryData)
        .then(res => {
          ElMessage.success('询价创建成功');
          addInquiryDrawer.value = false;
          done();
          getInquiryList(); // 刷新询价列表
          
          // 清空选中的产品
          selectedProducts.value = [];
          selectedProductIds.value = [];
          
          // 同步清空外部selectList
          emit('update:selectList', []);
        })
        .catch(err => {
          done();
          ElMessage.error('询价创建失败');
          console.error('询价创建失败:', err);
        });
    }
  });
}

// 获取状态类型
function getStatusType(status) {
  const statusMap = {
    0: 'info', // 草稿
    1: 'success', // 已通过
  };
  return statusMap[status] || 'info';
}

// 获取状态文本
function getStatusText(status) {
  const statusMap = {
    0: '待供应商报价',
    1: '已报价',
  };
  return statusMap[status] || '未知';
}

// 暴露方法给父组件
defineExpose({
  getInquiryList,
  handleAddInquiry
});
</script>

<style scoped>
/* 询价卡片样式 */
.inquiry-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 16px;
}

.inquiry-card {
  min-height: 120px;
  position: relative;
  width: 100%;
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.inquiry-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
}

.inquiry-time {
  margin: 4px 0;
  color: #909399;
  font-size: 12px;
}

.multiple-offers {
  margin-top: 12px;
}

.offer-count {
  margin: 8px 0;
  color: #606266;
  font-size: 13px;
  font-weight: 500;
}

.offer-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.offer-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #f5f7fa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

.offer-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.offer-name {
  font-size: 13px;
  color: #303133;
  font-weight: 500;
}

.offer-time {
  font-size: 11px;
  color: #909399;
}

.offer-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.offer-buttons {
  display: flex;
  gap: 4px;
}

.add-card {
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-inquiry-card {
  height: 100%;
  width:  100%;
  cursor: pointer;
  transition: all 0.3s;
  border: 2px dashed #dcdfe6;
}

.add-inquiry-card:hover {
  border-color: #409eff;
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
}

.add-content p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.inquiry-form-section {
  margin-bottom: 24px;
}

.inquiry-form-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.inquiry-products-section {
  margin-bottom: 24px;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.products-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.batch-actions {
  display: flex;
  gap: 8px;
}
/* 询价卡片样式 */
.inquiry-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  padding: 16px;
}

.inquiry-card {
  min-height: 120px;
  position: relative;
}

.card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.inquiry-info h4 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.inquiry-time {
  margin: 8px 0;
  color: #606266;
  font-size: 14px;
}

/* 多个报价版本展示样式 */
.multiple-offers {
  margin-top: 8px;
}

.offer-count {
  margin: 4px 0;
  color: #909399;
  font-size: 12px;
  font-weight: 500;
}

.offer-list {
  /* max-height: 120px; */
  overflow-y: auto;
}

.offer-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 10px;
  margin: 4px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
  border-left: 3px solid #409eff;
}

.offer-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 8px;
}

.offer-name {
  color: #303133;
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.offer-time {
  color: #909399;
  font-size: 12px;
  white-space: nowrap;
}

.offer-actions {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 4px;
}

.offer-status {
  flex-shrink: 0;
}

.offer-buttons {
  display: flex;
  gap: 4px;
}

.offer-buttons .el-button {
  margin: 0;
  padding: 2px 8px;
  font-size: 12px;
}

.single-offer {
  margin-top: 8px;
}

.inquiry-footer {
  margin-top: 12px;

  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-top: 1px solid #ebeef5;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 5px;
}

.inquiry-footer .el-button {
  margin: 0;
}

.inquiry-footer .el-button--text {
  color: #606266;
}

.inquiry-footer .el-button--text:hover {
  color: #409eff;
}

.add-inquiry-card {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 2px dashed #dcdfe6;
  background-color: #fafafa;
}

.add-inquiry-card:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.add-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #409eff;
}

.add-content p {
  margin: 8px 0 0 0;
  font-size: 14px;
  font-weight: 500;
}

.inquiry-card .el-card {
  height: 100%;
}

.inquiry-card .el-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 新增询价抽屉样式 */
.add-inquiry-content {
  padding: 16px;
}

.inquiry-form-section {
  margin-bottom: 24px;
  padding: 16px;
  /* background-color: #f8f9fa; */
  border-radius: 8px;
}

.inquiry-form-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.inquiry-products-section {
  padding: 16px;
  /* background-color: #f8f9fa; */
  border-radius: 8px;
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.products-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.batch-actions {
  display: flex;
  gap: 8px;
}

.batch-actions .el-button {
  margin: 0;
}

.inquiry-products-section .el-table {
  margin-top: 0;
}

/* 询价详情抽屉样式 */
.inquiry-detail-content {
  padding: 16px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h3 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  border-bottom: 2px solid #409eff;
  padding-bottom: 8px;
}

.detail-section .el-descriptions {
  margin-bottom: 16px;
}

.detail-section .el-table {
  margin-top: 16px;
}
</style>