<template>
  <basic-container>
    <el-container>
      <el-aside
        width="250px"
        style="margin-right: 20px; height: calc(100% - 30px); overflow: hidden"
      >
        <avue-tree
          :option="treeOption"
          :data="treeData"
          @update="treeUpdate"
          @save="treeSave"
          node-key="id"
          ref="tree"
          @del="treeDel"
          
          @node-click="handleNodeClick"
          v-model="treeForm"
        >
        </avue-tree>
      </el-aside>
      <el-main>
        <avue-crud
         v-if="currentNode.id"
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @on-load="onLoad"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          @row-click="
            row => {
              $refs.crud.rowEdit(row, row.$index);
            }
          "
          :before-open="beforeOpen"
          
          @keyup.enter="onLoad"
          @row-del="rowDel"
          @search-reset="onLoad"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          v-model="form"
        >
          <template #menu="{ row, index }">
            <el-button type="primary" text icon="edit" @click.stop="handleEdit(row)"
              >编辑</el-button
            >
            <el-button type="danger" text icon="delete" @click.stop="$refs.crud.rowDel(row, index)"
              >删除</el-button
            >
          </template>
          <template #templateName="{ row }">
            <el-link @click="toDetail(row)" type="primary">{{ row.templateName }}</el-link>
          </template>
          <!-- <template #templateCategory-form>
        <templateTagSelect v-model="form.templateCategory"></templateTagSelect>
      </template>
      <template #templateCategory-search>
        <templateTagSelect  v-model="params.templateCategory"></templateTagSelect>
      </template> -->
          <template #templateCategory="{ row }">
            <div v-if="row.templateCategory" style="display: flex; flex-wrap: wrap">
              <el-tag effect='plain'
                style="margin: 2px"
                size="small"
                v-for="item in row.$templateCategory?.split('|') || []"
                :key="item"
                >{{ item }}</el-tag
              >
            </div>
          </template>
        </avue-crud>
        <el-empty v-else description="请选择左侧分类"></el-empty>
      </el-main>
    </el-container>

    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="分类模板" v-model="addVisible" size="50%"> </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import templateTagSelect from '../programme/compoents/templateTagSelect.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 150,
  border: true,
  column: [
    {
      label: '模板名称',
      prop: 'templateName',
      search: true,
      width: 300,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入模板名称',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务类型',
      // multiple: true,
      span: 12,
      width: 250,
      overHidden: true,
      parent: true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      search: true,
      display: true,
      filterable: true,
      prop: 'businessType',
      checkStrictly: true,
    },

    {
      label: '模板描述',
      prop: 'remark',
      type: 'textarea',
      search: true,
      span: 24,
    },
    {
      label: '创建人',
      display: false,
      prop: 'createUserName',
      width: 120,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      display: false,
      width: 160,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/optionTemplate/save';
const delUrl = '/api/vt-admin/optionTemplate/remove?ids=';
const updateUrl = '/api/vt-admin/optionTemplate/updateInfo';
const tableUrl = '/api/vt-admin/optionTemplate/page';
let params = ref({});
let tableData = ref([{}]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        templateType: 3,
        templateCategory: currentNode.value.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    templateType: 3,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function handleEdit(row) {
  router.push({
    path: '/CRM/programme/compoents/addOrUpdateTemplate',
    query: {
      id: row.id,
      name: '编辑报价模板',
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function toDetail(row) {
  router.push({
    path: '/CRM/programme/compoents/addOrUpdateTemplate',
    query: {
      id: row.id,
      name: row.templateName,
    },
  });
}
let addVisible = ref(false);

onMounted(() => {
  getTreeData();
});
let treeData = ref([]);
function getTreeData() {
  axios.get('/api/vt-admin/templateCategory/tree?level=3').then(res => {
    treeData.value = res.data.data;
    console.log(treeData.value);
    proxy.$nextTick(() => {
      proxy.$refs.tree.setCurrentKey(treeData.value[0].id);
      handleNodeClick(treeData.value[0]);
    });
  });
}

let treeOption = ref({
  defaultExpandAll: true,
  // menu: false,
  // addBtn: false,
  formOption: {
    labelWidth: 100,
    column: [
      {
        label: '分类名称',
        prop: 'categoryName',
        // width: 250,
        overHidden: true,
        align: 'left',
        search: true,
        rules: [
          {
            required: true,
            message: '请填写分类名称',
            trigger: 'blur',
          },
        ],
      },
      // {
      //   label: '备注',
      //   prop: 'remark',
      //   // width: 250,
      //   overHidden: true,
      //   // search: true,
      // },
    ],
  },
  props: {
    label: 'categoryName',
    children: 'children',
    value: 'id',
  },
});
let currentNode = ref({
  id:null
});
let treeForm = ref({});
function handleNodeClick(node) {
  console.log(node);
  currentNode.value = node;
  onLoad();
  form.value.templateType = currentNode.value.level == 0 ? 0 : currentNode.value.level == 1 ? 2 : 3;
}
function treeSave(node, data, done, loading) {
  console.log(node, data);
  const value = {
    ...data,
    level:3,
    parentId: node.data?.id,
  };
  axios
    .post('/api/vt-admin/templateCategory/save', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
      parentId.value = 0;
    });
}
function treeUpdate(node, data, done, loading) {
  const value = {
    ...data,
  };
  axios
    .post('/api/vt-admin/templateCategory/update', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        // onLoad();
        getTreeData;
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function treeDel(data, done) {
  console.log(data);
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/templateCategory/remove?ids=' + data.data.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getTreeData();
        done(form);
      });
    })
    .catch(() => {});
}
function beforeOpen(done, type) {
  if (['add'].includes(type)) {
    form.value.templateType =
      currentNode.value.level == 0 ? 0 : currentNode.value.level == 1 ? 2 : 3;
    form.value.templateCategory = currentNode.value.id;
  }
  done();
}
// function getPermission(key, data) {
//   if (key == 'addBtn' && data.level == 3) {
//     console.log(data);
//     return false;
//   } else {
//     return true;
//   }
// }
</script>

<style lang="scss" scoped></style>
