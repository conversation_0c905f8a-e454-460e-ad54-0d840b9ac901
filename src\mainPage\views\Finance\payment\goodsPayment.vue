<template>
  <div class="project-main">
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex == index ? 'active' : ''"
        @click="handleTabClick(item, index)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="tab-main" >
      <goodsPlan v-if="tabIndex == 0"></goodsPlan>
      <goodsHistory v-if="tabIndex == 1"></goodsHistory>
    </div>
  </div>
</template>

<script>
import goodsPlan from './goodsPlan.vue'
import goodsHistory from './goodsHistory.vue'
export default {
  components: {
    goodsPlan,
    goodsHistory
  },
  data() {
    return {
      // 标签索引
      tabIndex: 0,
      tabs: [
        {
          value: 0,
          label: '采购付款',
        },
        {
          value: 1,
          label: '付款记录',
        },
       
      ],
    };
  },
  activated() {
    this.$refs.execute.contractStatus = this.contractStatus;
    // if (this.tabIndex == 0) {
    //   this.$refs.execute.contractStatus = null;
    // }
    this.$refs.execute.onLoad();
  },
  methods: {
    handleTabClick(item, index) {
      const { value } = item;
      if (this.tabIndex == index) {
        return;
      }
      this.tabIndex = index;
    },
  },
};
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin: 0 6px 0 7px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 27px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        height: 33px;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
</style>

