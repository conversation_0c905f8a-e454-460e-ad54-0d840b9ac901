<template>
    <div style="margin: 0 5px">
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @current-change="onLoad"
        @refresh-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
        <template #menu="{ row }">
          <el-button type="primary" text @click="topage" icon="more"> 更多</el-button>
        </template>
      </avue-crud>
    </div>
    <dialogForm ref="dialogForm"></dialogForm>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  import moment from 'moment'
  let option = ref({
    // height: 'auto',
    align: 'center',
    addBtn: false,
    header: false,
    search: false,
    size:'small',
    editBtn: false,
    delBtn: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    menu: true,
    searchSpan: 4,
    menuWidth: 100,
    border: true,
    column: [
      {
        label: '商机名称',
        prop: 'name',
        width: 250,
        overHidden: true,
      },
      {
        label: '商机描述',
        prop: 'description',
        overHidden: true,
      },
      {
      // type: 'tree',
      label: '业务板块',
      multiple: true,
      width: 100,
      span: 12,
      parent: false,
      overHidden: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      display: true,
      filterable: true,
      prop: 'type',
      checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
    },
    {
      label: '登记时间',
      width: 135,
      prop: 'createTime',
     
      type: 'datetime',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
      
    ],
  });
  let form = ref({});
  let page = ref({
    pageSize:5,
    currentPage: 1,
    total: 0,
  });
  const props = defineProps({
    path: String,
  });
  let router = useRouter();
  const addUrl = '';
  const delUrl = '';
  const updateUrl = '';
  const tableUrl = '/api/vt-admin/businessOpportunity/page?selectType=0&stage=0';
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
          size,
          current,
          ...params.value,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        console.log(tableData.value);
        page.value.total = res.data.data.total;
      });
  }
  function topage(params) {
    router.push(props.path);
  }
  </script>
  
  <style lang="scss" scoped></style>
  