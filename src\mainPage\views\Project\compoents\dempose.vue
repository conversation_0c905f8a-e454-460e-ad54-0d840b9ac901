<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-button type="primary" @click="$refs?.crud.rowAdd" icon="plus">添加</el-button>
        <el-button type="primary" @click="$refs?.crud.rowAdd" icon="plus">从产品库添加</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps({
    modelValue:Array
});
const emits = defineEmits(['updateModelValue'])
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  size: 'small',
  dialogType: 'drawer',
  dialogWidth: '40%',
  menuWidth: 100,
  selection: false,
  column: [
    {
      label: '产品',
      prop: 'customProductName',

      cell: true,
      overHidden: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      cell: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      cell: true,
      overHidden: true,
      // search: true,
      span: 24,

      type: 'input',
    },
    {
      label: '单位',
      prop: 'customUnit',
      width: 90,
      span: 12,
      cell: true,
    },
    {
      label: '深化数量',
      prop: 'number',
      type: 'number',
      align: 'center',
      span: 12,
      width: 160,
      cell: true,
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      cell: true,
      width: 160,
      type: 'textarea',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});


let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);



function rowSave(form, done, loading) {
    const data = [...props.modelValue,{...form}]
    emits('updateModelValue',data)
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
