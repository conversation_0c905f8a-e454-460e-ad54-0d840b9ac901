import { createApp, ref } from 'vue';
import GlobalUploader from './index.vue';
import axios from 'axios';

// 创建全局上传组件实例
const createUploader = () => {
  const uploaderApp = createApp(GlobalUploader);
  const fragment = document.createDocumentFragment();
  const vm = uploaderApp.mount(fragment);
  document.body.appendChild(fragment);
  return vm;
};

// 上传服务
const uploadService = (() => {
  let uploaderInstance = null;

  const getUploader = () => {
    if (!uploaderInstance) {
      uploaderInstance = createUploader();
    }
    return uploaderInstance;
  };

  // 假设使用 axios 进行文件上传，需要先安装 axios：npm install axios
  const mockUpload = (file, onProgress, onSuccess, onError) => {
    let cancelUpload;

    const formData = new FormData();
    const { projectId, catalogueId, file: raw, fileName } = file.raw;

    formData.append('projectId', projectId);
    formData.append('catalogueId', catalogueId);
    formData.append('file', raw);
    formData.append('fileName', fileName);

    const config = {
      onUploadProgress: progressEvent => {
        const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
        onProgress(percentCompleted);
      },
      cancelToken: new axios.CancelToken(function executor(cancel) {
        // 这里将 cancel 函数暴露出去，用于取消请求
        cancelUpload = cancel;
      }),
    };

    axios
      .post('/api/blade-resource/attach/uploadProjectFile', formData, config)
      .then(response => {
        onSuccess(response.data);
      })
      .catch(error => {
        if (axios.isCancel(error)) {
          console.log('上传已取消:', error.message);
        } else {
          onError(error);
        }
      });

    return () => {
      if (cancelUpload) {
        cancelUpload('用户取消上传');
      }
    };
  };

  // 实际的上传方法
  const uploadFile = async (file, updateFileProgress, onFileComplete) => {
    const cancelUpload = mockUpload(
      file,
      progress => {
      
        updateFileProgress(file.id, progress, 'uploading');
      },
      () => {
        updateFileProgress(file.id, 100, 'success');
        if (onFileComplete) {
          onFileComplete(file, null);
        }
      },
      () => {
        updateFileProgress(file.id, 100, 'error');
        if (onFileComplete) {
          onFileComplete(file, new Error('上传失败'));
        }
      }
    );

    return 0;
  };

  // 更新文件状态的通用方法
  const updateFileProgress = (id, progress, status) => {
    const uploader = getUploader();
    const index = uploader.files.findIndex(f => f.id === id);
    if (index !== -1) {
      uploader.files[index].progress = progress;
      uploader.files[index].status = status;
      uploader.files = [...uploader.files];
    }
  };

  // 公开的上传方法
  const uploadFiles = (fileList, options = {}) => {
    const { onAllComplete, ...otherOptions } = options;
    const uploader = getUploader();
    uploader.visible = true;

    const files = fileList.map(file => ({
      id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      fileName: file.fileName,
      raw: file,
      progress: 0,
      status: 'pending',
    }));

    uploader.files = [...uploader.files, ...files];

    let cancelFunctions = [];

    const uploadSequentially = async () => {
      for (const file of files) {
        try {
          await new Promise((resolve, reject) => {
            const cancel = uploadFile(
              file,
              (id, progress, status) => {
                const index = uploader.files.findIndex(f => f.id === id);
                if (index !== -1) {
                  uploader.files[index].progress = progress;
                  uploader.files[index].status = status;
                  if (status === 'success') {
                    uploader.files[index].shouldRemove = true;
                  }
                  uploader.files = [...uploader.files];
                }
              },
              (completedFile, error) => {
                if (options.onFileComplete) {
                  options.onFileComplete(completedFile, error);
                }
                error ? reject(error) : resolve();
              }
            );
            cancelFunctions.push(cancel);
          });
        } catch (error) {
          console.error('文件上传失败:', error);
          continue;
          // 移除此处的break语句
        }
      }

      // 触发全局完成回调
      if (options.onAllComplete) {
        options.onAllComplete();
      }
      // 自动关闭上传面板
      // uploadService.hideUploader();
    };

    return {
      cancelAll: () => {
        cancelFunctions.forEach(cancel => {
          if (typeof cancel === 'function') {
            cancel();
          }
        });
      },
      allDone: uploadSequentially(),
    };
  };

  return {
    uploadFiles,
    retryUpload: file => {
      const uploader = getUploader();
      const index = uploader.files.findIndex(f => f.id === file.id);
      if (index !== -1) {
        uploader.files[index].progress = 0;
        uploader.files[index].status = 'pending';
        uploader.files = [...uploader.files];
        uploadFile(file, updateFileProgress);
      }
    },
    showUploader: () => {
      const uploader = getUploader();
      uploader.visible = true;
    },
    hideUploader: () => {
      const uploader = getUploader();
      uploader.visible = false;
    },
  };
})();

export default uploadService;
