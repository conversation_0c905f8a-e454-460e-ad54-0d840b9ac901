<template>
  <el-dialog title="产品选择" class="avue-dialog avue-dialog--top" v-model="visible" append-to-body>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @selection-change="selectionChange"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @row-click="rowClick"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      
      <template #objectNumber="{ row }">
        <el-input-number
          @click.stop="void"
          v-model="row.needNumber"
          :min="1"
         
          v-show="
            selectList.find(item => {
              return item.id == row.id;
            })
          "
        ></el-input-number>
      </template>
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button @click="handleConfirm" type="primary">确 定</el-button>
    </div>
  </el-dialog>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const props = defineProps({
 
  purchaseContractId: {
    type: String,
    default: '',
  },
  url: {
    type: String,
    default: '/api/vt-admin/purchaseContract/detail',
  },
});
console.log(props.id);
let option = ref({
  // height: 'auto',
  align: 'center',
  menu: false,
  addBtn: false,
  editBtn: true,
  header: false,
  delBtn: true,
  reserveSelection: true,
  // calcHeight: 30,
  searchMenuSpan: 8,
  selection: true,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden:true,
      bind:'productVO.productName'
    },

    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true, bind:'productVO.productSpecification',
      // search: true,
    
      span: 24,
      type: 'input',
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden:true, bind:'productVO.productBrand',
    },

    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
   
  
    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',

      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,

      cell: false,
    },
   
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const visible = ref(false);

const emits = defineEmits(['confirm']);

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();

let loading = ref(false);
function onLoad() {

  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(props.url, {
      params: {
        size,
        current,
        ...params.value,
        id: props.purchaseContractId,
       
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.detailVOList
      // page.value.total = res.data.data.total;
    });
}
function open() {
  visible.value = true;
  proxy.$nextTick(() => {
    proxy.$refs.crud.clearSelection();
    onLoad();
  });
}
function close(params) {
  visible.value = false;
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list.map(item => {
    return {
      ...item,
      // needNumber: item.needNumber || 1,
    };
  });
  emits('select', selectList.value);
}
let router = useRouter();
function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm() {
  visible.value = false;
  
  getSelectTableData();
  
  emits('confirm', getSelectTableData());
}
function getSelectTableData() {
  return selectList.value.reduce((pre, item) =>{
    let i 
    i = tableData.value.find(i => i.id == item.id)
    pre.push(i)
    return pre
  },[])
}
function reset() {
  proxy.$nextTick(() => {
    proxy.$refs.crud.toggleSelection(selectList.value);
  });
}
function rowClick(row) {
  console.log(row);
  if (row.isInvoice == 1) return;
  proxy.$refs.crud.toggleSelection([row]);
}
defineExpose({
  open,
  reset,
});
</script>

<style lang="scss" scoped></style>
