import { ElNotification } from 'element-plus';
import { h } from 'vue';
import progress from '@/components/progress/index.vue';
import { downloadOwn, downloadXls as downloadExcel } from './util';
export const download = (url, params, c) => {
  let notice = ElNotification({
    title: '下载中',
    position: 'bottom-right',
    duration: 0,
    message: h(progress, {
      // 事件要以onXxx的形式书写
      onFinish: status => {
        if (status.value == 'ok') {
          notice.close(); // 关闭ElNotification
        }
      },
    }),
  });

  axios
    .post(url + '?id=' + params.id,params)
    .then(res => {
      // 获取当前网页的域名，包含协议部分
      const currentOrigin = window.location.origin;
      // 正则表达式匹配协议和域名部分
      const domainRegex = /https?:\/\/[^\/]+/;
      // 替换 res.data.data 中的域名
      res.data.data = res.data.data.replace(domainRegex, currentOrigin);
      axios
        .get(res.data.data, {
          responseType: 'blob',
          withCredentials: false,
          onDownloadProgress(e) {},
        })
        .then(r => {
          console.log(r);

          const name = res.data.data.split('/').pop();
          downloadOwn(r.data, name);
          notice.close();

          if (c) {
            c();
          }
        })
        .catch(err => {
          console.log(err);
          notice.close();
        });
    })
    .catch(err => {
      notice.close();
    });
};
export const downloadByUrl = (url, params, method = 'post') => {
  let notice = ElNotification({
    title: '下载中',
    position: 'bottom-right',
    duration: 0,
    message: h(progress, {
      // 事件要以onXxx的形式书写
      onFinish: status => {
        if (status.value == 'ok') {
          notice.close(); // 关闭ElNotification
        }
      },
    }),
  });
     // 获取当前网页的域名，包含协议部分
     const currentOrigin = window.location.origin;
     // 正则表达式匹配协议和域名部分
     const domainRegex = /https?:\/\/[^\/]+/;
     // 替换 res.data.data 中的域名
     url = url.replace(domainRegex, currentOrigin);
  axios(url, {
    responseType: 'blob',
    withCredentials: false,
    params: params,
    data:params,
    method: method,
    onDownloadProgress(e) {},
  })
    .then(r => {
      let name = decodeURI(r.headers['content-disposition']).split('=')[1];
      downloadOwn(r.data, name);
      notice.close();

      if (c) {
        c();
      }
    })
    .catch(err => {
      console.log(err);
      notice.close();
    });
};
export const downloadXls = ({ url, params, data, method = 'get' }) => {
  let notice = ElNotification({
    title: '下载中',
    position: 'bottom-right',
    duration: 0,
    message: h(progress, {
      // 事件要以onXxx的形式书写
      onFinish: status => {
        if (status.value == 'ok') {
          notice.close(); // 关闭ElNotification
        }
      },
    }),
  });
  // 获取当前网页的域名，包含协议部分
  const currentOrigin = window.location.origin;
  // 正则表达式匹配协议和域名部分
  const domainRegex = /https?:\/\/[^\/]+/;
  // 替换 res.data.data 中的域名
  url = url.replace(domainRegex, currentOrigin);
  axios({
    method,
    url: url,
    params,
    data,
    responseType: 'blob',
    withCredentials: false,
  })
    .then(res => {
      console.log(res);
      let name = decodeURI(res.headers['content-disposition']).split('=')[1];
      let type = res.headers['content-disposition'].split('.')[1];
      downloadExcel(res.data, name);
      notice.close();
    })
    .catch(err => {
      notice.close();
    });
};
