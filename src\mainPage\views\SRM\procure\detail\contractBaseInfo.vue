<template>
  <!-- 基本信息 -->
  <div>
    <avue-form :option="detailOption" @submit="handleSubmit" :modelValue="props.form">
      <template #contractFiles> <File :fileList="form.attachList || []"></File> </template
    ></avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { businessOpportunityData } from '@/const/const';
const props = defineProps({
  form: Object,
  isEdit: Boolean,
});
const form1 = ref({});

watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
});
const route = useRoute();
let detailOption = ref({
  labelWidth: 150,
  submitBtn: false,
  detail: true,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '合同编号',
          prop: 'contractCode',
          width: 250,
          overHidden: true,
          search: !route.query.id,
        },
        {
          label: '关联供应商',
          prop: 'supplierName',
          overHidden: true,
          searchLabelWidth: 100,
          search: !route.query.id,
        },

        {
          label: '关联商机',
          prop: 'businessOpportunityName',
          overHidden: true,
          search: !route.query.id,
        },
        {
          label: '关联报价',
          prop: 'offerName',
          overHidden: true,
          search: !route.query.id,
        },
        {
          label: '采购数量',
          prop: 'purchaseNumber',
        }, {
          label: '优惠金额',
          prop: 'discountPrice',
        },
      
        // {
        //   label: '已付款金额',
        //   prop: 'totalmoney',
        // },
        {
          label: '采购日期',
          type: 'datetime',
          prop: 'purchaseDate',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm',
        },
        {
          label: '合同总额',
          prop: 'contractPrice',
        },
        {
          label: '到货日期',
          type: 'datetime',
          prop: 'arriveDate',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm',
        },
        {
          label: '到货状态',
          prop: 'arriveStatus',
          type: 'select',
          dicData: [
            {
              label: '未到货',
              value: 0,
            },
            {
              label: '部分到货',
              value: 1,
            },
            {
              label: '全部到货',
              value: 2,
            },
          ],
        },
        // {
        //   label: '付款状态',
        //   type: 'payStatus',
        //   prop: 'select',
        //   dicData: [
        //     {
        //       label: '未付款',
        //       value: '0',
        //     },
        //     { label: '已付款', value: '1' },
        //   ],
        // },
        {
          label: '附件',
          prop: 'contractFiles',
          span: 24,
        },
        {
          label: '备注',
          prop: 'remark',
          span: 24,
        },
      ],
    },
    {
      label: '供应商信息',
      column: [
        {
          label: '供应商名称',
          prop: 'supplierName',
          width: 250,
          overHidden: true,
          search: true,
        },
        // {
        //   label: '联系人',
        //   prop: 'contact',
        // },
        // {
        //   label: '联系电话',
        //   prop: 'contactPhone',
        // },
        {
          label: '供应商地址',
          prop: 'address',
          overHidden: true,
        },

        {
          type: 'select',
          label: '供应商分类',
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择供应商分类',
            },
          ],
          display: true,
          prop: 'supplierClassify',
          dicUrl: '/blade-system/dict/dictionary?code=supplierClassify',
        },
        {
          type: 'input',
          label: '供应商特色',
          span: 12,

          display: true,
          prop: 'supplierFeature',
        },
        {
          label: '供应商网址',
          prop: 'webUrl',
        },
        {
          type: 'radio',
          label: '账期',
          cascader: [],
          rules: [
            {
              required: true,
              message: '请选择账期',
            },
          ],
          span: 24,
          display: true,
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentMethod',
          dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
          remote: false,
        },
      ],
    },
    {
      label: '送货信息',
      column: [
        {
          label: '送货地址',
          type: 'input',
          prop: 'deliveryAddress',
          span: 24,
        },
        {
          label: '送货方式',
          type: 'radio',
          prop: 'deliveryMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=delivery_method',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '付款期限',
          type: 'radio',
          span: 15,
          prop: 'paymentTerm',
          dicUrl: '/blade-system/dict/dictionary?code=payment_term',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
        {
          label: '自定义账期',
          span: 9,

          prop: 'fixedBillingDate',
        },
        {
          label: '付款方式',
          type: 'radio',
          prop: 'paymentMethod',
          span: 24,
          dicUrl: '/blade-system/dict/dictionary?code=payment_methods',
          props: {
            value: 'id',
            label: 'dictValue',
          },
        },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: route.query.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
</script>

<style lang="scss" scoped></style>
