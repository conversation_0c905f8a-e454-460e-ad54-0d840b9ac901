<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #menu="{ row }">
      <el-button type="primary" text size="small" icon="el-icon-view" @click="handleView(row)"
        >详情</el-button
      >
      <el-button
        text
        :type="row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'"
        icon="el-icon-download"
        @click="download(row)"
        >下载报价单</el-button
      >
      <el-button text icon="Operation" type="primary" v-if="props.currentStatus" @click="transCurrent(row)"
        >转为当前版本</el-button
      >
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { downloadOwn } from '@/utils/util';
import { ElNotification } from 'element-plus';
import progress from '@/components/progress/index.vue';
let option = ref({
  //   height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  search: false,
  //   calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 6,
  menuWidth: 300,
  border: true,
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      width: 250,
      overHidden: true,
    },

    {
      label: '生成时间',
      prop: 'createTime',

      searchSpan: 6,
      searchRange: true,
      type: 'date',
    },
    {
      label: '报价版本',
      prop: 'version',
      type: 'input',
      formatter: row => {
        return `v${row.version}.0`;
      },
    },
 
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/offerHistory/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  currentStatus:{
    type:Boolean,
  }
});
let loading = ref(false);
watchEffect(() => {
  if (props.id) {
    onLoad();
  }
});

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        offerId: props.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleView(row) {
  if (row.dataJson && row.isHasOption != 1) {
    router.push({
      path: '/CRM/quotation/compoents/add',
      query: {
        id: row.id,
        name: row.offerName,
        type: 'detail',
      },
    });
  } else if (row.dataJson && row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.name,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  } else if(!row.dataJson && row.isHasOption == 0) {
    router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        type: 'detail',
        name: row.offerName + `(v${row.version}.0)`,
        isHistory:1,
        businessOpportunityId: row.isHasOption == 1 ? row.businessOpportunityId : null,
      },
    });
  }else{
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.name,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  }
}
function download(row) {
  let notice = ElNotification({
    title: '下载中',
    position: 'bottom-right',
    duration: 0,
    message: h(progress, {
      // 事件要以onXxx的形式书写
      onFinish: status => {
        if (status.value == 'ok') {
          notice.close(); // 关闭ElNotification
        }
      },
    }),
  });

  axios
    .post('/api/vt-admin/offerHistory/downloadOffer?id=' + row.id)
    .then(res => {
      axios
        .get(res.data.data, {
          responseType: 'blob',
          onDownloadProgress(e) {},
        })
        .then(r => {
          const name = res.data.data.split('/').pop();
          downloadOwn(r.data, name);
          notice.close();
          onLoad();
        })
        .catch(err => {
          notice.close();
          proxy.$message.error('下载失败');
        });
    })
    .catch(err => {
      notice.close();
    });
}
function transCurrent(row) {
  proxy.$confirm('此操作将会覆盖当前版本的数据，请先去保存当前数据为历史版本，如已保存请跳过', '提示', {
      confirmButtonText: '确定',
      
      type: 'warning',
    })
    .then(res => {
      router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        name: row.offerName,
        type: 'edit',
        isTranslate: 1,
      },
    });
    });
}
</script>

<style lang="scss" scoped></style>
