<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #menu-left="{}">
      <el-button type="warning" @click="exportData" icon="download" v-if="$store.getters.permission.ticketDetail_export" plain>导出</el-button>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">发票总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">不含税总额：</el-text>
        <el-text type="primary" size="large"
          >￥{{ (noTaxPrice * 1).toLocaleString() }}</el-text
        >
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">税额总额：</el-text>
        <el-text type="primary" size="large"
          >￥{{ (taxPrice * 1).toLocaleString() }}</el-text
        >
    
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { downloadXls } from '@/utils/download';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchIcon: true,
  searchIndex: 4,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  menu: false,

  column: [
    {
      type: 'input',
      label: '发票号码',
      span: 12,
      display: true,
      width: 180,
      search: true,
      overHidden: true,
      prop: 'invoiceNumber',
    },
    {
      type: 'select',
      label: '发票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      search: true,
      width: 120,
      display: true,
      overHidden: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [
        {
          required: true,
          message: '请选择发票类型',
        },
      ],
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      width: 120,
      display: true,
      format: 'YYYY-MM-DD',
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      display: false,
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDateRange',

      formatter: row => {
        return row.invoiceDate;
      },
    },
    {
      type: 'input',
      label: '销方名称',
      span: 24,
      display: true,
      width: 160,
      overHidden: true,
      search: true,
      prop: 'supplierName',

      // hide: true,
    },
    {
      label: '货物、应税劳务及服务',
      prop: 'productName',
    },
    {
      label: '规格型号',
      prop: 'specification',
    },
    {
      label: '单位',
      prop: 'unitName',
      width: 80,
    },
    {
      label: '数量',
      prop: 'number',
      width: 80,
      formatter:row => {
        return parseFloat(row.number) ;
      }
    },
    {
      label: '单价',
      prop: 'price',
      width: 100,
      overHidden: true,
    },
    {
      label: '不含税金额',
      prop: 'productTotalPrice',
      width: 120,
    },
    {
      label: '税率',
      type: 'select',
      cell: true,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      search: true,
      width: 100,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter:row => {
        return parseFloat(row.taxRate) + '%';
      }
    },
    {
      label: '税额',
      prop: 'taxPrice',
      width: 120,
    },
    {
      label: '价税合计',
      prop: 'totalPrice',
      width: 120,
    },
      {
          label: '收票公司',
          type: 'select',
          prop: 'billingCompany',
          props: {
            label: 'companyName',
            value: 'id',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span:24,
          search: true,
          hide: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/purchaseContractInvoiceDetail/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalPrice = ref(0);

let taxPrice = ref(0);
let noTaxPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDateRange && params.value.invoiceDateRange[0],
        invoiceEndDate: params.value.invoiceDateRange && params.value.invoiceDateRange[1],
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
      // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseContractInvoiceDetail/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDateRange && params.value.invoiceDateRange[0],
        invoiceEndDate: params.value.invoiceDateRange && params.value.invoiceDateRange[1],
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    
      noTaxPrice.value = res.data.data.noTaxPrice;
      taxPrice.value = res.data.data.taxPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function exportData() {
  const { pageSize: size, currentPage: current } = page.value;
  downloadXls({
    url: '/api/vt-admin/purchaseContractInvoiceDetail/exportList',
    method: 'post',
    params: {
      size,
        current,
        ...params.value,
        invoiceStartDate: params.value.invoiceDateRange && params.value.invoiceDateRange[0],
        invoiceEndDate: params.value.invoiceDateRange && params.value.invoiceDateRange[1],
    },
  });
}
</script>

<style lang="scss" scoped></style>
