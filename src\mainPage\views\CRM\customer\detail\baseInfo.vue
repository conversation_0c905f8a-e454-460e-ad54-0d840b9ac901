<template>
  <el-row :gutter="20">
    <el-col :span="16">
      <el-card shadow="never">
        <!-- 基本信息 -->
        <div>
          <avue-form
            v-if="!isEdit"
            :option="detailOption"
            @submit="handleSubmit"
            :modelValue="props.form"
          ></avue-form>
          <avue-form v-else :option="editOption" @submit="handleSubmit" v-model="form1">
            <template #customerName>
              <el-autocomplete
                style="width: 100%"
                v-model="form1.customerName"
                :fetch-suggestions="querySearch"
                :trigger-on-focus="false"
                value-key="customerName"
                placeholder="请输入客户名称"
              ></el-autocomplete>
            </template>
          </avue-form>
        </div>
      </el-card>
    </el-col>
    <el-col :span="8">
      <el-card shadow="never" style="height: 100%" :body-style="{ padding: 0 }">
        <Title
          >跟进信息
          <template #foot>
            <el-button type="primary" icon="plus" size="small" @click="handleAddFollow"
              >新增跟进</el-button
            >
          </template>
        </Title>
        <div style="height: 500px; overflow-y: auto; overflow-x: hidden">
          <el-timeline>
            <el-timeline-item
              center
              v-for="(item, index) in followList"
              :timestamp="item.createTime"
              placement="top"
            >
              <el-form>
                <el-form-item label="跟进人:" style="margin: 0; color: var(--el-color-primary)"
                  >{{ item.followName }}
                  <el-link
                    style="margin-left: 10px"
                    @click="viewDetail(item)"
                    size="small"
                    type="primary"
                    ><el-icon><View /></el-icon>
                  </el-link>
                </el-form-item>
                <el-form-item style="margin: 0; white-space: wrap" label="跟进类型">
                  <el-tag effect="plain" type="primary">
                    {{ followType.find(i => i.value === item.followType).label }}
                  </el-tag>
                </el-form-item>
                <el-form-item label="跟进记录:" style="margin: 0; white-space: wrap">
                  <el-tooltip :content="item.followContent" placement="">
                    {{ item.followContent }}
                  </el-tooltip>
                </el-form-item>
              </el-form>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-card>
    </el-col>
  </el-row>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { followData, followType } from '@/const/const';
const props = defineProps({
  form: {
    type: Object,
    default: () => {},
  },
  customerId: {
    type: String,
  },
  isEdit: Boolean,
});
const form1 = ref({});

watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
});
watchEffect(() => {
  // 设置账期固定是否显示
  console.log(props.form.isPaymentPeriodName, props.isEdit);
  if (props.form.isPaymentPeriodName || !!props.isEdit) {
    setDisplay();
  }
});
const route = useRoute();
const router = useRouter();
let isPaymentPeriodData = ref([]);
let detailOption = ref({
  labelWidth: 120,
  submitBtn: false,
  emptyBtn: false,
  detail: true,
  column: [
    {
      type: 'input',
      label: '客户名称',
      span: 12,
      display: true,
      prop: 'customerName',
      required: true,
    },
  
    {
      type: 'input',
      label: '联系人',
      span: 12,
      display: true,
      prop: 'contactPerson',
    },
    {
      label: '所在区域',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },

      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      type: 'input',
      label: '联系方式',
      span: 12,
      display: true,
      prop: 'contactPhone',
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
    },
    {
      type: 'input',
      label: '客户类型',
      cascader: [],

      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerTypeName',
    },
    {
      type: 'select',
      label: '是否账期',
      cascader: [],

      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'remark',
      },
      dicFormatter: res => {
        isPaymentPeriodData.value = res.data;
        return res.data;
      },
      // control: (val, form, b, c) => {
      //   // console.log(val, isPaymentPeriodData.value, form, form.$isPaymentPeriod);
      //   return {
      //     fixedBillingDate: {
      //       display: form.$isPaymentPeriod == '固定账期',
      //     },
      //   };
      // },
      prop: 'isPaymentPeriod',
      dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
      remote: false,
    },
    {
      type: 'number',
      label: '固定账期时间',
      span: 12,
      display: false,
      min: 1,
      max: 31,
      tip: '输入1到31之间的数字,账期则为每月这个时间',
      prop: 'fixedBillingDate',
      rules: [
        {
          required: true,
          message: '请输入固定账期时间',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'input',
      label: '客户级别',
      cascader: [],

      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerLevelName',
    },
    {
      type: 'input',
      label: '推荐人',
      span: 12,
      display: true,
      prop: 'referrer',
    },
    // {
    //   type: 'input',
    //   label: '客户阶段',
    //   cascader: [],

    //   span: 12,
    //   display: true,
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },
    //   prop: 'customerStageName',

    // },
    {
      type: 'input',
      label: '客户来源',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'customerSourceName',
    },
    {
      type: 'number',
      label: '注册资金(万元)',
      span: 12,
      display: true,
      prop: 'registeredCapital',
      controls: false,

      precision: 2,
    },
    {
      type: 'select',
      label: '跟进状态',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'label',
        value: 'value',
      },
      prop: 'followStatus',
      dicData: followData,
      remote: false,
    },
    {
      type: 'input',
      label: '人员规模',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'staffSizeName',
    },
    {
      label: '跟进人',
      component: 'wf-user-select',
      span: 12,
      display: true,
      prop: 'follower',
    },
    {
      type: 'input',
      label: '所属行业',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'industryName',
    },
    {
      type: 'date',
      label: '下次跟进日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD hh:mm:ss',
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
      prop: 'nextFollowTime',
    },
    {
      type: 'input',
      label: '法人代表',
      span: 12,

      display: true,
      prop: 'legalRepresentative',
    },
   
   
    {
      type: 'input',
      label: '分公司',
      span: 12,

      display: true,
      prop: 'branchOffice',
    },
    {
      type: 'select',
      label: '采购方式',
      dicUrl: '/blade-system/dict/dictionary?code=procurementMethod',
      span: 24,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      display: true,
      prop: 'procurementMethod',
    },
    {
      type: 'textarea',
      label: '主营业务',
      span: 24,
      display: true,

      prop: 'mainBusiness',
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
let editOption = ref({
  labelWidth: 120,
  submitBtn: true,
  emptyBtn: true,
  detail: false,
  column: [
    {
      type: 'input',
      label: '客户名称',
      span: 12,
      display: true,
      prop: 'customerName',
      required: true,
      rules: [
        {
          required: true,
          message: '客户名称必须填写',
        },
      ],
    },
   
    {
      type: 'input',
      label: '联系人',
      span: 12,
      display: true,
      prop: 'contactPerson',
      rules: [
        {
          required: true,
          message: '联系人必须填写',
        },
      ],
    },
    {
      label: '所在区域',
      prop: 'province_city_area',
      search: true,
      hide: true,
      searchSpan: 5,
      type: 'cascader',
      props: {
        label: 'title',
        value: 'id',
      },
      rules: [
        {
          required: true,
          message: '所在区域必须填写',
          trigger: 'blur',
        },
      ],
      lazy: true,
      lazyLoad(node, resolve) {
        let stop_level = 2;
        let level = node.level;
        let data = node.data || {};
        let id = data.id;
        let list = [];
        let callback = () => {
          resolve(
            (list || []).map(ele => {
              return Object.assign(ele, {
                leaf: level >= stop_level || !ele.hasChildren,
              });
            })
          );
        };
        axios.get(`${baseUrl}?parentCode=${id || '00'}`).then(res => {
          list = res.data.data;
          callback();
        });
      },
    },
    {
      type: 'input',
      label: '联系电话',
      span: 12,
      display: true,
      prop: 'contactPhone',
      rules: [
        {
          required: true,
          message: '联系电话必须填写',
        },
      ],
    },
    {
      type: 'input',
      label: '详细地址',
      span: 12,
      display: true,
      prop: 'address',
    },
    {
      type: 'select',
      label: '客户类型',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择客户类型',
        },
      ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerType',
      dicUrl: '/blade-system/dict/dictionary?code=customer_type',
      remote: false,
    },
    {
      type: 'radio',
      label: '是否账期',
      cascader: [],
      rules: [
        {
          required: true,
          message: '请选择账期',
        },
      ],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      control: (val, form, b, c) => {
        return {
          fixedBillingDate: {
            display: isPaymentPeriodData.value.find(item => item.id == val).dictKey == 'fixed',
          },
        };
      },
      prop: 'isPaymentPeriod',
      dicUrl: '/blade-system/dict/dictionary?code=isPaymentPeriod',
      remote: false,
    },
    {
      type: 'number',
      label: '固定账期时间',
      span: 12,
      display: true,
      min: 1,
      max: 31,
      tip: '输入1到31之间的数字,账期则为每月这个时间',
      prop: 'fixedBillingDate',
      rules: [
        {
          required: true,
          message: '请输入固定账期时间',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'select',
      label: '客户级别',
      cascader: [],
     
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'customerLevel',
      dicUrl: '/blade-system/dict/dictionary?code=customerLevel',
      remote: false,
    },
    {
      type: 'input',
      label: '推荐人',
      span: 12,
      display: true,
      prop: 'referrer',
    },
    // {
    //   type: 'select',
    //   label: '客户阶段',
    //   cascader: [],
    //   rules: [
    //     {
    //       required: true,
    //       message: '请选择客户阶段',
    //     },
    //   ],
    //   span: 12,
    //   display: true,
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //     desc: 'desc',
    //   },
    //   prop: 'customerStage',
    //   dicUrl: '/blade-system/dict/dictionary?code=customer_stage',
    //   remote: false,
    // },
    {
      type: 'select',
      label: '客户来源',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },

      prop: 'customerSource',
      dicUrl: '/blade-system/dict/dictionary?code=customer_source',
      remote: false,
    },
    {
      type: 'number',
      label: '注册资金(万元)',
      span: 12,
      display: true,
      prop: 'registeredCapital',
      controls: false,
     
      precision: 2,
    },
    {
      type: 'select',
      label: '跟进状态',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'label',
        value: 'value',
      },
      prop: 'followStatus',
      dicData: followData,
      remote: false,
    },
    {
      type: 'select',
      label: '人员规模',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      
      prop: 'staffSize',
      dicUrl: '/blade-system/dict/dictionary?code=staffSize',
      remote: false,
    },
    {
      label: '跟进人',
      component: 'wf-user-select',
      span: 12,
      display: true,
      prop: 'follower',
    },
    {
      type: 'select',
      label: '所属行业',
      cascader: [],
      span: 12,
      display: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择所属行业',
        },
      ],
      prop: 'industry',
      dicUrl: '/blade-system/dict/dictionary?code=industry',
      remote: false,
    },
    {
      type: 'date',
      label: '下次跟进日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD hh:mm:ss',
      valueFormat: 'YYYY-MM-DD hh:mm:ss',
      prop: 'nextFollowTime',
    },
    {
      type: 'input',
      label: '法人代表',
      span: 12,
     
      display: true,
      prop: 'legalRepresentative',
    },
   
   
    {
      type: 'select',
      label: '采购方式',
      dicUrl: '/blade-system/dict/dictionary?code=procurementMethod',
      span: 24,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      display: true,
      prop: 'procurementMethod',
    },
    {
      type: 'textarea',
      label: '主营业务',
      span: 24,
      display: true,

      prop: 'mainBusiness',
    },
  ],
});

function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: props.customerId,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/customer/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
function setDisplay() {
  console.log(props.form.isPaymentPeriodName);
  const fixedBillingDateDetail = proxy.findObject(detailOption.value.column, 'fixedBillingDate');
  const fixedBillingDateEdit = proxy.findObject(editOption.value.column, 'fixedBillingDate');

  if (props.form.isPaymentPeriodName == '固定账期') {
    fixedBillingDateDetail.display = true;
    fixedBillingDateEdit.display = true;
  } else {
    fixedBillingDateDetail.display = false;
    fixedBillingDateEdit.display = false;
  }
}
function querySearch(val, cb) {
  if (!val) return;
  axios
    .get('/api/vt-admin/customer/list', {
      params: {
        size: 1000,
        customerName: val,
      },
    })
    .then(res => {
      cb(res.data.data.records);
    });
}
let followList = ref([]);

watch(
  () => props.customerId,
  () => {
    getFollowList();
  },
  {
    immediate: true,
  }
);
function getFollowList() {
  axios
    .get('/api/vt-admin/customerFollow/pageForDetail', {
      params: {
        size: 5000,

        customerId: props.customerId,
      },
    })
    .then(res => {
      followList.value = res.data.data.records;
    });
}
function handleAddFollow() {
  router.push({
    path: '/CRM/follow/compoents/update',
    query: {
      type: 0,
      customerId: props.customerId || null,
    },
  });
}
function viewDetail(item) {
  router.push({
    path: '/CRM/follow/compoents/detail',
    query: {
      id: item.id,
    },
  });
}
</script>

<style lang="scss" scoped></style>
