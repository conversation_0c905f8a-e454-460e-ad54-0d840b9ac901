<template>
  <div>
    <el-empty :description="'暂无提交的方案'" ></el-empty>
  </div>
</template>

<script setup>
import detail from '@/views/CRM/programme/compoents/updateVersion3.vue';
import History from '@/views/CRM/programme/compoents/history.vue';
const props = defineProps({
  stageStatus: {
    type: Number,
    default: -1,
  },
  businessPerson: {
    type: String,
    default: '',
  },
  optionId: {
    type: String,
    default: '',
  },
});
let activeNames = ref(['detail', 'history']);
</script>

<style lang="scss" scoped></style>
