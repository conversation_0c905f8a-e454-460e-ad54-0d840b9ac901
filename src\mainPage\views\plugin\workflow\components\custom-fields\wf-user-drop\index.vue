<template>
  <div>
    <el-select
      v-model="name"
      filterable
      remote
      style="width: 100%"
      reserve-keyword
      :disabled="disabled"
      :placeholder="placeholder || '请输入名字'"
      :remote-method="remoteMethod"
      :loading="loading"
      clearable
      @blur="handleBlur"
      @change="handleUserSelectConfirm"
    >
      <el-option v-for="item in nameList" :key="item.id" :label="item.name" :value="item.name" />
    </el-select>
   
  </div>
</template>
<script>
export default {
  name: 'user-drop',
  components: {},
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      default: () => {
        return 'small';
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    userUrl: {
      type: String,
      default: () => {
        return '/blade-system/search/user';
      },
    },
    tenantId: String,
    change: Function,
    params: {
      type: Object,
      default: () => {
        return { roleKeys: 'sales_leader,sales_person,manager' };
      },
    },
    type:String
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          this.name = val;
        } else {
          this.name = '';
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      nameList: '',
      loading: false,
    };
  },
  mounted() {
    this.remoteMethod('');
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['user-select'].visible = true;
    },
    remoteMethod(value) {
      this.loading = true;
      axios
        .get('/api/blade-system/search/user?name=' + value, {
          params: {
            ...this.params,
            size: 1000,
          },
        })
        .then(res => {
          this.nameList = res.data.data.records;
          this.loading = false;
        });
    },
    handleUserSelectConfirm(id) {
      console.log(id);
      this.$emit('update:modelValue', id);
      if (this.change && typeof this.change == 'function') this.change({ value: id });
    },
    handleBlur(e) {
      let value = e.target.value; // 输入框值
      if (value) {
        // 只有输入才有这个值，下拉框选择的话 这个值为空
        this.name = value;
        this.$emit('update:modelValue', value);
        if (this.change && typeof this.change == 'function') this.change({ value: value });
      }
    },
  },
};
</script>
<style lang="scss"></style>
