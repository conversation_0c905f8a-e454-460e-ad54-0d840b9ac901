<template>
  <div style="margin-bottom: 10px">
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>合同总额统计</div>
          <div style="display: flex; gap: 20px">
            <el-date-picker
              v-model="query.year"
              :clearable="false"
              style="width: 120px"
              v-if="echartsType == '时间模式1' || echartsType == '时间模式2'"
              value-format="YYYY"
              @change="getDetail"
              type="year"
              placeholder=""
            ></el-date-picker>
            <el-date-picker
              v-model="query.year"
              :clearable="false"
              style="width: 120px"
              v-else
              value-format="YYYY"
              @change="initCustomerEcharts"
              type="year"
              placeholder=""
            ></el-date-picker>
            <wfUserSelectDrop
              v-model="query.userId"
              @change="getDetail"
              v-if="form.selectType != 0"
              style="width: 100px"
            ></wfUserSelectDrop>

            <el-radio-group v-model="echartsType" @change="handleTypeChange">
              <el-radio-button value="时间模式2" label="时间模式2"
                ><div style="display: flex; justify-content: center; align-items: center">
                  <span>当月合同与对应收款</span>
                  <el-tooltip effect="dark" content="月销售额和月销售额对应的回款" placement="top">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip></div
              ></el-radio-button>
              <el-radio-button value="时间模式1" label="时间模式1"
                ><div style="display: flex; justify-content: center; align-items: center">
                  <span>月合同与月收款</span>
                  <el-tooltip effect="dark" content="月销售额和月回款" placement="top">
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip></div
              ></el-radio-button>

              <el-radio-button value="按排名" label="按排名" />
            </el-radio-group>
          </div>
        </div>
      </template>
      <div v-if="echartsType == '时间模式1' || echartsType == '时间模式2'">
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div
            style="
              display: flex;
              align-items: center;
              gap: 20px;
              justify-content: center;
              margin-bottom: 10px;
            "
          >
            <span style="font-weight: bolder">合同总额：</span>
            <el-text type="primary" size="large">{{ contractTotal }}</el-text>
            <span style="font-weight: bolder">回款总额：</span>
            <el-text type="primary" size="large">{{ refundAmountTotal }}</el-text>

            <!-- <span style="font-weight: bolder">合同总额：</span>
          <el-text type="primary" size="large">{{ echartsForCustomerForm.totalPrice }}</el-text> -->
          </div>
          <wfCustomerSelect
            v-model="query.customerId"
            :change="handleCustomerChange"
            @clear="getDetail"
            :Url="`/vt-admin/customer/page?type=${form.selectType == 1 ? '5' : '0'}`"
            style="width: 300px"
          ></wfCustomerSelect>
        </div>
        <!-- 图表 -->
        <div style="height: 400px">
          <div ref="chartRef" style="height: 100%"></div>
        </div>
      </div>
      <div v-else v-loading="loading">
        <div
          v-if="form.selectType == 1"
          style="display: flex; justify-content: right; margin-bottom: 5px; align-items: center"
        >
          <!-- <span style="font-weight: bolder">合同总额：</span>
          <el-text type="primary" size="large">{{ echartsForCustomerForm.totalPrice }}</el-text> -->
          <el-radio-group size="mini" v-model="searchType" @change="initCustomerEcharts">
            <el-radio-button :label="0">按客户</el-radio-button>
            <el-radio-button :label="1">按业务员</el-radio-button>
            <el-radio-button :label="2">按产品</el-radio-button>
            <el-radio-button :label="3">按品牌</el-radio-button>
          </el-radio-group>
        </div>

        <div style="height: 350px">
          <div ref="chartRefForCustomer" style="height: 100%"></div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import wfUserSelectDrop from './userSelect.vue';
import wfCustomerSelect from '@/views/plugin/workflow/components/custom-fields/wf-customer-select/index.vue';

import * as echarts from 'echarts';
import { ref, onMounted, computed, watch, inject } from 'vue';
import moment from 'moment';
import axios from 'axios';
const chartRef = ref(null);
const radio2 = ref('月');
let searchType = ref(0);
onMounted(() => {
  getDetail();
});
const form = inject('form');
let query = ref({
  year: moment(new Date()).format('YYYY'),
  type1: 0,
});
watch(form.value, () => {
 
  query.value.userId = form.value.userId;
  query.value.year = form.value.year;
   getDetail();
});
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],

    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: true,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: detailForm.value.x, //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '合同总额', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: [
      {
        name: '合同总额', //名称
        type: 'bar', //类型
        barWidth: 18, //宽度
        smooth: true,
        data: detailForm.value.y, //数值
        z: 1,
        barGap: 0,
        label: {
          show: true,
          position: 'top',
        },

        // 添加 markLine 配置（平均值）
      },
      {
        name: '已回款金额', //名称
        type: 'bar', //类型
        barWidth: 18, //宽度
        smooth: true,
        data: refundForm.value.y, //数值
        z: 1,
        barGap: 0,
        // 添加 markLine 配置（平均值）
      },
    ],
  };
  chart.setOption(option);
}
function initChart2() {
  const chart = echarts.init(chartRef.value);
  let option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],

    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: true,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: detailForm.value.x, //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '合同总额', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: [
      {
        name: '合同总额', //名称
        type: 'bar', //类型
        barWidth: 18, //宽度
        smooth: true,
        data: detailForm.value.y, //数值
        z: 1,
        barGap: 0,
        label: {
          show: true,
          position: 'top',
        },

        // 添加 markLine 配置（平均值）
      },
      {
        name: '已回款金额', //名称
        type: 'bar', //类型
        barWidth: 18, //宽度
        smooth: true,
        data: refundFormBySignDate.value.y, //数值
        z: 1,
        barGap: 0,
        // 添加 markLine 配置（平均值）
      },
    ],
  };
  chart.setOption(option);
}


async function getDetail() {
  await getAllAmount();
  await getRefundAmount();
  await getRefundAmountBySignDate();
  if (echartsType.value == '时间模式1') {
    initChart();
  } else if (echartsType.value == '时间模式2') {
    initChart2();
  } else {
    initCustomerEcharts();
  }
}
let detailForm = ref({ y: [] });
function getAllAmount(params) {
  return axios
    .get('/api/vt-admin/statistics/contractAmountStatistics', {
      params: {
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
    });
}
let refundForm = ref({ y: [] });
function getRefundAmount() {
  return axios
    .get('/api/vt-admin/statistics/refundAmountStatistics', {
      params: {
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      refundForm.value = res.data.data;
    });
}
let refundFormBySignDate = ref({ y: [] });
function getRefundAmountBySignDate() {
  return axios
    .get('/api/vt-admin/statistics/refundAmountBySignDateStatistics', {
      params: {
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      refundFormBySignDate.value = res.data.data;
    });
}

// 计算合同总额 - detailForm.value.y 求和
const contractTotal = computed(() => {
  if (!detailForm.value.y || !Array.isArray(detailForm.value.y)) {
    return '0.00';
  }
  const total = detailForm.value.y.reduce((sum, value) => {
    return sum + (parseFloat(value) || 0);
  }, 0);
  return total.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
});

// 计算回款总额 - 根据时间模式选择不同的数据源
const refundAmountTotal = computed(() => {
  let dataSource;
  if (echartsType.value === '时间模式1') {
    // 时间模式1：使用 refundForm.value.y
    dataSource = refundForm.value.y;
  } else if (echartsType.value === '时间模式2') {
    // 时间模式2：使用 refundFormBySignDate.value.y
    dataSource = refundFormBySignDate.value.y;
  } else {
    return '0.00';
  }

  if (!dataSource || !Array.isArray(dataSource)) {
    return '0.00';
  }

  const total = dataSource.reduce((sum, value) => {
    return sum + (parseFloat(value) || 0);
  }, 0);
  return total.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
});
let echartsType = ref('时间模式2');
let echartsForCustomerInstance = null;
let echartsForCustomerForm = ref({});
let chartRefForCustomer = ref(null);
function handleTypeChange(val) {
  if (val == '时间模式1') {
    initChart();
  } else if (val == '时间模式2') {
    initChart2();
  } else {
    initCustomerEcharts();
  }
}

async function initCustomerEcharts() {
  await getdetailForCustomer({});
  loading.value = false;
  const chart = echarts.init(chartRefForCustomer.value);
  let title =
    searchType.value == 0
      ? `总客户数：${echartsForCustomerForm.value.length}`
      : searchType.value == 1
      ? `业务员人数：${echartsForCustomerForm.value.length}`
      : searchType.value == 2
      ? '出货前100名'
      : '';
  let option = {
    // 设置主标题，总客户数为 y 轴数据的总数
    title: {
      text:title,
      left: 'center',
    },
    color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#303133'],
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
    },
    legend: {
      show: false,
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%', // 调整顶部间距以显示标题
      containLabel: true,
    },
    xAxis: {
      type: 'value',
      boundaryGap: [0, 0.01],
    },
    dataZoom: [
      {
        type: 'slider',
        show: true,
        start: 100 - (10 / echartsForCustomerForm.value.length) * 100,
        end: 100,
        minValueSpan: 15,
        maxValueSpan: 15,
        zoomLock: true,
        handleSize: 20,
        yAxisIndex: 0,
      },
      {
        type: 'inside',
        start: 100 - (10 / echartsForCustomerForm.value.length) * 100,
        end: 100,
        yAxisIndex: 0,
      },
    ],
    yAxis: {
      type: 'category',
      data: echartsForCustomerForm.value.map(item => item.name).reverse(),
      axisLabel: {
        formatter: val => {
          return val.slice(0, 18);
        },
      },
    },
    series: [
      {
        name: '合同总额',
        type: 'bar',
        itemStyle: {
          barWidth: 20,
        },
        data: echartsForCustomerForm.value.map(item => item.totalPrice).reverse(),
      },
    ],
  };
  chart.setOption(option);
}
let loading = ref(false);
function getdetailForCustomer({ size = 10000, current = 1 }) {
  loading.value = true;
  return axios
    .get('/api/vt-admin/statistics/sealRankingList', {
      params: {
        size,
        current,
        ...form.value,
        ...query.value,
        type: searchType.value,
      },
    })
    .then(res => {
      console.log(res);
      echartsForCustomerForm.value = res.data.data;
    });
}
function handleCustomerChange(val) {
  getDetail();
}
</script>

<style lang="scss" scoped>
.el-card {
  height: 500px;
}
</style>
