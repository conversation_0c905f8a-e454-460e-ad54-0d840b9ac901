<template>
  <el-dialog
    :title="formOption.title || '标题'"
    v-model="dialogVisible"
    class="avue-dialog avue-dialog--top"
    :width="formOption.width || '50%'"
  >
    <el-alert type="success" style="margin-bottom: 10px" v-if="formOption.tip" :closable="false">{{
      formOption.tip
    }}</el-alert>
    <avue-form
      v-if="dialogVisible"
      ref="dialogForm"
      :option="formOption.option"
      @submit="submit"
      v-model="form"
    ></avue-form>
    <!-- <slot ></slot> -->
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button @click="$refs.dialogForm.submit()" type="primary">{{
        formOption.submitText || '确 定'
      }}</el-button>
    </div>
  </el-dialog>
</template>

<script setup>
import { getCurrentInstance, reactive } from 'vue';
import { ElDialog, ElButton } from 'element-plus';
let dialogVisible = ref(false);
let formOption = ref({});
let form = ref({});
function show(o) {
  form.value = o.option.column.reduce((pre, cur) => {
    pre[cur.prop] = cur.value;
    return pre;
  }, {});
  dialogVisible.value = true;
  formOption.value = {
    ...o,
    option: {
      ...o.option,
      submitBtn: false,
      emptyBtn: false,
    },
  };
}
const { proxy } = getCurrentInstance();
function submit(row, down, loading) {
 
  // proxy.$refs.dialogForm.resetFields()
  formOption.value.callback({
    data: row,
    close: close,
  });
  setTimeout(() => {
    down();
  },200)

}
function close() {
  dialogVisible.value = false;
  formOption.value.option = {};
  form.value = {};
}
defineExpose({
  show,
});
</script>

<style lang="scss" scoped></style>
