<template>
    
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
      <template #isInvoice="{ row }">
        <el-tag effect='plain' v-if="row.isInvoice === 1" type="success">是</el-tag>
        <el-tag effect='plain' v-else-if="row.isInvoice === 2" type="success">部分开票</el-tag>
        <el-tag effect='plain' v-else type="danger">否</el-tag>
      </template>
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
   
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  const props = defineProps(['id'])
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: true,
    delBtn: true,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    menuWidth: 270,
    menu:false,
    border: true,
    column: [
    {
      label: '产品',
      prop: 'customProductName',
      
      overHidden:true,
    },

   
    {
      label: '规格型号',
      prop: 'customProductSpecification',
     
      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
    },
  
    {
      label: '描述',
      prop: 'customProductDescription',
     
      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '单位',
      prop: 'unitName',
      bind:'product.unitName',
      overHidden: true,
      width: 100,
      // search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      width: 100,
      overHidden: true,
      // search: true,
      span: 24,
      type: 'number',
    },
    {
      label: '关联合同',
      prop: 'contractName',
      
      overHidden:true,
    },

    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',

      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,

      cell: false,
    },
    {
      label: '是否开票',
      prop: 'isInvoice',
    },
  ]
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  
  const addUrl = ''
  const delUrl = ''
  const updateUrl = ''
  const tableUrl = '/api/vt-admin/businessOpportunity/getTransactionProducts'
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
         
          size,
          current,
          ...params.value,
          id: props.id || null,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        page.value.total = res.data.data.total;
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  </script>
  
  <style lang="scss" scoped></style>
  