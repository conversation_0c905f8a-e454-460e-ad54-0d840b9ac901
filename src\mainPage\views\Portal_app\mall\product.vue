<template>
  <basic-container>
    <el-container>
      <el-aside
        width="250px"
        style="margin-right: 20px; height: calc(100% - 20px); overflow: hidden"
      >
        <avue-tree
          :option="treeOption"
          :data="treeData"
          @update="treeUpdate"
          @save="treeSave"
          node-key="id"
          style="height: 700px"
          ref="tree"
          @del="treeDel"
          :beforeOpen="beforeOpen"
          @check-change="treeCheck"
          :permission="getPermission"
          @node-click="handleNodeClick"
          v-model="treeForm"
        >
          <template #default="{ node, data }">
            <el-checkbox
              v-if="!data.disabled"
              :checked="data.isCheck"
              @change="handClick($event, data)"
            />
            {{ data.categoryName }}
          </template>
        </avue-tree>
      </el-aside>
      <el-main>
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @on-load="onLoad"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          :before-open="beforeOpen"
          @row-del="rowDel"
          @search-reset="searchReset"
          @search-change="searchChange"
          @refresh-change="onLoad"
          @current-change="onLoad"
          @size-change="onLoad"
          v-model="form"
        >
          <template #header> </template>
          <template #menu="{ row, index }"> </template>
          <template #templateName="{ row }">
            <el-link @click.stop="toDetail(row)" type="primary">{{ row.templateName }}</el-link>
          </template>
          <template #isUp="{ row }">
            <el-switch
              v-model="row.isUp"
              :active-value="1"
              :inactive-value="0"
              active-text="上架"
              inactive-text="下架"
              inline-prompt
              @click="handleChange(row)"
            >
            </el-switch>
          </template>
          <template #isRecommend="{ row }">
            <el-switch
              v-model="row.isRecommend"
              :active-value="1"
              :inactive-value="0"
              :disabled="row.isUp == 0"
              @click="handleIsRecommendChange(row)"
            >
            </el-switch>
          </template>
          <template #templateCategory="{ row }">
            <div v-if="row.templateCategory" style="display: flex; flex-wrap: wrap">
              <el-tag
                effect="plain"
                style="margin: 2px"
                size="small"
                v-for="item in row.$templateCategory?.split('|') || []"
                :key="item"
                >{{ item }}</el-tag
              >
            </div>
          </template>
        </avue-crud>
      </el-main>
    </el-container>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="分类模板" v-model="addVisible" size="50%"> </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  viewBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  labelWidth: 150,
  border: true,
  index: true,
  column: [
  
    {
      label: '商城产品名称',
      prop: 'name',
      overHidden: true,

      hide: true,
      span: 24,
    },
      {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      search: true,
      span: 12,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      search: true,
      width: 150,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      search: true,
      span: 12,
      type: 'input',
    },
    {
      label: '单位',
      type: 'select',
      width: 100,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',

      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      type: 'tree',
      addDisplay: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '轮播图',
      prop: 'homePictureIds',
      type: 'upload',
      value: [],
      dataType: 'string',
      listType: 'picture-card',
      loadText: '附件上传中，请稍等',
      span: 24,
      accept: 'image/png, image/jpeg,image/jpg',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      tip: '封面图尺寸150px * 150px',
      rules: [
        {
          required: true,
          message: '请上传封面图',
          trigger: 'blur',
        },
      ],
      hide: true,
      action: '/blade-resource/attach/upload',
    },
    {
      label: '状态',
      prop: 'isUp',
      type: 'select',
      width: 100,
      display: false,
      search: true,
      dicData: [
        {
          label: '上架',
          value: '1',
        },
        {
          label: '下架',
          value: '0',
        },
      ],
    },
    {
      label: '是否推荐',
      prop: 'isRecommend',
      type: 'switch',
      width: 110,
      display: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      label: '产品详情',
      prop: 'introduce',
      component: 'AvueUeditor',
      action: '/api/blade-resource/attach/upload',
      options: {
        action: '/api/blade-resource/attach/upload',
        accept: 'image/png, image/jpeg, image/jpg,.mp4',
      },
      propsHttp: {
        res: 'data',
        url: 'link',
      },

      rules: [
        {
          required: true,
          message: '请输入内容',
          trigger: 'blur',
        },
      ],
      hide: true,
      minRows: 6,
      span: 24,
    },
    // {
    //   label:'子系统无分类',
    // }

    // {
    //   label: '创建人',
    //   display: false,
    //   prop: 'createUserName',
    //   width: 120,
    // },
    // {
    //   label: '创建时间',
    //   prop: 'createTime',
    //   display: false,
    //   width: 160,
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/optionTemplate/save';
const delUrl = '/api/vt-admin/optionTemplate/remove?ids=';
const updateUrl = '/api/vt-admin/optionTemplate/updateInfo';
const tableUrl = '/api/vt-admin/shopProduct/pageForWeb';
let params = ref({
  templateCategory: '',
});
let tableData = ref([{}]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,

        categoryId: currentNode.value.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(() => {
      loading.value = false;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post('/api/vt-admin/shopProduct/up', {
      ...form,
    })
    .then(res => {
      ElMessage.success('上架成功');
      done();
    });
}

function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function handleEdit(row) {
  router.push({
    path: '/CRM/programme/compoents/addOrUpdateTemplate',
    query: {
      id: row.id,
      name: '编辑方案模板',
      businessType: row.businessType,
    },
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function toDetail(row) {
  router.push({
    path: '/CRM/programme/compoents/addOrUpdateTemplate',
    query: {
      id: row.id,
      name: row.templateName,
    },
  });
}
function searchReset() {
  params.value = {};
  onLoad();
}
let addVisible = ref(false);

onMounted(() => {
  getTreeData();
});
// onActivated(() => {
//   onLoad();
//   getTreeData();
// });

let treeData = ref([]);
function getTreeData() {
  axios.get('/api/vt-admin/shopCategory/treeForWeb').then(res => {
    treeData.value = res.data.data.map(item => {
      return {
        ...item,
        disabled: true,
        children: item.children.map(child => {
          return {
            ...child,
            children: null,
          };
        }),
      };
    });
  });
}

let treeOption = ref({
  defaultExpandAll: true,
  menu: false,
  addBtn: false,
  // multiple: true,
  editBtn: true,
  delBtn: false,
  draggable: true,
  formOption: {
    labelWidth: 100,
    column: [
      {
        label: '排序',
        prop: 'sort',
        type: 'number',
        // width: 250,
        overHidden: true,
        // search: true,
      },
    ],
  },
  props: {
    label: 'categoryName',
    children: 'children',
    value: 'id',
  },
});
let currentNode = ref({});
let treeForm = ref({});
function handleNodeClick(node) {
  currentNode.value = node;
  onLoad();
}
function treeSave(node, data, done, loading) {
  console.log(node, data);
  const value = {
    ...data,
    level: node.data?.id ? null : 0,
    parentId: node.data?.id,
  };
  axios
    .post('/api/vt-admin/templateCategory/save', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
      parentId.value = 0;
    });
}
function treeUpdate(node, data, done, loading) {
  const value = {
    ...data,
  };
  axios
    .post('/api/vt-admin/templateCategory/update', value)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        // onLoad();
        getTreeData;
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function treeDel(data, done) {
  console.log(data);
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/templateCategory/remove?ids=' + data.data.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getTreeData();
        done(form);
      });
    })
    .catch(() => {});
}
function beforeOpen(done, type) {
  console.log(type);

  if (type == 'view') {
    axios
      .get('/api/vt-admin/shopProduct/detailByProductId?productId=' + form.value.id)
      .then(res => {
        if (res.data.data) {
          form.value = res.data.data;
          form.value.id = null;
        } else {
          form.value = {
            ...form.value,
            productId: row.id,
            id: null,
          };
        }
        done();
      });
  }
  done();
}
function getPermission(key, data) {
  if (key == 'addBtn' && data.level == 2) {
    console.log(data);
    return false;
  } else {
    return true;
  }
}
function handClick(b, a) {
  axios.post(`/api/vt-admin/shopCategory/check?id=${a.id}&isCheck=${b ? 1 : 0}`).then(res => {
    getTreeData();
  });
}
function handleChange(row) {
  if (row.isUp == 1) {
    axios.get('/api/vt-admin/shopProduct/detailByProductId?productId=' + row.id).then(res => {
      if (res.data.data) {
        form.value = res.data.data;

        form.value.id = null;
        proxy.$refs.crud.rowAdd();
      } else {
        form.value = {
          introduce: row.description,
          name: `${row.productBrand}${row.productName}${row.productSpecification}`,
          ...row,
          productId: row.id,
          id: null,
        };
        proxy.$refs.crud.rowAdd();
      }
    });
  } else {
    axios.post('/api/vt-admin/shopProduct/down?productId=' + row.id).then(res => {
      ElMessage.success('操作成功');
    });
  }
}
function handleIsRecommendChange(item) {
  if (item.isUp != 1) {
    item.isUp = 0;
    return ElMessage.warning('请先上架');
  }
  axios
    .post('/api/vt-admin/shopProduct/recommend', {
      isRecommend: item.isRecommend,
      id: item.upId,
    })
    .then(res => {
      proxy.$message.success('操作成功');
    });
}
</script>

<style lang="scss" scoped>
:deep(.avue-tree__content) {
  padding: 0;
}
</style>
