<template>
  <div style="height: 100%" v-loading="loading">
    <el-row style="height: 100%" :gutter="20">
      <el-col :span="5">
        <avue-tree
          ref="tree"
          :option="treeOption"
          :data="treeData"
          @node-click="handleNodeClick"
        ></avue-tree>
      </el-col>
      <el-col :span="19">
        <el-button
          type="primary"
          icon="plus"
          v-if="!props.detail"
          style="margin-bottom: 5px"
          @click="$refs.productSelectRef.visible = true"
        >
          新增</el-button
        >

        <!-- <el-table
          class="avue-crud"
          height="550"
          ref="table"
          :data="tableData"
          border
          align="center"
        >
          <el-table-column
            label="设备名称"
            show-overflow-tooltip
            prop="customProductName"
          ></el-table-column>
          <el-table-column
            label="规格型号"
            show-overflow-tooltip
            prop="customProductSpecification"
          ></el-table-column>
          
          <el-table-column
            label="产品描述"
            show-overflow-tooltip
            width="200"
            prop="customProductDescription"
          ></el-table-column>
          <el-table-column label="品牌" prop="product.productBrand"></el-table-column>
          <el-table-column label="单位" prop="product.unitName"></el-table-column>
          <el-table-column label="数量" prop="deepenNumber"> </el-table-column>
          <el-table-column label="已采购数量" #default="{ row }" prop="purchaseNums">
          </el-table-column>
          <el-table-column label="未采购数量" #default="{ row }" prop="noPurchaseNums">
          </el-table-column>
          <el-table-column
            show-overflow-tooltip
            label="备注"
            #default="{ row }"
            width="300"
            prop="deepenRemark"
          >
            <el-input
              v-if="!props.detail"
              v-model="row.deepenRemark"
              type="input"
              placeholder=""
            ></el-input>
          </el-table-column>
          
        </el-table> -->
        <avue-crud
          :option="option"
          :data="tableData"
          ref="crud"
          @selection-change="selectionChange"
        ></avue-crud>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { watchEffect, getCurrentInstance } from 'vue';
import { useRoute } from 'vue-router';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
const props = defineProps({
  id: {
    type: String,
    default: '',
  },
  detail: {
    type: Boolean,
    default: true,
  },
  disableList: Array,
});
let option = ref({
  tip: true,
  border: true,
  header: false,
  reserveSelection: true,
  selectable: (row, index) => {
    console.log(parseFloat(row.noPurchaseNums) <= 0);
    return (
      !props.disableList.some(item => item.detailId == row.id) && parseFloat(row.noPurchaseNums) > 0
    );
  },
  menu: false,
  header: false,
  addBtn: false,
  selection: true,
  viewBtn: true,
  dialogType: 'drawer',
  dialogWidth: 700,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      formatter: row => {
        return row.customProductName || row.product?.productName;
      },
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      formatter: row => {
        return row.customProductSpecification || row.product?.productSpecification;
      },
    },
    {
      label: '产品描述',
      prop: 'customProductDescription',
      overHidden: true,
      formatter: row => {
        return row.customProductDescription || row.product?.description;
      },
    },
    {
      label: '品牌',
      prop: 'productName',
      bind: 'product.productBrand',
      formatter: row => {
        return row.productBrand || row.product?.productBrand;
      },
    },
    {
      label: '单位',
      prop: 'productName',
      bind: 'product.unitName',
      width: 100,
      formatter: row => {
        return row.customUnit || row.product?.unitName;
      },
    },
    {
      label: '清单数量',
      prop: 'deepenNumber',
      width: 100,
      align: 'center',
    },
    {
      label: '已请购数量',
      prop: 'purchaseNums',
      align: 'center',
      width: 100,
      formatter: row => {
        return parseFloat(row.purchaseNums);
      },
    },
    {
      label: '未采购数量',
      prop: 'noPurchaseNums',
      width: 100,
      align: 'center',
      formatter: row => {
        return parseFloat(row.noPurchaseNums);
      },
    },
    {
      label: '深化备注',
      prop: 'deepenRemark',
      overwidth: true,
    },
  ],
});
const route = useRoute();
const { proxy } = getCurrentInstance();
let treeData = ref([]);
let allData = ref([]);
let tableData = ref([]);
let form = ref({});
//   watchEffect(() => {
//     if (props.id) {
//       getDetail();
//     }
//   });
onMounted(() => {
  getDetail();
});
let loading = ref(false);
function getDetail() {
  allData.value = [];
  tableData.value = [];
  loading.value = true;
  axios.post('/api/vt-admin/project/projectProducts?id=' + route.query.id, {}).then(res => {
    form.value = res.data.data;
    form.value.businessOpportunityId = props.id;
    formatData(res.data.data.moduleVOList);
    form.value.moduleVOList = null;
    loading.value = false;
  });
}

let treeOption = ref({
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  props: {
    value: 'value',
    label: 'label',
  },
});

function formatData(data) {
  treeData.value = data.map(item => {
    item.label = item.moduleName;
    item.children = item.detailVOList
      .map(i => {
        return {
          value: i.classify,
          label: i.classify || '---',
          parentId: item.id,
        };
      })
      .reduce((acc, cur) => {
        if (!acc.find(item => item.value === cur.value)) {
          acc.push(cur);
        }
        return acc;
      }, []);
    return item;
  });
  proxy.$nextTick(() => {
    proxy.$refs.tree.setCurrentKey(treeData.value[0].children[0].value);
    handleNodeClick(treeData.value[0].children[0]);
  });
  allData.value = data;
}
let currModuleId = ref(null);
let currClassfiy = ref(null);
function handleNodeClick(node) {
  currModuleId.value = node.parentId;
  currClassfiy.value = node.value;
  tableData.value = [];
  if (node.children && node.children.length > 0) return;
  allData.value
    .find(item => item.id === node.parentId)
    .detailVOList.forEach(element => {
      if (element.classify === node.value) {
        tableData.value.push(element);
      }
    });
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
  console.log(list);
}
function clearToggle() {
  console.log(333);
  proxy.$refs.crud.toggleSelection();
}
defineExpose({
  selectList,
  clearToggle,
  treeData,
});
</script>

<style lang="scss" scoped></style>
