<template>
  <div>
    <myStatement v-if="props.info.type == 0 || props.info.type == 2" :customerId="props.info.customerId"></myStatement>
    <allStatement v-else :customerId="props.info.customerId"></allStatement>
  </div>
</template>

<script setup>
import myStatement from '@/views/Finance/statement/myStatement.vue';
import allStatement from '@/views/Finance/statement/allStatement.vue';
const props = defineProps(['info']);
</script>

<style lang="scss" scoped></style>
