<template>
    <el-dialog
      ref="nf-dialog"
      class="nf-dialog"
      v-model="visible"
      title="产品选择"
      width="100%"
      :before-close="handleClose"
      append-to-body
    >
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <span>产品选择</span>
          <el-button
            type="primary"
            text
            icon="link"
            style="font-size: 25px"
            @click="openIframe"
          ></el-button>
          <!-- <el-button type="" @click="message">发送消息</el-button> -->
        </div>
      </template>
      <div style="display: flex; overflow: hidden">
        <div style="width: 30%">
          <!-- <el-input placeholder="输入关键字进行过滤" v-model="filterText" @input="getTreeData">
          </el-input> -->
          <avue-tree
            style="margin-right: 20px; height: 800px; overflow-y: auto"
            :option="treeOption"
            :data="treeData"
            ref="treeRef"
            @node-click="nodeClick"
          >
          </avue-tree>
        </div>
        <avue-crud
          v-if="isInit && visible"
          :option="option"
          :table-loading="loading"
          :data="data"
          v-model:page="page"
          v-model="form"
          v-model:search="query"
          ref="crud"
          @search-change="searchChange"
          :row-class-name="tableRowClassName"
          @keyup.enter="onLoad"
          @search-reset="searchReset"
          @selection-change="selectionList = $event"
          @current-change="page.currentPage = $event"
          @size-change="page.pageSize = $event"
          @row-click="rowClick"
          @on-load="onLoad"
        >
          <template #menu-left>
            <el-button plain icon="upload" @click="upload1" v-if="isLeaf" type="primary"
              >导入</el-button
            >
          </template>
          <template v-if="checkType == 'radio'" #radio="{ row }">
            <el-radio v-model="form.radio" :label="row.id"><i></i></el-radio>
          </template>
          <template #productName="{ row }">
            <el-tooltip :content="row.takeEffectReason" v-if="row.isTakeEffect == 1" placement="">
              <i
                style="color: var(--el-color-danger); font-size: 25px"
                class="element-icons el-icon-shixiaozhong"
              ></i>
            </el-tooltip>
            <el-tooltip
              :content="row.takeEffectReason"
              v-if="row.isSpecial == 1"
              effect="light"
              placement=""
            >
              <template #content>
                <Title>专项供应</Title>
                <div v-for="item in row.specialBusinessName && row.specialBusinessName.split(',')">
                  <el-text type="primary">《{{ item }}》</el-text>
                </div>
              </template>
  
              <i
                style="color: var(--el-color-primary); font-size: 20px; cursor: pointer"
                class="element-icons el-icon-biaoqian"
              ></i>
            </el-tooltip>
            <span>{{ row.productName }}</span>
          </template>
          <template #productParam-search>
            <div v-if="searchPropertyList.length == 0">
              <el-alert :closable="false" size="small" type="info"
                >未配置参数或未选中三级分类</el-alert
              >
            </div>
            <div v-else>
              <div
                class="item"
                style="display: flex; justify-content: flex-start; align-items: center"
                v-for="(item, index) in searchPropertyList"
              >
                <el-form>
                  <el-form-item style="font-weight: bold" :label="item.propertyName + ':'">
                    <el-tag effect='plain'
                      :type="query.productPropertyVoS[index] == i.id ? 'success' : 'info'"
                      style="margin-right: 10px; cursor: pointer"
                      @click="handleClick(i, index)"
                      v-for="i in item.valuesEntityList"
                      >{{ i.value }}</el-tag
                    >
                  </el-form-item>
                </el-form>
                <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
                <!-- <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
                  <span style="margin-left: 15px">
                    <span  v-for="i in item.valuesEntityList" style="margin-right: 10px;">{{ i.value }}</span>
                    <el-radio-group v-model="params.productPropertyVoS[index]">
                      <el-radio
                        v-for="i in item.valuesEntityList"
                        :label="i.id"
                        size="large"
                        >{{ i.value }}</el-radio
                      >
                    </el-radio-group>
                  </span> -->
              </div>
            </div>
          </template>
          <template #costPrice="{ row }">
            <div v-if="row.priceWarnType == 0">
              <div>{{ row.costPrice }}</div>
            </div>
            <div v-else>
              <div v-if="row.priceWarnType == 1" style="color: var(--el-color-success)">
                {{ row.costPrice }}
              </div>
              <div v-else-if="row.priceWarnType == 2" style="color: var(--el-color-warning)">
                {{ row.costPrice }}
              </div>
              <div v-else style="color: var(--el-color-danger)">{{ row.costPrice }}</div>
            </div>
          </template>
          <template #costPrice-header="{ column }">
            <span>{{ column.label }}</span>
  
            <el-tooltip placement="top">
              <template #content>
                <div style="color: #999; font-size: 14px">
                  <span>
                    <el-icon style="color: var(--el-color-danger)"><WarningFilled /></el-icon>
                    大于维护周期7天
                  </span>
                  <span>
                    <el-icon style="color: var(--el-color-warning)"><WarningFilled /></el-icon>
                    大于维护周期且小于7天
                  </span>
                  <span>
                    <el-icon style="color: var(--el-color-success)"><WarningFilled /></el-icon>
                    小于维护周期
                  </span>
                </div>
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>
        </avue-crud>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-text size="small" type="primary">找不到产品?</el-text>
          <el-text style="cursor: pointer" text size="small" @click="handleAdd" type="success"
            >新增</el-text
          >
          <el-button style="margin-left: 10px" @click="handleClose" size="large">取 消</el-button>
          <el-button type="primary" @click="handleConfirm" size="large">确 定</el-button>
        </span>
      </template>
      <el-drawer destroy-on-close size="50%" append-to-body v-model="drawer" title="新增产品">
        <el-alert type="success" :closable="false">
          此处新增商品不会保存到产品库，待相关人员审核过后即会自动添加到产品库
        </el-alert>
        <avue-form v-model="addForm" :option="addOption" @submit="submit">
          <template #productProperty>
            <div>
              <div
                class="item"
                style="display: flex; justify-content: flex-start; align-items: center"
                v-for="item in propertyList"
              >
                <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
                <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
                <span style="margin-left: 15px" v-if="item.type == 1">
                  <el-checkbox-group v-model="item.selectList">
                    <el-checkbox
                      v-for="i in item.valuesEntityList"
                      :label="i.id + `-` + i.value"
                      size="large"
                      >{{ i.value }}</el-checkbox
                    >
                  </el-checkbox-group>
                </span>
                <span style="margin-left: 15px" v-else-if="item.type == 0">
                  <el-radio
                    v-model="item.radioSelect"
                    v-for="i in item.valuesEntityList"
                    :label="i.id + `-` + i.value"
                    size="large"
                    @click.native.prevent="
                      item.radioSelect =
                        item.radioSelect == i.id + `-` + i.value ? '' : i.id + `-` + i.value
                    "
                    >{{ i.value }}</el-radio
                  >
                </span>
                <span style="margin-left: 15px" v-else-if="item.type == 2">
                  <el-input v-model="item.value" size="small"></el-input>
                </span>
              </div>
            </div>
          </template>
          <template #productName>
            <el-autocomplete
              style="width: 100%"
              v-model="addForm.productName"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              placeholder="请输入产品名称"
            >
              <template #default="{ item }">
                <div class="productBox">
                  <div><span>名称：</span>{{ item.productName }}</div>
                  <div><span>品牌：</span>{{ item.productBrand }}</div>
                  <div><span>型号：</span>{{ item.productSpecification }}</div>
                </div>
              </template>
            </el-autocomplete>
          </template>
        </avue-form>
      </el-drawer>
      <el-dialog
        title="导入产品"
        v-model="dialogVisible"
        width="30%"
        style="height: 400px"
        class="avue-dialog avue-dialog--top"
      >
        <el-upload
          class="upload-demo"
          drag
          ref="upload"
          :on-success="handleSuccess"
          :on-error="handleError"
          :on-exceed="handleExceed"
          :limit="1"
          action="/api/vt-admin/product/importProduct?isNew=1"
          :headers="{
            [website.tokenHeader]: $store.getters.token,
            Authorization: `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
          }"
          multiple
          :auto-upload="false"
        >
          <i class="el-icon-upload"></i>
          <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
          <div><el-link type="primary" slot="tip" @click.stop="download">下载模板</el-link></div>
        </el-upload>
        <div class="avue-dialog__footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button @click="handleSubmit" type="primary">确 定</el-button>
        </div>
      </el-dialog>
    </el-dialog>
  </template>
  <script>
  import { Base64 } from 'js-base64';
  import { getUser } from '@/api/system/user';
  
  import { loadFile } from '@/utils/file';
  import { ElNotification } from 'element-plus';
  import progress from '@/components/progress/index.vue';
  export default {
    props: {
      defaultChecked: String,
      userUrl: {
        type: String,
        default: () => {
          return '/vt-admin/product/page';
        },
      },
      customOption: Object,
      checkType: {
        type: String,
        default: () => {
          return 'radio';
        },
      },
      categoryId: {
        type: String,
        default: '',
      },
    },
    watch: {
      checkType: {
        handler(val) {
          if (val == 'radio') {
            this.option.selection = false;
            this.findObject(this.option.column, 'radio').hide = false;
          } else {
            this.option.selection = true;
            this.findObject(this.option.column, 'radio').hide = true;
          }
        },
        immediate: true,
      },
      visible: {
        handler(val) {
          if (val) {
            console.log(val);
            this.getTreeData();
          }
        },
      },
    },
    computed: {
      ids() {
        let ids = new Set();
        this.selectionList.forEach(ele => {
          ids.add(ele.id);
        });
        return Array.from(ids).join(',');
      },
      names() {
        let names = new Set();
        this.selectionList.forEach(ele => {
          names.add(ele.productName);
        });
        return Array.from(names).join(',');
      },
    },
    data() {
      return {
        isInit: false,
        visible: false,
        drawer: false,
        form: {},
        query: {
          productPropertyVoS: [],
        },
        loading: false,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        selectionList: [],
        data: [],
        props: {
          id: 'id',
          name: 'productName',
          records: 'data.data.records',
          total: 'data.data.total',
        },
        option: {
          size: 'default',
          searchSize: 'default',
          align: 'center',
          menu: false,
          addBtn: false,
          header: true,
          border: true,
          // tip: false,
          selectable: (row, index) => {
            return row.isTakeEffect == 0;
          },
          reserveSelection: true,
          highlightCurrentRow: true,
          gutter: 5,
          // searchIndex: 2,
          // searchIcon: true,
          reserveSelection: true,
          searchMenuSpan: 12,
          selection: true,
          column: [
            {
              label: '关键字',
              prop: 'keys',
              overHidden: true,
              placeholder: '名称，型号，品牌',
              display: false,
              search: true,
              hide: true,
            },
            {
              label: '',
              prop: 'radio',
              type: 'radio',
              width: 55,
              hide: true,
            },
            {
              label: '产品分类',
              prop: 'categoryId',
              search: false,
  
              hide: true,
              type: 'tree',
              dicUrl: '/api/vt-admin/productCategory/list',
              props: {
                value: 'id',
                label: 'categoryName',
              },
              children: 'hasChildren',
              lazy: true,
              treeLoad: function (node, resolve) {
                axios
                  .get('/api/vt-admin/productCategory/list', {
                    params: {
                      parentId: node.data.id,
                    },
                  })
                  .then(res => {
                    resolve(
                      res.data.data.map(item => {
                        return {
                          ...item,
                          leaf: !item.hasChildren,
                        };
                      })
                    );
                  });
              },
            },
            // {
            //   label: '产品编号',
            //   prop: 'productCode',
            //   overHidden: true,
            //   searchSpan: 8,
            //   search: true,
            // },
            {
              label: '产品名称',
              prop: 'productName',
              searchSpan: 8,
              overHidden: true,
              search: true,
            },
  
            {
              label: '品牌',
              prop: 'productBrand',
              overHidden: true,
              width: 130,
              rules: [
                {
                  required: true,
                  message: '请输入品牌',
                  trigger: 'change',
                },
              ],
            },
            {
              label: '单位',
              type: 'select',
              width: 80,
              props: {
                label: 'dictValue',
                value: 'id',
                desc: 'desc',
              },
              rules: [
                {
                  required: true,
                  message: '请选择单位',
                  trigger: 'blur',
                },
              ],
              prop: 'unit',
              dicUrl: '/blade-system/dict/dictionary?code=unit',
              remote: false,
            },
  
            {
              label: '规格型号',
              prop: 'productSpecification',
              overHidden: true,
              search: true,
              span: 24,
              type: 'input',
            },
            {
              label: '商品描述',
              prop: 'description',
              overHidden: true,
              type: 'textarea',
              span: 24,
            },
            {
              label: '成本价',
              prop: 'costPrice',
              type: 'number',
              // readonly: true,
              width: 130,
              editDisplay: true,
              // placeholder: '自动计算',
              overHidden: true,
              formatter: row => {
                return parseFloat(row.costPrice).toLocaleString() == 'NaN'
                  ? ''
                  : parseFloat(row.costPrice).toLocaleString();
              },
            },
            {
              label: '参数查询',
              prop: 'productParam',
              hide: true,
              search: true,
              addDisplay: false,
              editDisplay: false,
              searchSpan: 24,
              searchSlot: true,
            },
            {
              label: '产品图片',
              prop: 'coverUrl',
              type: 'upload',
              width: 130,
              dataType: 'object',
              listType: 'picture-img',
              loadText: '图片上传中，请稍等',
              span: 24,
              slot: true,
              limit: 1,
              // align: 'center',
              propsHttp: {
                res: 'data',
                url: 'link',
                name: 'originalName',
              },
              action: '/blade-resource/attach/upload',
              uploadAfter: (res, done) => {
                form.value.coverId = res.id;
                done();
              },
            },
          ],
        },
        treeOption: {
          //   defaultExpandAll: true,
          menu: false,
          filter: true,
          addBtn: false,
          props: {
            labelText: '标题',
            label: 'categoryName',
            value: 'id',
            children: 'children',
          },
          dicUrl: '/api/vt-admin/productCategory/tree',
        },
        searchPropertyList: [],
        treeData: [],
        filterText: '',
        addOption: {
          emptyBtn: false,
          submitText: '提交并添加',
          group: [
            {
              arrow: true,
              label: '基本信息',
              prop: 'baseInfo',
              column: [
                {
                  label: '产品名称',
                  prop: 'productName',
                  overHidden: true,
                  rules: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
                  search: true,
                },
                {
                  label: '产品分类',
                  prop: 'categoryId',
                  search: true,
                  filterable: true,
                  hide: true,
                  type: 'tree',
                  rules: [
                    {
                      required: true,
                      message: '请选择产品分类',
                      trigger: 'change',
                    },
                  ],
                  children: 'hasChildren',
                  parent: false,
                  dicUrl: '/api/vt-admin/productCategory/tree',
                  props: {
                    label: 'categoryName',
                    value: 'id',
                  },
                  change: val => {
                    if (!val.value) return;
                    this.getPropertyList(val.value);
                  },
                },
                {
                  label: '品牌',
                  prop: 'productBrand',
                  overHidden: true,
                  search: true,
                },
                {
                  label: '单位',
                  type: 'select',
                  props: {
                    label: 'dictValue',
                    value: 'id',
                    desc: 'desc',
                  },
                  rules: [
                    {
                      required: true,
                      message: '请选择单位',
                      trigger: 'blur',
                    },
                  ],
                  prop: 'unit',
                  dicUrl: '/blade-system/dict/dictionary?code=unit',
                  remote: false,
                },
  
                {
                  label: '规格型号',
                  prop: 'productSpecification',
                  overHidden: true,
                  search: true,
                  span: 24,
                  type: 'input',
                },
                {
                  label: '是否专项',
                  prop: 'isSpecial',
                  type: 'radio',
                  hide: true,
                  value: 0,
                  dicData: [
                    {
                      label: '是',
                      value: 1,
                    },
                    {
                      label: '否',
                      value: 0,
                    },
                  ],
  
                  overHidden: true,
                  control: val => {
                    console.log(val);
                    return {
                      specialBusiness: {
                        display: !!val,
                      },
                    };
                  },
                },
                {
                  label: '商机选择',
                  prop: 'specialBusiness',
                  hide: true,
                  span: 24,
                  placeholder: '请选择商机',
                  component: 'wf-business-select',
  
                  params: {
                    checkType: 'checkbox',
                    Url: '/vt-admin/businessOpportunity/page?selectType=2',
                  },
                },
                {
                  label: '产品图片',
                  prop: 'coverUrl',
                  type: 'upload',
                  dataType: 'object',
                  listType: 'picture-img',
                  loadText: '图片上传中，请稍等',
                  span: 24,
                  slot: true,
                  limit: 1,
                  // align: 'center',
                  propsHttp: {
                    res: 'data',
                    url: 'link',
                    name: 'originalName',
                  },
                  action: '/blade-resource/attach/upload',
                  uploadAfter: (res, done) => {
                    this.addForm.coverId = res.id;
  
                    done();
                  },
                },
                {
                  label: '产品参数',
                  prop: 'productProperty',
                  type: 'input',
                  hide: true,
                  slot: true,
                  span: 24,
                },
                {
                  label: '商品描述',
                  prop: 'description',
                  overHidden: true,
                  type: 'textarea',
                  span: 24,
                },
  
                {
                  label: '用途',
                  prop: 'purpose',
                  overHidden: true,
                  type: 'textarea',
                  span: 24,
                },
              ],
            },
            {
              label: '价格信息',
              arrow: true,
              prop: 'priceInfo',
              labelWidth: '100px',
              value: 1,
              column: [
                {
                  label: '是否含税',
                  prop: 'isHasTax',
                  type: 'radio',
                  rules: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
                  labelTip: '采购价是否含税',
                  dicData: [
                    { label: '是', value: 1 },
                    { label: '否', value: 0 },
                  ],
                },
                // {
                //   label: '采购价',
                //   prop: 'purchasePrice',
                //   type: 'number',
                //   overHidden: true,
                //   rules: [{ required: true, message: '请输入采购价', trigger: 'blur' }],
                // },
                {
                  label: '成本价',
                  prop: 'costPrice',
                  type: 'number',
                  overHidden: true,
                },
                // {
                //   label: '市场价',
                //   prop: 'marketPrice',
                //   type: 'number',
                //   overHidden: true,
                // },
              ],
            },
            {
              label: '供应商信息',
              prop: 'supplierInfo',
              column: [
                {
                  label: '供应商名称',
                  prop: 'supplierId',
                  labelWidth: '120px',
                  overHidden: true,
                  component: 'wf-supplier-select',
                  // rules: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
                },
              ],
            },
          ],
        },
        addForm: {},
        propertyList: [],
        isLeaf: false,
        dialogVisible: false,
        notice: null,
        Base64,
     
      };
    },
    mounted() {
      this.init();
    },
    methods: {
      init() {
        if (!this.isInit) {
          if (this.customOption) {
            const { column, userProps } = this.customOption;
            if (column) this.option.column = column;
            if (userProps) this.props = userProps;
          }
          this.isInit = true;
        }
      },
      handleConfirm() {
        if (this.selectionList.length === 0) {
          this.$message.warning('请选择至少一条数据');
          return;
        }
        this.$emit('onConfirm', this.ids, this.names);
        this.handleClose();
      },
      handleClose(done) {
        // this.selectionClear()
        this.visible = false;
        if (done && typeof done == 'function') done();
      },
      searchReset() {
        this.query = {
          productPropertyVoS: [],
          categoryId: '',
        };
        this.$refs.treeRef.setCurrentKey(null);
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = {
          ...params,
          productPropertyVoS: this.query.productPropertyVoS,
        };
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
        this.setDefault();
      },
      selectionClear() {
        this.selectionList = [];
        if (this.$refs.crud) this.$refs.crud.toggleSelection();
      },
      rowClick(row) {
        if (row.isTakeEffect != 0) return;
        if (this.checkType == 'radio') {
          this.selectionList = [row];
          this.form.radio = row.id;
        } else this.$refs.crud.toggleSelection([row]);
      },
      async changeDefaultChecked() {
        if (!this.defaultChecked) return;
        let defaultChecked = this.defaultChecked;
  
        if (this.checkType == 'checkbox') {
          // this.selectionClear()
          const checks = defaultChecked.split(',');
          if (checks.length > 0) {
            setTimeout(() => {
              checks.forEach(async c => {
                let row = this.data.find(d => d.id == c); // 当前页查找
                if (!row) {
                  row = this.selectionList.find(d => d.id == c); // 勾选列表查找
                  if (!row) {
                    let res = await getDetail(c); // 接口查找
                    if (res.data.data) row = res.data.data;
                  }
                }
                if (row && this.$refs.crud) this.$refs.crud.toggleRowSelection(row, true);
              });
            }, 500);
          }
        } else {
          let row = this.data.find(d => d.id == defaultChecked);
          if (!row) {
            let res = await getDetail(defaultChecked);
            if (res.data.data) row = res.data.data;
          }
  
          if (row) {
            this.selectionList = [row];
            this.form.radio = defaultChecked;
          } else {
            this.selectionList = [];
            this.form.radio = '';
          }
        }
      },
      onLoad(page, params = {}) {
        console.log(this.query);
        this.loading = true;
        const param = {
          current: page.currentPage,
          size: page.pageSize,
          ...Object.assign(params, this.query),
          valueIds: this.query.productPropertyVoS.filter(item => item).join(),
          productPropertyVoS: null,
        };
        this.$axios.get(this.userUrl, { params: param }).then(res => {
          this.page.total = this.getAsVal(res, this.props.total);
          this.data = this.getAsVal(res, this.props.records) || [];
          this.loading = false;
  
          this.changeDefaultChecked();
        });
      },
      getAsVal(obj, bind = '') {
        let result = this.deepClone(obj);
        if (this.validatenull(bind)) return result;
        bind.split('.').forEach(ele => {
          if (!this.validatenull(result[ele])) {
            result = result[ele];
          } else {
            result = '';
          }
        });
        return result;
      },
      getDetail(c) {
        return axios.get('/api/vt-admin/product/detail', {
          params: {
            id: c,
          },
        });
      },
  
      nodeClick(val) {
        this.query = {
          productPropertyVoS: [],
        };
        this.page.currentPage = 1;
        this.query.categoryId = val.id;
        this.isLeaf = val.children.length === 0;
        this.getParamsList(val.id);
  
        this.loading = true;
        const param = {
          current: this.page.currentPage,
          size: this.page.pageSize,
          ...this.query,
          valueIds: this.query.productPropertyVoS.filter(item => item).join(),
          productPropertyVoS: null,
        };
        this.$axios.get('/vt-admin/product/page', { params: param }).then(res => {
          this.page.total = this.getAsVal(res, this.props.total);
          this.data = this.getAsVal(res, this.props.records) || [];
          this.loading = false;
          this.changeDefaultChecked();
        });
      },
      getTreeData(value) {
        axios.get('/api/vt-admin/productCategory/tree').then(res => {
          this.treeData = res.data.data;
          this.setDefault();
        });
      },
  
      getParamsList(categoryId) {
        // 获取参数
        axios.get('/api/vt-admin/productCategory/detail?id=' + categoryId).then(res => {
          this.searchPropertyList = res.data.data.propertyVOList.filter(item => item.type != 2);
        });
      },
      handleClick(i, index) {
        if (this.query.productPropertyVoS[index] == i.id) {
          this.query.productPropertyVoS[index] = '';
        } else {
          this.query.productPropertyVoS[index] = i.id;
        }
      },
      handleAdd() {
        this.drawer = true;
        if (this.isLeaf) {
          this.addForm.categoryId = this.query.categoryId;
        }
        this.addForm.isHasTax = 1;
      },
      submit(form, done, loading) {
        const propertyDTOList = this.formatData(this.propertyList, 'add');
        const data = {
          ...form,
          propertyDTOList,
          supplierList: [
            {
              supplierId: form.supplierId,
              unitPrice: form.costPrice,
              isHasTax: form.isHasTax,
            },
          ],
        };
        console.log(data);
        axios
          .post('/api/vt-admin/product/addNewProduct', data)
          .then(res => {
            if (res.data.code == 200) {
              this.$message.success(res.data.msg);
              this.drawer = false;
              this.addForm = {};
              this.propertyList = [];
              this.searchReset();
              this.$emit('onConfirm', res.data.data.id);
            }
          })
          .catch(err => {
            done();
          });
      },
      getPropertyList(categoryId) {
        // 获取参数
        axios.get('/api/vt-admin/productCategory/detail?id=' + categoryId).then(res => {
          this.propertyList = res.data.data.propertyVOList.map(item => {
            return {
              ...item,
            };
          });
        });
      },
      formatData(data, type) {
        return data.map(item => {
          let valuesDTOList = [];
          let value = '';
          if (item.type == 0) {
            if (!item.radioSelect) {
              valuesDTOList = [];
            } else {
              const [valuesId, value] = item.radioSelect && item.radioSelect.split('-');
              valuesDTOList = [
                {
                  valuesId,
                  value,
                },
              ];
            }
          } else if (item.type == 1) {
            valuesDTOList =
              item.selectList &&
              item.selectList.map(i => {
                const [valuesId, value] = i && i.split('-');
                return {
                  valuesId,
                  value,
                };
              });
          } else {
            value = item.value;
          }
          return {
            propertyId: type == 'add' ? item.id : item.propertyId,
            valuesDTOList,
            value,
          };
        });
      },
      tableRowClassName({ row }) {
        if (row.isNew === 1) {
          return 'warning-row';
        } else if (row.isTakeEffect == 1) {
          return 'info-row';
        } else {
          return '';
        }
      },
      setDefault() {
        this.$nextTick(() => {
          console.log(this.$refs.treeRef, this.categoryId);
          this.$refs.treeRef.setCurrentKey(this.categoryId);
        });
      },
      querySearch(val, cb) {
        if (!val) return;
        axios
          .get('/api/vt-admin/product/list', {
            params: {
              keys: val,
              size: 50,
            },
          })
          .then(res => {
            cb(res.data.data.records);
          });
      },
      // let dialogVisible = ref(false);
      upload1() {
        console.log(111);
        this.dialogVisible = true;
      },
  
      download() {
        // let a = document.createElement('a'); // 创建a标签
        // a.href = '/template/productImport.xlsx'; // 文件路径
        // a.download = '参数导入模板.xlsx'; // 文件名称
        // a.style.display = 'none'; // 隐藏a标签
        // document.body.appendChild(a);
        // // 定时器(可选)
        // setTimeout(() => {
        //   a.click(); // 模拟点击(要加)
        //   document.removeChild(a); //删除元素(要加)
        //   setTimeout(() => {
        //     self.URL.revokeObjectURL(a.href); // 用来释放文件路径(可选)
        //   }, 200);
        // }, 66);
        loadFile('/api/vt-admin/product/exportProductTemplate?categoryId=' + this.query.categoryId);
      },
      handleSuccess(res) {
        console.log(res);
  
        if (res.code == 200) {
          this.$message({
            message: res.msg.split(',').join('<br>'),
            dangerouslyUseHTMLString: true,
            type: 'success',
            duration: 2000,
          });
          this.onLoad(this.page);
        } else {
          this.$message({
            message: '数据有误',
            dangerouslyUseHTMLString: true,
            type: 'warning',
            duration: 2000,
          });
          // 获取文本内容
          var textContent = res.msg.replace(/,/g, '\n');
  
          // 创建 Blob 对象
          var blob = new Blob([textContent], { type: 'text/plain' });
  
          // 创建下载链接
          var downloadLink = document.createElement('a');
          downloadLink.href = URL.createObjectURL(blob);
  
          // 设置下载文件的名称
          downloadLink.download = '错误数据.txt';
  
          // 将下载链接添加到页面
          document.body.appendChild(downloadLink);
  
          // 模拟点击下载链接
          downloadLink.click();
  
          // 移除下载链接
          document.body.removeChild(downloadLink);
        }
  
        this.notice.close();
        this.$refs.upload.clearFiles();
        // dialogVisible.value = false
      },
      // let notice = ref(null);
      handleError(res) {
        this.$message.error(res.data.msg);
        this.$refs.upload.clearFiles();
        this.notice.close();
      },
      handleSubmit(params) {
        this.$nextTick(() => {
          this.$refs.upload.submit();
          this.notice = ElNotification({
            title: '导入中',
            position: 'bottom-right',
            duration: 0,
            message: h(progress, {
              // 事件要以onXxx的形式书写
              onFinish: status => {
                if (status.value == 'ok') {
                  this.notice.close(); // 关闭ElNotification
                }
              },
            }),
          });
        });
      },
      openIframe() {
        this.visible = false;
        this.$emit('openIframe')
      },
     
    },
  };
  </script>
  <style lang="scss" scoped>
  .nf-dialog {
    display: flex;
    flex-direction: column;
    margin: 0 !important;
    width: 100% !important;
    height: 100%;
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -40%);
    max-height: calc(100% - 30px);
    max-width: calc(100% - 30px);
    .el-dialog__body {
      flex: 1;
      overflow: auto;
    }
  }
  .el-dialog {
    width: 100% !important;
    height: 100%;
  }
  .productBox div {
    span {
      color: var(--el-color-primary);
      font-weight: bold;
    }
  }
  </style>
  