<template>
    <basic-container block >
        <div class="page-content" style="height: 100%">
          <!-- <el-tabs v-model="activeName" class="demo-tabs">
            <el-tab-pane label="我的周计划" name="first"></el-tab-pane>
            <el-tab-pane label="我的日程" name="second"></el-tab-pane>
          </el-tabs> -->
          <div class="calendar-box"  v-if="activeName == 'first'">
            <div class="calendar-box-left">
              <MCalendarWeek
                type="self"
                
                :user-id="$store.getters.userInfo.user_id"
                ref="calendarRef"
                @calendarChange="handlePlanChange"
              />
            </div>
            <div class="calendar-box-right">
              <MToDoListWeek :data="planList" @isFull="getFull" ref="todoListRef" type="self" :date="planDate" />
            </div>
          </div>
          <!-- <div class="calendar-box" v-else>
            <div class="calendar-box-left"><MCalendar ref="calendarRef" @calendarChange="handleCalendarChange" />
            </div>
            <div class="calendar-box-right"><m-to-do-list :data="todoList" :date="todoDate" />            
            </div>
                     
          </div> -->
        </div>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ElMessage } from 'element-plus';
  import { ref, getCurrentInstance, onMounted, onActivated, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import MCalendarWeek from '@/views/home/<USER>';
  import MToDoListWeek from '@/views/home/<USER>';
  onMounted(() => {
    queryPlanList();
  });

// 计划
let planDate = ref([]);
let planList = ref([]);
function handlePlanChange(e) {
  planDate.value = e;
  queryPlanList();
}
// 查询日历待办列表
function queryPlanList() {
  axios({
    url: '/api/vt-admin/weekPlan/myList',
    method: 'get',
    params: {
      startDate: planDate.value[0],
      endDate: planDate.value[1],
    },
  }).then(e => {
    let data = e.data.data;
    planList.value = data;
  });
}
let activeName = ref('first');
let isFull = ref(0)
function getFull(value) {
  isFull.value = value
}
  </script>
  
  <style lang="scss" scoped>
  .calendar-box {
    width: 100%;
    height: calc(100% - 40px);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  
    .calendar-box-left {
      width: 600px;
      height: 100%;
    }
    .calendar-box-right {
      width: calc(100% - 325px);
      height: 100%;
  
      padding: 10px 0 10px 16px;
      box-sizing: border-box;
    }
  }
  </style>
  