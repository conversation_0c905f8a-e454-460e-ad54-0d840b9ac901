<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @keyup.enter="onLoad"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      :cell-style="customCellStyle"
      :header-cell-class-name="customHeaderCellStyle"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="toDetail(row)">详情</el-button>
        <!-- <el-button type="primary" text icon="edit" @click="editNumber(row)">修改库存</el-button> -->
      </template>
      <template #menu-left>
        <el-button type="primary" icon="plus" @click="handleAdd">新增</el-button>
      </template>
      <template #surplusNumber="{ row }">
        <el-link type="primary">{{ row.surplusNumber }}</el-link>
      </template>
      <template #footer>
        <el-form inline>
          <!-- <el-form-item label="本页数量小计：">{{
             tableData.reduce((pre, cur) => {
              pre += cur.endNumber * 1;
              return pre;
            }, 0)
            }}</el-form-item> -->
          <el-form-item label="本页期末金额："
            >{{
              tableData
                .reduce((pre, cur) => {
                  pre += cur.endPrice * 1;
                  return pre;
                }, 0)
                .toFixed(2)
            }}元</el-form-item
          >
          <!-- <el-form-item label="总数量：">{{ statisticData.totalNumber }}</el-form-item> -->
          <el-form-item label="期末总金额：">{{ statisticData.totalPrice }}元</el-form-item>
        </el-form>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import moment from 'moment';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 75,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  menu: true,
  border: true,

  column: [
    {
      label: '产品编号',
      prop: 'productCode',
      overHidden: true,

      // search: true,
    },
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,component:'wf-product-drop',
      search: true,
      placeholder: '名称,型号,品牌模糊查询',
    },
    {
      label: '时间',
      type: 'month',
      prop: 'date',
      search: true,
      format: 'YYYY-MM',
      valueFormat: 'YYYY-MM',
      hide: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      width: 80,
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },

    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      span: 24,
      type: 'input',
    },
    {
      label: '本期期初',
      prop: 'beginningNumber',
      children: [
        {
          label: '数量',
          prop: 'startNumber',
          width: 100,
        },
        {
          label: '金额',
          prop: 'startPrice',
        },
      ],
    },
    {
      label: '入库数量',
      prop: 'inStorageNumber',

      children: [
        {
          label: '订单采购',
          prop: 'ddcg',
          bind: 'inNumberMap.ddcg',
          width: 100,
        },
        {
          label: '储备采购',
          prop: 'cbrk',
          bind: 'inNumberMap.cbrk',
          width: 100,
        },
        {
          label: '客户退货',
          prop: 'khth',
          bind: 'inNumberMap.khth',
          width: 100,
        },
        {
          label: '其他',
          prop: 'qtrk',
          bind: 'inNumberMap.qtrk',
          width: 80,
        },
      ],
    },
    {
      label: '出库数量',
      prop: 'surplusNumber',
      children: [
        {
          label: '订单出库',
          prop: 'cgck',
          bind: 'outNumberMap.cgck',
        },
        {
          label: '借测出库',
          prop: 'jcck',
          bind: 'outNumberMap.jcck',
        },
        {
          label:'采购退货',
          prop: 'cgth',
          bind: 'outNumberMap.cgth',
        },
        {
          label: '其他',
          prop: 'qtck',
          bind: 'outNumberMap.qtck',
        },
      ],
    },
    {
      label: '期末库存',
      prop: 'surplusNumber',
      children: [
        {
          label: '数量',
          prop: 'endNumber',
          width: 100,
        },
        {
          label: '金额',
          prop: 'endPrice',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/inventoryHistory/page';
let params = ref({
  date: moment(new Date()).format('YYYY-MM'),
});
let tableData = ref([]);
let statisticData = ref({});
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
  
});
onActivated(() => {
  onLoad();
 
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item.productVO,
          ...item,
        };
      });
      page.value.total = res.data.data.total;
    });
    axios
    .get('/api/vt-admin/inventoryHistory/pageStatistics', {
      params: {
        projectId: route.query.id,
        humanId: params.value.humanId,
        ...params.value
      },
    })
    .then(res => {
      console.log(res.data.data);
      statisticData.value = res.data.data;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function toDetail(row) {
  router.push({
    path: '/SRM/warehouse/detail/houseDetail',
    query: {
      id: row.id,
      productName: row.productName,
    },
  });
}
function handleAdd() {
  router.push({
    path: '/SRM/warehouse/compoents/addInhouse',
    query: {
      inStorageType: 1,
    },
  });
}
function editNumber(row) {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          type: 'number',
          label: '数量',
          span: 24,
          prop: 'newNumber',
          value: row.surplusNumber,
          rules: [
            {
              required: true,
              message: '请输入数量',
              trigger: 'blur',
            },
          ],
        },
        {
          label: '备注',
          prop: 'reason',
          span: 24,
          type: 'textarea',
          rows: 4,
          maxlength: 200,
          showWordLimit: true,
          placeholder: '请输入备注',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/inventoryAdjustRecord/save', {
          inventoryId: row.id,
          ...res.data,
        })
        .then(r => {
          if (r.data.code == 200) {
            proxy.$message.success(r.data.msg);
            res.close();
            onLoad();
          }
        });
    },
  });
}



function customCellStyle(val) {
  if (['startPrice', 'qtrk', 'qtck', 'endPrice'].includes(val.column.property)) {
    return {
      borderRight: '2px solid var(--el-color-primary)',
    };
  }
}
function customHeaderCellStyle(val) {
  console.log(val);
  if (
    [
      'startPrice',
      'qtrk',
      'qtck',
      'inStorageNumber',
      'beginningNumber',
      'surplusNumber',
      'endPrice',
    ].includes(val.column.property)
  ) {
    return 'customClass';
  }
}
</script>

<style lang="scss" scoped>
.el-form-item {
  margin: 5px;
}
::v-deep .customClass {
  border-right: 2px solid var(--el-color-primary);
}
</style>
