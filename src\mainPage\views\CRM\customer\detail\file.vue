<template>
  <div>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      :table-loading="loading"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #fileIds="{ row }">
        <File :fileList="row.attaches"></File>
      </template>
    </avue-crud>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import { dateFormat } from '@/utils/date.js';
const props = defineProps(['type', 'id']);
const store = useStore();
let route = useRoute();
let { proxy } = getCurrentInstance();
let option = ref({
  labelWidth: 120,
  // align: 'center',
  // height:'auto',
  addBtn : props.type == 0 || props.type == 2,
  editBtn : props.type == 0 || props.type == 2,
  delBtn : props.type == 0 || props.type == 2,
  border:true,
  // calcHeight:30,
  column: [
    {
      label: '附件名称',
      prop: 'fileTypeName',
    },
    {
      label: '附件',
      prop: 'fileIds',
      type: 'upload',
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '上传日期',
      prop: 'createTime',
      type: 'date',
      disabled: true,
      format: 'YYYY-MM-DD HH:mm:ss',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
    },
    {
      label: '上传人',
      disabled: true,
      value: computed(() => store.getters.userInfo.nick_name),
      prop: 'createName',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let tableData = ref([]);

let loading = ref(false);

watchEffect(() => {
  if (props.id) {
    onLoad();
  }
},{
  flush: 'post'
})

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get('/api/vt-admin/customerFiles/page', {
      params: {
        customerId: props.id,
        size,
        current,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          fileIds:item.attaches.map(item => {
            return {
              value:item.id,
              label:item.originalName
            }
          })
        }
      });
      page.value.total = res.data.data.total;
    });
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
    customerId: props.id,
    fileIds: form.fileIds.map(item => item.value).join(','),
    createUser: null,
  };
  axios
    .post('/api/vt-admin/customerFiles/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    fileIds: row.fileIds.map(item => item.value).join(','),
    createUser: null,
  };
  axios
    .post('/api/vt-admin/customerFiles/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
     
      axios.post('/api/vt-admin/customerFiles/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>
