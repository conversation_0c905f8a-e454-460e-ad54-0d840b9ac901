<template>
  <!-- 基本信息 -->
  <div>
    <avue-form :option="detailOption" @submit="handleSubmit" :modelValue="props.form">
      <template #productProperty>
            <div>
              <div
                class="item"
                style="display: flex; justify-content: flex-start; align-items: center"
                v-for="item in props.form.propertyList"
              >
                <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
                <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
                <span style="margin-left: 15px" v-if="item.type == 1">
                  <el-checkbox-group disabled v-model="item.selectList">
                    <el-checkbox
                      v-for="i in item.valuesEntityList"
                      :label="i.id + `-` + i.value"
                      size="large"
                      >{{ i.value }}</el-checkbox
                    >
                  </el-checkbox-group>
                </span>
                <span style="margin-left: 15px" v-else-if="item.type == 0">
                  <el-radio
                  disabled
                    v-model="item.radioSelect"
                    v-for="i in item.valuesEntityList"
                    :label="i.id + `-` + i.value"
                    size="large"
                    >{{ i.value }}</el-radio
                  >
                </span>
                <span style="margin-left: 15px" v-else-if="item.type == 2">
                  <el-input disabled v-model="item.value" size="small"></el-input>
                </span>
              </div>
            </div>
          </template>
      <template #contractFiles> <File :fileList="form.attachList || []"></File> </template
    ></avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';

const props = defineProps({
  form: Object,
  isEdit: Boolean,
});
const form1 = ref({});

watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
});
const route = useRoute();
let detailOption = ref({
  detail: true,
  emptyBtn: false,
  submitBtn: false,
  labelWidth: 150,
  column: [
    {
      label: '产品编号',
      prop: 'productCode',
      overHidden: true,
      // placeholder: '自动生成',
      disabled: true,
      search: true,
    },

    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      search: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      search: true,
    },
    {
      label: '单位',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },

    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      search: true,
      span: 24,
      type: 'input',
    },
    {
      label: '协议商品',
      prop: 'isAgreement',
      type: 'radio',
      span: 12,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '成本价',
      prop: 'costPrice',
      type: 'number',
      hide: true,
      editDisplay: false,
      overHidden: true,
    },
    {
      label: '产品图片',
      prop: 'coverUrl',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        form.value.coverId = res.id;
        console.log(form.value);
        done();
      },
    },
    {
      label: '产品参数',
      prop: 'productProperty',
      type: 'input',
      slot:true,
      span: 24,
    },
  
    {
      label: '商品描述',
      prop: 'description',
      overHidden: true,
      type: 'textarea',
      span: 24,
    },

    {
      label: '用途',
      prop: 'purpose',
      overHidden: true,
      type: 'textarea',
      span: 24,
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: props.form.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
</script>

<style lang="scss" scoped></style>
