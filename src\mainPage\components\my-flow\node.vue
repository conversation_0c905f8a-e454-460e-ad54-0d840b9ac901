<template>
    <div ref="node"
         :style="flowNodeContainer"
         @mouseenter="showDelete"
         @mouseleave="hideDelete"
         @mouseup="changeNodeSite"
         :left="node.left"
         :top="node.top"
         disabled
        
         :mask="false">
      <div class="box_1"   :class="{'active':node.status == 1,'complete':node.status == 2,'no':node.status == 0,'end':node.type == 6}">
        <!-- <div class="avue-flow__node-header" style="background-color: var(--el-color-primary);">
          <span  class="avue-flow__node-drag">
          </span>
          <el-icon>
            <el-icon-rank />
          </el-icon>
          <slot name="header"
                :node="node">
          </slot>
        </div> -->
        <div  >
          <slot :node="node">
          </slot>
        </div>
  
      </div>
    </div>
  </template>
  
  <script>
  
  export default {
    name: 'flow',
    props: {
      active: [String, Number],
      index: [String, Number],
      node: Object
    },
    data () {
      return {
        // 控制节点操作显示
        mouseEnter: false
      }
    },
    computed: {
      // 节点容器样式
      flowNodeContainer: {
        get () {
          return {
            position: 'absolute',
            width: '200px',
            top: this.setPx(this.node.top),
            left: this.setPx(this.node.left),
            boxShadow: this.mouseEnter ? 'var(--el-color-primary) 0px 0px 12px 0px' : '',
            backgroundColor: 'transparent'
          }
        }
      }
    },
    methods: {
      // 鼠标进入
      showDelete () {
        this.mouseEnter = true
      },
      // 鼠标离开
      hideDelete () {
        this.mouseEnter = false
      },
      // 鼠标移动后抬起
      changeNodeSite () {
        // 避免抖动
        if (this.node.left == this.$refs.node.style.left && this.node.top == this.$refs.node.style.top) {
          return;
        }
        this.$emit('changeNodeSite', {
          index: this.index,
          left: Number(this.$refs.node.style.left.replace('px', '')),
          top: Number(this.$refs.node.style.top.replace('px', '')),
        })
      },
      
    }
  }
  </script>

  <style scoped >
    .box_1{
        border: 1px solid var(--el-color-primary);
        height: 80px;
        text-align: center;
        line-height: 80px;
        
    }
    .active{
        animation: trans 1s linear infinite;
        background-color: var(--el-color-primary)!important;
    }   
    .complete{
        border: 1px solid var(--el-color-success)!important;
        background-color: var(--el-color-success)!important;
    }
    .no{
        border: 1px solid var(--el-color-info);
    }
    .end{
        border-radius: 50%;
    }
    @keyframes trans {
        0%{
            border : 2px solid var(--el-color-info);
            box-shadow: none;
            
        }
        100%{
            border : 2px solid var(--el-color-primary);
            box-shadow: 0px 0px 5px var(--el-color-primary);
            
        }
    }
</style>