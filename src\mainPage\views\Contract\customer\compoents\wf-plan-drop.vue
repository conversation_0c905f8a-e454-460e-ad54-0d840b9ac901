<template>
  <div style="width: 100%">
    <el-select
      v-model="name"
      remote
      style="width: 100%"
      reserve-keyword
      clearable
      @change="handleChange"
      :placeholder="placeholder || '请输入名字'"
      :loading="loading"
    >
      <el-option
        v-for="item in invoiceList"
        :key="item.id"
        :label="item.planName"
        :value="item.id"
      >
        <el-form inline label-position="left">
          <el-form-item label="计划名称：">
            <el-tag effect='plain'>{{ item.planName }}</el-tag>
          </el-form-item>
          <el-form-item label="计划收款金额：">
            <el-tag effect='plain'>{{ item.planCollectionPrice }}</el-tag>
          </el-form-item>
          <el-form-item label="收款状态：">
            <el-tag effect='plain' size="small" type="success" v-if="item.collectionStatus == 2">已收款</el-tag>
            <el-tag effect='plain' size="small" type="warning" v-if="item.collectionStatus == 1">部分收款</el-tag>
            <el-tag effect='plain' size="small" type="warning" v-if="item.collectionStatus == 0">未收款</el-tag>
          </el-form-item>
          <!-- 计划收款时间 -->
          <el-form-item label="计划收款时间：">
            <el-tag effect='plain'>{{ item.planCollectionDate }}</el-tag>
          </el-form-item>
        </el-form>
      </el-option>
    </el-select>
  </div>
</template>
<script>
//   import { getUser } from '../../../api/process/user';

//   import NfUserSelect from '../../nf-user-select/index.vue';

export default {
  name: 'user-drop',
  components: {},
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      default: () => {
        return 'small';
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    userUrl: {
      type: String,
      default: () => {
        return '/vt-admin/sealContractPlanCollection/page';
      },
    },
    tenantId: String,
    change: Function,
    id: String,
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          this.name = val;
        } else {
          this.name = '';
        }
      },
      immediate: true,
    },
    userUrl: {
      handler(val) {
        if (val && this.id) {
          this.getInvoiceList();
        }
      },
      immediate: true,
    },
    id: {
      handler(val) {
        if (val) {
          this.getInvoiceList();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      invoiceList: [],
      loading: false,
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['user-select'].visible = true;
    },
    handleChange(val) {
      console.log(val);
      this.$emit('update:modelValue', val);
    },
    // remoteMethod(value) {
    //   if (value === '') return (this.nameList = []);
    //   this.loading = true;
    //   axios.get(`${userUrl}?customerId=` + value).then(res => {
    //     this.nameList = res.data.data.records;
    //     this.loading = false;
    //   });
    // },
    getInvoiceList() {
      axios.get(`${this.userUrl}?sealContractId=${this.id}`,{
        params:{
          size:500,
          isInvoice:0,
          selectType:1
        }
      }).then(res => {
        this.invoiceList = res.data.data.records;
        this.loading = false;
      });
    },
    // handleUserSelectConfirm(id) {
    //   console.log(id);
    //   this.$emit('update:modelValue', id);
    //   if (this.change && typeof this.change == 'function') this.change({ value: id });
    // },
    // handleBlur(e) {
    //   let value = e.target.value; // 输入框值
    //   if (value) {
    //     // 只有输入才有这个值，下拉框选择的话 这个值为空
    //     this.name = value;
    //     this.$emit('update:modelValue', value);
    //     if (this.change && typeof this.change == 'function') this.change({ value: value });
    //   }
    // },
  },
};
</script>
<style lang="scss"></style>
