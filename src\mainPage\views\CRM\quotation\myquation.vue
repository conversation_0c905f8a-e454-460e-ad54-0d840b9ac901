<template>
  <basic-container :shadow="props.customerId ? 'shadow' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="searchReset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
      @expand-change="expandChanges"
      :row-class-name="getClass"
    >
      <template #menu-left="{}">
        <div style="display: flex; gap: 20px">
          <el-button
            type="primary"
            icon="el-icon-plus"
            @click="handleAdd"
            v-if="!route.query.type || route.query.type == 0"
            >新增</el-button
          >
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">总报价额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
          </div>
          <!-- <el-checkbox @change="onLoad" v-if="props.customerId" v-model="params.isSelectHistory"
            >查看历史报价
            <el-tooltip
              content="客户被转移时，可以看之前业务员的报价"
              placement="top"
              effect="dark"
            >
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </el-checkbox> -->
        </div>
      </template>
      <template #menu="{ row }">
        <div style="display: flex; justify-content: center; flex-wrap: wrap">
          <!-- <div v-if="row.offerPeople == $store.getters.userInfo.user_id">
            <el-button
              text
              type="primary"
              icon="el-icon-link"
              @click="handleEditOffer(row)"
              v-if="row.offerStatus == 0 && $store.getters.permission.quation_qua"
              >报价</el-button
            >

          
          </div> -->
          <el-button
            text
            type="primary"
            icon="el-icon-edit"
            @click="handleEdit(row)"
            v-if="
              ((row.auditStatus == 0 && row.offerStatus == 0) ||
                row.auditStatus == 2 ||
                (row.offerStatus == 3 && row.auditStatus == 1)) &&
              (!route.query.type || route.query.type == 0)
            "
            >编辑</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.auditStatus == 1 && (!route.query.type || route.query.type == 0)"
            icon="el-icon-download"
            @click="handleAddDialog(row)"
            >合同</el-button
          >
          <el-button
            text
            type="primary"
            v-if="
              row.offerStatus == 3 &&
              row.auditStatus == 1 &&
              (!route.query.type || route.query.type == 0)
            "
            icon="el-icon-view"
            @click="customerConfirm(row)"
            >确认</el-button
          >
          <el-button
            text
            :type="row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'"
            icon="el-icon-download"
            @click="download(row)"
            v-if="row.auditStatus == 1 || row.offerStatus == 2"
            >下载</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus != 6 && row.isHasOption != 1 && row.offerStatus != 3"
            icon="el-icon-clock"
            @click="viewHistory(row)"
            >历史</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus != 6"
            icon="el-icon-clock"
            @click="viewProcess(row)"
            >流程</el-button
          >
          <el-button
            text
            type="primary"
            icon="CaretLeft"
            @click="back(row)"
            v-if="
              row.offerStatus == 3 &&
              row.isHasOption == 1 &&
              (!route.query.type || route.query.type == 0)
            "
            >撤回</el-button
          >
          <el-button
            text
            type="primary"
            icon="CaretLeft"
            @click="backToConfirm(row)"
            v-if="row.offerStatus == 2 && row.isHasContract == 0"
            >撤回</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.isHasOption !== 1 && row.offerStatus != 6"
            icon="CopyDocument"
            @click="copyOffer(row)"
            >复制</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus != 6 "
            icon="VideoPause"
            @click="pause(row)"
            >暂停</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.offerStatus == 6 && row.isHasOption !== 1"
            icon="VideoPlay"
            @click="start(row)"
            >重启</el-button
          >
          <el-button
            text
            type="primary"
            v-if="row.isHasOption !== 1 && row.offerStatus != 2"
            icon="delete"
            @click="$refs.crud.rowDel(row)"
            >删除</el-button
          >
        </div>

        <!-- <el-button
          text
          type="primary"
          v-if="row.offerStatus == 6 && row.isHasOption !== 1"
          icon="VideoPlay"
          @click="start(row)"
          >重启</el-button
        >
        <el-button
          text
          type="primary"
          v-if="row.offerStatus != 6 && row.isHasOption !== 1 && row.offerStatus == 3"
          icon="VideoPause"
          @click="pause(row)"
          >暂停</el-button
        > -->
      </template>
      <template #offerName="{ row }">
        <div style="display: flex;align-items: center;">
           <el-tag type="danger" v-if="row.isCooperation == 1" effect="dark" size="small">合作</el-tag> <el-link type="primary" @click="handleView(row)">{{ row.offerName }}</el-link>
        </div>
      </template>
      <template #auditStatus="{ row }">
        <div v-if="row.auditStatus == 1 || row.auditStatus == 2">
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.technologyRemark"
            :disabled="!row.technologyRemark"
            placement="top-start"
            v-if="row.auditStatus == 1"
          >
            <el-badge :hidden="!row.technologyRemark" is-dot class="item">
              <el-tag effect="plain" v-if="row.auditStatus == 1" size="small" type="success"
                >审核成功</el-tag
              >
            </el-badge>
          </el-tooltip>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditReason"
            placement="top-start"
            v-if="row.auditStatus == 2"
          >
            <el-tag effect="plain" size="small" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
          <el-tag effect="plain" v-if="row.auditType == 1" size="small" type="info"
            >待采购审核</el-tag
          >
          <el-tag effect="plain" v-if="row.auditType == 2" size="small" type="info"
            >待主管审核</el-tag
          >
          <el-tag effect="plain" v-if="row.auditType == 3" size="small" type="info"
            >待总经理审核</el-tag
          >
        </div>
      </template>
      <template #offerStatus="{ row }">
        <el-tooltip
          :content="row.failReason || row.pauseReason"
          v-if="row.offerStatus == 4 || row.offerStatus == 6"
        >
          <el-tag effect="plain" size="small" type="danger">{{ row.$offerStatus }}</el-tag>
        </el-tooltip>
        <span v-else>{{ row.$offerStatus }}</span>
      </template>
      <template #expand="{ row }">
        <div style="padding: 0 10px">
          <el-row :gutter="20" style="display: flex; flex-wrap: wrap">
            <el-col :span="5" v-for="item in row.offerVOS || []" :key="item.id">
              <el-card
                class="maCard"
                :shadow="item.offerVersionType == 0 ? 'never' : 'always'"
                style="height: 200px; overflow-y: auto; border: 1px solid var(--el-color-primary)"
              >
                <template #header>
                  <el-row>
                    <el-col
                      :span="24"
                      style="overflow: hidden; text-overflow: ellipsis; white-space: nowrap"
                    >
                      <el-tooltip :content="item.offerName">
                        <el-text
                          type="primary"
                          size="default"
                          style="
                            font-weight: bolder;
                            font-family: Inter, Helvetica Neue, Helvetica, PingFang SC,
                              Hiragino Sans GB, Microsoft YaHei, \5fae\8f6f\96c5\9ed1, Arial,
                              sans-serif;
                          "
                          >{{ item.offerName }}</el-text
                        >
                      </el-tooltip>
                    </el-col>

                    <el-col :span="24">
                      <div
                        v-if="item.offerVersionType == 1"
                        style="display: flex; justify-content: flex-end"
                      >
                        <!-- <el-button
                          type="info"
                          @click="handleEditSub(item)"
                          circle
                          icon="edit"
                          text
                        ></el-button> -->
                        <el-button
                          type="info"
                          title="预览"
                          @click="handleViewSub(item)"
                          circle
                          icon="view"
                          text
                        ></el-button>
                        <el-button
                          text
                          title="下载"
                          icon="el-icon-download"
                          :type="item.offerStatus == 1 && item.auditStatus == 1 ? 'danger' : 'info'"
                          @click="download(item)"
                        ></el-button>
                        <el-button
                          text
                          title="确认"
                          type="info"
                          v-if="
                            item.offerStatus == 3 &&
                            item.auditStatus == 1 &&
                            (!route.query.type || route.query.type == 0)
                          "
                          icon="SuccessFilled"
                          @click="
                            customerConfirm({
                              ...item,
                              isIntelligentizeProject: row.isIntelligentizeProject,
                            })
                          "
                        ></el-button>
                        <el-button
                          text
                          type="info"
                          title="撤回"
                          icon="CaretLeft"
                          @click="back(item)"
                          v-if="
                            item.offerStatus == 3 &&
                            item.isHasOption == 1 &&
                            (!route.query.type || route.query.type == 0)
                          "
                        ></el-button>
                      </div>
                      <div v-else>
                        <div style="display: flex; justify-content: flex-end">
                          <el-button
                            type="info"
                            title="预览"
                            @click="handleView(row)"
                            circle
                            icon="view"
                            text
                          ></el-button>
                          <el-button
                            text
                            type="primary"
                            title="确认"
                            v-if="
                              row.offerStatus == 3 &&
                              row.auditStatus == 1 &&
                              (!route.query.type || route.query.type == 0)
                            "
                            icon="CircleCheckFilled"
                            @click="customerConfirm(row)"
                          ></el-button>
                          <el-button
                            text
                            title="下载"
                            :type="
                              row.offerStatus == 1 && row.auditStatus == 1 ? 'danger' : 'primary'
                            "
                            icon="el-icon-download"
                            @click="download(row)"
                            v-if="row.auditStatus == 1 || row.offerStatus == 2"
                          ></el-button>
                          <el-button
                            text
                            title="历史"
                            type="primary"
                            v-if="row.offerStatus != 6"
                            icon="el-icon-clock"
                            @click="viewHistory(row)"
                          ></el-button>

                          <el-button
                            text
                            title="撤回"
                            type="primary"
                            icon="CaretLeft"
                            @click="back(row)"
                            v-if="
                              row.offerStatus == 3 &&
                              row.isHasOption == 1 &&
                              (!route.query.type || route.query.type == 0)
                            "
                          ></el-button>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                </template>
                <el-row style="margin-top: 10px; margin-left: 5px">
                  <el-col :span="20">
                    <el-text
                      style="font-size: 30px"
                      :type="item.optionStatus == 0 ? 'info' : 'success'"
                      >￥{{ item.offerPrice }}</el-text
                    >
                  </el-col>
                  <el-col :span="4">
                    <el-tag
                      effect="plain"
                      v-if="item.offerStatus == 2 || item.offerStatus == 4"
                      :type="item.offerStatus == 2 ? 'success' : 'danger'"
                    >
                      {{ item.offerStatus == 2 ? '成交' : '失单' }}
                    </el-tag>
                  </el-col>
                </el-row>
                <el-row style="margin-top: 10px; margin-left: 6px">
                  <el-col :span="24">
                    <el-text type="info">{{ item.remark }}</el-text>
                  </el-col>
                </el-row>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog title="历史记录" v-model="dialogVisible">
      <history :id="currentId" :currentStatus="currentStatus"></history>
    </el-dialog>
    <process ref="processRef" :currentId="currentId" :type="1"></process>
    <el-drawer v-model="drawer" :with-header="false" size="80%" title="拆解">
      <el-row :gutter="20" style="height: 100%">
        <el-col :span="10">
          <el-card shadow="never" style="height: 100%">
            <el-alert type="success" :closable="false">请将需要拆分的产品勾选上</el-alert>
            <avue-crud
              :option="needDeposeOption"
              :data="needDeposeData"
              @row-click="handleRowClick"
            >
              <template #menu="{ row }">
                <el-switch v-model="row.isDepose" :active-value="1" :inactive-value="0">
                </el-switch>
              </template>
            </avue-crud>
          </el-card>
        </el-col>
        <el-col style="height: 100%" :span="14">
          <el-row style="margin-bottom: 10px">
            <el-col :span="24">
              <el-card shadow="never">
                <!-- <avue-form :value="detailForm" :option="decomposeOption"></avue-form> -->
                <el-descriptions border :title="detailForm.customProductName" :column="3">
                  <el-descriptions-item label="规格型号">{{
                    detailForm.customProductSpecification
                  }}</el-descriptions-item>
                  <el-descriptions-item label="品牌">{{
                    detailForm.productBrand
                  }}</el-descriptions-item>
                  <el-descriptions-item label="单位">{{
                    detailForm.unitName
                  }}</el-descriptions-item>
                  <el-descriptions-item label="描述">
                    {{ detailForm.customProductDescription }}
                  </el-descriptions-item>
                </el-descriptions>
              </el-card>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <el-card shadow="never">
                <template #header>
                  <!-- <el-alert type="info" :closable="false"
                    >拆解会删除被拆解的产品，如需保留请再拆解列表里面添加</el-alert
                  > -->
                  <div style="display: flex; justify-content: space-between">
                    <div>拆解产品</div>
                    <div>
                      <el-form v-if="detailForm.productId">
                        <el-form-item label="保留原产品">
                          <el-switch
                            v-model="detailForm.isPre"
                            @change="handleIsPreChange"
                          ></el-switch>
                        </el-form-item>
                      </el-form>
                    </div>
                  </div>
                </template>
                <avue-crud
                  @row-del="rowDelProduct"
                  :data="detailForm.detailDTOList"
                  :option="decomposeEditFormOption"
                >
                  <template #menu-left>
                    <div v-if="detailForm.isDepose">
                      <el-button
                        icon="plus"
                        type="primary"
                        size="small"
                        @click="$refs.productSelectRef.visible = true"
                      ></el-button>
                      <productSelectDrop
                        v-if="$route.query.type != 'detail'"
                        @select="handleProductSelectConfirm"
                        style="margin-left: 5px"
                      ></productSelectDrop>
                    </div>
                  </template>
                  <template #number="{ row }">
                    <el-input-number
                      size="small"
                      style="width: 80%"
                      controls-position="right"
                      v-model="row.number"
                    ></el-input-number>
                  </template>
                </avue-crud>
              </el-card>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
      <template #footer>
        <el-button type="primary" icon="check" @click="decomposeSubmit">确 定</el-button>
        <el-button icon="close" @click="drawer = false">取 消</el-button>
      </template>
    </el-drawer>
    <wfProductSelect
      @onConfirm="handleProductSelectConfirm"
      ref="productSelectRef"
    ></wfProductSelect>

    <el-drawer title="导出合同" v-model="contractDrawer" size="50%">
      <el-radio-group v-model="contractType">
        <el-radio :label="0">产品销售合同</el-radio>
        <el-radio :label="1">项目工程合同</el-radio>
        <el-radio :label="2">技术服务合同</el-radio>
        <el-radio :label="3">IT运维服务合同</el-radio>
        <el-radio :label="4">硬件+服务销售合同</el-radio>
      </el-radio-group>
      <!-- 销售合同 -->
      <avue-form
        :option="contractOptionA"
        ref="contractRef"
        v-if="contractType == 0"
        @submit="handleImport"
        v-model="contractForm"
      >
        <template #afterSealsServiceMethod>
          <el-radio v-model="contractForm.afterSealsServiceMethod" :label="1"
            >1）产品按照厂家保修标准执行保修</el-radio
          >
          <el-radio v-model="contractForm.afterSealsServiceMethod" :label="2"
            >2）产品保修
            <el-input
              v-model="contractForm.warrantyYear"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            年，起算时间为甲方收到货物之日起</el-radio
          >
        </template>
        <template #payMethod>
          <el-radio style="margin-bottom: 20px" v-model="contractForm.payMethod" :label="1"
            >1）甲方应在本合同签订后<el-input
              v-model="contractForm.firstWorkDays"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />个工作日内，向乙方支付全部款项。</el-radio
          >
          <el-radio v-model="contractForm.payMethod" :label="2">
            <div style="white-space: wrap">
              2）甲方应在本合同签订后<el-input
                v-model="contractForm.secondWorkDays"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />个工作日内，向乙方支付<el-input
                v-model="contractForm.downPayment"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />首期款项；余下的<el-input
                v-model="contractForm.surplusMoney"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />，在货物交付验收后，<el-input
                v-model="contractForm.workDays"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />个工作日内付清货款。
            </div>
          </el-radio>
        </template>
        <template #weiyue>
          <div>
            <el-text style="color: var(--el-radio-text-color)">
              1、甲方逾期付款，每逾期一日，按逾期付款金额的
              <el-input
                v-model="contractForm.aOverduePercent"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />向卖方支付违约金。对于需要先支付货款的订单，甲方逾期付款，乙方有权顺延货物交付日期。违约金额不超过合同总额的5%
              。
            </el-text>
          </div>
          <div>
            <el-text style="color: var(--el-radio-text-color)">
              2、如乙方在规定期限内不能按时交货，则每迟延一天，乙方向甲方支付延期交货产品价款的<el-input
                v-model="contractForm.bOverduePercent"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />的违约金。违约金额不超过合同总额的5% 。
            </el-text>
          </div>
        </template>
        <template #contractTotalPrice>
          <div>
            <el-text style="color: var(--el-radio-text-color)">
              本项目合同预算总金额为: 人民币（大写）：
              <el-input
                v-model="contractForm.contractTotalPriceCapital"
                style="width: 200px"
                placeholder=""
                size="small"
                clearable
              />
              ，（小写）：
              <el-input
                v-model="contractForm.contractTotalPrice"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />整。
            </el-text>
          </div>
        </template>
      </avue-form>
      <!-- 项目合同 -->
      <avue-form
        :option="contractOptionB"
        ref="contractRef"
        @submit="handleImport"
        v-else-if="contractType == 1"
        v-model="contractForm"
      >
        <template #contractTotalPrice>
          <div>
            <el-text style="color: var(--el-radio-text-color)">
              本项目合同预算总金额为: 人民币（大写）：
              <el-input
                v-model="contractForm.contractTotalPriceCapital"
                style="width: 200px"
                placeholder=""
                size="small"
                clearable
              />
              ，（小写）：
              <el-input
                v-model="contractForm.contractTotalPrice"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />整。
            </el-text>
          </div>
        </template>
        <template #firstPayment>
          本合同签订后
          <el-input
            v-model="contractForm.firstPaymentWorkDays"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />个工作日内甲方应付给乙方本项目合同总额的
          <el-input
            v-model="contractForm.firstPaymentPercent"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />%款项作为首期款或订金，计人民币（大写）：
          <el-input
            v-model="contractForm.firstPaymentPriceCapital"
            style="width: 200px"
            placeholder=""
            size="small"
            clearable
          />
          ，（小写）：
          <el-input
            v-model="contractForm.firstPaymentPrice"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />元整，甲方转帐至乙方指定的开户银行和帐号。
        </template>
        <template #secondPayment>
          综合布线工程已经完成，并通知甲方项目经理或监理验收后，主要设备进场前，甲方应付给乙方本项目合同总额的
          <el-input
            v-model="contractForm.secondPaymentPercent"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />%款项作为进度款，计人民币（大写）：
          <el-input
            v-model="contractForm.secondPaymentPriceCapital"
            style="width: 200px"
            placeholder=""
            size="small"
            clearable
          />
          ，（小写）：
          <el-input
            v-model="contractForm.secondPaymentPrice"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />元整，甲方转帐至乙方指定的开户银行和帐号。
        </template>
        <template #threePayment>
          设备到场并安装调试完且通过甲乙双方共同验收后，甲方于<el-input
            v-model="contractForm.thirdPaymentWorkDays"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />个工作日内应付给乙方本项目合同总额的<el-input
            v-model="contractForm.thirdPaymentPercent"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />
          %款项作为验收款，计人民币（大写）：<el-input
            v-model="contractForm.thirdPaymentPriceCapital"
            style="width: 200px"
            placeholder=""
            size="small"
            clearable
          />
          ，（小写）：
          <el-input
            v-model="contractForm.thirdPaymentPrice"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />元整，甲方转帐至乙方指定的开户银行和帐号。
        </template>
        <template #fourPayment>
          从项目验收之日起12个月后，<el-input
            v-model="contractForm.fourthlyPaymentWorkDays"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />个工作日内甲方应支付给乙方本项目合同总额的
          <el-input
            v-model="contractForm.fourthlyPaymentPercent"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />%款项作为质保款，计人民币（大写）：
          <el-input
            v-model="contractForm.fourthlyPaymentPriceCapital"
            style="width: 200px"
            placeholder=""
            size="small"
            clearable
          />
          ，（小写）：
          <el-input
            v-model="contractForm.fourthlyPaymentPrice"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />元整，甲方转帐至乙方指定的开户银行和帐号。 乙方应给甲方开具本工程合同等额的税率为
          <el-input
            v-model="contractForm.taxRate"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />%的增值税专用发票。
        </template>
        <template #warrantyYearInfo>
          <div>
            <el-text
              >1、本系统正式运行期间如出现故障，乙方在接到通知后4小时委派项目工程师到达甲方现场，进行现场维修，直到排除故障。</el-text
            >
          </div>
          <div>
            <el-text
              >2、本项目所有设备乙方负责
              <el-input
                v-model="contractForm.warrantyYear"
                style="width: 80px"
                placeholder=""
                size="small"
                clearable
              />
              年免费维护，终身签约维修。</el-text
            >
          </div>
        </template>
        <template #prosecutionMethodInfo>
          <div>
            <el-radio
              style="margin-bottom: 20px"
              v-model="contractForm.prosecutionMethod"
              :label="1"
              ><div><el-text>1、提交乙方所在地仲裁委员会仲裁；</el-text></div></el-radio
            >
            <el-radio v-model="contractForm.payMethod" :label="2">
              <div><el-text>2、依法向乙方所在地的人民法院起诉。</el-text></div>
            </el-radio>
          </div>
        </template>
      </avue-form>
      <!-- 技术服务合同 -->
      <avue-form
        :option="contractOptionC"
        ref="contractRef"
        @submit="handleImport"
        v-else-if="contractType == 2"
        v-model="contractForm"
      >
        <template #serviceContent>
          乙方向甲方提供
          <el-input
            v-model="contractForm.projectName"
            style="width: 300px"
            placeholder=""
            size="small"
            clearable
          />服务，甲方向乙方支付相应费用。双方经过平等友好协商，在真实、充分地表达各自意愿的基础上，根据《中华人民共和国民法典》的规定，达成如下条款，并由双方共同恪守。
        </template>

        <template #payMethod>
          <div>
            1、本合同技术服务费用金额总计人民币（大写）
            <el-input
              v-model="contractForm.contractTotalPriceCapital"
              style="width: 200px"
              placeholder=""
              size="small"
              clearable
            />（￥
            <el-input
              v-model="contractForm.contractTotalPrice"
              style="width: 100px"
              placeholder=""
              size="small"
              clearable
            />
            元）
          </div>
          <div>
            2、付款时间：甲方应在本合同签订后<el-input
              v-model="contractForm.firstWorkDays"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />日内，向乙方支付上述全部款项。
          </div>
          <div>
            3、乙方在收到甲方所支付合同款后<el-input
              v-model="contractForm.secondWorkDays"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />个工作日内，向甲方开具等值的正式增值税专用发票。
          </div>
        </template>
      </avue-form>
      <!-- 硬件+服务销售合同 -->
      <avue-form
        :option="contractOptionD"
        ref="contractRef"
        @submit="handleImport"
        v-else-if="contractType == 4"
        v-model="contractForm"
      >
        <template #serviceContent>
          乙方向甲方提供
          <el-input
            v-model="contractForm.projectName"
            style="width: 300px"
            placeholder=""
            size="small"
            clearable
          />设备并提供相应技术服务，甲方向乙方支付相应费用。双方经过平等友好协商，在真实、充分地表达各自意愿的基础上，根据《中华人民共和国民法典》的规定，达成如下条款，并由双方共同恪守。
        </template>
        <template #menu1="{ row }">
          <el-button type="primary" @click="transformProduct(row.$index, 1)" text>转移</el-button>
        </template>
        <template #menu2="{ row }">
          <el-button type="primary" @click="transformProduct(row.$index, 2)" text>转移</el-button>
        </template>
        <template #deliveryInfo>
          甲方按本合同规定的方式支付相应款项后，乙方于
          <el-input
            v-model="contractForm.firstWorkDays"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />个工作日内向甲方提供货物 交货及安装地址
          <el-input
            v-model="contractForm.deliveryAddress"
            style="width: 400px"
            placeholder=""
            size="small"
            clearable
          />
          或甲方在乙方确认交货前以书面方式指定具体交货地点，接货人
          <el-input
            v-model="contractForm.aConcat"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />
          ，电话
          <el-input
            v-model="contractForm.aPhone"
            style="width: 150px"
            placeholder=""
            size="small"
            clearable
          />
          。
        </template>
        <template #acceptanceInfo>
          甲方收到目标产品以及乙方为甲方提供安装调试服务后<el-input
            v-model="contractForm.secondWorkDays"
            style="width: 80px"
            placeholder=""
            size="small"
            clearable
          />个工作日内应完成验收。若甲方未在该期限内针对验收提出书面意见或者乙方认为甲方提出的书面意见不合理的，则视为验收通过。验收标准适用乙方供应商的企业标准。
        </template>
        <template #afterSalesService>
          <div>
            硬件产品免费保修期为
            <el-input
              v-model="contractForm.warrantyYear"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            年
          </div>
          <div>乙方供应商为甲方提供退货、换货、硬件保修等售后服务。</div>
        </template>
        <template #payMethod>
          <div>
            1、本合同项下产品金额总计人民币
            <el-input
              v-model="contractForm.productTotalPriceCapital"
              style="width: 200px"
              placeholder=""
              size="small"
              clearable
            />（￥
            <el-input
              v-model="contractForm.productTotalPrice"
              style="width: 100px"
              placeholder=""
              size="small"
              clearable
            />
            元）；本合同项下技术服务费用金额总计人民币
            <el-input
              v-model="contractForm.serviceTotalPriceCapital"
              style="width: 200px"
              placeholder=""
              size="small"
              clearable
            />
            元整（￥
            <el-input
              v-model="contractForm.serviceTotalPrice"
              style="width: 100px"
              placeholder=""
              size="small"
              clearable
            />
            元整）； 本合同金额总计人民币
            <el-input
              v-model="contractForm.contractTotalPriceCapital"
              style="width: 200px"
              placeholder=""
              size="small"
              clearable
            />
            元整（￥
            <el-input
              v-model="contractForm.contractTotalPrice"
              style="width: 100px"
              placeholder=""
              size="small"
              clearable
            />
            元整）。
          </div>
          <div>
            2、乙方在合同签订后3个工作日内，向甲方开具合法等值的正式增值税专用发票。（硬件产品开具13%点，安装服务费及云服务开具6%点。）
          </div>
          <div>
            3、付款时间：甲方应在收到乙方提供的合法等值发票后5个工作日内，向乙方付清上述全部合同款项。
          </div>
        </template>
        <template #otherInfo>
          <div>
            1、本合同壹式<el-input
              v-model="contractForm.copies"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />份，双方各执
            <el-input
              v-model="contractForm.eachHoldCopies"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />份，具有同等法律效力。
          </div>
          <div>2、本合同的变更必须由双方协商一致，并以书面形式确定。</div>
          <div>3、本合同经双方法定代表人签字并加盖公章后生效。</div>
        </template>
      </avue-form>
      <!-- IT运维服务合同 -->
      <avue-form
        :option="contractOptionE"
        ref="contractRef"
        @submit="handleImport"
        v-else-if="contractType == 3"
        v-model="contractForm"
      >
        <template #serviceContent>
          <div>
            乙方向甲方提供
            <el-input
              v-model="contractForm.projectName"
              style="width: 300px"
              placeholder=""
              size="small"
              clearable
            />服务，甲方向乙方支付相应费用。双方经过平等友好协商，在真实、充分地表达各自意愿的基础上，根据《中华人民共和国民法典》的规定，达成如下条款，并由双方共同恪守。
          </div>
          <div>1，服务内容：</div>
          <div style="text-indent: 20px">
            <div>
              1）现有公司办公电脑的正常工作运维
              <el-input
                v-model="contractForm.computerNumber"
                style="width: 100px"
                placeholder=""
                type="number"
                size="small"
                clearable
              />
              台；
            </div>
            <div>
              2） 现有公司服务器的正常工作运维
              <el-input
                v-model="contractForm.serviceNumber"
                style="width: 100px"
                placeholder=""
                type="number"
                size="small"
                clearable
              />
              台；
            </div>
            <div>
              3） 网络设备的正常工作运维
              <el-input
                v-model="contractForm.networkEquipmentNumber"
                style="width: 100px"
                placeholder=""
                type="number"
                size="small"
                clearable
              />
              台；
            </div>
            <div>
              4） 打印机等办公设备的正常工作运维
              <el-input
                v-model="contractForm.workEquipmentNumber"
                style="width: 100px"
                placeholder=""
                type="number"
                size="small"
                clearable
              />
              台；
            </div>
            <div>5）数据的备份培训与提醒；</div>
            <div>6）新增IT设备的安装调试；</div>
            <div>7）IT资产的统计登记及管理；</div>
          </div>
          <div>
            2，服务总费用：大写
            <el-input
              v-model="contractForm.contractTotalPriceCapital"
              style="width: 200px"
              placeholder=""
              size="small"
              clearable
            />；小写：
            <el-input
              v-model="contractForm.contractTotalPrice"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />元整
          </div>
          <div>
            3，服务期限：本合同经甲、乙双方签字盖章后生效，协议期限为
            <el-input
              v-model="contractForm.startYear"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            年
            <el-input
              v-model="contractForm.startMonth"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            月
            <el-input
              v-model="contractForm.startDay"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            日至
            <el-input
              v-model="contractForm.endYear"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            年
            <el-input
              v-model="contractForm.endMonth"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            月
            <el-input
              v-model="contractForm.endDay"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />
            日
          </div>
        </template>

        <template #payMethod>
          <div>
            支付方式：合同签订后<el-input
              v-model="contractForm.firstWorkDays"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />个工作日内，甲方应以银行转账方式向乙方支付本合同规定期限的服务费用；乙方在收到甲方所支付合同款后<el-input
              v-model="contractForm.secondWorkDays"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />个工作日内，向甲方开具等值的正式增值税专用发票（6%）。
          </div>
        </template>
        <template #otherInfo>
          <div>
            1、本合同壹式<el-input
              v-model="contractForm.copies"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />份，双方各执
            <el-input
              v-model="contractForm.eachHoldCopies"
              style="width: 80px"
              placeholder=""
              size="small"
              clearable
            />份，具有同等法律效力。
          </div>
          <div>2、本合同的变更必须由双方协商一致，并以书面形式确定。</div>
          <div>3、本合同经双方盖章后生效。</div>
        </template>
      </avue-form>
      <template #footer>
        <el-button type="primary" icon="check" v-loading="exportLoading" :disabled="exportLoading" @click="$refs.contractRef?.submit">导 出</el-button>
      </template>
    </el-drawer>
    <el-dialog
      v-model="addDialogVisible"
      width="300"
      :title="'选择合同类型'"
      style="border-radius: 10px"
      :show-close="false"
      align-center
    >
      <div style="display: flex; align-items: center; justify-content: center">
        <el-button type="primary" @click="handleToAdd(0)" size="large" round>产品买卖</el-button>
        <el-button type="primary" size="large" @click="handleToAdd(1)" round>工程项目</el-button>
        <!-- <el-button type="primary" size="large" @click="handleToAdd(2)" round>IT服务</el-button> -->
      </div>
    </el-dialog>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { offerStatus, auditStatus } from '@/const/const.js';
import { downloadOwn } from '@/utils/util';
import { ElNotification } from 'element-plus';
import { h } from 'vue';
import { download as downloadOffer, downloadByUrl } from '@/utils/download.js';
import history from './compoents/history.vue';
import progress from '@/components/progress/index.vue';
import process from './compoents/process.vue';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { detail } from '@/api/flow/flow';
import productSelectDrop from '@/views/CRM/quotation/compoents/productSelectDrop.vue';
import { column } from 'element-plus/es/components/table-v2/src/common';
import { DX } from '@/utils/util';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchIcon: true,
  searchIndex: 4,
  menuWidth: 280,
  border: true,
  expand: true,
  rowKey: 'id',
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      width: 200,
      overHidden: true,
      search: true,
    },

    {
      label: '关联商机',
      prop: 'businessOpportunityName',
      width: 200,
      component: 'wf-bussiness-drop',
      overHidden: true,
      search: true,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      overHidden: true,
      component: 'wf-customer-drop',
      search: !props.customerId,
      hide: !!props.customerId,
    },
    // {
    //   label: '关联联系人',
    //   prop: 'contactPersonName',
    // },

    {
      label: '业务员',
      prop: 'createName',
      width: 100,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      search: true,
      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '总金额（元）',
      prop: 'offerPrice',
      width: 120,
      formatter: row => {
        return parseFloat(row.offerPrice).toLocaleString();
      },
    },
    {
      label: '有效期(天)',
      prop: 'offerValidity',
      width: 100,
    },
    {
      label: '创建时间',
      prop: 'createTime',
      type: 'date',
      overHidden: true,
      format: 'YYYY-MM-DD HH:mm',
    },
    {
      label: '报价状态',
      type: 'select',
      dicData: offerStatus,
      width: 120,
      prop: 'offerStatus',
      search: true,
    },
    {
      label: '审核状态',
      type: 'select',
      width: 100,
      dicData: auditStatus,
      prop: 'auditStatus',
      search: true,
    },
    {
      label: '产品名称',
      type: 'input',
      width: 100,
      hide: true,
      prop: 'productName',
      search: true,
    },
    {
      label: '版本',
      type: 'input',
      width: 70,
      prop: 'version',
      formatter: row => {
        return `v${row.version}.0`;
      },
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  customerId: String,
});
const addUrl = '';
const delUrl = '/api/vt-admin/offer/deletedById?id=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/offer/page';
let route = useRoute();
let params = ref({
  ids: route.query.ids,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
onActivated(() => {
  onLoad();
});
watch(
  () => route.query.ids,
  () => {
    params.value.ids = route.query.ids;
    onLoad();
  }
);
let loading = ref(false);
let totalPrice = ref(0);
// let isMy = ref(route.query.type == 0 || !route.query.type);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        // selectType: isMy.value ? 0 : 2,
        isSelectHistory: params.value.isSelectHistory ? 1 : '',
        selectType: props.customerId ? 2 : 0,
        customerId: props.customerId || null,

        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });

  // 获取统计数据
  axios
    .get('/api/vt-admin/offer/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        // selectType: isMy.value ? 0 : 2,
        isSelectHistory: params.value.isSelectHistory ? 1 : '',
        customerId: props.customerId || null,
        selectType: props.customerId ? 2 : 0,
        startTime: params.value.offerDate && params.value.offerDate[0],
        endTime: params.value.offerDate && params.value.offerDate[1],
        offerDate: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function searchReset() {
  (params.value.ids = null), onLoad();
}
function download(row) {
  downloadOffer('/api/vt-admin/offer/downloadOffer', { id: row.id }, () => {
    onLoad();
    proxy.$store.dispatch('getMessageList');
  });
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleAdd() {
  router.push('/CRM/quotation/compoents/addVersion3?type=add');
}
function handleEdit(row) {
  if (row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'edit',
        name: '编辑方案',
        isAdmin: 'admin',
      },
    });
  } else {
    router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        name: row.offerName,
      },
    });
  }
}
function handleEditOffer(row) {
  router.push({
    path: '/CRM/quotation/compoents/editOffer',
    query: {
      id: row.id,
      type: 'edit',
    },
  });
}
function handleView(row) {
  if (row.dataJson && row.isHasOption != 1) {
    router.push({
      path: '/CRM/quotation/compoents/add',
      query: {
        id: row.id,
        name: row.offerName,
        type: 'detail',
      },
    });
  } else if (row.dataJson && row.isHasOption == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.offerName,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  } else if (!row.dataJson && !row.isHasOption) {
    router.push({
      path: '/CRM/quotation/compoents/addVersion3',
      query: {
        id: row.id,
        type: 'detail',
        name: row.offerName,
        repairId: row.repairId,
        businessOpportunityId: row.isHasOption == 1 ? row.businessOpportunityId : null,
      },
    });
  } else {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.businessOpportunityId,
        type: 'detail',
        name: row.offerName,
        // businessOpportunityId: row.businessOpportunityId,
      },
    });
  }

  // }
}
function handleViewSub(row) {
  if (row.isHasDataJson == 1) {
    router.push({
      path: '/CRM/programme/compoents/update',
      query: {
        id: row.id,
        // name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else if (row.isHasDataJson == 0 || !row.isHasDataJson) {
    router.push({
      path: '/CRM/programme/compoents/updateVersion3',
      query: {
        id: row.optionId,
        // name: row.optionName,
        type: 'detail',
        isEditSubedition: 1,
      },
    });
  } else {
    proxy.$message.error('未查询到方案');
  }
}
let dialogRef = '';
function customerConfirm(row) {
  proxy.$refs.dialogForm.show({
    title: '登记',
    option: {
      labelWidth: 150,
      column: [
        {
          label: '登记结果',
          prop: 'offerStatus',
          type: 'radio',
          rules: [
            {
              required: true,
              message: '请选择登记结果',
              trigger: 'change',
            },
          ],
          dicData: [
            {
              label: '成交',
              value: 2,
            },

            {
              label: '失单',
              value: 4,
            },
          ],
          control: val => {
            return {
              failReason: {
                display: val == 4,
              },
              isSplit: {
                display: val == 2 && row.isIntelligentizeProject != 1,
              },
              purchaseRemark: {
                display: val == 2,
              },
            };
          },
          span: 24,
        },
        // {
        //   label: '拆解产品',
        //   prop: 'isSplit',
        //   type: 'switch',
        //   dicData: [
        //     {
        //       label: '否',
        //       value: 0,
        //     },
        //     {
        //       label: '是',
        //       value: 1,
        //     },
        //   ],
        //   span: 12,
        //   display: false,
        // },
        // {
        //   label: '采购注意事项',
        //   prop: 'purchaseRemark',
        //   type: 'textarea',
        //   display: false,
        //   span: 24,
        // },
        {
          label: '失单原因',
          prop: 'failReason',
          type: 'textarea',
          display: false,
          span: 24,
        },
      ],
    },
    callback(res) {
      console.log(res);
      dialogRef = res;
      if (res.data.isSplit == 1) {
        customerConfirmAndDecompose(row);
      } else {
        if (res.data.offerStatus == 2) {
          if (row.isIntelligentizeProject == 1) {
            router.push({
              path: '/Project/add',
              query: {
                offerId: row.id,
              },
            });
          } else {
            router.push({
              path: '/Order/salesOrder/compoents/addOrder',
              query: {
                offerId: row.id,
              },
            });
          }
          res.close();
        } else {
          axios
            .post('/api/vt-admin/offer/customerConfirm', {
              id: row.id,
              ...res.data,
            })
            .then(e => {
              proxy.$message.success(e.data.msg);
              res.close();

              onLoad();
            });
        }
      }
    },
  });
}

let dialogVisible = ref(false);
let currentId = ref(null);
let currentStatus = ref(null);
function viewHistory(row) {
  currentId.value = row.id;
  currentStatus.value =
    ((row.auditStatus == 0 && row.offerStatus == 0) ||
      row.auditStatus == 2 ||
      (row.offerStatus == 3 && row.auditStatus == 1)) &&
    row.isHasOption == 0;

  dialogVisible.value = true;
}
function back(row) {
  proxy.$refs.dialogForm.show({
    title: '撤回',
    option: {
      column: [
        {
          label: '撤回原因',
          prop: 'failReason',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/offer/backOption', {
          id: row.id,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('撤回成功');
          res.close();
          onLoad();
        });
    },
  });
}
function backToConfirm(row) {
  proxy.$confirm('确定要退回到待确认状态吗？', '提示', {}).then(() => {
    axios
      .post('/api/vt-admin/offer/returnWaitConfirm', {
        id: row.id,
      })
      .then(e => {
        proxy.$message.success('操作成功');
        onLoad();
      });
  });
}
// 流程
function viewProcess(row) {
  currentId.value = row.id;
  proxy.$refs.processRef.open();
}
function expandChanges(row, expendList) {
  if (expendList.length) {
    option.value.expandRowKeys = [];
    if (row) {
      option.value.expandRowKeys.push(row.id);
    }
  } else {
    option.value.expandRowKeys = [];
  }
}
function getClass({ row }) {
  return row.isHasManyOffer == 0 ? 'hide_icon' : '';
}
// 拆解
let drawer = ref(false);
let currentRow = ref(null);
function customerConfirmAndDecompose(row) {
  drawer.value = true;
  currentRow.value = row;
  getNeedDeposeData(row);
}

let decomposeEditFormOption = ref({
  submitBtn: false,
  emptyBtn: false,
  editBtn: false,
  // header:false,
  addBtn: false,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      width: 120,
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
let needDeposeData = ref([]);
let needDeposeOption = ref({
  header: false,
  addBtn: false,
  border: true,
  editBtn: false,
  delBtn: false,
  menuWidth: 80,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      overHidden: true,
      cell: false,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      type: 'input',
    },
    {
      label: '产品分类',
      prop: 'categoryId',

      hide: true,
      filterable: true,
      type: 'tree',
      cell: false,
      rules: [
        {
          required: true,
          message: '请选择产品分类',
          trigger: 'change',
        },
      ],
      children: 'hasChildren',
      parent: false,
      addDisplay: false,
      cell: false,
      dicUrl: '/api/vt-admin/productCategory/tree',
      props: {
        label: 'categoryName',
        value: 'id',
      },
    },
    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      overHidden: true,
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      value: '1693833617402880001',
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入数量',
          trigger: 'blur',
        },
      ],
    },
  ],
});
function getNeedDeposeData(row) {
  axios
    .get('/api/vt-admin/offer/getOfferProducts', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      needDeposeData.value = res.data.data;
    });
}
let detailForm = ref({});
function handleRowClick(row) {
  detailForm.value = row;
  if (!detailForm.value.detailDTOList) {
    detailForm.value.detailDTOList = [];
  }
}
async function handleProductSelectConfirm(id) {
  console.log(detailForm.value == needDeposeData.value[0]);
  const res = await axios.get('/api/vt-admin/product/detail?id=' + id);
  detailForm.value.detailDTOList.push({
    productId: res.data.data.id,
    productName: res.data.data.productName,
    productSpecification: res.data.data.productSpecification,
    productBrand: res.data.data.productBrand,
    unit: res.data.data.unitName,
    number: detailForm.value.number,
  });
}
function rowDelProduct(form) {
  proxy
    .$confirm('此操作将删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      detailForm.value.detailDTOList = detailForm.value.detailDTOList.filter(
        item => item.productId !== form.productId
      );
    })
    .catch(() => {});
}
function handleIsPreChange(val) {
  console.log(val);
  if (val) {
    handleProductSelectConfirm(detailForm.value.productId);
  } else {
    rowDelProduct({
      productId: detailForm.value.productId,
    });
  }
}
function decomposeSubmit() {
  const data = {
    id: currentRow.value.id,
    offerStatus: 2,
    dismantleList: needDeposeData.value
      .filter(item => item.isDepose == 1)
      .map(item => {
        return {
          id: item.id,
          productList: item.detailDTOList.map(item => {
            return {
              productId: item.productId,
              number: item.number,
            };
          }),
        };
      }),
  };
  console.log(data);
  axios.post('/api/vt-admin/offer/customerConfirm', data).then(e => {
    proxy.$message.success(e.data.msg);
    dialogRef.close();
    drawer.value = false;

    onLoad();

    if (currentRow.value.isIntelligentizeProject == 1) {
      router.push({
        path: '/Project/add',
        query: {
          offerId: currentRow.value.id,
        },
      });
    } else {
      router.push({
        path: '/Order/salesOrder/compoents/addOrder',
        query: {
          offerId: currentRow.value.id,
        },
      });
    }
  });
}
function copyOffer(row) {
  router.push({
    path: '/CRM/quotation/compoents/addVersion3',
    query: {
      id: row.id,
      name: row.offerName,
      copy: 1,
    },
  });
}
function pause(row) {
  proxy.$refs.dialogForm.show({
    title: '暂停',

    option: {
      column: [
        {
          label: '暂停原因',
          prop: 'pauseReason',
          span: 24,
          type: 'textarea',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/offer/pause', {
          id: row.id,
          pauseReason: res.data.pauseReason,
        })
        .then(r => {
          proxy.$message.success(r.data.msg);
          res.close();
          onLoad();
        });
    },
  });
}
function start(row) {
  proxy
    .$confirm('是否重启?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/offer/restart', {
          id: row.id,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          onLoad();
        });
    });
}
let contractDrawer = ref(false);
let companyData = ref([]);

let contractOptionA = ref({
  submitBtn: false,
  emptyBtn: false,
  // tabs: true,
  group: [
    {
      label: '甲方信息',
      prop: 'A',

      column: [
        {
          label: '客户方',
          prop: 'aName',
          span: 24,
          type: 'input',
        },
        {
          label: '法定代表人',
          prop: 'aLegalRepresentative',
          span: 12,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'aConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'aAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'aPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'aEmail',
          span: 12,
        },
      ],
    },
    {
      label: '乙方信息',
      prop: 'B',

      column: [
        {
          label: '供货方',
          type: 'select',
          prop: 'bName',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            companyData.value = res.data.records;
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '法定代表人',
          prop: 'bLegalRepresentative',
          span: 12,
          readonly: true,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'bConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'bAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'bPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'bEmail',
          span: 12,
        },
      ],
    },
    {
      label: '交付信息',
      column: [
        {
          label: '关联联系人',
          type: 'input',
          prop: 'consignee',
          component: 'wf-contact-select',
          placeholder: '请先选择报价',
          // disabled: true,
          params: {},
          change: ({ value }) => {
            setBaseInfo2(value);
          },
        },
        {
          type: 'input',
          label: '联系电话',
          span: 12,
          display: true,
          prop: 'consigneePhone',
        },
        {
          label: '交付地址',
          prop: 'deliveryAddress',
          span: 24,
        },
      ],
    },
    {
      label: '售后信息',
      prop: 'C',
      column: [
        {
          label: '售后方式',

          prop: 'afterSealsServiceMethod',
        },
      ],
    },
    {
      label: '支付信息',
      prop: 'payMentInfo',
      column: [
        {
          label: '合同总额',
          prop: 'contractTotalPrice',
          span: 24,
        },
        {
          label: '支付方式',
          prop: 'payMethod',
          span: 24,
        },
        {
          type: 'select',
          label: '名称',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
          prop: 'payName',
        },
        {
          label: '地  址',
          prop: 'payAddress',
          span: 24,
          type: 'input',
        },
        {
          type: 'select',
          label: '开户行',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            bankList.value = res.data.records;
            return res.data.records;
          },
          change: ({ value }) => {
            if (!value) return;
            setBankInfo(value);
          },
          props: {
            label: 'bankName',
            value: 'bankName',
            desc: 'desc',
          },
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'payBank',
        },
        {
          label: '账号',

          prop: 'payAccount',
          span: 12,
        },
        {
          label: '税  号',
          prop: 'payTaxNumber',
          span: 12,
        },
        {
          label: '电　 话',
          prop: 'payPhone',
          span: 12,
        },
      ],
    },
    {
      label: '违约责任',
      prop: 'D',
      column: [
        {
          label: '售后方式',
          prop: 'weiyue',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
  ],
});
let contractOptionB = ref({
  submitBtn: false,
  emptyBtn: false,
  labelWidth: 120,
  group: [
    {
      label: '基本信息',
      prop: 'A',
      column: [
        {
          label: '项目名称',
          prop: 'projectName',
        },
        {
          label: '项目执行时间',
          prop: 'completeDays',
          append: '天',
        },
        {
          label: '项目地址',
          prop: 'projectAddress',
          span: 24,
        },

        {
          label: '项目金额',
          prop: 'contractTotalPrice',
          span: 24,
        },
      ],
    },
    {
      label: '甲方信息',
      prop: 'B',
      column: [
        {
          label: '甲方',
          prop: 'aName',
          span: 24,
          type: 'input',
        },
        {
          label: '地址',
          prop: 'aAddress',
          span: 24,
        },
        {
          label: '联系人',
          prop: 'aConcat',
          span: 12,
        },

        {
          label: '联系电话',
          prop: 'aPhone',
          span: 12,
        },
      ],
    },
    {
      label: '乙方信息',
      prop: 'C',

      column: [
        {
          label: '乙方',
          type: 'select',
          prop: 'bName',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            companyData.value = res.data.records;
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '地址',
          prop: 'bAddress',
          span: 24,
        },
        {
          label: '联系人',
          prop: 'bConcat',
          span: 12,
        },

        {
          label: '联系电话',
          prop: 'bPhone',
          span: 12,
        },
      ],
    },
    {
      label: '支付信息',
      prop: 'payMentInfo',
      column: [
        {
          type: 'select',
          label: '名称',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
          prop: 'payName',
        },
        {
          label: '地  址',
          prop: 'payAddress',
          span: 24,
          type: 'input',
        },
        {
          type: 'select',
          label: '开户行',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            bankList.value = res.data.records;
            return res.data.records;
          },
          change: ({ value }) => {
            if (!value) return;
            setBankInfo(value);
          },
          props: {
            label: 'bankName',
            value: 'bankName',
            desc: 'desc',
          },
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'payBank',
        },
        {
          label: '账号',

          prop: 'payAccount',
          span: 12,
        },
        {
          label: '税  号',
          prop: 'payTaxNumber',
          span: 12,
        },
        {
          label: '电　 话',
          prop: 'payPhone',
          span: 12,
        },
      ],
    },
    {
      label: '第一笔款(预付款)',
      prop: 'D',
      column: [
        {
          label: '',
          prop: 'firstPayment',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
    {
      label: '第二笔款（进度款）',
      prop: 'E',
      column: [
        {
          label: '',
          prop: 'secondPayment',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
    {
      label: '第三笔款(验收款)',
      prop: 'F',
      column: [
        {
          label: '',
          prop: 'threePayment',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
    {
      label: '第四笔款(质保款)',
      prop: 'G',
      column: [
        {
          label: '',
          prop: 'fourPayment',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
    {
      label: '售后信息',
      prop: 'H',
      column: [
        {
          prop: 'warrantyYearInfo',
          span: 24,
          labelWidth: 0,
        },
      ],
    },
    {
      label: '争议解决',
      prop: 'I',
      column: [
        {
          prop: 'prosecutionMethodInfo',
          labelWidth: 0,
          span: 24,
        },
      ],
    },
  ],
});
let contractOptionC = ref({
  submitBtn: false,
  emptyBtn: false,
  // tabs: true,
  group: [
    {
      label: '甲方信息',
      prop: 'A',

      column: [
        {
          label: '客户方',
          prop: 'aName',
          span: 24,
          type: 'input',
        },
        {
          label: '法定代表人',
          prop: 'aLegalRepresentative',
          span: 12,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'aConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'aAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'aPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'aEmail',
          span: 12,
        },
      ],
    },
    {
      label: '乙方信息',
      prop: 'B',

      column: [
        {
          label: '供货方',
          type: 'select',
          prop: 'bName',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            companyData.value = res.data.records;
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '法定代表人',
          prop: 'bLegalRepresentative',
          span: 12,
          readonly: true,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'bConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'bAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'bPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'bEmail',
          span: 12,
        },
      ],
    },
    {
      label: '服务内容',
      prop: 'C',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'serviceContent',
          span: 24,
        },
      ],
    },
    {
      label: '支付信息',
      prop: 'payMentInfo',
      column: [
        {
          label: '支付方式',
          prop: 'payMethod',
          span: 24,
        },
        {
          type: 'select',
          label: '名称',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
          prop: 'payName',
        },
        {
          label: '地  址',
          prop: 'payAddress',
          span: 24,
          type: 'input',
        },
        {
          type: 'select',
          label: '开户行',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            bankList.value = res.data.records;
            return res.data.records;
          },
          change: ({ value }) => {
            if (!value) return;
            setBankInfo(value);
          },
          props: {
            label: 'bankName',
            value: 'bankName',
            desc: 'desc',
          },
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'payBank',
        },
        {
          label: '账号',

          prop: 'payAccount',
          span: 12,
        },
        {
          label: '税  号',
          prop: 'payTaxNumber',
          span: 12,
        },
        {
          label: '电　 话',
          prop: 'payPhone',
          span: 12,
        },
      ],
    },
  ],
});
let contractOptionD = ref({
  submitBtn: false,
  emptyBtn: false,
  // tabs: true,
  group: [
    {
      label: '甲方信息',
      prop: 'A',

      column: [
        {
          label: '客户方',
          prop: 'aName',
          span: 24,
          type: 'input',
        },
        {
          label: '法定代表人',
          prop: 'aLegalRepresentative',
          span: 12,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'aConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'aAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'aPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'aEmail',
          span: 12,
        },
      ],
    },
    {
      label: '乙方信息',
      prop: 'B',

      column: [
        {
          label: '供货方',
          type: 'select',
          prop: 'bName',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            companyData.value = res.data.records;
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '法定代表人',
          prop: 'bLegalRepresentative',
          span: 12,
          readonly: true,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'bConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'bAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'bPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'bEmail',
          span: 12,
        },
      ],
    },
    {
      label: '服务内容',
      prop: 'C',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'serviceContent',
          span: 24,
        },
      ],
    },
    {
      label: '产品信息',
      prop: 'D',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'productList',
          span: 24,
          type: 'dynamic',
          children: {
            addBtn: false,
            editBtn: false,
            delBtn: false,
            align: 'center',
            size: 'small',
            showSummary: true,
            sumColumnList: [{ label: '合计:', name: 'hsze', type: 'sum', decimals: 2 }],
            column: [
              {
                label: '产品名称',
                prop: 'customProductName',
                cell: false,
                overHidden: true,
                formatter: row => {
                  return row.customProductName || row.productName;
                },
              },
              {
                label: '产品型号',
                prop: 'customProductSpecification',
                cell: false,
                overHidden: true,
                formatter: row => {
                  return row.customProductSpecification || row.productModel;
                },
              },
              {
                label: '单位',
                prop: 'customUnit',
                cell: false,
                width: 80,
                overHidden: true,
                formatter: row => {
                  return row.customUnit || row.unit;
                },
              },
              {
                label: '数量',
                prop: 'number',
                cell: false,
                width: 80,
                overHidden: true,
              },
              {
                label: '单价',
                prop: 'zhhsdj',
                cell: false,
                width: 100,
                overHidden: true,
                formatter: row => {
                  return row.zhhsdj || row.price;
                },
              },
              {
                label: '金额',
                prop: 'hsze',
                cell: false,
                width: 120,
                overHidden: true,
                formatter: row => {
                  return row.hsze || row.totalPrice;
                },
              },
              {
                label: '操作',
                prop: 'menu1',
                width: 100,
                cell: true,
              },
            ],
          },
        },
      ],
    },
    {
      label: '交货',
      prop: 'E',
      column: [
        {
          labelWidth: 0,
          prop: 'deliveryInfo',
          span: 24,
        },
      ],
    },
    {
      label: '验收',
      prop: 'F',
      column: [
        {
          labelWidth: 0,
          prop: 'acceptanceInfo',
          span: 24,
        },
      ],
    },
    {
      label: '产品售后服务',
      prop: 'G',
      column: [
        {
          labelWidth: 0,
          prop: 'afterSalesService',
          span: 24,
        },
      ],
    },
    {
      label: '服务列表',
      prop: 'I',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'serviceList',
          span: 24,
          type: 'dynamic',
          children: {
            addBtn: false,
            editBtn: false,
            delBtn: false,
            align: 'center',
            size: 'small',
            showSummary: true,
            sumColumnList: [{ label: '合计:', name: 'hsze', type: 'sum', decimals: 2 }],
            column: [
              {
                label: '产品名称',
                prop: 'customProductName',
                cell: false,
                overHidden: true,
                formatter: row => {
                  return row.customProductName || row.productName;
                },
              },
              {
                label: '产品型号',
                prop: 'customProductSpecification',
                cell: false,
                overHidden: true,
                formatter: row => {
                  return row.customProductSpecification || row.productModel;
                },
              },
              {
                label: '单位',
                prop: 'customUnit',
                cell: false,
                width: 80,
                overHidden: true,
                formatter: row => {
                  return row.customUnit || row.unit;
                },
              },
              {
                label: '数量',
                prop: 'number',
                cell: false,
                width: 80,
                overHidden: true,
              },
              {
                label: '单价',
                prop: 'zhhsdj',
                cell: false,
                width: 100,
                overHidden: true,
                formatter: row => {
                  return row.zhhsdj || row.price;
                },
              },
              {
                label: '金额',
                prop: 'hsze',
                cell: false,
                width: 120,
                overHidden: true,
                formatter: row => {
                  return row.hsze || row.totalPrice;
                },
              },
              {
                label: '操作',
                prop: 'menu2',
                width: 100,
                cell: true,
              },
            ],
          },
        },
      ],
    },
    {
      label: '支付信息',
      prop: 'payMentInfo',
      column: [
        {
          label: '支付方式',
          prop: 'payMethod',
          span: 24,
        },
        {
          type: 'select',
          label: '名称',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
          prop: 'payName',
        },
        {
          label: '地  址',
          prop: 'payAddress',
          span: 24,
          type: 'input',
        },
        {
          type: 'select',
          label: '开户行',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            bankList.value = res.data.records;
            return res.data.records;
          },
          change: ({ value }) => {
            if (!value) return;
            setBankInfo(value);
          },
          props: {
            label: 'bankName',
            value: 'bankName',
            desc: 'desc',
          },
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'payBank',
        },
        {
          label: '账号',

          prop: 'payAccount',
          span: 12,
        },
        {
          label: '税  号',
          prop: 'payTaxNumber',
          span: 12,
        },
        {
          label: '电　 话',
          prop: 'payPhone',
          span: 12,
        },
      ],
    },
    {
      label: '其它',
      prop: 'other',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'otherInfo',
          span: 24,
        },
      ],
    },
  ],
});
let contractOptionE = ref({
  submitBtn: false,
  emptyBtn: false,
  // tabs: true,
  group: [
    {
      label: '甲方信息',
      prop: 'A',

      column: [
        {
          label: '客户方',
          prop: 'aName',
          span: 24,
          type: 'input',
        },
        {
          label: '法定代表人',
          prop: 'aLegalRepresentative',
          span: 12,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'aConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'aAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'aPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'aEmail',
          span: 12,
        },
      ],
    },
    {
      label: '乙方信息',
      prop: 'B',

      column: [
        {
          label: '供货方',
          type: 'select',
          prop: 'bName',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            companyData.value = res.data.records;
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
        },
        {
          label: '法定代表人',
          prop: 'bLegalRepresentative',
          span: 12,
          readonly: true,
          type: 'input',
        },
        {
          label: '联系人',
          prop: 'bConcat',
          span: 12,
        },
        {
          label: '通讯地址',
          prop: 'bAddress',
          span: 24,
        },
        {
          label: '电话',
          prop: 'bPhone',
          span: 12,
        },
        {
          label: '信箱',
          prop: 'bEmail',
          span: 12,
        },
      ],
    },
    {
      label: '服务内容',
      prop: 'C',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'serviceContent',
          span: 24,
        },
      ],
    },

    {
      label: '支付信息',
      prop: 'payMentInfo',
      column: [
        {
          label: '支付方式',
          prop: 'payMethod',
          span: 24,
        },
        {
          type: 'select',
          label: '名称',
          props: {
            label: 'companyName',
            value: 'companyName',
          },
          dicFormatter: res => {
            return res.data.records;
          },
          span: 24,
          overHidden: true,
          cell: false,
          dicUrl: '/api/vt-admin/company/page?size=100',
          prop: 'payName',
        },
        {
          label: '地  址',
          prop: 'payAddress',
          span: 24,
          type: 'input',
        },
        {
          type: 'select',
          label: '开户行',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            bankList.value = res.data.records;
            return res.data.records;
          },
          change: ({ value }) => {
            if (!value) return;
            setBankInfo(value);
          },
          props: {
            label: 'bankName',
            value: 'bankName',
            desc: 'desc',
          },
          span: 12,
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'payBank',
        },
        {
          label: '账号',

          prop: 'payAccount',
          span: 12,
        },
        {
          label: '税  号',
          prop: 'payTaxNumber',
          span: 12,
        },
        {
          label: '电　 话',
          prop: 'payPhone',
          span: 12,
        },
      ],
    },
    {
      label: '其它',
      prop: 'other',
      column: [
        {
          label: '',
          labelWidth: 0,
          prop: 'otherInfo',
          span: 24,
        },
      ],
    },
  ],
});
let contractForm = ref({});
async function addContract(row) {}
//
let exportLoading = ref(false)
function handleImport(form, done, loading) {
  
  const data = {
    ...form,
    contractType: contractType.value,
    offerId: currentRowForContract.value.id,
    productIds: form.productList?.map(item => item.id).join(','),
    serviceIds: form.serviceList?.map(item => item.id).join(','),
  };
  exportLoading.value = true
  axios
    .post(`/api/vt-admin/contractFiles/${form.id ? 'update' : 'save'}`, data)
    .then(res => {
      proxy.$message.success('操作成功');
      done();
      // downloadByUrl(res.data.data,{},'get');
      window.open(res.data.data);
       exportLoading.value = false
    })
    .catch(() => {
      done();
      exportLoading.value = false
    });
}

function setBaseInfo2(id) {
  axios
    .get('/api/vt-admin/customerContact/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      contractForm.value.consigneePhone = res.data.data.phone;
    });
}
let bankList = ref([]);
function setBankInfo(value) {
  const bank = bankList.value.find(item => item.bankName == value);
  contractForm.value.payAccount = bank.accountNumber;
}

let addDialogVisible = ref(false);
let currentRowForContract = ref({});
function handleAddDialog(row) {
  // addDialogVisible.value = true;
  currentRowForContract.value = row;
  let value = row.isIntelligentizeProject;
  handleToAdd(value);
}
let contractType = ref(0);
watch(
  () => contractType.value,
  (nv, ov) => {
    contractForm.value.secondWorkDays = nv == 0 || nv == 4 ? 5 : 3;
    contractForm.value.firstWorkDays = nv == 0 || nv == 4 ? 5 : 3;
    contractForm.value.copies = nv == 4 ? '肆' : '贰';
    contractForm.value.eachHoldCopies = nv == 4 ? '贰' : '壹';
  }
);
async function handleToAdd(value) {
  contractForm.value.id = '';
  const row = currentRowForContract.value;
  contractType.value = value;
  if (proxy.$refs.contractRef) {
    proxy.$refs.contractRef.resetForm();
  }
  axios
    .get('/api/vt-admin/contractFiles/detailByOfferId', {
      params: {
        offerId: currentRowForContract.value.id,
      },
    })
    .then(async res => {
      if (!res.data.data) {
        let res0 = await axios.get('/api/vt-admin/offer/detail', {
          params: {
            id: row.id,
          },
        });
        const offerRes = res0?.data?.data ?? {};
        let res = await axios.get('/api/vt-admin/customerContact/detail', {
          params: {
            id: offerRes.contactPerson,
          },
        });
        const customerContactRes = res?.data?.data ?? {};
        let res2 = await axios.get('/api/vt-admin/customer/detail', {
          params: { id: offerRes.customerId },
        });
        const customerRes = res2?.data?.data ?? {};
        const consignee = proxy.findObject(contractOptionA.value.group[2].column, 'consignee');
        consignee.params.Url = '/vt-admin/customerContact/page?customerId=' + offerRes.customerId;

        const {
          customerName: aName,
          legalRepresentative: aLegalRepresentative,
          address: aAddress,
        } = customerRes;
        const { name: aConcat, phone: aPhone } = customerContactRes;
        const companyInfo = offerRes.offerCompany
          ? await axios.get('/api/vt-admin/company/detail?id=' + offerRes.offerCompany)
          : {};
        const {
          legalRepresentative,
          contactPerson,
          contactPhone,
          address,
          companyName,
          email,
          taxNumber,
        } = companyInfo.data?.data ?? {};
        contractForm.value = {
          aName,
          aLegalRepresentative,
          aAddress,
          aConcat,
          aPhone,
          bName: companyName,
          bLegalRepresentative: legalRepresentative,
          bConcat: proxy.$store.getters.userInfo.real_name,
          bAddress: address,
          bPhone: contactPhone,
          bEmail: email,
          consignee: offerRes.contactPerson,
          consigneePhone: aPhone,
          deliveryAddress: aAddress,
          contractTotalPrice: offerRes.offerPrice,
          contractTotalPriceCapital: DX(offerRes.offerPrice),
          payName: companyName,
          payAddress: address,
          payPhone: contactPhone,
          payTaxNumber: taxNumber,
          afterSealsServiceMethod: 2,
          warrantyYear: parseFloat(offerRes.freeWarrantyYear || 3) || 3,
          payMethod: 2,
          firstWorkDays: 5,
          secondWorkDays: 5,
          downPayment: '30%',
          surplusMoney: '70%',
          workDays: 10,
          aOverduePercent: '0.5%',
          bOverduePercent: '0.5%',
          // 项目相关参数
          projectName: offerRes.offerName,
          thirdPaymentWorkDays: 10,
          fourthlyPaymentWorkDays: 10,
          projectAddress: aAddress,
          // 产品列表
          productList: offerRes.detailVOList,
          serviceList: [],
          // 开始日期
          startYear: new Date().getFullYear(),
          startMonth: new Date().getMonth() + 1,
          startDay: new Date().getDate(),
          // 合同份数
          copies: '贰',
          eachHoldCopies: '壹',
        };
        setTotal();
        contractDrawer.value = true;
      } else {
        contractForm.value = res.data.data;
        contractDrawer.value = true;
        contractType.value = res.data.data.contractType;
        contractForm.value.productList = res.data.data.productList?.map(item => {
          return {
            ...item,
            hsze: item.totalPrice,
          };
        });
        contractForm.value.serviceList = res.data.data.serviceList?.map(item => {
          return {
            ...item,
            hsze: item.totalPrice,
          };
        });
        const consignee = proxy.findObject(contractOptionA.value.group[2].column, 'consignee');
        consignee.params.Url =
          '/vt-admin/customerContact/page?customerId=' + res.data.data.customerId;
      }
    });
}
function setTotal() {
  contractForm.value.productTotalPrice = contractForm.value.productList
    .reduce((a, b) => {
      return a + (b.hsze * 1 || 0);
    }, 0)
    .toFixed(2);
  contractForm.value.productTotalPriceCapital = DX(contractForm.value.productTotalPrice);
  contractForm.value.serviceTotalPrice = contractForm.value.serviceList
    .reduce((a, b) => {
      return a + (b.hsze * 1 || 0);
    }, 0)
    .toFixed(2);
  contractForm.value.serviceTotalPriceCapital = DX(contractForm.value.serviceTotalPrice);
}
// 第一笔
watch(
  () => contractForm.value.firstPaymentPercent,
  () => {
    const value =
      (parseFloat(contractForm.value.firstPaymentPercent) / 100) *
      parseFloat(contractForm.value.contractTotalPrice);
    contractForm.value.firstPaymentPrice = value.toFixed(2) == 'NaN' ? '' : value.toFixed(2);
    contractForm.value.firstPaymentPriceCapital = DX(value);
  }
);
watch(
  () => contractForm.value.firstPaymentPrice,
  () => {
    contractForm.value.firstPaymentPriceCapital = DX(contractForm.value.firstPaymentPrice);
  }
);
// 第二笔
watch(
  () => contractForm.value.secondPaymentPercent,
  () => {
    const value =
      (parseFloat(contractForm.value.secondPaymentPercent) / 100) *
      parseFloat(contractForm.value.contractTotalPrice);
    contractForm.value.secondPaymentPrice = value.toFixed(2) == 'NaN' ? '' : value.toFixed(2);
    contractForm.value.secondPaymentPriceCapital = DX(value);
  }
);
watch(
  () => contractForm.value.secondPaymentPrice,
  () => {
    contractForm.value.secondPaymentPriceCapital = DX(contractForm.value.secondPaymentPrice);
  }
);
// 第三笔
watch(
  () => contractForm.value.thirdPaymentPercent,
  () => {
    const value =
      (parseFloat(contractForm.value.thirdPaymentPercent) / 100) *
      parseFloat(contractForm.value.contractTotalPrice);
    contractForm.value.thirdPaymentPrice = value.toFixed(2) == 'NaN' ? '' : value.toFixed(2);
    contractForm.value.thirdPaymentPriceCapital = DX(value);
  }
);
watch(
  () => contractForm.value.thirdPaymentPrice,
  () => {
    contractForm.value.thirdPaymentPriceCapital = DX(contractForm.value.thirdPaymentPrice);
  }
);
// 第四笔
watch(
  () => contractForm.value.fourthlyPaymentPercent,
  () => {
    const value =
      (parseFloat(contractForm.value.fourthlyPaymentPercent) / 100) *
      parseFloat(contractForm.value.contractTotalPrice);
    contractForm.value.fourthlyPaymentPrice = value.toFixed(2) == 'NaN' ? '' : value.toFixed(2);
    contractForm.value.fourthlyPaymentPriceCapital = DX(value);
  }
);
watch(
  () => contractForm.value.fourthlyPaymentPrice,
  () => {
    contractForm.value.fourthlyPaymentPriceCapital = DX(contractForm.value.fourthlyPaymentPrice);
  }
);
//  监听乙方名字变化
watch(
  () => contractForm.value.bName,
  val => {
    if (val) {
      const data = companyData.value.find(item => item.companyName == val);
      const { contactPerson, contactPhone, legalRepresentative, address, email } = data;
      contractForm.value.bConcat = contactPerson;
      contractForm.value.bPhone = contactPhone;
      contractForm.value.bLegalRepresentative = legalRepresentative;
      contractForm.value.bAddress = address;
      contractForm.value.bEmail = email;
    }
  }
);
watch(
  () => contractForm.value.payName,
  val => {
    if (val) {
      const data = companyData.value.find(item => item.companyName == val);
      const { taxNumber, address, email, contactPhone } = data;
      contractForm.value.payTaxNumber = taxNumber;
      contractForm.value.payPhone = contactPhone;
      contractForm.value.payPhone = contactPhone;
      contractForm.value.payAddress = address;
    }
  }
);
function transformProduct(index, value) {
  if (value == 1) {
    const row = contractForm.value.productList.splice(index, 1);
    contractForm.value.serviceList.push(row[0]);
  } else if (value == 2) {
    const row = contractForm.value.serviceList.splice(index, 1);
    contractForm.value.productList.push(row[0]);
  }
  setTotal();
}
</script>

<style lang="scss" scoped>
.item {
  margin-top: 5px;
}

:deep(.el-card__header) {
  padding: 5px;
}

:deep(.el-col) {
  margin-bottom: 0px;
}

:deep(.hide_icon td:first-child .cell) {
  visibility: hidden;
}
</style>
