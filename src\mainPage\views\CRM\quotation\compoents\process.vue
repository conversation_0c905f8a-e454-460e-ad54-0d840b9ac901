<template>
  <el-drawer size="80%" v-model="drawer" v-if="props.type == 1">
    <!-- <el-button type="" @click="handleClick">dayin</el-button> -->
    <my-flow
      :height="600"
      :width="1200"
      ref="flow"
      @myClick="handleClick"
      :option="option1"
      v-model="form"
    >
      <!-- <template #header="{node}">
      <i class="el-icon-delete"
         @click="$refs.flow.deleteNode(node.id)"></i>
         {{(node || {}).name}}
    </template> -->
      <template #="{ node }">
        <span style="font-weight: bolder">{{ (node || {}).name }}</span>
      </template>
    </my-flow>
  </el-drawer>
  <my-flow
  v-else
      :height="600"
      :width="1200"
      ref="flow"
      @myClick="handleClick"
      :option="option1"
      v-model="form"
    >
      <!-- <template #header="{node}">
      <i class="el-icon-delete"
         @click="$refs.flow.deleteNode(node.id)"></i>
         {{(node || {}).name}}
    </template> -->
      <template #="{ node }">
        <span style="font-weight: bolder">{{ (node || {}).name }}</span>
      </template>
    </my-flow>
  <el-drawer size="30%" title="详情" v-model="detailDrawer">
    <el-timeline >
      <el-timeline-item
        v-for="(item, index) in detailData"
        :key="index"
        :timestamp="item.createTime"
      >
        <span v-html="cusHtml(item.logContent)"></span>
      </el-timeline-item>
    </el-timeline>
  </el-drawer>
</template>

<script setup>
import myFlow from '@/components/my-flow/index.vue';
import { onMounted } from 'vue';
const props = defineProps({
  currentId: String,
  type: Number,
});
let drawer = ref(false);
let option1 = ref({
  name: '流程A',
  nodeList: [
    {
      id: '0',
      name: '商机',
      left: 52,
      top: 255,
      type: '0',
    },
    // {
    //     "id": "2",
    //     "name": "报备",
    //     "left": 59,
    //     "top": 3
    // },
    // {
    //     "id": "3",
    //     "name": "跟进",
    //     "left": 54,
    //     "top": 112
    // },
    {
      id: '1',
      name: '方案',
      left: 317,
      top: 255,
      type: '1',
    },
    {
      id: '2',
      name: '报价',
      left: 596,
      top: 255,
      type: '2',
    },
    {
      id: '6',
      name: '失单',
      left: 942,
      top: 50,
      type: '6',
    },
    {
      id: '3',
      name: '采购订单',
      left: 952,
      top: 255,
      type: '3',
    },
    // {
    //     "id": "8",
    //     "name": "询价",
    //     "left": 854,
    //     "top": 45
    // },
    // {
    //     "id": "9",
    //     "name": "供应商合同",
    //     "left": 1076,
    //     "top": 43
    // },
    // {
    //     "id": "10",
    //     "name": "付款",
    //     "left": 1296,
    //     "top": -48
    // },
    // {
    //     "id": "11",
    //     "name": "收票",
    //     "left": 1297,
    //     "top": 42
    // },
    // {
    //     "id": "12",
    //     "name": "入库",
    //     "left": 1299,
    //     "top": 148
    // },
    {
      id: '4',
      name: '销售合同',
      left: 952,
      top: 439,
      type: '4',
    },
    // {
    //     "id": "14",
    //     "name": "标的任务",
    //     "left": 821,
    //     "top": 376
    // },
    // {
    //     "id": "15",
    //     "name": "发货",
    //     "left": 1085,
    //     "top": 366
    // },
    // {
    //     "id": "16",
    //     "name": "费用",
    //     "left": 593,
    //     "top": 631
    // },
    // {
    //     "id": "17",
    //     "name": "计划收款",
    //     "left": 1110,
    //     "top": 497
    // },
    // {
    //     "id": "18",
    //     "name": "收款",
    //     "left": 1322,
    //     "top": 497
    // },
    // {
    //     "id": "19",
    //     "name": "开票",
    //     "left": 1088,
    //     "top": 630
    // }
  ],
  lineList: [
    { from: '0', to: '1' },
    { from: '1', to: '2' },
    { from: '2', to: '3' },
    { from: '2', to: '4' },
    { from: '2', to: '6' },
  ],
});
watchEffect(() => {
  if (props.currentId) {
    getProcess();
  }
});

function open() {
  drawer.value = true;
}
function getProcess() {
  axios
    .get('/api/vt-admin/businessOpportunity/getBusinessProgress', {
      params: {
        type: props.type,
        id: props.currentId,
      },
    })
    .then(res => {
      option1.value.nodeList = option1.value.nodeList.map(item => {
        return {
          ...item,
          type: null,
          status: null,
          ...res.data.data.find(i => item.type == i.type),
        };
      });
      console.log(option1.value);
    });
}
function handleClick(node) {
  getDetailData(node);
}
let detailData = ref([]);
function getDetailData({ id, type }) {
  axios
    .get('/api/vt-admin/businessOpportunityProgress/page', {
      params: {
        logicId:id,
        moduleType: type,
        current: 1,
        size: 1500,
      },
    })
    .then(res => {
      detailData.value = res.data.data.records;
      if(detailData.value.length == 0)return
      detailDrawer.value = true;
    });
}
function cusHtml(content) {

   const one =   content.replace(/</g, '<span style="color:var(--el-color-primary)">');
    const two =  one.replace(/>/g, '</span>');
   
     return two
}
let detailDrawer = ref(false);
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
