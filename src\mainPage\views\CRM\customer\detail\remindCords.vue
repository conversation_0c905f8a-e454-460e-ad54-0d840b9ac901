<template>
  <el-dialog title="关联供应商" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      :table-loading="loading"
      ref="crud"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #remindStatus="{row}">
        <el-tag effect='plain' type="success" v-if="row.remindStatus == 1">已提醒</el-tag>
        <el-tag effect='plain' type="info" v-else>未提醒</el-tag>
    </template>
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false" icon="close">关 闭</el-button>
    </div>
  </el-dialog>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  menu:false,
  searchMenuSpan: 4,
  labelWidth: 120,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '提醒人',
      prop: 'reminder',
    },
    {
      label: '提醒时间',
      prop: 'remindTime',
      Format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
    },
    {
      label: '提醒内容',
      prop: 'remindContent',
      type: 'textarea',
      overHidden: true,
    },
    {
      label: '提醒状态',
      prop: 'remindStatus',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  itCurrentId: String,
});
watchEffect(() => {
  if (props.itCurrentId) {
    onLoad();
  }
});
let dialogVisible = ref(false);

const tableUrl = '/api/vt-admin/customerItRemind/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        itId: props.itCurrentId,
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
function searchChange(params, done) {
  onLoad();
  done();
}

function open() {
  dialogVisible.value = true;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
