<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
  
    <template #collectionFiles="{ row }"> <File :fileList="row.attachList || []"></File></template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
  planCollectionId: String,
});
watchEffect(() => {
  if (props.planCollectionId) {
    onLoad();
  }
});
let option = ref({
  //   height: 'auto',
  align: 'center',
  addBtn: false,
  menu: false,
  editBtn: true,
  delBtn: true,
  //   calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      type: 'number',
      label: '本次收款金额',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualPrice',
    },
    {
      type: 'number',
      label: '本次收款比例',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollectionRate',
    },

    {
      type: 'date',
      label: '本次收款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD',
      prop: 'actualDate',
      disabled: false,
      readonly: false,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      label: '收款凭证',
      prop: 'collectionFiles',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      // formatter: (row) => {
      //   return row.attachList[0].link
      // },
      action: '/blade-resource/attach/upload',
      // uploadAfter: (res, done) => {
      //   imgId.value = res.id;
      //   done();
      // },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractActualCollection/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        planCollectionId: props.planCollectionId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
