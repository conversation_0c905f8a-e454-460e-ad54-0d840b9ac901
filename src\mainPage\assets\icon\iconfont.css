@font-face {
  font-family: "element-icons"; /* Project id 4248459 */
  src: url('iconfont.woff2?t=1694507353009') format('woff2'),
       url('iconfont.woff?t=1694507353009') format('woff'),
       url('iconfont.ttf?t=1694507353009') format('truetype');
}

.element-icons {
  font-family: "element-icons" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.el-icon-chulizhong:before {
  content: "\e6cd";
}

.el-icon-butongguo:before {
  content: "\e6ce";
}

.el-icon-shenqingchenggong:before {
  content: "\e6cf";
}

