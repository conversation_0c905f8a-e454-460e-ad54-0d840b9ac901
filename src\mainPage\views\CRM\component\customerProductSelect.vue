<template>
  <el-dialog
    title="产品选择"
    class="avue-dialog avue-dialog--top"
    v-model="visible"
    width="60%"
    append-to-body
  >
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @selection-change="selectionChange"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @row-click="rowClick"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    <template #contractName="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
          
        </el-link>
      </template>
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="visible = false">取 消</el-button>
      <el-button @click="handleConfirm" type="primary">确 定</el-button>
    </div>
  </el-dialog>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

const props = defineProps({
  customerId: {
    type: String,
    default: '',
  },
  url: {
    type: String,
    default: '/api/vt-admin/sealContract/myHistoryProductList',
  },
});
console.log(props.id);
let option = ref({
  // height: 'auto',
  align: 'center',
  menu: false,
  addBtn: false,
  editBtn: true,
  header: false,
  delBtn: true,
  reserveSelection: true,
  // calcHeight: 30,
  searchMenuSpan: 8,
  selection: true,
  searchSpan: 5,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '产品名称',
      prop: 'customProductName',
      type: 'input',
      searchSpan: 6,
    },
    {
      label: '关键字',
      prop: 'productKeys',
      type: 'input',
      search: true,
      placeholder: '输入产品名称或规格型号进行搜索',
      searchSpan: 6,
      hide: true,
    },

    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,

      span: 24,
      type: 'input',
    },
    {
      label: '品牌',
      prop: 'productBrand',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      width: 80,
      cell: false,
    },

    {
      label: '单价',
      prop: 'zhhsdj',
      type: 'number',
      width: 100,
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,

      cell: false,
    },
    {
      label: '序列号',
      prop: 'serialNumber',
      search: true,
      hide: true,
    },
    {
      label: '关联合同',
      prop: 'contractName',
      search: true,
      overHidden: true,
      width: 250,
    },
    {
      label: '签订日期',
      prop: 'signDate',

      component: 'wf-daterange-search',
      searchSpan: 8,
      width: 110,
      overHidden: true,
    },
    {
      label: '出货日期',
      prop: 'outDate',

      component: 'wf-daterange-search',
      searchSpan: 8,
      width: 110,
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const visible = ref(false);

const emits = defineEmits(['confirm']);

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(props.url, {
      params: {
        size,
        current,
        ...params.value,

        customerId: props.customerId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          needNumber: 1,
        };
      });
      page.value.total = res.data.data.total;
    });
}
function open() {
  visible.value = true;
  proxy.$nextTick(() => {
    proxy.$refs.crud.clearSelection();
    onLoad();
  });
}
function close(params) {
  visible.value = false;
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list.map(item => {
    return {
      ...item,
      // needNumber: item.needNumber || 1,
    };
  });
  emits('select', selectList.value);
}
let router = useRouter();
function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm() {
  visible.value = false;

  getSelectTableData();

  emits('confirm', getSelectTableData());
}
function getSelectTableData() {
  return selectList.value.reduce((pre, item) => {
    let i;
    i = tableData.value.find(i => i.id == item.id);
    pre.push(i);
    return pre;
  }, []);
}
function reset() {
  proxy.$nextTick(() => {
    proxy.$refs.crud.toggleSelection(selectList.value);
  });
}
function rowClick(row) {
  console.log(row);
  if (row.isInvoice == 1) return;
  proxy.$refs.crud.toggleSelection([row]);
}
function toDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId,
      delBtn: 1,
      selectType: 0,
    },
  });
}
defineExpose({
  open,
  reset,
});
</script>

<style lang="scss" scoped></style>
