<template>
  <!-- <div class="project-main">
    <div class="tabs">
      <div
        v-for="(item, index) in tabs"
        :key="index"
        class="tab-item"
        :class="tabIndex === item.value ? 'active' : ''"
        @click="handleTabClick(item,index)"
      >
        {{ item.label }}
      </div>
    </div>
  </div> -->
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex">
          <el-button
            type="primary"
            @click="addContract"
            icon="plus"
            v-if="!route.query.type || route.query.type == 0"
            >新增订单</el-button
          >
          <div style="display: flex; align-items: center">
            <span style="font-weight: bolder">订单总额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
          </div>
        </div>
      </template>
      <template #contractName="{ row }">
        <el-link type="primary" @click="toDetail(row)">
          {{ row.contractName }}
        </el-link>
      </template>
      <template #contractCode="{ row }">
        <el-link type="primary" @click="toContractDetail(row)">
          {{ row.contractCode }}
        </el-link>
      </template>
      <template #menu="{ row }">
        <el-button
          text
          type="primary"
          v-if="
            $store.getters.userInfo.user_id == row.businessUser &&
            (!route.query.type || route.query.type == 0)
          "
          @click="edit(row)"
          icon="edit"
          >编 辑</el-button
        >
        <!-- <el-button
          text
          type="primary"
          v-if="
           ( $store.getters.userInfo.user_id == row.businessUser &&
            (!route.query.type || route.query.type == 0)) && row.isApplyCancel != 1
          "
          @click="cancel(row)"
          icon="edit"
          >撤 回</el-button
        > -->
        <el-button text type="primary" @click="toDetail(row)" icon="view">详 情</el-button>
      </template>
      <template #contractTotalPrice-search="{ row }">
        <div style="display: flex">
          <el-input placeholder="最小合同额" v-model.number="params.contractMinPrice"></el-input>-
          <el-input placeholder="最大合同额" v-model.number="params.contractMaxPrice"></el-input>
        </div>
      </template>
      <template #deliveryStatus="{ row }">
        <el-tag effect='plain' type="info" v-if="row.deliveryStatus == 0">进行中</el-tag>
        <el-tag effect='plain' type="success" v-if="row.deliveryStatus == 1">已交付</el-tag>
        <i title="该订单正在申请撤销" v-if="row.isApplyCancel == 1" class="element-icons el-icon-chehui1" style="color:var(--el-color-warning);font-size:20px"></i>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted,onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';

console.log(window);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 6,
  searchSpan: 6,
  searchIcon: true,
  searchIndex: 3,
  searchLabelWidth: 120,
  menuWidth: 180,
  border: true,
  column: [
    {
      label: '订单名称',
      prop: 'contractName',
      overHidden: true,
      search: true,
      slot: true,
      
    },
    {
      label: '关联合同编号',
      prop: 'contractCode',
      overHidden: true,
      // search: true,
      searchSpan: 4,
      width: 160,
    },
    {
      label: '对方订单编号',
      prop: 'customerOrderNumber',
      overHidden: true,
      // search: true,
      searchSpan: 4,
      width: 160,
    },
    // {
    //   label: '类型',
    //   prop: 'type',
    //   searchType: 'radio',
    //   search: true,
    //   hide: true,
    //   dicData: [
    //     {
    //       label: '全部',
    //       value: '',
    //     },
    //     {
    //       label: '我的',
    //       value: 1,
    //     },
    //     {
    //       label: '我参与的',
    //       value: 2,
    //     },
    //   ],
    // },

    {
      label: '客户名称',
      prop: 'customerName',
      //width: 150,
      search: true,component: 'wf-customer-drop',
      overHidden: true,
    },

    {
      label: '订单总额',
      prop: 'contractTotalPrice',
      search: true,
      // hide: true,
      searchSpan: 6,
      searchLabelWidth: 120,
      searchSlot: true,
      sortable: true,
      width:120
    },
    // {
    //   label: '商机名称',
    //   prop: 'businessOpportunityName',
    //   //width: 150,
    //   width: 200,
    //   search: true,
    // },
    {
      label: '签订日期',
      type: 'date',
      prop: 'signDate',
      // hide: true,
      sortable: true,
      search: true,
      component: 'wf-daterange-search',
      search: true,
      searchSpan: 6,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    // {
    //   label: "回款逾期",
    //   prop: "overdue",
    // },
    {
      label: '业务员',
      prop: 'businessName',
      search: true,
      component: 'wf-user-drop',
      formatter: (row, value, column, cell) => {
        return row.businessUserName;
      },
      width:90
      // hide: true,
    },
    {
      label: '交付负责人',
      prop: 'deliveryUserName',
      overHidden: true,
      // hide: true,
      width:100
    },
    {
      label: '交付日期',
      prop: 'contractDeliveryDate',
      width:110
      // hide: true,
    },
    {
      label: '业务板块',
      prop: 'businessTypeName',
      overHidden: true,
      // hide: true,
    },
    {
      label: '交付状态',
      prop: 'deliveryStatus',
      type: 'select',
      // hide: true,
      width:90
    },
    // {
    //   label: '状态',
    //   children: [
    //     // {
    //     //   label: "合同签订时间",
    //     //   prop: "contractSignDate",
    //     // },
    //     {
    //       label: '合同状态',
    //       prop: 'contractStatus',
    //       dicData: [
    //         {
    //           value: 0,
    //           label: '待审批',
    //         },
    //         {
    //           value: 1,
    //           label: '执行中',
    //         },
    //         {
    //           value: 2,
    //           label: '待执行',
    //         },
    //         {
    //           value: 3,
    //           label: '暂停',
    //         },
    //         {
    //           value: 4,
    //           label: '终止',
    //         },
    //         {
    //           value: 5,
    //           label: '诉讼',
    //         },
    //         {
    //           value: 6,
    //           label: '待签订',
    //         },
    //         {
    //           value: 7,
    //           label: '已签未回',
    //         },
    //       ],
    //       width: 80,
    //       type: 'select',
    //       dataType: 'string',
    //     },
    //   ],
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let tabs = ref([
  {
    label: '全部合同',
    value: null,
  },
  {
    label: '订单合同',
    value: 0,
  },
  {
    label: '项目合同',
    value: 1,
  },
]);

let tabIndex = ref(null);
function handleTabClick(item, index) {
  tabIndex.value = item.value;
  onLoad();
}
onActivated(() => {
  onLoad();
})
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContract/page';
let params = ref({
  contractMinPrice: '',
  contractMaxPrice: '',
  signDate: [`${new Date().getFullYear()}-01-01`, `${new Date().getFullYear()}-12-31`],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
// onMounted(() => {
//   onLoad();
// });
let loading = ref(false);
let totalPrice = ref(0);
let isMy = ref(route.query.type == 0 || !route.query.type);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        customerId: route.query.id || null,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        contractTotalPrice: null,
        selectType: isMy.value ? 0 : 2,
        contractType: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/sealContract/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        signStartDate: params.value.signDate && params.value.signDate[0],
        signEndDate: params.value.signDate && params.value.signDate[1],
        signDate: null,
        customerId: route.query.id || null,
        contractTotalPrice: null,
        selectType: isMy.value ? 0 : 2,
        contractType: 0,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function addContract() {
  router.push({
    path: '/Order/salesOrder/compoents/addOrder',
  });
}
function toDetail(row) {
  router.push({
    path: '/Order/salesOrder/compoents/detail',
    query: {
      id: row.id,
      name: `${row.contractName}-详情`,
      delBtn: 1,
    },
  });
}
function toContractDetail(row) {
  router.push({
    path: '/Contract/customer/compoents/detail',
    query: {
      id: row.id,
      name: `${row.contractName}-详情`,
      delBtn: 1,
    },
  });
}
function reset() {
  params.value.contractMaxPrice = '';
  params.value.contractMinPrice = '';
  onLoad();
}
function edit(row) {
  router.push({
    path: '/Order/salesOrder/compoents/addOrder',
    query: {
      id: row.id,
      name: `编辑-${row.contractName}`,
    },
  });
}
function cancel(row) {
  proxy.$refs.dialogForm.show({
    title: '撤 回',
    tip:"撤回到报价可编辑状态，需要重新走流程",
    option: {
      column: [
        {
          label: '撤回原因',
          prop: 'applyReason',
          span: 24,
          rules: [{ required: true, message: '请输入撤回原因', trigger: 'blur' }],
          type: 'textarea',
          placeholder: '请输入撤回原因',
          maxlength: 200,
          showWordLimit: true,
          rows: 4,
        },
      ],
    },
    callback(res) {
      axios.post('/api/vt-admin/sealContractCancel/save',{
        ...res.data,
        sealContractId: row.id
      }).then(r => {
        proxy.$message({
          type: 'success',
          message: '申请成功',
        });
        onLoad();
        res.close()
      })
    },
  });
}
</script>

<style lang="scss" scoped>
.project-main {
  width: calc(100% - 12px);
  margin: 0 auto;

  .tabs {
    width: calc(100% - 12px);
    margin-bottom: -10px;
    height: 35px;
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tab-item {
      width: 96px;
      height: 30px;
      line-height: 30px;
      font-size: 15px;
      text-align: center;
      background-color: #fff;
      margin-bottom: -4px;
      border-radius: 5px 5px 0px 0px;
      color: #303133;
      cursor: pointer;
      margin-right: 5px;
      &.active {
        color: #fff;
        background-color: var(--el-color-primary);
      }
    }
  }
  .tab-main {
    width: 100%;
    height: calc(100% - 35px);
  }
}
</style>
