<template>
  <div style="width: 100%">
    <el-autocomplete
      v-model="value"
      style="width: 100%;"
      :fetch-suggestions="querySearch"
      :trigger-on-focus="false"
      clearable
      value-key="name"
      class="inline-input w-50"
      placeholder="请输入商机名称"
      @select="handleUserSelectConfirm"
      @blur="handleBlur"
    />
  </div>
</template>
<script>
export default {
  name: 'bussiness-drop',
};
</script>
<script setup>
import axios from 'axios';
import { watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
  },
  url: {
    type: String,
    default: '/api/vt-admin/businessOpportunity/page?selectType=2',
  },
});
watch(
  () => props.modelValue,
  val => {
    value.value = val;
  }
);
const emits = defineEmits(['update:modelValue']);
let value = ref('');
function querySearch(value, cb) {
  if (!value) return [];
  axios.get(props.url, { params: { name: value, size: 100 } }).then(res => {
    cb(res.data.data.records);
  });
}
function handleUserSelectConfirm(value) {
  emits('update:modelValue', value.name);
}
function handleBlur() {
  emits('update:modelValue', value.value);
}
</script>

<style lang="scss" scoped></style>
