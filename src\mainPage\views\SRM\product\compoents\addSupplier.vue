<template>
  <el-dialog title="关联供应商" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    </avue-crud>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false" icon="close">关 闭</el-button>
    </div>
  </el-dialog>
  <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  labelWidth: 120,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '供应商名称',
      prop: 'supplierId',
      hide: true,
      component: 'wf-supplier-select',
      rules: [
        {
          required: true,
          message: '请选择供应商',
        },
      ],
    },
    {
      label: '供应商名称',
      prop: 'supplierName',

      editDisplay: false,
      addDisplay: false,
    },
    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',
      rules: [
        {
          required: true,
          message: '请输入单价',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const props = defineProps({
  productId: String,
});
watchEffect(() => {
  if (props.productId) {
    onLoad();
  }
});
let dialogVisible = ref(false);
const addUrl = '/api/vt-admin/supplierProduct/save';
const delUrl = '/api/vt-admin/supplierProduct/remove';
const updateUrl = '/api/vt-admin/supplierProduct/update';
const tableUrl = '/api/vt-admin/supplierProduct/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();

let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        productId: props.productId,
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    productId: props.productId,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

function open() {
  dialogVisible.value = true;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
