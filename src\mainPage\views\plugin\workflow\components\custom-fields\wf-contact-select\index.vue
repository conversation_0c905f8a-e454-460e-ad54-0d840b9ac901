<template>
  <div>
    <el-input
      v-model="name"
      :size="size"
      suffix-icon="el-icon-user"
      :placeholder="placeholder || '产品选择'"
      readonly
      :disabled="disabled"
      @click="handleSelect"
    ></el-input>
    
    <wf-contact-select
      ref="contact-select"
      :check-type="checkType"
      :userUrl="Url"
      :default-checked="modelValue"
      @onConfirm="handleUserSelectConfirm"
    ></wf-contact-select>
  </div>
</template>
<script>
import { getUser } from '../../../api/process/user';

import WfContactSelect from '@/components/Y-UI/wf-contact-select.vue';

export default {
  name: 'contact-select',
  components: { WfContactSelect },
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      // default: () => {
      //   return 'small'
      // }
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    Url: {
      type: String,
      default: () => {
        return '/vt-admin/customerContact/page';
      },
    },
    change: Function,
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          if (/\D/.test(val)) return (this.name = val);
          const name = [];
          const checks = (val + '').split(',');
          const asyncList = [];
          checks.forEach(c => {
            asyncList.push(this.getDetail(c));
          });
          Promise.all(asyncList).then(res => {
            res.forEach(r => {
              const data = r.data.data;
              if (data) name.push(data.name);
            });
            this.name = name.join(',');
          });
        } else this.name = '';
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['contact-select'].visible = true;
    },
    handleUserSelectConfirm(id) {
      this.$emit('update:modelValue', id);
      if (this.change && typeof this.change == 'function') this.change({ value: id });
    },
    getDetail(c) {
      return axios.get('/api/vt-admin/customerContact/detail?id=' + c, {
        id: c,
      });
    },
  },
};
</script>
<style lang="scss"></style>
