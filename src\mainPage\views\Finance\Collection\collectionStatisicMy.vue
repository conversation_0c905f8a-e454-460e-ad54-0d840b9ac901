<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">合同总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">未开票总额：</el-text>
        <el-text type="primary" size="large">￥{{ (noInvoicePrice * 1).toLocaleString() }}</el-text>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">未收款总额：</el-text>
        <el-text type="primary" size="large"
          >￥{{ (noReceivedPrice * 1).toLocaleString() }}</el-text
        >
      </template>
      <template #contractTotalPrice="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.contractTotalPrice }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer title="统计详情" v-model="dialog" size="90%">
      <template #header>
        <el-divider content-position="left"
          ><span style="color: var(--el-color-primary); font-size: 20px; font-weight: bolder"
            >{{ currentCustomerName }} | {{ currentSignDate[0] }}-{{ currentSignDate[1] }}</span
          ></el-divider
        >
      </template>
      <contractList
        :selectType="0"
        :customerName="currentCustomerName"
        :signDate="currentSignDate"
      ></contractList>
    </el-drawer>
   
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import contractList from './component/contractList.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  menu: false,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '签订日期',
      prop: 'date',
      width: 250,
      overHidden: true,
      format: 'YYYY-MM-DD',
      searchSpan: 6,
      component: 'wf-daterange-search',
      search: true,
      display: false,
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '客户名称',
      prop: 'customerName',
      search: true,
    },

    {
      label: '合同总额',
      prop: 'contractTotalPrice',
    },

    {
      label: '已开票金额',
      prop: 'hasInvoice',
    },
    {
      label: '未开票金额',
      prop: 'noInvoice',
    },
    {
      label: '已收款金额',
      prop: 'receivedPrice',
    },
    {
      label: '未收款金额',
      prop: 'noReceivedPrice',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContract/getUserAccountReceivable';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalPrice = ref(0);
let noInvoicePrice = ref(0);
let noReceivedPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        signStartDate: params.value.date && params.value.date[0],
        signEndDate: params.value.date && params.value.date[1],
        selectType: 0,
        date: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  axios
    .get('/api/vt-admin/sealContract/getUserAccountReceivableStatistics', {
      params: {
        size,
        current,
        ...params.value,
        signStartDate: params.value.date && params.value.date[0],
        signEndDate: params.value.date && params.value.date[1],
        selectType: 0,
        date: null,
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
      noInvoicePrice.value = res.data.data.noInvoicePrice;
      noReceivedPrice.value = res.data.data.noReceivedPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function toDetail(row) {}
function reset() {
  onLoad();
}
let currentCustomerName = ref('');
let currentSignDate = ref([]);

let dialog = ref(false);
function viewDetail(row) {
  const text = ['01', '03', '05', '07', '08', '10', '12'].includes(row.date.split('-')[1]);
  dialog.value = true;
  currentCustomerName.value = row.customerName;
  currentSignDate.value = [`${row.date}-01`, `${row.date}-${text ? '31' : '30'}`];
}
function searchChange(params, done) {
  onLoad();
  done();
}


</script>

<style lang="scss" scoped></style>
