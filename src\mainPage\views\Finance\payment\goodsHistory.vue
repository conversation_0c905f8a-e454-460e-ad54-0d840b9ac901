<template>
  <basic-container :shadow="supplierId ? 'never' : 'always'">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #paymentFiles="{ row }"> <File :fileList="row.attachList || []"></File></template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="viewDetail(row)">明细</el-button>
        <!-- <el-button type="primary" text icon="Back" @click="back(row)">撤回</el-button> -->
      </template>
    </avue-crud>
    <el-drawer title="收款详情" size="80%" v-model="drawer">
      <historyDetail :purchaseContractPlanId="currentIds"></historyDetail>
    </el-drawer>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import historyDetail from './historyDetail.vue';
const props = defineProps(['supplierId', 'contractId', 'purchaseContractId']);
let option = ref({
  //   height: 'auto',
  align: 'center',
  addBtn: false,
  menu: true,
  editBtn: false,
  delBtn: false,
  //   calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  searchLabelWidth: 120,
  column: [
    {
      type: 'input',
      label: '供应商名称',
      span: 12,
      display: true,
      search: !props.supplierId,
      hide: !!props.supplierId,
      prop: 'supplierName',
    },
    {
      type: 'input',
      label: '合同编号',
      span: 12,
      display: true,
      search: true,
      search: !props.contractId,
      hide: !!props.contractId,
      prop: 'contractCode',
    },
    {
      type: 'number',
      label: '付款金额',
      span: 12,
      display: true,
      prop: 'actualPaymentPrice',
    },
    {
      type: 'date',
      label: '付款日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      search: true,
      searchSpan: 6,
      component: 'wf-daterange-search',
      prop: 'actualPaymentDate',
    },
    {
      type: 'select',
      label: '付款账号',

      cascader: [],
      span: 12,
      display: true,

      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      prop: 'paymentAccount',
    },
    {
      label: '付款凭证',
      prop: 'paymentFiles',
      type: 'upload',
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        imgId.value = res.id;
        done();
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/purchaseContractActualPayment/mergePage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  ;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        purchaseContractId: props.contractId,
        paymentStartDate: params.value.actualPaymentDate && params.value.actualPaymentDate[0],
        paymentEndDate: params.value.actualPaymentDate && params.value.actualPaymentDate[1],
        actualPaymentDate: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
let drawer = ref(false);
let currentIds = ref('');
function viewDetail(row) {
  currentIds.value = row.id;
  drawer.value = true;
}
</script>

<style lang="scss" scoped></style>
