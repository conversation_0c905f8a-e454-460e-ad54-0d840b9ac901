<template>
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @keyup.enter="onLoad"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @refresh-change="onLoad"
        @current-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  let option = ref({
    height: 'auto',
    align: 'center',
    menu:false,
    addBtn: false,
    editBtn: false,
    delBtn: true,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchSpan: 4,
    menuWidth: 270,
    border: true,
    column: [
      {
        label: '合同名称',
        prop: 'contractName',
        width: 250,
        overHidden: true,
       
      },
      {
      label: '产品',
      prop: 'customProductName',

      cell: false,
      overHidden: true,
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',

      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '单位',
      prop: 'unitName',
     
      span: 12,
    },
    {
      label: '未税单价',
      prop: 'wsdj',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '未税金额',
      prop: 'wsze',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '含税单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '含税金额',
      prop: 'hsze',
      type: 'number',
      span: 12,
      cell: false,
    },
    ],
  });
  const props = defineProps({
    id:String
  })
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  watch(() => {
    if(props.id){
        onLoad()
    }
  })
  const addUrl = ''
  const delUrl = ''
  const updateUrl = ''
  const tableUrl = ''
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get('/api/vt-admin/sealContractStatement/detail', {
        params: {
            id:props.id
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.detailVOList;
      
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form) {
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          onLoad();
        });
      })
      .catch(() => {});
  }
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  </script>
  
  <style lang="scss" scoped></style>
  