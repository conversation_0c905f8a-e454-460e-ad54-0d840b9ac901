<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      :before-open="beforeOpen"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <!-- <el-button
            type="primary"
            @click="$refs.crud.rowEdit(row, index)"
            text
            icon="edit"
            v-if="row.borrowStatus == 0"
            >编辑</el-button
          > -->
        <!-- <el-button
            type="primary"
            v-if="row.borrowStatus == 0"
            text
            icon="back"
            @click="confirmRecevie(row)"
            >确认借货</el-button
          >
          <el-button
            type="primary"
            v-if="row.borrowStatus == 1 || row.borrowStatus == 3"
            text
            icon="back"
            @click="confirmReturn(row)"
            >确认归还</el-button
          > -->
        <!-- <el-button
            type="primary"
            v-if="
              (row.depositStatus == 0 || row.depositStatus == null) && row.isHasSecurityDeposit == 1 && row.borrowStatus == 1
            "
            @click="payAmount(row)"
            text
            icon="upload"
            >支付押金</el-button
          > -->
        <el-button
          type="primary"
          v-if="row.depositStatus == 1"
          @click="receviewAmount(row)"
          text
          icon="download"
          >收款</el-button
        >
        <!-- <el-button
            type="primary"
            v-if="row.borrowStatus == 0"
            @click="$refs.crud.rowDel(row, index)"
            text
            icon="delete"
            >删除</el-button
          > -->
      </template>
      <template #borrowCode="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.borrowCode }}</el-link>
      </template>
      <template #borrowFiles="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
      <template #borrowStatus="{ row }">
        <el-tag
          :type="row.borrowStatus == 0 ? 'danger' : row.borrowStatus == 1 ? 'warning' : 'success'"
          effect="plain"
          >{{ row.$borrowStatus }}</el-tag
        >
      </template>
      <template #fileList-form>
        <File :fileList="form.attachList"></File>
      </template>
      <template #paymentFile-form>
        <File :fileList="form.borrowDepositVO && form.borrowDepositVO.paymentFileList"></File>
      </template>
      <template #isHasSecurityDeposit="{ row }">
        <el-tag :type="row.isHasSecurityDeposit == 0 ? 'danger' : 'success'" effect="plain">{{
          row.$isHasSecurityDeposit
        }}</el-tag
        ><el-tag
          style="margin-left: 5px"
          type="warning"
          v-if="
            (row.depositStatus == 0 || row.depositStatus == null) && row.isHasSecurityDeposit == 1
          "
          effect="plain"
          >未支付</el-tag
        >
        <el-tag
          style="margin-left: 5px"
          type="success"
          v-if="row.depositStatus == 1 && row.isHasSecurityDeposit == 1"
          effect="plain"
          >已支付</el-tag
        >
        <el-tag
          style="margin-left: 5px"
          type="success"
          v-if="row.depositStatus == 2 && row.isHasSecurityDeposit == 1"
          effect="plain"
          >已收款</el-tag
        >
      </template>
      <template #returnFile-form>
        <File :fileList="form.borrowDepositVO && form.borrowDepositVO.returnFileList"></File>
      </template>
      <template #overDate="{ row }">
        {{ row.overDate }}
        <span v-if="row.borrowStatus != 2">
          <span
            :style="{
              color:
                row.overDays > 7
                  ? 'var(--el-color-success)'
                  : row.overDays <= 7 && row.overDays >= 0
                  ? 'var(--el-color-warning)'
                  : 'var(--el-color-danger)',
            }"
            class="planCollectionDays"
            >{{ row.overDays }}</span
          ><span>{{ row.overDays || row.overDays == 0 ? '天' : '' }}</span>
        </span>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <wfProductSelect
      :checkType="'checkBox'"
      ref="wfProductSelectRef"
      @onConfirm="handleProductSelectConfirm"
    ></wfProductSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import { dateFormat } from '@/utils/date';
import { ElMessage } from 'element-plus';
import { TreeOptionsEnum } from 'element-plus/es/components/tree-v2/src/virtual-tree';
let wfProductSelectRef = ref(null);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  dialogClickModal: true,
  border: true,
  labelWidth: 120,
  dialogType: 'drawer',
  column: [
    {
      label: '借货单编号',
      prop: 'borrowCode',
      width: 180,
      overHidden: true,
      search: true,
      disabled: true,
      searchLabelWidth: 120,
    },
    {
      label: '供应商名称',
      prop: 'supplierName',
      search: true,
      display: false,
      component: 'wf-supplier-drop',
      searchLabelWidth: 120,
      hide: true,
    },
    {
      label: '供应商名称',
      prop: 'supplierId',
      component: 'wf-supplier-select',
      formatter: row => {
        return row.supplierName;
      },
    },
    // {
    //   label: '客户借货单',
    //   prop: 'borrowI',hide:true,
    //   clearable:true,
    //   value:222
    // },
    {
      label: '关联产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      viewDisplay: false,
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择关联产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        rowAdd: done => {
          wfProductSelectRef.value.visible = true;
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            bind: 'productVO.productName',
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            bind: 'productVO.productSpecification',
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
            bind: 'productVO.productBrand',
            cell: false,
            width: 100,
          },
          {
            label: '单位',
            prop: 'unitName',
            overHidden: true,
            bind: 'productVO.unitName',
            cell: false,
            width: 100,
          },
          {
            label: '数量',
            prop: 'number',
            width: 150,
            type: 'number',
            span: 12,
            cell: true,
            // change: val => {
            //   form.value.refundPrice = totalAmount();
            // },
          },
          {
            label: '已归还数量',
            prop: 'returnNumber',
            width: 150,
            type: 'number',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            span: 12,
            cell: true,
            // change: val => {
            //   form.value.refundPrice = totalAmount();
            // },
          },
          //   {
          //     label: '单价',
          //     prop: 'purchasePrice',
          //     type: 'number',
          //     span: 12,
          //     width: 150,
          //     cell: true,
          //     change: val => {
          //       form.value.refundPrice = totalAmount();
          //     },
          //   },
          //   {
          //     label: '金额',
          //     prop: 'totalPrice',
          //     type: 'number',
          //     span: 12,
          //     width: 120,
          //     cell: false,
          //     formatter: row => {
          //       return row.number * row.purchasePrice;
          //     },
          //   },
          //   {
          //     label: '序列号',
          //     prop: 'serialNumber',
          //     span: 12,
          //     cell: true,

          //     width: 200,
          //     type: 'textarea',
          //   },
        ],
      },
    },
    {
      label: '借货时间',
      prop: 'borrowDate',
      type: 'date',
      format: 'YYYY-MM-DD',
      width: 120,
      valueFormat: 'YYYY-MM-DD',
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
    },
    {
      label: '预计归还时间',
      prop: 'overDate',
      type: 'date',
      width: 160,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '是否有押金',
      type: 'switch',
      width: 120,
      prop: 'isHasSecurityDeposit',
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      control: val => {
        return {
          depositAmount: {
            display: val == 1,
          },
        };
      },
    },
    {
      label: '押金金额',
      prop: 'depositAmount',
      type: 'number',
      span: 12,
      width: 120,
      rules: [
        {
          required: true,
          message: '请输入押金金额',
        },
      ],
    },

    {
      label: '附件',
      prop: 'borrowFiles',
      type: 'upload',
      span: 24,
      dragFile: true,
      // rules: [
      //   {
      //     required: true,
      //     validator: validatorPath,
      //     trigger: "change",
      //   },
      // ],
      hide: true,
      dataType: 'object',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
      },
      action: '/api/blade-resource/attach/upload',
      viewDisplay: false,
    },
    {
      label: '附件',
      prop: 'fileList',
      span: 24,
      addDisplay: false,
      editDisplay: false,
      hide: true,
    },
    {
      label: '借货备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },

    {
      label: '借货状态',
      prop: 'borrowStatus',
      type: 'select',
      // search: true,
      display: false,
      width: 160,
      dicData: [
        {
          value: 0,
          label: '待借用',
        },
        {
          value: 1,
          label: '借用中',
        },
        {
          value: 2,
          label: '已归还',
        },
        {
          value: 3,
          label: '部分归还',
        },
      ],
    },
    {
      label: '支付状态',
      prop: 'depositStatus',
      hide: true,
      search: true,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '未支付',
        },
        {
          value: 1,
          label: '已支付',
        },
        {
          value: 2,
          label: '已收款',
        },
      ],
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '归还信息',
      prop: 'returnInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
        {
          label: '归还时间',
          prop: 'returnDate',
          span: 24,
        },
        {
          label: '归还备注',
          prop: 'returnRemark',
          type: 'textarea',
          span: 24,
        },
        {
          label: '',
          prop: 'detailDTOList',
          type: 'dynamic',
          labelWidth: 0,
          labelPosition: 'top',
          hide: true,
          rules: [
            {
              required: true,
              message: '请选择关联产品',
            },
          ],
          span: 24,
          children: {
            align: 'center',
            headerAlign: 'center',
            labelWidth: 0,
            size: 'small',
            index: false,
            rowAdd: done => {
              wfProductSelectRef.value.visible = true;
              // done();
            },
            rowDel: (row, done) => {
              done();
            },

            column: [
              {
                label: '产品名称',
                prop: 'productName',
                overHidden: true,
                bind: 'productVO.productName',
                cell: false,
              },

              {
                label: '规格型号',
                prop: 'productSpecification',
                overHidden: true,
                bind: 'productVO.productSpecification',
                // search: true,
                cell: false,
                span: 24,
                type: 'input',
              },
              {
                label: '品牌',
                prop: 'productBrand',
                overHidden: true,
                bind: 'productVO.productBrand',
                cell: false,
                width: 100,
              },
              {
                label: '单位',
                prop: 'unitName',
                overHidden: true,
                bind: 'productVO.unitName',
                cell: false,
                width: 100,
              },
              {
                label: '数量',
                prop: 'number',
                width: 150,
                type: 'number',
                span: 12,
                cell: true,
                // change: val => {
                //   form.value.refundPrice = totalAmount();
                // },
              },
              {
                label: '已归还数量',
                prop: 'returnNumber',
                width: 150,
                type: 'number',
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true,
                span: 12,
                cell: true,
                // change: val => {
                //   form.value.refundPrice = totalAmount();
                // },
              },
              //   {
              //     label: '单价',
              //     prop: 'purchasePrice',
              //     type: 'number',
              //     span: 12,
              //     width: 150,
              //     cell: true,
              //     change: val => {
              //       form.value.refundPrice = totalAmount();
              //     },
              //   },
              //   {
              //     label: '金额',
              //     prop: 'totalPrice',
              //     type: 'number',
              //     span: 12,
              //     width: 120,
              //     cell: false,
              //     formatter: row => {
              //       return row.number * row.purchasePrice;
              //     },
              //   },
              //   {
              //     label: '序列号',
              //     prop: 'serialNumber',
              //     span: 12,
              //     cell: true,

              //     width: 200,
              //     type: 'textarea',
              //   },
            ],
          },
        },
      ],
    },
    {
      label: '押金信息',
      prop: 'payInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
        {
          label: '支付时间',
          prop: 'paymentDate',
          bind: 'borrowDepositVO.paymentDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '押金附件',
          prop: 'paymentFile',
        },
        {
          type: 'select',
          label: '付款账号',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          bind: 'borrowDepositVO.collectionAccountStr',
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'paymentAccount',
        },
        {
          label: '退款时间',
          prop: 'returnDate1',
          bind: 'borrowDepositVO.returnDate',
          type: 'date',

          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '押金附件',
          prop: 'returnFile',
        },
        {
          type: 'select',
          label: '收款账号',

          cascader: [],
          span: 24,
          // search: true,
          display: true,
          bind: 'borrowDepositVO.collectionAccount',

          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
      ],
    },
    {
      labelWidth: 0,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/borrow/save';
const delUrl = '/api/vt-admin/borrow/remove?ids=';
const updateUrl = '/api/vt-admin/borrow/update';
const tableUrl = '/api/vt-admin/borrow/pageForPayment';
let params = ref({
  depositStatus: 1,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        borrowType: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    borrowType: 0,
    borrowFiles: form.borrowFiles && form.borrowFiles.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    borrowFiles: row.borrowFiles && row.borrowFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleProductSelectConfirm(list) {
  list.split(',').forEach(item => {
    axios
      .get('/api/vt-admin/product/detail', {
        params: {
          id: item,
        },
      })
      .then(res => {
        form.value.detailDTOList.push({
          productId: res.data.data.id,
          productVO: res.data.data,
          number: 1,
        });
      });
  });
}
function beforeOpen(done, type) {
  if (['edit', 'view'].includes(type)) {
    axios.get('/api/vt-admin/borrow/detail', { params: { id: form.value.id } }).then(res => {
      form.value = {
        ...res.data.data,
        detailDTOList: res.data.data.detailVOList.map(item => {
          return {
            ...item,
            productId: item.productId,
            id: null,
          };
        }),
        borrowFiles:
          res.data.data.attachList &&
          res.data.data.attachList.map(item => {
            return {
              value: item.id,
              label: item.originalName,
            };
          }),
      };
    });
  }

  done();
}
function confirmRecevie(row) {
  //   axios.get('/api/vt-admin/borrow/detail', { params: { id: row.id } }).then(res => {
  //     const detailDTOList = res.data.data.detailVOList.map(item => {
  //       return {
  //         ...item,
  //         number: item.number * 1,
  //         id: item.contractDetailId,
  //       };
  //     });
  //     proxy.$refs.dialogForm.show({
  //       title: '确认借货',
  //       width: '70%',
  //       option: {
  //         labelWidth:120,
  //         column: [
  //           {
  //             label: '关联产品',
  //             prop: 'detailDTOList',
  //             type: 'dynamic',
  //             labelWidth: 0,
  //             value: detailDTOList,
  //             hide: true,

  //             span: 24,
  //             children: {
  //               align: 'center',
  //               headerAlign: 'center',
  //               size: 'small',
  //               rowAdd: done => {
  //                 productSelectRef.value.open();
  //                 // done();
  //               },
  //               rowDel: (row, done) => {
  //                 done();
  //               },

  //               column: [
  //                 {
  //                   label: '产品名称',
  //                   prop: 'productName',
  //                   overHidden: true,
  //                   bind: 'productVO.productName',
  //                   cell: false,
  //                 },

  //                 {
  //                   label: '规格型号',
  //                   prop: 'productSpecification',
  //                   overHidden: true,
  //                   bind: 'productVO.productSpecification',
  //                   // search: true,
  //                   cell: false,
  //                   span: 24,
  //                   type: 'input',
  //                 },
  //                 {
  //                   label: '品牌',
  //                   prop: 'productBrand',
  //                   overHidden: true,
  //                   bind: 'productVO.productBrand',
  //                   cell: false,
  //                   width: 120,
  //                 },
  //                 {
  //                   label: '单位',
  //                   prop: 'unitName',
  //                   overHidden: true,
  //                   bind: 'productVO.unitName',
  //                   cell: false,
  //                   width: 120,
  //                 },
  //                 {
  //                   label: '数量',
  //                   prop: 'number',
  //                   type: 'number',
  //                   span: 12,
  //                   cell: true,
  //                   width: 120,
  //                 },
  //                 // {
  //                 //   label: '单价',
  //                 //   prop: 'purchasePrice',
  //                 //   type: 'number',
  //                 //   span: 12,
  //                 //   cell: false,
  //                 //   width: 100,
  //                 // },
  //                 // {
  //                 //   label: '金额',
  //                 //   prop: 'totalPrice',
  //                 //   type: 'number',
  //                 //   span: 12,
  //                 //   width: 100,
  //                 //   cell: false,
  //                 //   formatter: row => {
  //                 //     return row.number * row.purchasePrice;
  //                 //   },
  //                 // },
  //                 // {
  //                 //   label: '序列号',
  //                 //   prop: 'serialNumber',
  //                 //   span: 12,
  //                 //   cell: true,
  //                 //   row: 1,
  //                 //   width: 200,
  //                 //   type: 'textarea',
  //                 // },
  //               ],
  //             },
  //           },
  //           {
  //             label: '借货时间',
  //             prop: 'borrowDate',
  //             type: 'date',
  //             format: 'YYYY-MM-DD',
  //             valueFormat: 'YYYY-MM-DD',
  //             value: row.borrowDate,
  //           },
  //           {
  //             label: '预计归还时间',
  //             prop: 'overDate',
  //             type: 'date',
  //             format: 'YYYY-MM-DD',
  //             valueFormat: 'YYYY-MM-DD',
  //             value: row.overDate,
  //           },
  //           {
  //             label: '是否有押金',
  //             type: 'switch',
  //             prop: 'isHasSecurityDeposit',
  //             dicData: [
  //               {
  //                 value: 0,
  //                 label: '否',
  //               },
  //               {
  //                 value: 1,
  //                 label: '是',
  //               },
  //             ],
  //             control: val => {
  //               return {
  //                 depositAmount: {
  //                   display: val == 1,
  //                 },
  //               };
  //             },
  //           },
  //           {
  //             label: '押金金额',
  //             prop: 'depositAmount',
  //             type: 'number',
  //             span: 12,
  //             display:false,
  //             rules: [
  //               {
  //                 required: true,
  //                 message: '请输入押金金额',
  //               },
  //             ],
  //           },

  //           {
  //             label: '备注',
  //             type: 'textarea',
  //             span: 24,
  //           },
  //         ],
  //       },
  //       callback(res) {
  //         axios
  //           .post('/api/vt-admin/purchaseReturn/confirmReturn', {
  //             ...row,
  //             ...res.data,
  //           })
  //           .then(ref => {
  //             ElMessage.success('操作成功');
  //             onLoad();
  //             res.close();
  //           });
  //       },
  //     });
  //   });
  proxy.$confirm('确认借货吗', '提示').then(res => {
    axios
      .post('/api/vt-admin/borrow/borrow', {
        ...row,
        ...res.data,
      })
      .then(ref => {
        ElMessage.success('操作成功');
        onLoad();
      });
  });
}
function confirmReturn(row) {
  axios.get('/api/vt-admin/borrow/detail', { params: { id: row.id } }).then(res => {
    const detailDTOList = res.data.data.detailVOList.map(item => {
      return {
        ...item,
        number: item.number * 1,
        inputNumber: item.number * 1 - item.returnNumber * 1,
      };
    });
    proxy.$refs.dialogForm.show({
      title: '确认归还',
      width: '70%',
      option: {
        labelWidth: 120,
        column: [
          {
            label: '关联产品',
            prop: 'detailDTOList',
            type: 'dynamic',
            labelWidth: 0,
            value: detailDTOList,
            hide: true,

            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              size: 'small',
              rowAdd: done => {
                productSelectRef.value.open();
                // done();
              },
              rowDel: (row, done) => {
                done();
              },

              column: [
                {
                  label: '产品名称',
                  prop: 'productName',
                  overHidden: true,
                  bind: 'productVO.productName',
                  cell: false,
                },

                {
                  label: '规格型号',
                  prop: 'productSpecification',
                  overHidden: true,
                  bind: 'productVO.productSpecification',
                  // search: true,
                  cell: false,
                  span: 24,
                  type: 'input',
                },
                {
                  label: '品牌',
                  prop: 'productBrand',
                  overHidden: true,
                  bind: 'productVO.productBrand',
                  cell: false,
                  width: 120,
                },
                {
                  label: '单位',
                  prop: 'unitName',
                  overHidden: true,
                  bind: 'productVO.unitName',
                  cell: false,
                  width: 120,
                },
                {
                  label: '借货数量',
                  prop: 'number',
                  type: 'number',
                  span: 12,
                  cell: false,
                  width: 120,
                },
                {
                  label: '已归还数量',
                  prop: 'returnNumber',
                  type: 'number',
                  span: 12,
                  cell: false,
                  formatter: row => {
                    return parseFloat(row.returnNumber * 1);
                  },
                  width: 120,
                },
                {
                  label: '数量',
                  prop: 'inputNumber',
                  type: 'number',
                  span: 12,
                  cell: true,
                  width: 120,
                },
                // {
                //   label: '单价',
                //   prop: 'purchasePrice',
                //   type: 'number',
                //   span: 12,
                //   cell: false,
                //   width: 100,
                // },
                // {
                //   label: '金额',
                //   prop: 'totalPrice',
                //   type: 'number',
                //   span: 12,
                //   width: 100,
                //   cell: false,
                //   formatter: row => {
                //     return row.number * row.purchasePrice;
                //   },
                // },
                // {
                //   label: '序列号',
                //   prop: 'serialNumber',
                //   span: 12,
                //   cell: true,
                //   row: 1,
                //   width: 200,
                //   type: 'textarea',
                // },
              ],
            },
          },
          {
            label: '归还时间',
            prop: 'returnDate',
            type: 'date',
            format: 'YYYY-MM-DD',
            span: 24,
            valueFormat: 'YYYY-MM-DD',
            value: dateFormat(new Date(), 'yyyy-MM-dd'),
          },

          {
            label: '备注',
            type: 'textarea',
            prop: 'returnRemark',
            span: 24,
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin/borrow/returnProduct', {
            ...row,
            ...res.data,
          })
          .then(ref => {
            ElMessage.success('操作成功');
            onLoad();
            res.close();
          });
      },
    });
  });
}

function payAmount(row) {
  proxy.$refs.dialogForm.show({
    title: '支付押金',
    option: {
      column: [
        {
          label: '押金金额',
          prop: 'depositAmount',
          type: 'number',
          span: 12,
          value: row.depositAmount,
          rules: [
            {
              required: true,
              message: '请输入押金金额',
            },
          ],
        },
        {
          label: '支付时间',
          prop: 'paymentDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
        },
        {
          label: '押金附件',
          prop: 'paymentFile',
          type: 'upload',
          span: 24,

          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',
          viewDisplay: false,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/borrowDeposit/payment', {
          borrowId: row.id,
          ...res.data,
          paymentFile:
            res.data.paymentFile && res.data.paymentFile.map(item => item.value).join(','),
        })
        .then(r => {
          ElMessage.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function receviewAmount(row) {
  proxy.$refs.dialogForm.show({
    title: '收回押金',
    option: {
      column: [
        {
          label: '押金金额',
          prop: 'depositAmount',
          type: 'number',
          value: row.depositAmount,
          span: 12,
          rules: [
            {
              required: true,
              message: '请输入押金金额',
            },
          ],
        },
        {
          label: '退回时间',
          prop: 'returnDate',
          type: 'datetime',
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
        },
        {
          type: 'select',
          label: '收款账号',

          cascader: [],
          span: 12,
          // search: true,
          display: true,

          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        {
          label: '押金附件',
          prop: 'returnFile',
          type: 'upload',
          span: 24,

          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',
          viewDisplay: false,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/borrowDeposit/return', {
          borrowId: row.id,
          ...res.data,
          returnFile: res.data.returnFile && res.data.returnFile.map(item => item.value).join(','),
        })
        .then(r => {
          ElMessage.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
</script>

<style lang="scss" scoped>
:deep(.planCollectionDays) {
  font-size: 25px;
  font-weight: bolder;
}
</style>
