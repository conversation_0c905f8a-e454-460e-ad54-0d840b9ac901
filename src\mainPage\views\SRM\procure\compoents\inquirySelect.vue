<template>
  <el-dialog title="" width="80%" v-model="dialogVisible"  >
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-click="rowClick"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #radio="{ row }">
        <el-radio v-model="selectRowId" :label="row.id">{{}}</el-radio>
      </template>
    </avue-crud>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let productSelect = ref(null);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  searchLabelWidth: 120,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '',
      prop: 'radio',
      display:false,
    },
    {
      label: '询价单编号',
      prop: 'code',
      width: 250,
      overHidden: true,
      search: true,
      display: false,
    },
    {
      label: '询价类型',
      prop: 'type',
      width: 250,
      overHidden: true,
      search: true,
      display: false,
      dicData: [
        {
          label: '采购询价',
          value: 0,
        },
        {
          label: '销售询价',
          value: 1,
        },
      ],
    },
    {
      label: '关联供应商',
      prop: 'followContent',
      search: true,
    },
    {
      label: '创建时间',
      prop: 'jobPerformance',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      type: 'textarea',
    },
    {
      label: '关联产品',
      prop: 'productList',
      type: 'dynamic',
      span: 24,
      display: false,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          productSelect.value.visible = true;
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            width: 260,
            editDisabled: true,
            overHidden: true,

            cell: false,
          },

          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,

            editDisabled: true,
            cell: false,
          },
          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            span: 24,
            overHidden: true,
            editDisabled: true,

            type: 'input',
            cell: false,
          },
          {
            label: '数量',
            prop: 'number',
            type: 'number',
          },
        ],
      },
    },
    {
      label: '状态',
      prop: 'status',
      type: 'select',
      display:false,
      dicData: [
        {
          label: '草稿',
          value: '0',
        },
        {
          label: '已提交',
          value: '1',
        },
        {
          label: '已报价',
          value: '2',
        },
        {
          label: '已确认',
          value: '3',
        },
        {
          label: '已拒绝',
          value: '4',
        },
        {
          label: '已取消',
          value: '5',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
let selectRowId = ref(null);
function rowClick(row) {
  selectRowId.value = row.id;
}
let dialogVisible = ref(false)
function open() {
    dialogVisible.value = true
}
function handleConfirm() {
    if (!selectRowId.value) {
        return proxy.$message.warning('请选择一条数据')
    }
}
function searchChange(params, done) {
  onLoad();
  done();
}
defineExpose({
    open
})
</script>

<style lang="scss" scoped></style>
