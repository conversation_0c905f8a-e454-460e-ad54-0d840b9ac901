<template>
 
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const props = defineProps({
    objectId:String
})
let option = ref({
 
  align: 'center',
  addBtn: true,
  editBtn: true,
  header:false,
  menu:false,
  delBtn: true,
 
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '延期时间',
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      prop: 'delayDate',
      rules: [
        {
          required: true,
          message: '请选择延期时间',
        },
      ],
      span: 24,
    },
    {
      label: '延期理由',
      type: 'textarea',
      overHidden:true,
      span: 24,
      rules: [
        {
          required: true,
          message: '请输入延期理由',
        },
      ],
      prop: 'delayReason',
    },
    {
      label: '操作人',
      prop: 'createName',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/vt-admin/sealContractObjectDelay/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
watchEffect(() => {
  if(props.objectId){
    onLoad()
  }
})
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        objectId:props.objectId
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
