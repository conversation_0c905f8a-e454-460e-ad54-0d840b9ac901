<template>
  <basic-container>
    <el-container>
      <el-aside width="280px" style="margin-right: 20px; height: calc(100% - 30px);overflow: hidden;">
        <el-input placeholder="输入关键字进行过滤" v-model="filterText" @input="getTreeData">
        </el-input>
        <avue-tree
          :option="treeOption"
          node-key="id"
          ref="tree"
          style="height: 660px"
          :data="treeData"
          @node-click="nodeClick"
        >
          <template #default="{ node, data }">
            <span>{{ node.label }}</span>
            <span v-if="!data.hasChildren" style="margin-left: 10px; color: var(--el-color-primary)"
              >{{ data.priceMaintenanceDays }}{{ data.priceMaintenanceDays ? '天' : '-' }}</span
            >
            <span v-if="!data.hasChildren" style="margin-left: 10px; color: var(--el-color-success)"
              >{{ data.laborCost ? '￥' : '-' }}{{ data.laborCost }}</span
            >
          </template>
        </avue-tree>
      </el-aside>
      <el-main>
        <avue-crud
          :option="option"
          :data="tableData"
          v-model:page="page"
          v-model:search="params"
          @on-load="onLoad"
          @row-update="rowUpdate"
          @row-save="rowSave"
          :table-loading="loading"
          ref="crud"
          @row-del="rowDel"
          @search-reset="
            () => {
              params.productPropertyVoS = [];
              $refs.tree.setCurrentKey(null);
              categoryId = null;
              onLoad();
            }
          "
          @search-change="searchChange"
          @current-change="onLoad"
          @refresh-change="onLoad"
          @keyup.enter="onLoad"
          @size-change="onLoad"
          v-model="form"
        >
          <template #menu="{ row }">
            <el-popover placement="bottom" :width="180" trigger="click">
              <template #reference>
                <el-button text type="primary" icon="Search" class="m-2">比价</el-button>
              </template>
              <template #default>
                <el-button text type="primary" @click="searchPrice(row, 1)">京东</el-button>
                <el-button text type="primary" @click="searchPrice(row, 2)">淘宝</el-button>
              </template>
            </el-popover>
            <el-button type="primary" icon="circlePlus" @click="addSupplier(row)" text
              >关联供应商</el-button
            >
            <!-- <el-button type="primary" icon="view" @click="addSupplier(row, 'view')" text
              >查看</el-button
            > -->
          </template>
          <template #productName="{row}">
            <el-tooltip :content="row.takeEffectReason" v-if="row.isTakeEffect == 1" placement="">
              <i  style="color: var(--el-color-danger);font-size: 25px;" class="element-icons el-icon-shixiaozhong"></i>
            </el-tooltip>
            <span>{{ row.productName }}</span>
          </template>
          <template #menu-left>
            <el-button icon="plus" v-show="!hasChildren" @click="setCycle" type="primary" plain
              >设置价格维护周期</el-button
            >
            <el-button icon="plus" v-show="!hasChildren" @click="setLaborCost" type="success" plain
              >设置人工费</el-button
            >
          </template>
          
          <template #productCode="{ row }">
            <el-link type="primary" @click="toDetail(row)">{{ row.productCode }}</el-link>
          </template>
          <template #productParam-search>
            <div v-if="searchPropertyList.length == 0">
              <el-alert :closable="false" size="small" type="info"
                >未配置参数或未选中三级分类</el-alert
              >
            </div>
            <div v-else>
              <div
                class="item"
                style="display: flex; justify-content: flex-start; align-items: center"
                v-for="(item, index) in searchPropertyList"
              >
                <el-form>
                  <el-form-item style="font-weight: bold" :label="item.propertyName + ':'">
                    <el-tag effect='plain'
                      :type="params.productPropertyVoS[index] == i.id ? 'success' : 'info'"
                      style="margin-right: 10px; cursor: pointer"
                      @click="handleClick(i, index)"
                      v-for="i in item.valuesEntityList"
                      >{{ i.value }}</el-tag
                    >
                  </el-form-item>
                </el-form>
                <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
                <!-- <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
                <span style="margin-left: 15px">
                  <span  v-for="i in item.valuesEntityList" style="margin-right: 10px;">{{ i.value }}</span>
                  <el-radio-group v-model="params.productPropertyVoS[index]">
                    <el-radio
                      v-for="i in item.valuesEntityList"
                      :label="i.id"
                      size="large"
                      >{{ i.value }}</el-radio
                    >
                  </el-radio-group>
                </span> -->
              </div>
            </div>
          </template>
          <template #costPrice="{ row }">
            <div v-if="row.priceWarnType == 0">
              <div>{{ row.costPrice }}</div>
            </div>
            <div v-else>
              <div v-if="row.priceWarnType == 1" style="color: var(--el-color-success)">
                {{ row.costPrice }}
              </div>
              <div v-else-if="row.priceWarnType == 2" style="color: var(--el-color-warning)">
                {{ row.costPrice }}
              </div>
              <div v-else style="color: var(--el-color-danger)">{{ row.costPrice }}</div>
            </div>
          </template>
          <template #costPrice-header="{ column }">
            <span>{{ column.label }}</span>

            <el-tooltip placement="top">
              <template #content>
                <div style="color: #999; font-size: 14px">
                  <span>
                    <el-icon style="color: var(--el-color-danger)"><WarningFilled /></el-icon>
                    大于维护周期7天
                  </span>
                  <span>
                    <el-icon style="color: var(--el-color-warning)"><WarningFilled /></el-icon>
                    大于维护周期且小于7天
                  </span>
                  <span>
                    <el-icon style="color: var(--el-color-success)"><WarningFilled /></el-icon>
                    小于维护周期
                  </span>
                </div>
              </template>
              <el-icon><QuestionFilled /></el-icon>
            </el-tooltip>
          </template>
        </avue-crud>
      </el-main>
    </el-container>

    <dialogForm ref="dialogForm"></dialogForm>
    <AddSupplier ref="supplier" :productId="productId"></AddSupplier>
    <!-- 产品选择弹窗 -->
    <wfSupplierSelect
      ref="product-select"
      check-type="box"
      :userUrl="`/api/vt-admin/supplier/pageForSupplier?productId=${productId}`"
      @onConfirm="handleUserSelectConfirm"
    ></wfSupplierSelect>

    <el-dialog title="关联供应商" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
      <avue-form
        :option="addOption"
        ref="addFormRef"
        v-model="addForm"
        @submit="submit"
        v-if="!typeStr"
      ></avue-form>
      <avue-crud
        v-else
        :data="addForm.supplierList"
        @row-update="rowSupplierUpdate"
        @row-del="rowSupplierDel"
        :option="detailOption"
      ></avue-crud>
      <!-- <slot ></slot> -->
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="$refs.addFormRef.submit()" v-if="!typeStr" type="primary"
          >确 定</el-button
        >
      </div>
    </el-dialog>
    <detail_drawer :id="currentId" ref="detailRef"></detail_drawer>
    
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import AddSupplier from './compoents/addSupplier.vue';
import wfSupplierSelect from '@/components/Y-UI/wf-supplier-select.vue';
import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import detail_drawer from './compoents/detail_drawer.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  header: true,
  searchMenuSpan: 6,
  searchSpan: 4,
  labelWidth: 150,
  searchIndex: 4,
  searchIcon: true,
  menuWidth: 280,
  border: true,
  column: [
    {
      label: '关键字',
      prop: 'keys',
      overHidden: true,
      placeholder: '名称，型号，品牌',
      display: false,
      search: true,
      hide: true,
    },
    {
      label: '产品编号',
      prop: 'productCode',
      overHidden: true,
      editDisplay: false,
      search: true,
    },
    {
      label: '产品名称',
      prop: 'productName',
      width: 260,
      editDisabled: true,
      overHidden: true,
      search: true,
    },
    {
      label: '所属分类',
      prop: 'categoryName',
      overHidden: true,
      // search: true,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      search: true,
      editDisabled: true,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      span: 24,  component:'wf-product-drop',
      overHidden: true,
      editDisabled: true,
      search: true,
      type: 'input',
    },
    {
      label: '参数查询',
      prop: 'productParam',
      hide: true,
      search: true,
      addDisplay: false,
      editDisplay: false,
      searchSpan: 24,
      searchSlot: true,
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'radio',
      value: 1,
      rules: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
      labelTip: '采购价是否含税',
      dicData: [
        { label: '是', value: 1 },
        { label: '否', value: 0 },
      ],
    },
    {
      label: '采购价',
      prop: 'purchasePrice',
      type: 'number',
      overHidden: true,
      editDisplay: false,
      // hide: true,
      rules: [{ required: true, message: '请输入采购价', trigger: 'blur' }],
    },
    {
      label: '参考采购价',
      prop: 'referPurchasePrice',
      type: 'number',
      hide: true,
      editDisplay: false,
      placeholder: '输入或者自动计算',
      overHidden: true,
    },
   
    {
      label: '成本价',
      prop: 'costPrice',
      type: 'number',
      // readonly: true,
      editDisplay: true,
      // placeholder: '自动计算',
      overHidden: true,
    },

    {
      label: '最低销售价',
      prop: 'minSealPrice',
      type: 'number',
      placeholder: '自动计算',
      readonly: true,
      overHidden: true,
    },
    {
      label: '销售价',
      prop: 'sealPrice',
      type: 'number',
      // readonly: true,
      hide: true,
      editDisplay: false,
      overHidden: true,
    },
    {
      label: '参考销售价',
      prop: 'referSealPrice',
      type: 'number',
      placeholder: '自动计算',
      readonly: true,
      overHidden: true,
    },

    {
      label: '市场价',
      prop: 'marketPrice',
      type: 'number',
      overHidden: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  defaultExpandAll: false,
  menu: false,
  filter: false,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    value: 'id',
    children: 'children',
  },
  lazy: true,
  treeLoad: function (node, resolve) {
    axios
      .get('/api/vt-admin/productCategory/list', {
        params: {
          parentId: node.data.id,
        },
      })
      .then(res => {
        resolve(
          res.data.data.map(item => {
            return {
              ...item,
              leaf: !item.hasChildren,
            };
          })
        );
      });
  },
});
const addUrl = '/api/vt-admin/product/save';
const delUrl = '/api/vt-admin/product/remove?ids=';
const updateUrl = '/api/vt-admin/product/updatePrice';
const tableUrl = '/api/vt-admin/product/page';
let params = ref({
  productPropertyVoS: [],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  // onLoad();
  getTreeData();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        categoryId: categoryId.value,
        ...params.value,
        isNew: 0,
        valueIds: params.value.productPropertyVoS.filter(item => item).join(),
        productPropertyVoS: null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    categoryId: categoryId.value,
    coverId: form.coverId[0] && form.coverId[0].value,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  page.value.currentPage = 1;
  console.log(page.value);
  onLoad();
  done();
}
let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/productCategory/list', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren,
        };
      });
    });
}
let filterText = ref('');
let categoryId = ref('');
let categoryName = ref('');
let hasChildren = ref(true);
function nodeClick(val, accountName) {
  console.log(val);
  categoryId.value = val.id;
  hasChildren.value = val.hasChildren;
  categoryName.value = val.categoryName;
  getParamsList(val.id);
  params.value.productPropertyVoS = [];
  onLoad();
}
function searchPrice(row, type) {
  if (type == 1) {
    window.open(
      'https://search.jd.com/Search?keyword=' +
        `${row.productName} ${row.productSpecification} ${row.productBrand}`
    );
  } else if (type == 2) {
    window.open(
      'https://s.taobao.com/search?q=' +
        `${row.productName} ${row.productSpecification} ${row.productBrand}`
    );
  }
}
let productId = ref('');
let typeStr = ref('');
function addSupplier(row, type) {
  // addForm.value = {}
  // productId.value = row.id;
  // dialogVisible.value = true;
  // if (type == 'view') {
  //   typeStr.value = type;
  //   addOption.value.detail = true;
  //   getSupplierList()
  // } else {
  //   typeStr.value = '';
  //   addOption.value.detail = false;
  // }
  router.push({
    path: '/SRM/product/compoents/detail',
    query: {
      id: row.id,
      activeName: 'supplierInfo',
    },
  });
}
function getSupplierList(params) {
  axios
    .get('/api/vt-admin/supplierProduct/page', {
      params: {
        size: 500,
        productId: productId.value,
      },
    })
    .then(res => {
      addForm.value.supplierList = res.data.data.records;
    });
}
function setCycle() {
  proxy.$refs.dialogForm.show({
    title: categoryName.value,
    option: {
      labelWidth: 120,
      column: [
        {
          label: '维护周期(天)',
          rules: [
            {
              required: true,
              message: '请输入维护周期',
              trigger: 'blur',
            },
          ],
          type: 'number',
          prop: 'priceMaintenanceDays',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/productCategory/update', {
          id: categoryId.value,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getTreeData();
        });
    },
  });
}
function setLaborCost() {
  proxy.$refs.dialogForm.show({
    title: categoryName.value,
    option: {
      labelWidth: 120,
      column: [
        {
          label: '人工费',
          rules: [
            {
              required: true,
              message: '请输入人工费',
              trigger: 'blur',
            },
          ],
          type: 'number',
          prop: 'laborCost',
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/productCategory/update', {
          id: categoryId.value,
          ...res.data,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getTreeData();
        });
    },
  });
}

let searchPropertyList = ref([]);
function getParamsList(categoryId) {
  // 获取参数
  axios.get('/api/vt-admin/productCategory/detail?id=' + categoryId).then(res => {
    searchPropertyList.value = res.data.data.propertyVOList.filter(item => item.type != 2);
  });
}
function handleClick(i, index) {
  if (params.value.productPropertyVoS[index] == i.id) {
    params.value.productPropertyVoS[index] = '';
  } else {
    params.value.productPropertyVoS[index] = i.id;
  }
}
let dialogVisible = ref(false);
let addForm = ref({});
const addOption = ref({
  submitBtn: false,
  emptyBtn: false,
  column: [
    {
      label: '',
      prop: 'supplierList',
      type: 'dynamic',
      span: 24,
      labelWidth: 0,
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          addProduct();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '供应商',
            prop: 'supplierId',
            component: 'wf-supplier-select',
          },

          {
            label: '单价',
            prop: 'unitPrice',
            type: 'number',
            span: 12,
          },
        ],
      },
    },
  ],
});
const detailOption = ref({
  submitBtn: false,
  emptyBtn: false,
  addBtn: false,
  delBtn: true,
  align: 'center',
  border: true,
  menu: true,
  column: [
    {
      label: '供应商',
      prop: 'supplierName',
      editDisabled: true,
    },

    {
      label: '单价',
      prop: 'unitPrice',
      type: 'number',
      span: 12,
    },
  ],
});
function addProduct() {
  proxy.$refs['product-select'].visible = true;
}

function handleUserSelectConfirm(ids) {
  addForm.value = {
    supplierList: ids.split(',').map(item => {
      return {
        supplierId: item,
      };
    }),
  };
}
function submit(form, done) {
  const data = form.supplierList.map(item => {
    return {
      productId: productId.value,
      ...item,
    };
  });
  axios
    .post('/api/vt-admin/supplierProduct/saveBatch', data)
    .then(res => {
      proxy.$message.success('添加成功');
      proxy.$refs.addFormRef.resetForm();
      done();
      dialogVisible.value = false;
      addForm.value = {};
    })
    .catch(() => {
      proxy.$message.error(res.data.msg);
      proxy.$refs.addFormRef.resetForm();
    });
}
function rowSupplierUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/supplierProduct/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getSupplierList();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowSupplierDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post('/api/vt-admin/supplierProduct/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        getSupplierList();
      });
    })
    .catch(() => {});
}
let currentId = ref(null);
let detailRef = ref(null)
function toDetail(row) {
  currentId.value = row.id;

  detailRef.value.open();
}
</script>

<style lang="scss" scoped></style>
