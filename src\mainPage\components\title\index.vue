<template>
  <div class="title-container" :style="{ height: height + 'px', 'line-height': height + 'px' }">
    <div style="min-width: 100px;"><slot></slot></div>
   <div style="flex:100;display: flex;justify-content: flex-end;"  >
    <slot name="foot"  style="float: right;" ></slot>
   </div>
  </div>
</template>

<script>
export default {
  props: {
    // 组件高度
    height: {
      type: Number,
      default: 40,
    },
  },
};
</script>

<style lang="scss" scoped>
.title-container {
  display: flex;
  align-items: center;
  // justify-content: flex-start;
  overflow: hidden;
  font-weight: bolder;
  // border-bottom:1px #ccc dotted;
}
.title-container::before {
  content: '';
  height: 100%;
  width: 10px;
  display: inline-block;
  
  background-color: #409eff;
  border-radius: 5px;
  transform: translateX(-5px);
}
</style>
