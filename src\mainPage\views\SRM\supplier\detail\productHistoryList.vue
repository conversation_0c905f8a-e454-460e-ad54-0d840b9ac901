<template>
  <el-drawer title="产品历史报价信息" v-model="drawer" size="60%">
    <el-row :gutter="10">
      <el-col :span="6">
        <el-card shadow="never" >
          <el-descriptions
            border
            :column="1"
            style="margin-bottom: 5px"
            :title="productInfo.productName"
            size="small"
            direction="vertical"
          >
            <el-descriptions-item label-width="120px" label="品牌">{{
              productInfo.productBrand
            }}</el-descriptions-item>
            <el-descriptions-item label="型号">{{
              productInfo.productSpecification.slice(0, 20)
            }}</el-descriptions-item>
            <el-descriptions-item label="单位">{{ productInfo.unitName }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="18">
        <el-card shadow="never" body-style="padding:5px">
          <historyPrice :product-id="productId" :supplier-id="supplierId"></historyPrice>
        </el-card>
      </el-col>
    </el-row>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #orderNo="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.orderNo }}</el-link>
      </template>
    </avue-crud>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import historyPrice from './historyPrice.vue';
const props = defineProps(['supplierId', 'productId', 'productInfo']);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  addBtn: false,
  delBtn: true,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  border: true,
  column: [
    {
      label: '采购订单',
      prop: 'orderNo',
      width: 200,
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      span: 12,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
    },
    {
      label: '产品质保期（年）',
      prop: 'warrantyPeriod',
      type: 'number',
      span: 12,
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'switch',
      value: 1,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      span: 12,
    },
    {
      label: '税率',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter:row => {
        return parseFloat(row.taxRate) + '%'
      }
    },
    {
      label: '价格',
      prop: 'purchasePrice',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
watch(
  () => props.productId,
  () => {
    onLoad();
  }
);
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/supplierProduct/historyPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        supplierId: props.supplierId,
        productId: props.productId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let drawer = ref(false);
function open() {
  drawer.value = true;
}
function toDetail(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/orderDetail',
    query: {
      id: row.orderId,
      activeName,
    },
  });
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
