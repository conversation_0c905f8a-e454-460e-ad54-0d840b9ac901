<template>
  <div id="app" class="bg-gray-100">
    
    <!-- 导航栏 -->
    <Navbar />
    
    <!-- 主要内容区域 -->
    <main>
     
      <router-view />
      
    </main>
    
    
  </div>
</template>

<script setup>
import Navbar from './components/Navbar.vue'
import Footer from './components/Footer.vue'
</script>

<style scoped>
#app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
</style>