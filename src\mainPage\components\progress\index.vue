<template>
    <div class="task-list">
      <div class="task-item">
        <i class="iconfont icon-file-folder"></i>
        
        <el-progress :percentage="50" :show-text="false" :format="format" :indeterminate="true" />
        
      </div>
    </div>
  </template>
  
  <script>
  import { ref } from 'vue'
  import { ElProgress } from 'element-plus'
  
  export default {
    name: 'progress',
    props: ['propA'], // 接收父组件传递的prop
    emits: ['finish'], // 接收父组件传递的事件
    setup(props, { emit }) {
      let progress = ref(0)
      let status = ref('')
      // 改变进度条状态的相关逻辑代码
      function changeProgress() {
        if (progress.value >= 100 || status.value == 'ok') {
          emit('finish', status.value) // 发射事件，并传递一个参数
        }
      }
      return {
        progress,
        status,
      }
    }
  }
  </script>
  
  <style scoped>
    .task-item {
        margin-left:-50px;
      width: 300px;
      display: grid;
      grid-template-columns: 50px 1fr;
      grid-template-rows: 2fr 1fr 2fr;
      align-items: center;
    }
    .task-item i {
      font-size: 30px;
      color: rgb(252, 203, 66);
      grid-column-start: 1;
      grid-column-end: 2;
      grid-row-start: 1;
      grid-row-end: 4;
    }
  </style>
  