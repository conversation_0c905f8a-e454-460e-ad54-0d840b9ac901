<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @row-del="rowDel"
      @search-reset="
        () => {
          params.productPropertyVoS = [];
          onLoad();
        }
      "
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      @keyup.enter="onLoad"
      v-model="form"
    >
      <template #productProperty-form>
        <div>
          <div
            class="item"
            style="display: flex; justify-content: flex-start; align-items: center"
            v-for="item in propertyList"
          >
            <!-- <el-switch  v-model="item.isUse"></el-switch> <span>{{ item.propertyName }}</span> -->
            <el-tag effect='plain'>{{ item.propertyName }}</el-tag>
            <span style="margin-left: 15px" v-if="item.type == 1">
              <el-checkbox-group v-model="item.selectList">
                <el-checkbox
                  v-for="i in item.valuesEntityList"
                  :label="i.id + `-` + i.value"
                  size="large"
                  >{{ i.value }}</el-checkbox
                >
              </el-checkbox-group>
            </span>
            <span style="margin-left: 15px" v-else-if="item.type == 0">
              <el-radio
                v-model="item.radioSelect"
                v-for="i in item.valuesEntityList"
                :label="i.id + `-` + i.value"
                size="large"
                @click.native.prevent="
                  item.radioSelect =
                    item.radioSelect == i.id + `-` + i.value ? '' : i.id + `-` + i.value
                "
                >{{ i.value }}</el-radio
              >
            </span>
            <span style="margin-left: 15px" v-else-if="item.type == 2">
              <el-input v-model="item.value" size="small"></el-input>
            </span>
          </div>
        </div>
      </template>
      <template #productName="{ row }">
        <el-tooltip
          :content="row.takeEffectReason"
          v-if="row.isSpecial == 1"
          effect="light"
          placement=""
        >
          <template #content>
            <Title>专项供应</Title>
            <div v-for="item in row.specialBusinessName && row.specialBusinessName.split(',')">
              <el-text type="primary">《{{ item }}》</el-text>
            </div>
          </template>

          <i
            style="color: var(--el-color-primary); font-size: 20px; cursor: pointer"
            class="element-icons el-icon-biaoqian"
          ></i>
        </el-tooltip>
        <span>{{ row.productName }}</span>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" @click="deleteProduct(row)" text icon="SetUp">作废</el-button>
      </template>
    </avue-crud>

    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  tabs: true,
  delBtn: false,
  calcHeight: 30,
  searchIndex: 4,
  searchIcon: true,
  searchMenuSpan: 6,
  searchSpan: 4,
  editBtnText: '录入产品库',
  updateBtnText: '录入产品库',
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '关键字',
      prop: 'keys',
      overHidden: true,
      placeholder: '名称，型号，品牌',
      display: false,
      search: true,
      hide: true,
    },
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
      search: true,
      display: false,
    },
    {
      label: '品牌',
      prop: 'productBrand',
      overHidden: true,
      search: true,
      display: false,
    },
    {
      label: '单位',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      display: false,
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],
      prop: 'unit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
    },

    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
      search: true,
      span: 24,
      type: 'input',
      display: false,
    },

    {
      label: '产品图片',
      prop: 'coverUrl',
      type: 'upload',
      display: false,
      dataType: 'object',
      listType: 'picture-img',
      loadText: '图片上传中，请稍等',
      span: 24,
      slot: true,
      limit: 1,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'link',
        name: 'originalName',
      },
      action: '/blade-resource/attach/upload',
      uploadAfter: (res, done) => {
        form.value.coverId = res.id;
        console.log(form.value);
        done();
      },
    },
    {
      label: '产品参数',
      prop: 'productProperty',
      type: 'input',
      hide: true,
      span: 24,
      display: false,
    },
    {
      label: '商品描述',
      prop: 'description',
      overHidden: true,
      type: 'textarea',
      span: 24,
      display: false,
    },

    {
      label: '用途',
      prop: 'purpose',
      overHidden: true,
      type: 'textarea',
      span: 24,
      display: false,
    },
  ],
  group: [
    {
      arrow: true,
      label: '基本信息',
      prop: 'baseInfo',
      column: [
        {
          label: '产品名称',
          prop: 'productName',
          overHidden: true,
          rules: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
          search: true,
        },
        {
          label: '产品分类',
          prop: 'categoryId',
          search: true,
          hide: true,
          type: 'tree',
          rules: [
            {
              required: true,
              message: '请选择产品分类',
              trigger: 'change',
            },
          ],
          dicUrl: '/api/vt-admin/productCategory/tree',
          props: {
            value: 'id',
            label: 'categoryName',
          },
          children: 'hasChildren',
          //   lazy: true,
          //   treeLoad: function (node, resolve) {
          //     axios
          //       .get('/api/vt-admin/productCategory/list', {
          //         params: {
          //           parentId: node.data.id,
          //         },
          //       })
          //       .then(res => {
          //         resolve(
          //           res.data.data.map(item => {
          //             return {
          //               ...item,
          //               leaf: !item.hasChildren,
          //             };
          //           })
          //         );
          //       });
          //   },
          change: val => {
            console.log(val, form.value);
            if (!val.value || val.value == form.value.categoryId) return;
            getPropertyList(val.value);
          },
        },
        {
          label: '品牌',
          prop: 'productBrand',
          overHidden: true,
          search: true,
        },
        {
          label: '单位',
          type: 'select',
          props: {
            label: 'dictValue',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择单位',
              trigger: 'blur',
            },
          ],
          prop: 'unit',
          dicUrl: '/blade-system/dict/dictionary?code=unit',
          remote: false,
        },

        {
          label: '规格型号',
          prop: 'productSpecification',
          overHidden: true,
          search: true,
          span: 24,
          type: 'input',
        },
        {
          label: '协议商品',
          prop: 'isAgreement',
          type: 'radio',
          span: 24,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],
        },
        {
          label: '是否专项',
          prop: 'isSpecial',
          type: 'radio',
          hide: true,
          value: 0,
          dicData: [
            {
              label: '是',
              value: 1,
            },
            {
              label: '否',
              value: 0,
            },
          ],

          overHidden: true,
          control: val => {
            console.log(val);
            return {
              specialBusiness: {
                display: !!val,
              },
            };
          },
        },
        {
          label: '商机选择',
          prop: 'specialBusiness',
          hide: true,
          span: 24,
          placeholder: '请选择商机',
          component: 'wf-business-select',
          params: {
            checkType: 'checkbox',
            Url: '/vt-admin/businessOpportunity/page?selectType=2',
          },
        },
        {
          label: '产品图片',
          prop: 'coverUrl',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            this.addForm.coverId = res.id;

            done();
          },
        },
        {
          label: '产品参数',
          prop: 'productProperty',
          type: 'input',
          hide: true,
          slot: true,
          span: 24,
        },
        {
          label: '商品描述',
          prop: 'description',
          overHidden: true,
          type: 'textarea',
          span: 24,
        },

        {
          label: '用途',
          prop: 'purpose',
          overHidden: true,
          type: 'textarea',
          span: 24,
        },
      ],
    },
    {
      label: '价格信息',
      arrow: true,
      prop: 'priceInfo',
      labelWidth: '100px',
      value: 1,
      column: [
        {
          label: '是否含税',
          prop: 'isHasTax',
          type: 'radio',
          rules: [{ required: true, message: '请选择是否含税', trigger: 'change' }],
          labelTip: '采购价是否含税',
          dicData: [
            { label: '是', value: 1 },
            { label: '否', value: 0 },
          ],
        },
        // {
        //   label: '采购价',
        //   prop: 'purchasePrice',
        //   type: 'number',
        //   overHidden: true,
        //   rules: [{ required: true, message: '请输入采购价', trigger: 'blur' }],
        // },
        {
          label: '成本价',
          prop: 'costPrice',
          type: 'number',
          overHidden: true,
        },
        // {
        //   label: '市场价',
        //   prop: 'marketPrice',
        //   type: 'number',
        //   overHidden: true,
        // },
      ],
    },
    {
      label: '供应商信息',
      prop: 'supplierInfo',
      column: [
        {
          label: '供应商名称',
          prop: 'supplierId',
          labelWidth: '120px',
          overHidden: true,
          component: 'wf-supplier-select',
          // rules: [{ required: true, message: '请选择供应商', trigger: 'blur' }],
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
const tableUrl = '/api/vt-admin/product/newPage';
let params = ref({
  productPropertyVoS: [],
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
// onMounted(() => {
//   onLoad();
// });
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();
let propertyList = ref([]);
function beforeOpen(done, type) {
  if (type == 'edit') {
    axios.get('/api/vt-admin/product/detail?id=' + form.value.id).then(res => {
      form.value.supplierId = res.data.data.supplierId;
      propertyList.value = res.data.data.productPropertyVoS.map(item => {
        let radioSelect = '';
        let selectList = [];
        let value;
        if (item.type == 0) {
          if (item.entityList.filter(item => item.isCheck == 1).length > 0) {
            const { id, value } = item.entityList.filter(item => item.isCheck == 1)[0];
            radioSelect = `${id}-${value}`;
          }
        } else if (item.type == 1) {
          selectList = item.entityList
            .filter(item => item.isCheck == 1)
            .map(item => {
              const { id, value } = item;
              return `${id}-${value}`;
            });
        } else {
          value = item.value;
        }
        return {
          ...item,
          valuesEntityList: item.entityList,
          selectList,
          radioSelect,
          value,
        };
      });
      done();
    });
  }else{
    done(); 
  }
  
  
}
function searchChange(params, done) {
  page.value.currentPage = 1;
  console.log(page.value);
  onLoad();
  done();
}
function getPropertyList(categoryId) {
  // 获取参数
  axios.get('/api/vt-admin/productCategory/detail?id=' + categoryId).then(res => {
    propertyList.value = res.data.data.propertyVOList.map(item => {
      return {
        ...item,
      };
    });
  });
}
function rowUpdate(row, index, done, loading) {
  const propertyDTOList = formatData(propertyList.value, 'edit');
  const data = {
    ...row,
    propertyDTOList,
  };
  axios
    .post('/api/vt-admin/product/updateNewProduct', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        proxy.$store.dispatch('getMessageList');
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function formatData(data, type) {
  return data.map(item => {
    let valuesDTOList = [];
    let value = '';
    if (item.type == 0) {
      if (!item.radioSelect) {
        valuesDTOList = [];
      } else {
        const [valuesId, value] = item.radioSelect && item.radioSelect.split('-');
        valuesDTOList = [
          {
            valuesId,
            value,
          },
        ];
      }
    } else if (item.type == 1) {
      valuesDTOList =
        item.selectList &&
        item.selectList.map(i => {
          const [valuesId, value] = i && i.split('-');
          return {
            valuesId,
            value,
          };
        });
    } else {
      value = item.value;
    }
    return {
      propertyId: type == 'add' ? item.id : item.propertyId,
      valuesDTOList,
      value,
    };
  });
}
function deleteProduct(row) {
  proxy
    .$confirm('确定作废此产品?', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/product/replaceNewId?newId=' + row.id, {
        
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          onLoad();
        });
    });
}
</script>

<style lang="scss" scoped></style>
