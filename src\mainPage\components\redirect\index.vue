<template>
    <div>
        {{  $route.params.path }}
    </div>
</template>

<script setup>
import { onMounted } from 'vue';
import { useRoute,useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter();
onMounted(() => {
    console.log('路由参数：',route.params.path);
   router.push(`/${route.params.path}`) // 跳转到首页，也可以跳转到其他页面，如：/about,/us
})
</script>

<style lang="scss" scoped>

</style>