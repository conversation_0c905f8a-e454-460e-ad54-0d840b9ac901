<template>
  <nav :class="['navbar fixed top-0 left-0 right-0 z-50', isHomePage ? 'bg-transparent' : 'bg-white shadow-md']">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <h1 :class="['text-2xl font-bold', isHomePage ? 'text-white' : 'text-blue-600']">智聚联云</h1>
          </div>
        </div>
        
        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <template v-for="item in navItems" :key="item.name">
              <!-- 普通导航项 -->
              <router-link
                v-if="!item.dropdown"
                :to="item.path"
                class="nav-link px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                :class="getNavLinkClass(item.path)"
              >
                {{ item.name }}
              </router-link>
              
              <!-- 下拉菜单项 -->
              <div 
                v-else
                class="relative"
                @mouseenter="showDropdown = item.name"
                @mouseleave="showDropdown = null"
              >
                <button
                  class="nav-link px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 flex items-center"
                  :class="getDropdownButtonClass(item)"
                >
                  {{ item.name }}
                  <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                  </svg>
                </button>
                
                <!-- 下拉菜单 -->
                <div 
                  v-if="showDropdown === item.name"
                  class="absolute top-full left-0  w-48 bg-white rounded-md shadow-lg border border-gray-200 py-1 z-50"
                >
                  <router-link
                    v-for="subItem in item.dropdown"
                    :key="subItem.name"
                    :to="subItem.path"
                    class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200"
                    :class="{ 'bg-blue-50 text-blue-600': $route.path === subItem.path }"
                  >
                    {{ subItem.name }}
                  </router-link>
                </div>
              </div>
            </template>
          </div>
        </div>
        
        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="toggleMobileMenu"
            :class="getMobileButtonClass()"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>
    </div>
    
    <!-- Mobile Navigation -->
    <div 
      :class="['mobile-menu md:hidden', { 'active': mobileMenuOpen }]"
      class="fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out"
    >
      <div class="flex items-center justify-between h-16 px-4 border-b">
        <h1 class="text-xl font-bold text-blue-600">智聚联云</h1>
        <button 
          @click="toggleMobileMenu"
          class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
      <div class="px-2 pt-2 pb-3 space-y-1">
        <template v-for="item in navItems" :key="item.name">
          <!-- 普通导航项 -->
          <router-link 
            v-if="!item.dropdown"
            :to="item.path"
            @click="mobileMenuOpen = false"
            class="block px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
            :class="{ 'bg-blue-600 text-white': $route.path === item.path, 'text-gray-700 hover:bg-gray-100': $route.path !== item.path }"
          >
            {{ item.name }}
          </router-link>
          
          <!-- 下拉菜单项 -->
          <div v-else class="space-y-1">
            <button
              @click="toggleMobileDropdown(item.name)"
              class="w-full flex items-center justify-between px-3 py-2 rounded-md text-base font-medium transition-colors duration-200"
              :class="{ 'bg-blue-600 text-white': isDropdownActive(item), 'text-gray-700 hover:bg-gray-100': !isDropdownActive(item) }"
            >
              {{ item.name }}
              <svg 
                class="h-4 w-4 transition-transform duration-200"
                :class="{ 'rotate-180': mobileDropdownOpen === item.name }"
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            
            <!-- 移动端下拉菜单内容 -->
            <div 
              v-if="mobileDropdownOpen === item.name"
              class="ml-0 space-y-1"
            >
              <router-link
                v-for="subItem in item.dropdown"
                :key="subItem.name"
                :to="subItem.path"
                @click="mobileMenuOpen = false"
                class="block px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200"
                :class="{ 'bg-blue-50 text-blue-600': $route.path === subItem.path, 'text-gray-600 hover:bg-gray-100': $route.path !== subItem.path }"
              >
                {{ subItem.name }}
              </router-link>
            </div>
          </div>
        </template>
      </div>
    </div>
    
    <!-- Mobile menu overlay -->
    <div 
      v-if="mobileMenuOpen" 
      @click="toggleMobileMenu"
      class="fixed inset-0 z-30 bg-black bg-opacity-50 md:hidden"
    ></div>
  </nav>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()
const mobileMenuOpen = ref(false)
const showDropdown = ref(null)
const mobileDropdownOpen = ref(null)

// 判断是否是首页
const isHomePage = computed(() => route.path === '/')

const navItems = [
  { name: '首页', path: '/' },
  // { name: '产品库', path: '/products' },
  { name: '品牌库', path: '/brand' },
  { name: '方案库', path: '/knowledge' },
  // { name: '培训库', path: '/training' },
  // { name: '人力资源库', path: '/hr' },
  // { 
  //   name: '更多支持', 
  //   dropdown: [
  //     { name: '对外小程序系统', path: '/more/mini-program' },
  //     { name: '项目资金托底', path: '/more/funding-support' },
  //     { name: '商机方案', path: '/more/business-solutions' }
  //   ]
  // },
  // { name: '关于我们', path: '/about' },
  // { name: '联系我们', path: '/contact' }
]

const toggleMobileMenu = () => {
  mobileMenuOpen.value = !mobileMenuOpen.value
  // 关闭移动端下拉菜单
  mobileDropdownOpen.value = null
}

const toggleMobileDropdown = (itemName) => {
  mobileDropdownOpen.value = mobileDropdownOpen.value === itemName ? null : itemName
}

const isDropdownActive = (item) => {
  if (!item.dropdown) return false
  return item.dropdown.some(subItem => route.path === subItem.path)
}

// 获取导航链接样式
const getNavLinkClass = (path) => {
  const isActive = route.path === path

  if (isHomePage.value) {
    // 首页样式：白色文字
    return {
      'bg-white bg-opacity-20 text-white': isActive,
      'text-white hover:bg-white hover:bg-opacity-10': !isActive
    }
  } else {
    // 其他页面样式：原来的样式
    return {
      'bg-blue-600 text-white': isActive,
      'text-gray-700 hover:bg-gray-100': !isActive
    }
  }
}

// 获取下拉按钮样式
const getDropdownButtonClass = (item) => {
  const isActive = isDropdownActive(item)

  if (isHomePage.value) {
    // 首页样式：白色文字
    return {
      'bg-white bg-opacity-20 text-white': isActive,
      'text-white hover:bg-white hover:bg-opacity-10': !isActive
    }
  } else {
    // 其他页面样式：原来的样式
    return {
      'bg-blue-600 text-white': isActive,
      'text-gray-700 hover:bg-gray-100': !isActive
    }
  }
}

// 获取移动端按钮样式
const getMobileButtonClass = () => {
  if (isHomePage.value) {
    // 首页样式：白色文字
    return 'inline-flex items-center justify-center p-2 rounded-md text-white hover:text-white hover:bg-white hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white'
  } else {
    // 其他页面样式：原来的样式
    return 'inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-blue-500'
  }
}
</script>

<style scoped>
.mobile-menu {
  transform: translateX(-100%);
}

.mobile-menu.active {
  transform: translateX(0);
}
</style>