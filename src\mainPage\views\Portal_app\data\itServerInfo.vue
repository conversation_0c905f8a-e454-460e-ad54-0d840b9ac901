<template>
  <basic-container>
    <el-container>
      <el-aside style="width: 200px">
        <div style="display: flex; flex-direction: column; gap: 10px">
          <div
            v-for="(item, index) in typeList"
            :key="item.id || item.value"
            style="
              background-color: var(--el-color-primary-light-8);
              padding: 10px 15px;
              border-radius: 15px;
              cursor: pointer;
              margin-right: 10px;
              font-size: 14px;
            "
            @click="handleClick(item)"
            :style="{
              backgroundColor:
                activeName == item.value
                  ? ' var(--el-color-primary-light-8)'
                  : ' var(--el-color-info-light-8)',
            }"
          >
            <el-text
              style="font-weight: bold"
              :style="{
                color:
                  activeName == item.value ? ' var(--el-color-primary)' : ' var(--el-color-info)',
              }"
              class="mx-1"
              >{{ item.label }}</el-text
            >
          </div>
        </div>
      </el-aside>
      <el-main>
        <avue-form :option="option" v-model="form" @submit="submit"></avue-form>
      </el-main>
    </el-container>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let questionTypeData = ref([]);
let activeName = ref(0);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,

  dialogType: 'drawer',
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '内容',
      prop: 'content',
      component: 'AvueUeditor',
      action: '/api/blade-resource/attach/upload',
      options: {
        action: '/api/blade-resource/attach/upload',
        accept: 'image/png, image/jpeg, image/jpg,.mp4',
      },
      propsHttp: {
        res: 'data',
        url: 'link',
      },

      minRows: 8,
      span: 24,
    },
  ],
});
let form = ref({
  content: '',
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let typeList = ref([
  {
    label: '设备安装',
    value: 0,
  },
  {
    label: '维修检测',
    value: 1,
  },
  {
    label: '驻场服务',
    value: 2,
  },
  {
    label: '远程服务',
    value: 3,
  },
  {
    label: '软件服务',
    value: 4,
  },
  {
    label: '维保服务',
    value: 5,
  },
//   {
//     label: '更多服务',
//     value: 6,
//   },
]);
onMounted(() => {
  onLoad();
});
let tableUrl = '/api/vt-admin/itServer/page';
function onLoad() {
  axios.get(tableUrl, {}).then(res => {
    res.data.data.records.forEach(item => {
      if (!item.type && item.type != 0) return;
      const label = typeList.value[item.type]?.label;
      const value = typeList.value[item.type]?.value;
      typeList.value[item.type] = item;
      typeList.value[item.type].label = label;
      typeList.value[item.type].value = value;
    });
    form.value = {
      ...typeList.value[activeName.value],
    };
  });
}
function handleClick(item) {
  console.log(item);

  activeName.value = item.value;
  form.value = {
    ...item,
    content: item.content,
  };
}
function submit(form, done) {
  console.log(form);

  axios
    .post('/api/vt-admin/itServer/save', {
      ...form,
      type: activeName.value,
      id: null,
    })
    .then(res => {
      done();
      ElMessage.success('保存成功');
      onLoad();
    }).catch(err => {
      done();
    });
}
</script>

<style lang="scss" scoped></style>
