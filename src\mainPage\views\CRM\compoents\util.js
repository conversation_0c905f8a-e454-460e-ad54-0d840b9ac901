let headerIndex = null;
let headerKey = '产品名称'
/**
 * 查找表头所在行
 * @key 关键字
 * @column 所在列
 * @option 所在表
 */
function getRowByHeaderValue(key = headerKey, column = 0,option) {
  if (headerIndex) return headerIndex;
  // 遍历总行数
  let row = 0;
  for (let index = 0; index < getAllRowLength(option); index++) {
    if (window.luckysheet.getCellValue(index, column,option) === key) {
      row = index;
      headerIndex = index;
      break;
    }
  }
  return row;
}
/**
 * 刷新表头所在行
 * @key 关键字
 * @column 所在列
 */
function refreshRowByHeaderValue(key = headerKey, column = 0) {
  
  // 遍历总行数
 
  for (let index = 0; index < getAllRowLength(); index++) {
    if (window.luckysheet.getCellValue(index, column) === key) {
      
      headerIndex = index;
      break;
    }
  }
 
}
/**
 * 查找某个字段所在行
 * @key 关键字
 * @column 所在列
 */
function getRowByValue(key = '', option) {

  // 遍历总行数
  let row = 0;
  for (let index = 0; index < getAllRowLength(option); index++) {
    const data = window.luckysheet.getCellValue(index, 0,option)
   
    if (data === key) {
      row = index;
      break;
    }
  }
  return row;
}
/**
 * 获取表头值所在列
 * @headerValue 表头值
 * @type word 转字母 ，number 返回数字
 * @returns 所在列
 */
function getColumnByHeaderValue(headerValue,type = 'word',option) {
  
  // 遍历总列数
  let column = 0;
  for (let index = 0; index < getAllColumnLength(option); index++) {
    if (window.luckysheet.getCellValue(headerIndex, index,option) === headerValue) {
      column = index;
      break;
    }
  }
  return type === 'word' ? numberToExcelColumn(column + 1) : column ;
}
/**
 * 将序号转换成列序号
 * @n 序号
 */
function numberToExcelColumn(n) {

  let result = '';
  while (n > 0) {
      // 获取余数
      const remainder = (n - 1) % 26;
      // 将余数转换成对应的字母
      result = String.fromCharCode(65 + remainder) + result;
      // 更新 n，去掉已经处理的部分
      n = Math.floor((n - 1) / 26);
  }
  return result;
}
/**
 * 获取当前表格总行数
 * @returns 总行数
 */
function getAllRowLength(option) {
  
  if (!window.luckysheet.getSheet(option).visibledatarow) return 0;
  const length = window.luckysheet.getSheet(option).visibledatarow.length;
  return length;
}
/**
 * 获取当前表格总列数
 * @returns 总列数
 */
function getAllColumnLength(option) {
  if (!window.luckysheet.getSheet(option).visibledatacolumn) return 0;
  const length = window.luckysheet.getSheet(option).visibledatacolumn.length;
  return length;
}
/**
 * 获取当前列的表头值
 * @column 列
 * @returns 表头值
 */
function getCurrentHeaderValue(column) {
  return window.luckysheet.getCellValue(getRowByHeaderValue(), column);
}
/**
 * 知道表头和所在行设置值
 * @headerValue 表头值
 * @row 行
 * @value {object | string} 设置的值可以为cellData 对象
 */
function setValueByHeaderAndRow(headerValue, row, value,option) {
 
  window.luckysheet.setCellValue(row, getColumnByHeaderValue(headerValue,'number',option), value,option);
}
/**
 * 知道表头和所在行获取值
 * @headerValue 表头值
 * @row 行
 */
function getValueByHeaderAndRow(headerValue, row,option) {
 return window.luckysheet.getCellValue(row, getColumnByHeaderValue(headerValue,'number',option),option) || '';
}
/**
 * 隐藏列
 * @columns 列 Array
 */
function setHidecolumn(columns) {
    columns.forEach(item => {
        const value = getColumnByHeaderValue(item,'number');
        window.luckysheet.hideColumn(value,value)
    });
}
/**
 * 显示列
 * @columns 列 Array
 */
function setShowcolumn(columns) {
  columns.forEach(item => {
      const value = getColumnByHeaderValue(item,'number');
      window.luckysheet.showColumn(value,value)
  });
}
export {
  getRowByHeaderValue,
  getRowByValue,
  getColumnByHeaderValue,
  getAllRowLength,
  getAllColumnLength,
  getCurrentHeaderValue,
  setValueByHeaderAndRow,
  getValueByHeaderAndRow,
  setHidecolumn,
  setShowcolumn,
  refreshRowByHeaderValue,
  numberToExcelColumn
};
