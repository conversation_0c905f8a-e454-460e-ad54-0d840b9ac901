@use "element-plus/theme-chalk/src/common/var.scss" as *;
.el-card.is-always-shadow {
  // box-shadow: none;
  border: none !important;
}

.el-menu {
  border-right: none;
}

.el-message__icon,
.el-message__content {
  display: inline-block;
}

.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
  height: auto;
  overflow: hidden;
}

.el-dialog__wrapper {
  z-index: 2048;
}


.el-col {
  margin-bottom: 8px;
}

.el-main {
  padding: 0 !important;
}

.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover {
  background-color: transparent;
}


.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal > .el-menu-item:not(.is-disabled):focus, .el-menu--horizontal > .el-menu-item:not(.is-disabled):hover, .el-menu--horizontal > .el-sub-menu .el-sub-menu__title:hover {
  background-color: transparent !important;
}

.el-collapse-item__header {
  height: auto;
  overflow: hidden;
}

.el-button.is-text:not(.is-disabled):active {
  background-color: transparent;
}

.el-button.is-text:not(.is-disabled):focus, .el-button.is-text:not(.is-disabled):hover {
  background-color: transparent;
}

.avue-icon i, .avue-icon svg {
  line-height: 20px;
}

.avue--detail .el-form-item {
  background-color: #fafafa;
}
.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content{
  position: relative;
  color:$color-primary;
  border:1px solid $color-primary
}

.el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content::after{
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 5px;
  background-color:$color-primary;
  position: absolute;
  right: 10px;
}

.el-tree .el-tree-node.is-current >.el-tree-node__content{
  position: relative;
  color:$color-primary;
  border:1px solid $color-primary
}
.el-tree .el-tree-node.is-current>.el-tree-node__content::after{
  content: '';
  display: inline-block;
  width: 5px;
  height: 5px;
  border-radius: 5px;
  background-color:$color-primary;
  position: absolute;
  right: 10px;
}
.el-form-item__label {
  align-items: center;
}

// 单元格样式
.el-table__cell {
  position: static !important; // 解决el-image 和 el-table冲突层级冲突问题
}

.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-error-light-8)!important;
}

.el-table .info-row {
  --el-table-tr-bg-color: var(--el-color-info-light-8)!important;
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}

.el-empty{
  padding: 0;
}

.el-tabs__item.is-top.is-active{
  font-weight: bolder;
}
