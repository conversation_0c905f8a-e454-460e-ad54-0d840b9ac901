<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      :before-open="beforeOpen"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      :row-style="rowStyle"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <el-text size="large" style="font-weight: bolder; margin-left: 10px">报销总额：</el-text>
        <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
      </template>
      <template #menu="{ row }">
        <!-- <el-button
          type="primary"
          v-if="row.reimbursementStatus == 0"
          @click="$refs.crud.rowEdit(row)"
          icon="edit"
          text
          >编辑</el-button
        > -->
        <!-- <el-button
          type="primary"
          @click="complete(row)"
          v-if="row.reimbursementStatus == 2"
          icon="check"
          text
          >报销</el-button
        > -->
        <el-button type="primary" @click="downloadAll(row)" icon="download" text
          >打包下载</el-button
        >
      </template>
      <template #reimbursementFiles="{ row }">
        <File :fileList="row.fileList"></File>
      </template>
      <template #menu-form="{ row, index, type }">
        <el-button
          type="primary"
          icon="el-icon-check"
          v-if="type === 'edit'"
          @click="handleUpdate(0)"
          >修改</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          plain
          v-if="type === 'edit'"
          @click="handleUpdate(1)"
          >修改并提交</el-button
        >
        <el-button type="primary" icon="el-icon-check" v-if="type === 'add'" @click="handleAdd(0)"
          >保存</el-button
        >
        <el-button
          type="primary"
          icon="el-icon-check"
          plain
          v-if="type === 'add'"
          @click="handleAdd(1)"
          >保存并提交</el-button
        >
      </template>
      <template #reimbursementFiles1-form="{ row }">
        <File :fileList="row.fileList"></File>
      </template>
      <template #completeFiles-form="{ row }">
        <File :fileList="form.completeFileList"></File>
      </template>
      <template #reimbursementStatus="{ row }">
        <div v-if="row.reimbursementStatus == 1">
          <el-tag effect="plain" type="info" v-if="row.auditType == 0">待主管审核</el-tag>
          <el-tag effect="plain" type="info" v-if="row.auditType == 1">待总经理审核</el-tag>
        </div>
        <div v-else>
          <el-tooltip :content="row.auditRemark" :disabled="!(row.reimbursementStatus == 3)">
            <el-tag
              effect="plain"
              :type="
                row.reimbursementStatus == 0
                  ? 'info'
                  : row.reimbursementStatus == 2 || row.reimbursementStatus == 4
                  ? 'success'
                  : 'danger'
              "
              >{{ row.$reimbursementStatus }}</el-tag
            >
          </el-tooltip>
        </div>
      </template>
      <template #reimbursementCode="{ row }">
        <el-link type="primary" @click="crud.rowView(row)">{{ row.reimbursementCode }}</el-link>
      </template>
      <template #sealContractName-form="{ row }">
        <el-popover ref="popover1" placement="bottom" :disabled="row.reimbursementTypeName != '商务费'" title="明细" width="1300"  trigger="click">
          <template #reference>
            <span type="primary" style="cursor: pointer" >{{
              row.sealContractName
            }}</span>
          </template>
          <div style="height: 600px;overflow-y: auto;">
             <el-divider>利润明细</el-divider>
          <analysis :seal-contract-name="row.sealContractName" :seal-contract-id="row.sealContractId"></analysis>
          <el-divider>收款明细</el-divider>
          <collection :seal-contract-name="row.sealContractName" :seal-contract-id="row.sealContractId"></collection>
          </div>
        </el-popover>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <costSelect ref="costSelectRef" @handleConfirm="handleCostConfirm"></costSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date';
import costSelect from './components/costSelect.vue';
import { download } from '@/utils/download';
import { downloadByUrl } from '@/utils/download';
import analysis from './components/analysis.vue';
import collection from './components/collection.vue';
let costSelectRef = ref();
const { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  addBtnText: '发起',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  dialogType: 'drawer',
  dialogClickModal: true,
  dialogWidth: '70%',
  updateBtn: false,
  saveBtn: false,
  border: true,
  column: {
    reimbursementCode: {
      label: '报销单号',
      search: true,
      display: false,
    },

    reimbursementDetailDTOList: {
      label: '关联费用',
      type: 'dynamic',
      span: 24,
      hide: true,
      children: {
        align: 'center',
        headerAlign: 'center',
        showSummary: true,
        sumColumnList: [{ label: '总计:', name: 'expensePrice', type: 'sum', decimals: 2 }],
        rowAdd: done => {
          costSelectRef.value.open();
        },
        rowDel: (row, done) => {
          form.value.reimbursementPrice -= row.expensePrice * 1;
          done();
        },
        column: [
          {
            label: '关联客户',
            prop: 'customerName',
            overHidden: true,
            cell: false,
          },
          {
            label: '关联合同',
            prop: 'sealContractName',
            overHidden: true,
            // cell: false,
          },
          {
            type: 'tree',
            label: '费用类型',
            dicUrl: '/blade-system/dict/dictionary-tree?code=expenseType',
            cascader: [],
            span: 12,
            width: 110,
            // search: true,
            display: true,
            cell: false,
            props: {
              label: 'dictValue',
              value: 'id',
              desc: 'desc',
            },
            prop: 'expenseType',
            parent: false,
          },
          // {
          //   type: 'number',
          //   label: '票据张数',
          //   span: 12,
          //   display: true,
          //   hide: true,
          //   prop: 'a170080951774565537',
          // },
          {
            type: 'number',
            label: '费用金额',
            span: 12,

            width: 110,
            display: true,
            prop: 'expensePrice',
            cell: false,
          },

          {
            type: 'textarea',
            label: '用途',
            span: 24,
            display: true,
            prop: 'purpose',
            showWordLimit: true,
            cell: false,
          },

          {
            type: 'date',
            label: '日期',
            span: 12,
            display: true,
            cell: false,
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            prop: 'expenseDate',
            disabled: false,
            readonly: false,
            width: 110,
            required: true,
            formatter: (row, column, cellValue) => {
              return row.expenseDate;
            },
            rules: [
              {
                required: true,
                message: '计划收款时间必须填写',
              },
            ],
          },
          {
            label: '附件',
            prop: 'reimbursementFiles1',
            type: 'upload',
            overHidden: true,
            dataType: 'object',
            loadText: '附件上传中，请稍等',
            span: 24,
            slot: true,
            overHidden: true,
            // align: 'center',
            propsHttp: {
              res: 'data',
              url: 'id',
              name: 'originalName',
              // home: 'https://www.w3school.com.cn',
            },
            action: '/blade-resource/attach/upload',
            uploadPreview: file => {
              console.log(file, form.value);
            },
          },
        ],
      },
    },
    // reimbursementFiles: {
    //   label: '附件',

    //   type: 'upload',
    //   overHidden: true,
    //   dataType: 'object',
    //   loadText: '附件上传中，请稍等',
    //   span: 24,
    //   slot: true,
    //   overHidden: true,
    //   // align: 'center',
    //   propsHttp: {
    //     res: 'data',
    //     url: 'id',
    //     name: 'originalName',
    //     // home: 'https://www.w3school.com.cn',
    //   },
    //   action: '/blade-resource/attach/upload',
    //   uploadPreview: file => {
    //     console.log(file, form.value);
    //   },
    // },
    reimbursementStatus: {
      label: '报销状态',
      display: false,
      width: 120,
      search: true,
      type: 'select',
      dicData: [
        {
          value: 2,
          label: '审核成功',
        },
        {
          value: 3,
          label: '审核失败',
        },
        {
          value: 4,
          label: '完成报销',
        },
      ],
    },
    paymentTime: {
      label: '支付时间',
      type: 'date',

      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      width: 120,
      disabled: false,
      readonly: false,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
    },
    reimbursementPrice: {
      label: '报销金额',
      span: 12,
      width: 120,
    },
    remark: {
      label: '备注',
      span: 24,
      type: 'textarea',
    },
    createUserName: {
      label: '申请人',
      readonly: true,
      width: 120,
      component: 'wf-user-drop',
      search: true,
      params: {
        roleKeys: '',
      },
      value: proxy.$store.getters.userInfo.real_name,
    },
    createTime: {
      label: '申请时间',
      type: 'date',
      label: '日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      width: 120,
      disabled: false,
      readonly: false,
      required: true,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
    },
    dateRange: {
      type: 'date',
      label: '日期',
      span: 12,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',

      component: 'wf-daterange-search',

      width: 110,
      required: true,
      hide: true,
      display: false,
      search: true,
    },
  },
  group: [
    {
      label: '付款信息',
      prop: 'payInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
        {
          label: '付款时间',
          prop: 'paymentTime',
          span: 12,
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
        },

        {
          type: 'select',
          label: '付款账号',

          cascader: [],
          span: 12,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择付款账号',
              trigger: 'blur',
            },
          ],
          prop: 'paymentAccount',
        },
        {
          label: '支付凭证',
          prop: 'completeFiles',
          span: 24,
        },
        {
          label: '付款备注',
          prop: 'paymentRemark',
          span: 24,
          type: 'textarea',
        },
      ],
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/reimbursement/save';
const delUrl = '/api/vt-admin/reimbursement/remove?ids=';
const updateUrl = '/api/vt-admin/reimbursement/update';
const tableUrl = '/api/vt-admin/reimbursement/page';

let tableData = ref([]);
onActivated(() => {
  onLoad();
});
let route = useRoute();
let params = ref({
  reimbursementStatus: route.query.type == 23 ? 2 : '',
});
watch(
  () => route.query.ids,
  val => {
    params.value = {
      reimbursementStatus: route.query.type == 23 ? 2 : '',
    };
    onLoad();
  }
);
let loading = ref(false);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
        startTime: params.value.dateRange && params.value.dateRange[0],
        endTime: params.value.dateRange && params.value.dateRange[1],
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/reimbursement/pageStatistics', {
      params: {
        size,
        current,
        ...params.value,
        selectType: 2,
        startTime: params.value.dateRange && params.value.dateRange[0],
        endTime: params.value.dateRange && params.value.dateRange[1],
      },
    })
    .then(res => {
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    reimbursementFiles:
      form.reimbursementFiles && form.reimbursementFiles.map(item => item.value).join(','),
    createTime: null,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    reimbursementFiles:
      row.reimbursementFiles && row.reimbursementFiles.map(item => item.value).join(','),
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleCostConfirm(list) {
  const arr = list.map(item => {
    return {
      ...item,
      id: null,
      expenseId: item.id,
    };
  });

  form.value.reimbursementDetailDTOList.push(...arr);
  totalAmount();
}
function totalAmount() {
  form.value.reimbursementPrice = form.value.reimbursementDetailDTOList
    .reduce((pre, cur) => {
      pre = pre + cur.expensePrice * 1;
      return pre;
    }, 0)
    .tofixed(2);
}
function beforeOpen(done, type) {
  if (['view', 'edit'].includes(type)) {
    axios
      .get('/api/vt-admin/reimbursement/detail', {
        params: {
          id: form.value.id,
        },
      })
      .then(res => {
        form.value = res.data.data;
        form.value.reimbursementFiles =
          form.value.fileList &&
          form.value.fileList.map(item => {
            return {
              label: item.originalName,
              value: item.id,
            };
          });
        form.value.reimbursementDetailDTOList =
          form.value.detailVOList ||
          form.value.detailVOList.map(item => {
            return {
              ...item,
              id: null,
              expenseId: item.id,
            };
          });
        done();
      });
  } else {
    done();
  }
}
let crud = ref();
function handleUpdate(value) {
  form.value.reimbursementStatus = value;
  crud.value.rowUpdate();
}
function handleAdd(value) {
  form.value.reimbursementStatus = value;
  crud.value.rowSave();
}
function complete(row) {
  proxy
    .$confirm('确认报销完成吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios
        .post('/api/vt-admin/reimbursement/complete', {
          id: row.id,
        })
        .then(res => {
          proxy.$message.success(res.data.msg);
          onLoad();
        });
    });
}
function rowStyle({ row }) {
  return {
    backgroundColor: row.reimbursementStatus === 4 ? 'var(--el-color-success-light-9)' : '',
  };
}
function downloadAll(row) {
  downloadByUrl('/api/vt-admin/reimbursement/downExpenseFiles', {
    id: row.id,
  });
}
function viewDetail(row) {}
</script>

<style lang="scss" scoped></style>
