<template>
  <!-- 序号 -->
  <td class="index_product">
    <div style="display: flex; align-items: center">
      <div style="display: flex; align-items: center">
        <span class="index_product_span">{{ displayIndex }}</span>
        <el-button
          class="delete_category"
          type="primary"
          v-if="$route.query.isFromOther == 1"
          size="small"
          @click="$emit('export-product', [product])"
          style="margin-left: 10px"
          circle
          title="导入至分类"
          icon="Position"
        ></el-button>
        <span class="index_product_span source">{{
          ['库', '模', '询', '空', '历'][product.source]
        }}</span>
      </div>

      <div style="display: flex; align-items: center; justify-content: space-around;gap:3px;margin-left: 3px;">
        <el-icon
          @click="$emit('delete-product', product)"
          class="delete_product"
          type="danger"
          size="18"
          v-if="!isReadonly"
          circle
          icon="delete"
        >
          <delete></delete
        ></el-icon>
        <el-icon
          v-if="!isReadonly"
          class="sort delete_product"
          title="拖拽排序"
           size="18"
          style="cursor: move"
        >
          <sort></sort>
        </el-icon>
        <el-icon
          v-if="!isReadonly"
           size="18"
          class="edit-icon delete_product"
          style="color: var(--el-color-primary); cursor: pointer"
          @click="handleAdd(product)"
          title="加至询价单"
         
        >
          <document></document>
        </el-icon>
      </div>
    </div>
  </td>

  <!-- 产品名称 -->
  <td
    :class="{
      active: isActive,
      error: !product.customProductName,
    }"
    @click="$emit('row-click')"
  >
    <el-tooltip
      :content="'产品名称不能为空'"
      effect="light"
      placement=""
      :disabled="product.customProductName"
    >
      <div
        style="white-space: wrap; overflow: hidden; height: 100%"
        @focus="$emit('field-focus', 'customProductName')"
        @blur="handleFieldBlur('customProductName', $event)"
        :contenteditable="!isReadonly"
        :title="product.customProductName"
      >
        {{ product.customProductName }}
      </div>
    </el-tooltip>
  </td>

  <!-- 品牌 -->
  <td>
    <el-popover
      placement="bottom-start"
      :offset="10"
      :show-after="150"
      :disabled="!(!product.productId && product.source == 1 && product.categoryId)"
      style="padding: 0"
      trigger="focus"
    >
      <template #default>
        <ul
          class="el-scrollbar__view el-select-dropdown__list"
          style="max-height: 200px; overflow-y: auto"
        >
          <li
            @click.capture="$emit('brand-click', product, item)"
            v-for="item in brandList"
            :key="item"
            class="el-select-dropdown__item"
          >
            {{ item }}
          </li>
        </ul>
      </template>
      <template #reference>
        <div
          style="white-space: wrap; overflow: hidden; height: 100%"
          @blur="handleFieldBlur('productBrand', $event)"
          @focus="$emit('get-brand', product)"
          :contenteditable="!isReadonly"
          :title="product.productBrand"
        >
          {{ product.productBrand }}
        </div>
      </template>
    </el-popover>
  </td>

  <!-- 规格型号 -->
  <td :class="{ active: isActive }" @click="$emit('row-click')">
    <el-popover
      placement="bottom-start"
      :offset="10"
      width="auto"
      :disabled="!(!product.productId && product.source == 1 && product.categoryId)"
      :show-after="150"
      style="padding: 0"
      trigger="focus"
    >
      <template #default>
        <ul
          class="el-scrollbar__view el-select-dropdown__list"
          style="max-height: 200px; overflow-y: auto"
        >
          <li
            @click="$emit('specification-click', product, item)"
            v-for="item in productSpecificationList"
            :key="item.id"
            class="el-select-dropdown__item"
            style="height: 50px"
          >
            <div style="height: 25px; line-height: 25px">
              <span>名称：</span>{{ item.productName }}
            </div>
            <div style="height: 25px; line-height: 25px">
              <span>型号：</span>{{ item.productSpecification }}
            </div>
          </li>
        </ul>
      </template>
      <template #reference>
        <div
          style="white-space: nowrap; overflow: hidden"
          @focus="$emit('get-specification', product)"
          @blur="handleFieldBlur('customProductSpecification', $event)"
          :contenteditable="!isReadonly"
          :title="product.customProductSpecification"
        >
          {{ product.customProductSpecification }}
        </div>
      </template>
    </el-popover>
  </td>

  <!-- 产品描述 -->
  <td :class="{ active: isActive }" @click="$emit('row-click')">
    <el-input
      @blur="handleTextareaBlur('customProductDescription', $event)"
      v-if="isActive"
      v-model="product.customProductDescription"
      type="textarea"
      placeholder=""
    ></el-input>
    <textarea
      style="
        white-space: nowrap;
        overflow: hidden;
        outline: none;
        border: none;
        resize: none;
        height: 22px;
        min-height: 22px;
        max-height: 22px;
        line-height: 22px;
        font-size: 14px;
      "
      @focus="$emit('field-focus', 'customProductDescription')"
      @blur="handleTextareaBlur('customProductDescription', $event)"
      :disabled="isReadonly"
      v-model="product.customProductDescription"
      v-else
    >
    </textarea>
  </td>

  <!-- 单位 -->
  <td class="center">
    <div @blur="handleFieldBlur('customUnit', $event)" :contenteditable="!isReadonly">
      {{ product.customUnit || product.unitName }}
    </div>
  </td>

  <!-- 数量 -->
  <td
    :class="{
      active: isActive,
      error: product.number <= 0 || !isNumber(product.number),
    }"
    style="position: relative"
    @click="$emit('row-click')"
    class="center"
  >
    <el-tooltip
      :content="!isNumber(product.number) ? '格式有误' : '数量不能小于等于0'"
      effect="light"
      placement=""
      :disabled="!(product.number <= 0 || !isNumber(product.number))"
    >
      <div @blur="handleFieldBlur('number', $event)" :contenteditable="!isReadonly">
        {{ product.number }}
      </div>
    </el-tooltip>
  </td>

  <!-- 动态列 -->
  <template v-for="column in visibleColumns" :key="column.value">
    <!-- 设备单价 -->
    <td
      v-if="column.value === '设备单价'"
      :class="{
        active: isActive,
        error: hasUnitPriceError,
      }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <el-tooltip
        :content="getUnitPriceErrorMessage"
        placement=""
        effect="light"
        :disabled="!hasUnitPriceError"
      >
        <div @blur="handleFieldBlur('sealPrice', $event)" :contenteditable="!isReadonly">
          {{ product.sealPrice }}
        </div>
      </el-tooltip>
    </td>

    <!-- 设备金额 -->
    <td v-else-if="column.value === '设备金额'" class="right">
      {{ (Number(product.sealPrice || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 人工单价 -->
    <td
      v-else-if="column.value === '人工单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('laborCost', $event)" :contenteditable="!isReadonly">
        {{ product.laborCost }}
      </div>
    </td>

    <!-- 人工金额 -->
    <td v-else-if="column.value === '人工金额'" class="right">
      {{ (Number(product.laborCost || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 其他单价 -->
    <td
      v-else-if="column.value === '其他单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('qthsdj', $event)" :contenteditable="!isReadonly">
        {{ product.qthsdj }}
      </div>
    </td>

    <!-- 其他金额 -->
    <td v-else-if="column.value === '其他金额'" class="right">
      {{ (Number(product.qthsdj || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 延保单价 -->
    <td
      v-else-if="column.value === '延保单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('ybhsdj', $event)" :contenteditable="!isReadonly">
        {{ product.ybhsdj }}
      </div>
    </td>

    <!-- 延保金额 -->
    <td v-else-if="column.value === '延保金额'" class="right">
      {{ (Number(product.ybhsdj || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 综合单价 -->
    <td v-else-if="column.value === '综合单价'" class="right">
      {{
        (
          Number(product.sealPrice || 0) +
          Number(product.laborCost || 0) +
          Number(product.ybhsdj || 0) +
          Number(product.qthsdj || 0)
        ).toFixed(2)
      }}
    </td>

    <!-- 综合金额 -->
    <td v-else-if="column.value === '综合金额'" class="right">
      {{
        (
          (Number(product.sealPrice || 0) +
            Number(product.laborCost || 0) +
            Number(product.ybhsdj || 0) +
            Number(product.qthsdj || 0)) *
          Number(product.number || 0)
        ).toFixed(2)
      }}
    </td>

    <!-- 备注 -->
    <td
      v-else-if="column.value === '备注'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="left"
    >
      <el-input
        @blur="$emit('blur', $event)"
        v-if="isActive"
        v-model="product.remark"
        type="textarea"
        placeholder=""
      ></el-input>
      <textarea
        style="
          white-space: nowrap;
          overflow: hidden;
          outline: none;
          border: none;
          resize: none;
          height: 22px;
          min-height: 22px;
          max-height: 22px;
          line-height: 22px;
          font-size: 14px;
        "
        @focus="$emit('field-focus', 'remark')"
        @blur="handleFieldBlur('remark', $event)"
        v-model="product.remark"
        :disabled="isReadonly"
        v-else
      >
      </textarea>
    </td>

    <!-- 设备成本单价 -->
    <td
      v-else-if="column.value === '设备成本单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('costPrice', $event)" :contenteditable="!isReadonly">
        {{ product.costPrice || '' }}
      </div>
    </td>

    <!-- 人工成本单价 -->
    <td
      v-else-if="column.value === '人工成本单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('rgcbdj', $event)" :contenteditable="!isReadonly">
        {{ product.rgcbdj || '' }}
      </div>
    </td>

    <!-- 其他成本单价 -->
    <td
      v-else-if="column.value === '其他成本单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('qtcbdj', $event)" :contenteditable="!isReadonly">
        {{ product.qtcbdj || '' }}
      </div>
    </td>

    <!-- 延保成本单价 -->
    <td
      v-else-if="column.value === '延保成本单价'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('ybcbdj', $event)" :contenteditable="!isReadonly">
        {{ product.ybcbdj || '' }}
      </div>
    </td>

    <!-- 专项成本 -->
    <td
      v-else-if="column.value === '专项成本'"
      :class="{ active: isActive }"
      @click="$emit('row-click')"
      style="position: relative"
      class="right"
    >
      <div @blur="handleFieldBlur('specialCostPrice', $event)" :contenteditable="!isReadonly">
        {{ product.specialCostPrice || '' }}
      </div>
    </td>

    <!-- 设备成本金额 -->
    <td v-else-if="column.value === '设备成本金额'" class="right">
      {{ (Number(product.costPrice || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 人工成本金额 -->
    <td v-else-if="column.value === '人工成本金额'" class="right">
      {{ (Number(product.rgcbdj || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 其他成本金额 -->
    <td v-else-if="column.value === '其他成本金额'" class="right">
      {{ (Number(product.qtcbdj || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 延保成本金额 -->
    <td v-else-if="column.value === '延保成本金额'" class="right">
      {{ (Number(product.ybcbdj || 0) * Number(product.number || 0)).toFixed(2) }}
    </td>

    <!-- 综合成本单价 -->
    <td v-else-if="column.value === '综合成本单价'" class="right">
      {{
        (
          Number(product.rgcbdj || 0) +
          Number(product.specialCostPrice || product.costPrice || 0) +
          Number(product.ybcbdj || 0) +
          Number(product.qtcbdj || 0)
        ).toFixed(2)
      }}
    </td>

    <!-- 综合成本金额 -->
    <td v-else-if="column.value === '综合成本金额'" class="right">
      {{
        (
          (Number(product.rgcbdj || 0) +
            Number(product.specialCostPrice || product.costPrice || 0) +
            Number(product.ybcbdj || 0) +
            Number(product.qtcbdj || 0)) *
          Number(product.number || 0)
        ).toFixed(2)
      }}
    </td>

    <!-- 其他列的处理 -->
    <td v-else :class="column.align || 'right'">
      {{ getColumnValue(column.value) }}
    </td>
  </template>
</template>

<script setup>
import { computed, inject, onUnmounted } from 'vue';
import { Sort, Edit } from '@element-plus/icons-vue';


const props = defineProps({
  product: {
    type: Object,
    required: true,
  },
  displayIndex: {
    type: Number,
    required: true,
  },
  isActive: {
    type: Boolean,
    default: false,
  },
  isReadonly: {
    type: Boolean,
    default: false,
  },
  brandList: {
    type: Array,
    default: () => [],
  },
  productSpecificationList: {
    type: Array,
    default: () => [],
  },
  visibleColumns: {
    type: Array,
    default: () => [],
  },
});

const emit = defineEmits([
  'row-click',
  'field-focus',
  'blur',
  'delete-product',
  'export-product',
  'brand-click',
  'get-brand',
  'specification-click',
  'get-specification',
  'field-change',
]);

// 数字验证
const isNumber = value => {
  return !isNaN(value) && !isNaN(parseFloat(value));
};

// 缓存计算结果，避免重复计算
let priceErrorCache = null;
let lastPriceValues = null;

// 设备单价错误检查 - 使用缓存优化
const hasUnitPriceError = computed(() => {
  const price = props.product.sealPrice;
  const minPrice = props.product.minSealPrice;
  const costPrice = props.product.costPrice;

  // 检查是否需要重新计算
  const currentValues = `${price}-${minPrice}-${costPrice}`;
  if (lastPriceValues === currentValues && priceErrorCache !== null) {
    return priceErrorCache.hasError;
  }

  const hasError =
    price === '' ||
    (price * 1 <= minPrice * 1 && price * 1 !== 0) ||
    (price * 1 <= costPrice * 1 && price * 1 !== 0) ||
    !isNumber(price);

  // 缓存结果
  lastPriceValues = currentValues;
  priceErrorCache = { hasError, price, minPrice, costPrice };

  return hasError;
});

// 设备单价错误信息 - 使用缓存优化
const getUnitPriceErrorMessage = computed(() => {
  const price = props.product.sealPrice;
  const minPrice = props.product.minSealPrice;
  const costPrice = props.product.costPrice;

  // 使用缓存的计算结果
  if (priceErrorCache && lastPriceValues === `${price}-${minPrice}-${costPrice}`) {
    const { hasError } = priceErrorCache;
    if (!hasError) return '';

    if (price === '') return '单价不能为空';
    if (!isNumber(price)) return '格式有误';
    if (price * 1 < minPrice * 1) return '单价不能小于最低销售单价';
    if (price * 1 < costPrice * 1) return '单价不能小于成本单价';
  }

  return '';
});

// 缓存上一次的字段值，避免重复触发更新
const fieldValueCache = new Map();

// 处理字段失焦 - 优化版本
const handleFieldBlur = (field, event) => {
  const value = event.target.innerText?.trim() || event.target.value.trim();
  if (value !== undefined) {
    // 检查值是否真的发生了变化
    const cacheKey = `${field}-${props.product.uuid}`;
    const lastValue = fieldValueCache.get(cacheKey);

    let newValue;
    // 对于数字字段，确保转换为数字类型
    if (
      [
        'number',
        'sealPrice',
        'laborCost',
        'qthsdj',
        'ybhsdj',
        'costPrice',
        'rgcbdj',
        'qtcbdj',
        'ybcbdj',
        'specialCostPrice',
      ].includes(field)
    ) {
      newValue = Number(value) || '';
    } else {
      newValue = value;
    }

    // 只有当值真正发生变化时才更新和触发事件
    if (lastValue !== newValue) {
      props.product[field] = newValue;
      fieldValueCache.set(cacheKey, newValue);
      // 触发字段变更事件，通知父组件更新汇总数据
      emit('field-change', { field, value: newValue, product: props.product });
    }
  }
};

// 处理textarea字段失焦 - 优化版本
const handleTextareaBlur = (field, event) => {
  const value = event.target.value;
  if (value !== undefined) {
    // 检查值是否真的发生了变化
    const cacheKey = `${field}-${props.product.uuid}`;
    const lastValue = fieldValueCache.get(cacheKey);

    // 只有当值真正发生变化时才更新和触发事件
    if (lastValue !== value) {
      props.product[field] = value;
      fieldValueCache.set(cacheKey, value);
      // 触发字段变更事件，通知父组件更新汇总数据
      emit('field-change', { field, value, product: props.product });
    }
  }
};

// 列值计算缓存
const columnValueCache = new Map();
let lastProductState = null;

// 获取列值 - 优化版本，使用缓存
const getColumnValue = columnName => {
  const product = props.product;
  const number = product.number || 0;

  // 创建产品状态标识，用于检测是否需要重新计算
  const productState = `${product.uuid}-${number}-${product.costPrice}-${product.rgcbdj}-${product.qtcbdj}-${product.ybcbdj}-${product.specialCostPrice}`;

  // 如果产品状态没有变化，直接返回缓存的结果
  if (lastProductState === productState && columnValueCache.has(columnName)) {
    return columnValueCache.get(columnName);
  }

  // 计算列值 - 只处理剩余的其他列
  let value;
  switch (columnName) {
    case '专项供应商':
      value = product.specialSupplierId || '--';
      break;
    default:
      value = '--';
  }

  // 缓存计算结果
  columnValueCache.set(columnName, value);
  lastProductState = productState;

  return value;
};
const addInquiry = inject('addInquiry');
function handleAdd(row) {
  addInquiry(row);
}

// 组件销毁时清理缓存
onUnmounted(() => {
  fieldValueCache.clear();
  columnValueCache.clear();
  priceErrorCache = null;
  lastPriceValues = null;
  lastProductState = null;
});
</script>

<style scoped>
/* 现代化单元格样式 */
td {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: keep-all;
  max-width: 0; /* 配合table-layout: fixed使用 */
  font-size: 14px;
  height: 36px; /* 增加行高 */
  min-height: 36px;
  max-height: 36px;
  line-height: 20px;
  /* padding: 8px 12px;  */ /* 移除内边距 */
  border: 1px solid #e4e7ed;
  border-top: none;
  border-left: none;
  background: #fff;
  transition: all 0.2s ease;
  position: relative;
  cursor: pointer;
}

/* 单元格悬停效果 - 明显的黄色背景 */
td:hover {
  background-color: #fef3c7 !important;
}

/* 第一列左边框 */
td:first-child {
  border-left: 1px solid #e4e7ed;
}

/* 编辑状态下的现代化样式 */
td.active {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 36px;
  min-height: 36px;
  max-height: 36px;
  line-height: 20px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
  border: 2px solid #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
  z-index: 5;
}

/* 可编辑的div元素样式 - 现代化 */
div[contenteditable='true'] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  line-height: 20px;
  font-size: 14px;
  border-radius: 4px;
  padding: 2px 4px;
  transition: all 0.2s ease;
}

div[contenteditable='true']:focus {
  outline: none;
  background: rgba(64, 158, 255, 0.05);
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
}

/* 编辑状态下的div样式 */
td.active div[contenteditable='true'] {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 20px;
  min-height: 20px;
  max-height: 20px;
  line-height: 20px;
  background: rgba(64, 158, 255, 0.05);
}

/* textarea样式 - 现代化 */
textarea {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  resize: none;
  border: none;
  outline: none;
  background: transparent;
  font-size: 14px;
  border-radius: 4px;
  padding: 2px 4px;
  transition: all 0.2s ease;
}

textarea:focus {
  background: rgba(64, 158, 255, 0.05);
  box-shadow: 0 0 0 1px rgba(64, 158, 255, 0.3);
}

/* 现代化状态样式 - 淡蓝色背景，黑色字体 */
.active {
  background-color: #e6f4ff !important;
  color: #000 !important;
}

/* 选中行样式 */
tr.row-selected td,
tr.current-editing td {
  background-color: #e6f4ff !important;
  color: #000 !important;
  border-color: #d4edda !important;
}

/* 选中行悬停样式 */
tr.row-selected:hover td,
tr.current-editing:hover td {
  background-color: #cce7ff !important;
  color: #000 !important;
}

.error {
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%) !important;
  border-left: 3px solid #ef4444 !important;
}

/* 序号列样式优化 */
.index_product_span {
  /* margin-right: 8px; */
  font-weight: 500;
  color: #6b7280;
}

.index_product {
  position: relative;

  &:hover .delete_product {
    display: inline-flex;
    animation: fadeIn 0.2s ease;
  }

  &:hover .index_product_span {
    opacity: 0.5;
    transition: opacity 0.2s ease;
  }

  .delete_product {
    display: none;
    cursor: pointer;
    /* margin-left: 8px; */
    transition: all 0.2s ease;
  }

  .source {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    height: 22px;
    width: 22px;
    font-size: 12px;
    font-weight: 600;
    border-radius: 50%;
    border: 2px solid #10b981;
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    transition: all 0.2s ease;
  }

  .source:hover {
    background: rgba(16, 185, 129, 0.2);
    transform: scale(1.1);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
