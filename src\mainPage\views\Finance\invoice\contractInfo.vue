<template>
  <avue-form :option="option" v-model="props.detailForm">
    <template #productList v-if="props.invoicePriceType == 0">
      <div style="padding: 0px">
        <avue-crud :option="productOption" :data="props.detailForm.invoiceDetailVOList"></avue-crud>
      </div>
    </template>
    <template #contractFile> 
      <File :fileList="props.detailForm.attachList"></File>
    </template>
    <template #contractTotalPrice>
      <el-text style="padding: 0px">
        {{ props.detailForm.contractTotalPrice }}
        <span v-if="props.detailForm.discountsPrice * 1 > 0"
          >：(产品总额<span style="color: var(--el-color-danger);">{{( props.detailForm.contractTotalPrice* 1 + props.detailForm.discountsPrice* 1).toFixed(2)}}</span> -
          折减金额<span style="color: var(--el-color-danger);">{{ props.detailForm.discountsPrice }}</span> )</span
        >
      </el-text>
    </template>
  </avue-form>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps(['detailForm','invoicePriceType', 'hideCommonInfo']);

// 计算属性：根据 hideCommonInfo 动态生成配置
const option = computed(() => {
  const baseColumns = [
    {
      type: 'input',
      label: '关联报价',
      display: true,
      component: 'wf-quotation-select',
      prop: 'offerId',
    },
  ];

  // 公共信息字段（当 hideCommonInfo 为 true 时隐藏）
  const commonInfoColumns = [
    {
      label: '客户名称',
      span: 12,
      display: true,
      prop: 'customerId',
      component: 'wf-customer-select'
    },
    {
      type: 'input',
      label: '客户地址',
      span: 12,
      display: true,
      prop: 'customerAddress',
    },
    {
      label: '关联联系人',
      type: 'input',
      prop: 'customerContact',
      component: 'wf-contact-select',
      placeholder: '请先选择报价',
    },
    {
      type: 'input',
      label: '电话',
      span: 12,
      display: true,
      prop: 'customerPhone',
    },
  ];

  // 合同特有信息字段
  const contractSpecificColumns = [
    // {
    //   type: 'input',
    //   label: '合同编号',
    //   span: 12,
    //   display: true,
    //   readonly: true,
    //   placeholder: '自动生成',
    //   prop: 'contractCode',
    // },
    // {
    //   type: 'input',
    //   label: '项目名称',
    //   span: 12,
    //   display: true,
    //   prop: 'contractName',
    //   required: true,
    //   rules: [
    //     {
    //       required: true,
    //       message: '项目名称必须填写',
    //     },
    //   ],
    // },
    {
      type: 'input',
      label: '对方订单编号',
      span: 12,
      display: true,
      prop: 'customerOrderNumber',
    },
    {
      type: 'input',
      label: '合同金额',
      span: 12,
      display: true,
      prop: 'contractTotalPrice',
      required: true,
      rules: [
        {
          required: true,
          message: '合同金额必须填写',
        },
      ],
    },
    {
      label:'合同附件',
      span:12,
      type:'input',
      prop:'contractFile',
    },
    {
      label: '',
      labelWidth: 0,
      labelPosition:'top',
      prop: 'productList',
      span: 24,
    }
  ];

  // 是否开票字段（根据 hideCommonInfo 决定是否显示）
  const invoiceColumn = {
    label: '是否开票',
    type: 'switch',
    prop: 'isNeedInvoice',
    value: 1,
    dicData: [
      {
        value: 0,
        label: '否',
      },
      {
        value: 1,
        label: '是',
      },
    ],
  };

  // 根据 hideCommonInfo 决定显示哪些字段
  let columns = [...baseColumns];

  if (!props.hideCommonInfo) {
    columns = [...columns, ...commonInfoColumns];
  }

  columns = [...columns, ...contractSpecificColumns];

  if (!props.hideCommonInfo) {
    // 在合同金额后插入是否开票字段
    const contractAmountIndex = columns.findIndex(col => col.prop === 'contractTotalPrice');
    if (contractAmountIndex !== -1) {
      columns.splice(contractAmountIndex + 1, 0, invoiceColumn);
    }
  }

  return {
    detail: true,
    submitBtn: false,
    emptyBtn: false,
    labelWidth: 150,
    column: columns
  };
});
let productOption = ref({
  header: false,
  menu: false,
  addBtn: false,
  border: false,
  showSummary: true,
  sumColumnList: [{ label: '', name: 'totalPrice', type: 'sum', decimals: 2 }],
  column: [
    {
      label: '产品',
      prop: 'productId',

      formatter: row => {
        return row.customProductName || row.productVO?.productName;
      },
      cell: false,
    },

    {
      label: '规格型号',
      prop: 'productSpecification',

      formatter: row => {
        return row.customProductSpecification || row.productVO?.productSpecification;
      },
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '品牌',
      prop: 'productBrand',

      formatter: row => {
        return row.productBrand || row.productVO?.productBrand;
      },
      overHidden: true,
      // search: true,
      span: 24,
      cell: false,
      type: 'input',
    },
    {
      label: '数量',
      prop: 'number',
      type: 'number',
      span: 12,
      cell: false,
      width: 60,
    },
    {
      label: '单位',
      prop: 'unitName',

      type: 'input',
      formatter: row => {
        return row.customUnit || row.productVO?.unitName;
      },
      span: 12,
      cell: false,
      width: 60,
    },
    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
      width: 100,
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      span: 12,
      cell: false,
      width: 120,
    },
  ],
});
</script>

<style lang="scss" scoped></style>
