<template>
  <div style="height: 100%">
    <el-row style="height: 100%">
      <el-col :span="9" style="height: 100%">
        <basic-container style="height: 100%">
          <el-input placeholder="输入关键字进行过滤" v-model="filterText" @input="getTreeData">
          </el-input>
          <avue-tree :option="treeOption" style="max-height: 660px;overflow-y: auto;"  :data="treeData" @node-click="nodeClick">
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span v-if="!data.hasChildren">
                  <el-text type="info" style="margin: 0 20px;" > {{data.minProfitRate && data.minProfitRate + '%'}} </el-text>
                  <el-text type="success" > {{data.normalProfitRate && data.normalProfitRate + '%'}} </el-text>
                </span>
              </span>
            </template>
          </avue-tree>
        </basic-container>
      </el-col>
      <el-col :span="15" style="height: 100%">
        <basic-container style="height: 100%">
          <Title>{{ form.categoryName }}</Title>
          <avue-form
            v-if="!form.hasChildren"
            style="margin-top: 60px; height: 100%"
            :option="option"
            v-model="form"
            @submit="submit"
          ></avue-form>
          <el-empty v-else description="请先选中最后一级"></el-empty>
        </basic-container>
      </el-col>
    </el-row>

    <dialogForm ref="dialogForm"></dialogForm>
    <AddSupplier ref="supplier" :productId="productId"></AddSupplier>
  </div>
</template>

<script setup>
import axios from 'axios';
import AddSupplier from './compoents/addSupplier.vue';
import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
let option = ref({
  labelWidth: 150,
  column: [
    {
      label: '最低利润率(%)',
      prop: 'minProfitRate',
      type: 'number',
    },
    {
      label: '常规利润率(%)',
      prop: 'normalProfitRate',
      type: 'number',
    },
  ],
});
let form = ref({
  hasChildren: true,
});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  //   defaultExpandAll: true,
  menu: false,
  filter: false,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    value: 'id',
    children: 'children',
  },
  lazy: true,
  treeLoad: function (node, resolve) {
    axios
      .get('/api/vt-admin/productCategory/list', {
        params: {
          parentId: node.data.id,
        },
      })
      .then(res => {
        resolve(
          res.data.data.map(item => {
            return {
              ...item,
              leaf: !item.hasChildren,
            };
          })
        );
      });
  },
});
const addUrl = '/api/vt-admin/product/save';
const delUrl = '/api/vt-admin/product/remove?ids=';
const updateUrl = '/api/vt-admin/product/update';
const tableUrl = '/api/vt-admin/product/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  getTreeData();
});
let loading = ref(false);

let router = useRouter();

function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}

let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/productCategory/list', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren,
        };
      });
    });
}
let filterText = ref('');

function nodeClick(val, accountName) {
  axios.get('/api/vt-admin/productCategory/detail?id=' + val.id).then(res => {
    form.value = {
      ...res.data.data,
      minProfitRate: res.data.data.minProfitRate * 1,
      normalProfitRate: res.data.data.normalProfitRate * 1,
      hasChildren: val.hasChildren,
    };
  });
}
function submit(form, done) {
  const data = {
    ...form,
  };
  axios
    .post('/api/vt-admin/productCategory/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        getTreeData();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
let productId = ref('');
function addSupplier(row) {
  productId.value = row.id;
  proxy.$refs.supplier.open();
}
</script>

<style lang="scss" scoped></style>
