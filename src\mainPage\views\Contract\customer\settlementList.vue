<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="reset"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #menu-left>
      <el-button type="primary" @click="openSettlementDrawer({})" v-if="props.isApply">申请结算</el-button>
    </template>
    <template #menu="scope">
      <el-button type="primary" icon="view" link @click="handleView(scope.row)">详情</el-button>
      <el-button
        type="primary"
        icon="check"
        v-if="scope.row.settlementStatus == 2 && props.isApply"
        link
        @click="handleCheck(scope.row)"
        >结算确认</el-button
      >
      <el-button
        type="primary"
        icon="upload"
        v-if="scope.row.settlementStatus == 3 && props.isApply"
        link
        @click="uploadInvioce(scope.row)"
        >上传发票</el-button
      >
      <el-button
        type="primary"
        icon="edit"
        link
        v-if="scope.row.settlementStatus == 0 && props.isApply"
        @click="handleEdit(scope.row)"
        >编辑</el-button
      >
      <el-button
        type="primary"
        icon="back"
        link
        v-if="
          (scope.row.settlementStatus == 1 && props.isApply) ||
          (scope.row.settlementStatus == 2 && props.isAudit)
        "
        @click="handleBack(scope.row)"
        >撤回</el-button
      >

      <el-button
        type="primary"
        icon="Tickets"
        v-if="scope.row.settlementStatus == 1 && props.isAudit"
        link
        @click="openAuditDrawer(scope.row)"
        >结算审核</el-button
      >
      <el-button
        icon="delete"
        type="danger"
        v-if="scope.row.settlementStatus == 0 && props.isApply"
        link
        @click="$refs.crud.rowDel(scope.row)"
        >删除</el-button
      >
      <!-- 付款 -->
       <el-button
        icon="Tickets"
        type="primary"
        v-if="scope.row.settlementStatus == 3 && props.isPay"
        link
        @click="pay(scope.row)"
        >付款</el-button
      >
      <!-- 收款 -->
       <el-button
        icon="Tickets"
        type="primary"
        v-if="scope.row.settlementStatus == 4 && props.isCollect"
        link
        @click="collection(scope.row)"
        >收款</el-button
      >
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>

  <!-- 结算申请抽屉 -->
  <el-drawer
    v-model="drawerVisible"
    :title="Object.keys(currentRow).length > 0 ? '编辑结算' : '结算申请'"
    size="60%"
    :destroy-on-close="true"
  >
    <settlement-apply
      :detailForm="detailForm"
      ref="settlementApplyRef"
      :row-data="currentRow"
      :isApply="props.isApply"
      :isAudit="props.isAudit"
      @getDetailForm="getDetail"
      @submit-success="handleSubmitSuccess"
    />
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button type="primary" @click="settlementApplyRef?.submitForm(1)"> 提交申请 </el-button>
        <el-button @click="settlementApplyRef?.submitForm(0)"> 保存草稿 </el-button>
        <el-button @click="settlementApplyRef?.resetForm()">重置</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 结算审核抽屉 -->
  <el-drawer v-model="auditDrawerVisible" title="结算审核" size="60%" :destroy-on-close="true">
    <settlement-audit
      :detailForm="detailForm"
      ref="settlementAuditRef"
      :isApply="props.isApply"
      :isAudit="props.isAudit"
      :row-data="currentRow"
      @submit-success="handleAuditSuccess"
    />
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <el-button type="primary" @click="settlementAuditRef?.submitForm()">审核通过</el-button>
        <!-- <el-button type="danger" @click="settlementAuditRef?.rejectSettlement()">驳回</el-button> -->
      </div>
    </template>
  </el-drawer>

  <!-- 详情抽屉 -->
  <el-drawer
    v-model="detailDrawerVisible"
    title="结算详情"
    size="60%"
    :isApply="props.isApply"
    :isAudit="props.isAudit"
    :destroy-on-close="true"
    :before-close="handleDetailDrawerClose"
  >
    <settlement-detail :detailForm="detailForm" ref="settlementDetailRef" :row-data="currentRow" />
    <template #footer>
      <div style="display: flex; justify-content: flex-end">
        <!-- <el-button @click="handleCheck" icon="check" type="primary">确认</el-button> -->
        <!-- <el-button @click="handleReject" icon="ZoomOut" type="danger">驳回</el-button> -->
        <el-button @click="handleDetailDrawerClose" icon="close">关闭</el-button>
      </div>
    </template>
  </el-drawer>

  <!-- 上传发票抽屉 -->
  <el-drawer
    v-model="invoiceDrawerVisible"
    title="上传发票"
    size="800"
    :destroy-on-close="true"
    :before-close="handleInvoiceDrawerClose"
    class="invoice-drawer"
  >
    <div class="invoice-upload-container">
      <!-- 发票列表 -->
      <div class="invoice-list" v-if="invoiceList.length > 0">
        <div v-for="(invoice, index) in invoiceList" :key="index" class="invoice-item">
          <div class="invoice-header">
            <div class="invoice-title">
              <i class="el-icon-document"></i>
              发票 {{ index + 1 }}
            </div>
            <div class="remove-btn" @click="removeInvoice(index)">
              <el-button type="danger" size="mini" circle icon="delete"></el-button>
            </div>
          </div>

          <div class="invoice-form">
            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="发票号码" label-width="100px">
                  <el-input
                    v-model="invoice.invoiceNumber"
                    placeholder="请输入发票号码"
                    prefix-icon="el-icon-document-copy"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发票日期" label-width="100px">
                  <el-date-picker
                    v-model="invoice.invoiceDate"
                    type="date"
                    placeholder="选择发票日期"
                    valueFormat="YYYY-MM-DD"
                    style="width: 100%"
                    prefix-icon="el-icon-date"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="16">
              <el-col :span="12">
                <el-form-item label="税率(%)" label-width="100px">
                  <el-select
                    v-model="invoice.taxRate"
                    placeholder="请选择税率"
                    style="width: 100%"
                    @change="calculateTaxAmount(invoice)"
                  >
                    <el-option label="3%" :value="3" />
                    <el-option label="6%" :value="6" />
                    <el-option label="9%" :value="9" />
                    <el-option label="13%" :value="13" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="含税金额" label-width="100px">
                  <el-input-number
                    v-model="invoice.totalAmount"
                    :min="0"
                    :precision="2"
                    placeholder="含税金额"
                    style="width: 100%"
                    @change="calculateTaxAmount(invoice)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="不含税金额" label-width="100px">
                  <el-input-number
                    v-model="invoice.amountWithoutTax"
                    :precision="2"
                    placeholder="自动计算"
                    style="width: 100%"
                    class="calculated-field"
                    disabled
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="税额" label-width="100px">
                  <el-input-number
                    v-model="invoice.taxAmount"
                    :precision="2"
                    placeholder="自动计算"
                    style="width: 100%"
                    class="calculated-field"
                    disabled
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 文件上传区域 -->
            <div class="upload-section">
              <div class="upload-title">
                <i class="el-icon-paperclip"></i>
                发票附件
              </div>
              <el-upload
                action="/api/blade-resource/attach/upload"
                :before-upload="beforeUpload"
                :headers="{
                  [website.tokenHeader]: $store.getters.token,
                  Authorization: `Basic ${Base64.encode(
                    `${website.clientId}:${website.clientSecret}`
                  )}`,
                }"
                :on-success="(response, file) => handleUploadSuccess(response, file, invoice)"
                :file-list="invoice.fileList"
                :on-remove="file => handleRemoveFile(file, invoice)"
                multiple
                drag
                class="upload-dragger"
              >
                <i class="el-icon-upload"></i>
                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                <template #tip>
                  <div class="el-upload__tip">支持 JPG、PNG、PDF 格式，单个文件不超过 10MB</div>
                </template>
              </el-upload>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态提示 -->
      <div class="empty-state" v-if="invoiceList.length === 0">
        <div class="empty-icon">
          <i class="el-icon-document-add"></i>
        </div>
        <div class="empty-text">暂无发票信息</div>
        <div class="empty-desc">点击下方按钮开始添加发票</div>
      </div>

      <!-- 新增发票按钮 -->
      <div class="add-invoice-section">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="large"
          @click="addInvoice"
          class="add-invoice-btn-new"
        >
          新增发票
        </el-button>
      </div>

      <!-- 汇总信息 -->
      <div class="invoice-summary" v-if="invoiceList.length > 0">
        <div class="summary-title">
          <i class="el-icon-data-analysis"></i>
          发票汇总信息
        </div>
        <div class="summary-content">
          <div class="summary-item">
            <div class="summary-label">发票总数</div>
            <div class="summary-value">{{ invoiceList.length }} 张</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">含税金额合计</div>
            <div class="summary-value">¥{{ totalInvoiceAmount.toFixed(2) }}</div>
          </div>
          <div class="summary-item">
            <div class="summary-label">税额合计</div>
            <div class="summary-value">¥{{ totalTaxAmount.toFixed(2) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="drawer-footer">
        <el-button size="large" @click="handleInvoiceDrawerClose">
          <i class="el-icon-close"></i>
          取消
        </el-button>
        <el-button
          type="primary"
          size="large"
          @click="saveInvoices"
          :disabled="invoiceList.length === 0"
        >
          <i class="el-icon-check"></i>
          保存发票 ({{ invoiceList.length }})
        </el-button>
      </div>
    </template>
  </el-drawer>
     <dialogForm ref="dialogForm"></dialogForm>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import SettlementApply from './detail/settlementApply.vue';
import SettlementAudit from './detail/settlementAudit.vue';
import SettlementDetail from './detail/settlementDetail.vue';
import moment from 'moment';
import website from '@/config/website';
import { Base64 } from 'js-base64';
import { getToken } from 'utils/auth';
import { ElMessage, ElMessageBox } from 'element-plus';
import { dateFormat } from '@/utils/date';
const props = defineProps({
  isApply: Boolean,
  isAudit: Boolean,
  isPay: Boolean,
  isCollect: Boolean,
    getUrl:String,
  form: Object,
});
// 抽屉相关
const drawerVisible = ref(false);
const auditDrawerVisible = ref(false);
const detailDrawerVisible = ref(false);
const invoiceDrawerVisible = ref(false);
const currentRow = ref({});
const settlementApplyRef = ref(null);
const settlementAuditRef = ref(null);
const settlementDetailRef = ref(null);

// 发票相关
const invoiceList = ref([]);
const uploadUrl = '/api/vt-admin/file/upload'; // 文件上传接口
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    // 合同名称
    {
      label: '合同名称',
      prop: 'contractName',
    //   width: 200,
      hide: !!props.form?.id,
      overHidden: true,
      search: !props.form?.id,
    },
    {
      label: '合同总额',
      prop: 'contractTotalPrice',
      hide: !!props.form?.id,
      width:120,
    },
    //  关联计划

    {
      label: '结算金额',
      prop: 'applySettlementPrice',
      width: 100,
    },
    // 管理费
    {
      label: '管理费',
      prop: 'managementFee',
      width: 150,
      formatter: (row, column, cellValue) => {
        if (!cellValue || !row.contractTotalPrice || !row.managementFeePoints) {
          return cellValue || '0.00';
        }
        console.log(cellValue);
        
        // 计算百分比：管理费 / 合同总额 * 税点（managementFeePoints）* 100%
        const percentage = (parseFloat(cellValue) / (parseFloat(row.contractTotalPrice) * parseFloat(row.managementFeePoints)/100) * 100).toFixed(2);
        return `${cellValue} (${percentage}%)`;
      },
    },
    {
      label: `${props.isApply ? '开' : '收'}票应包含税额`,
      prop: 'invoicePrice',
    },
    // {
    //   label: `${props.isApply ? '开' : '收'}票已包含税额`,
    //   prop: 'invoicedAmount',
    //   width: 160,
    // },
    {
      label: `应${props.isApply ? '收' : '付'}款金额`,
      prop: 'shouldSettmentPrice',
    },
    // {
    //   label: `已${props.isApply ? '收' : '付'}款金额`,
    //   prop: 'receivedAmount',
    // },
    {
      label: '结算状态',
      prop: 'settlementStatus',
      dicData: [
        { label: '草稿', value: 0 },
        { label: '申请结算', value: 1 },
        { label: '结算待确认', value: 2 },
        { label: `待付款`, value: 3 },
        { label: `待收款`, value: 4 },
        { label: `已收款`, value: 5 },
      ],
    },
    // {
    //   label: '结算时间',
    //   prop: 'settlementTime',
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/sealContractSettlement/remove?ids=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/sealContractSettlement/page';
let params = ref({});
let tableData = ref([{}]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(props.getUrl, {
      params: {
        size,
        current,
        sealContractId: props.form?.id || null,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    })
    .catch(err => {
      loading.value = false;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}

// 打开结算申请抽屉
function openSettlementDrawer(row) {
  currentRow.value = row;
  drawerVisible.value = true;
}

// 处理编辑操作
function handleEdit(row) {
  // 使用结算申请抽屉进行编辑
  // 获取结算详情
  axios
    .get(`/vt-admin/sealContractSettlement/detail`, {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      getDetail(res.data.data.sealContractId).then(() => {
        currentRow.value = {
          ...res.data.data,
          planIds: res.data.data.planVOList.map(item => item.planId),
        };
      });
    });

  drawerVisible.value = true;
}

// 处理结算申请提交成功
function handleSubmitSuccess() {
  drawerVisible.value = false;
  onLoad(); // 重新加载表格数据
  proxy.$message.success('结算申请提交成功');
}

// 打开结算审核抽屉
function openAuditDrawer(row) {
  axios
    .get(`/vt-admin/sealContractSettlement/detail`, {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      getDetail(res.data.data.sealContractId).then(() => {
  currentRow.value = {
        ...res.data.data,
        planIds: res.data.data.planVOList.map(item => item.planId),
      };
      auditDrawerVisible.value = true;
      });
    
    });
}
// 打开详情抽屉
function handleView(row) {
  axios
    .get(`/vt-admin/sealContractSettlement/detail`, {
      params: {
        id: row.id,
      },
    })
    .then(res => {
        getDetail(res.data.data.sealContractId).then(() => {
             currentRow.value = {
        ...res.data.data,
        planIds: res.data.data.planVOList.map(item => item.planId),
      };
        })
      detailDrawerVisible.value = true;
    });
}

// 关闭详情抽屉
function handleDetailDrawerClose() {
  detailDrawerVisible.value = false;
  currentRow.value = {};
}

// 处理结算审核成功
function handleAuditSuccess(result) {
  auditDrawerVisible.value = false;
  onLoad(); // 重新加载表格数据

  if (result && result.status === 'approved') {
    proxy.$message.success('结算审核通过');
  } else if (result && result.status === 'rejected') {
    proxy.$message.warning(`结算审核已驳回: ${result.reason}`);
  }
}

// 获取合同详情
let detailForm = ref({});
onMounted(() => {
  getDetail();
});
function getDetail(id) {
  return new Promise((resolve, reject) => {
    if (!id && !props.form?.id) return;

    axios
      .get(`/vt-admin/sealContractSettlement/detailForAdd`, {
        params: {
          id: id || props.form?.id,
        },
      })
      .then(res => {
        detailForm.value = res.data.data;
        resolve(res.data.data);
      });
  });
}
// 撤回
function handleBack(params) {
  let text = params.settlementStatus == 1 ? '草稿' : params.settlementStatus == 2 ? '申请结算' : '';
  ElMessageBox.confirm(`确定撤回到${text}状态吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(() => {
    if (params.settlementStatus == 1) {
      axios
        .post('/api/vt-admin/sealContractSettlement/returnDraftStatus', {
          id: params.id,
        })
        .then(res => {
          if (res.data.code == 200) {
            proxy.$message.success(res.data.msg);
            onLoad();
          }
        });
    } else {
      axios
        .post('/api/vt-admin/sealContractSettlement/returnApplyStatus', {
          id: params.id,
        })
        .then(res => {
          if (res.data.code == 200) {
            proxy.$message.success(res.data.msg);
            onLoad();
          }
        });
    }
  });
}

// 上传发票相关方法
function uploadInvioce(row) {
  currentRow.value = row;
  invoiceList.value = [
    {
      id: null,
      invoiceNumber: '',
      invoiceDate: '',
      taxRate: null,
      totalAmount: null,
      amountWithoutTax: null,
      taxAmount: null,
      fileList: [],
    },
  ];
  invoiceDrawerVisible.value = true;
  // 如果已有发票数据，可以在这里加载
  //   loadExistingInvoices(row.id);
}

// 加载已有发票数据
function loadExistingInvoices(settlementId) {
  axios
    .get(`/api/vt-admin/invoice/list`, {
      params: { settlementId },
    })
    .then(res => {
      if (res.data.code === 200 && res.data.data) {
        invoiceList.value = res.data.data.map(item => ({
          id: item.id,
          invoiceNumber: item.invoiceNumber,
          invoiceDate: item.invoiceDate,
          taxRate: item.taxRate,
          totalAmount: item.totalAmount,
          amountWithoutTax: item.amountWithoutTax,
          taxAmount: item.taxAmount,
          fileList: item.attachments || [],
        }));
      }
    })
    .catch(err => {
      console.error('加载发票数据失败:', err);
    });
}

// 新增发票
function addInvoice() {
  const newInvoice = {
    id: null,
    invoiceNumber: '',
    invoiceDate: '',
    taxRate: null,
    totalAmount: null,
    amountWithoutTax: null,
    taxAmount: null,
    fileList: [],
  };
  invoiceList.value.push(newInvoice);
}

// 删除发票
function removeInvoice(index) {
  proxy
    .$confirm('确定要删除这张发票吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      invoiceList.value.splice(index, 1);
      proxy.$message.success('删除成功');
    })
    .catch(() => {});
}

// 计算税额和不含税金额
function calculateTaxAmount(invoice) {
  if (invoice.totalAmount && invoice.taxRate) {
    // 不含税金额 = 含税金额 / (1 + 税率)
    invoice.amountWithoutTax = parseFloat(
      (invoice.totalAmount / (1 + invoice.taxRate / 100)).toFixed(2)
    );
    // 税额 = 含税金额 - 不含税金额
    invoice.taxAmount = parseFloat((invoice.totalAmount - invoice.amountWithoutTax).toFixed(2));
  } else {
    invoice.amountWithoutTax = null;
    invoice.taxAmount = null;
  }
}

// 文件上传成功回调
function handleUploadSuccess(response, file, invoice) {
  debugger;
  if (response.code === 200) {
    const fileInfo = {
      name: file.name,
      url: response.data.url,
      id: response.data.id,
    };
    invoice.fileList.push(fileInfo);
    proxy.$message.success('文件上传成功');
  } else {
    proxy.$message.error('文件上传失败');
  }
}

// 删除文件
function handleRemoveFile(file, invoice) {
  const index = invoice.fileList.findIndex(item => item.uid === file.uid);
  if (index > -1) {
    invoice.fileList.splice(index, 1);
  }
}

// 文件上传前验证
function beforeUpload(file) {
  const isValidType = ['image/jpeg', 'image/png', 'application/pdf'].includes(file.type);
  const isLt10M = file.size / 1024 / 1024 < 10;

  if (!isValidType) {
    proxy.$message.error('只能上传 JPG/PNG/PDF 格式的文件!');
    return false;
  }
  if (!isLt10M) {
    proxy.$message.error('上传文件大小不能超过 10MB!');
    return false;
  }
  return true;
}

// 保存发票
function saveInvoices() {
  // 验证发票数据
  debugger;
  for (let i = 0; i < invoiceList.value.length; i++) {
    const invoice = invoiceList.value[i];
    if (
      !invoice.invoiceNumber ||
      !invoice.invoiceDate ||
      !invoice.taxRate ||
      !invoice.totalAmount
    ) {
      proxy.$message.error(`发票 ${i + 1} 信息不完整，请检查`);
      return;
    }
  }

  const saveData = {
    id: currentRow.value.id,
    invoiceDTOList: invoiceList.value.map(invoice => ({
      id: invoice.id,
      invoiceNumber: invoice.invoiceNumber,
      invoiceDate: invoice.invoiceDate,
      taxRate: invoice.taxRate,
      invoicePrice: invoice.totalAmount,
      amountWithoutTax: invoice.amountWithoutTax,
      taxPrice: invoice.taxAmount,
      invoiceFiles: invoice.fileList.map(item => item.id).join(','),
    })),
  };

  axios
    .post('/api/vt-admin/sealContractSettlement/addInvoice', saveData)
    .then(res => {
      if (res.data.code === 200) {
        proxy.$message.success('发票保存成功');
        handleInvoiceDrawerClose();
        onLoad(); // 刷新表格数据
      } else {
        proxy.$message.error(res.data.msg || '保存失败');
      }
    })
    .catch(err => {
      proxy.$message.error('保存失败');
      console.error('保存发票失败:', err);
    });
}

// 关闭发票上传抽屉
function handleInvoiceDrawerClose() {
  invoiceDrawerVisible.value = false;
  invoiceList.value = [];
  currentRow.value = {};
}

// 计算发票汇总数据
const totalInvoiceAmount = computed(() => {
  return invoiceList.value.reduce((total, invoice) => {
    return total + (invoice.totalAmount || 0);
  }, 0);
});

const totalTaxAmount = computed(() => {
  return invoiceList.value.reduce((total, invoice) => {
    return total + (invoice.taxAmount || 0);
  }, 0);
});

// 结算确认
function handleCheck(row) {
  proxy
    .$confirm('确定已经核对数据确认结算吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      // 确认结算
      axios
        .post('/api/vt-admin/sealContractSettlement/confirm', {
          id: row.id,
        })
        .then(res => {
          if (res.data.code === 200) {
            proxy.$message.success('结算确认成功');
            handleDetailDrawerClose();
            onLoad(); // 刷新表格数据
          } else {
            proxy.$message.error(res.data.msg || '确认失败');
          }
        })
        .catch(err => {
          proxy.$message.error('确认失败');
          console.error('确认结算失败:', err);
        });
    })
    .catch(() => {});
}
let imgId = ref('');

function pay(row) {
 
  proxy.$refs.dialogForm.show({
    title:'项目付款',
    option: {
      column: [
        {
          type: 'number',
          label: '付款金额',
          span: 12,
          disabled: true,
          value:row.shouldSettmentPrice,
          prop: 'payPrice',
        },
        {
          type: 'datetime',
          label: '付款日期',
          span: 12,
          display: true,
          format: 'YYYY-MM-DD HH:mm:ss',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          value: moment().format('YYYY-MM-DD HH:mm:ss'),
          prop: 'payTime',
        },
        {
          type: 'select',
          label: '付款账号',

          cascader: [],
          span: 12,
          display: true,

          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          prop: 'paymentAccount',
          rules: [
            {
              required: true,
              message: '请选择付款账号',
              trigger: 'change',
            },
          ],
        },
        {
          label: '付款凭证',
          prop: 'paymentFiles',
          type: 'upload',
          dataType: 'object',
          listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          limit: 1,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'link',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          uploadAfter: (res, done) => {
            imgId.value = res.id;
            done();
          },
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          display: true,
          prop: 'paymentRemark',
        },
      ],
    },
    callback(res) {
      console.log(res.data);

      axios
        .post('/api/vt-admin/sealContractSettlement/payment', {
          id: row.id,

          ...res.data,
          paymentFiles: imgId.value,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          proxy.$store.dispatch('getMessageList');
          res.close();
          imgId.value = '';
          onLoad();
        });
    },
  });
}
function collection(row) {
  let coverId = '';
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          type: 'number',
          label: '收款金额',
          controls: true,
          span: 12,
          disabled: true,
          display: true,
          value: row.shouldSettmentPrice,
          prop: 'actualPrice',
        },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'collectionTime',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        {
          type: 'select',
          label: '收款账号',
          cascader: [],
          disabled: true,
          span: 12,
          // search: true,
          display: true,
          value: row.collectionAccount,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
          dicFormatter: res => {
            return res.data.records;
          },
          props: {
            label: 'abbreviation',
            value: 'id',
            desc: 'desc',
          },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        
     
      ],
    },
    callback(res) {
      const data = {
        id: row.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/sealContractSettlement/confirmPayment', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
</script>

<style lang="scss" scoped>
.el-drawer__body {
  padding: 0;
}

// 发票上传抽屉样式
.invoice-drawer {
  .invoice-upload-container {
    padding: 0 12px 12px 12px;
    min-height: calc(100vh - 120px);
    display: flex;
    flex-direction: column;
  }

  .invoice-list {
    flex: 1;
    margin-bottom: 12px;
  }

  .invoice-item {
    background: linear-gradient(135deg, #f8faff 0%, #ffffff 100%);
    border: 1px solid #e1e8f0;
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 12px;
    box-shadow: 0 1px 6px rgba(0, 0, 0, 0.04);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      width: 3px;
      height: 100%;
      background: linear-gradient(180deg, #409eff 0%, #67c23a 100%);
    }

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.12);
      border-color: #409eff;
    }

    .invoice-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
      padding-bottom: 8px;
      border-bottom: 1px solid #f0f2f5;

      .invoice-title {
        font-weight: 600;
        color: #303133;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: #409eff;
          font-size: 16px;
        }
      }

      .remove-btn {
        width: 24px;
        height: 24px;
        border-radius: 50%;
        background-color: #fff;
        border: 1px solid #f56c6c;
        color: #f56c6c;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f56c6c;
          color: #fff;
          transform: scale(1.1);
        }

        i {
          font-size: 12px;
        }
      }
    }

    .invoice-form {
      .el-row {
        margin-bottom: 6px;
      }

      .el-form-item {
        margin-bottom: 0;

        .el-form-item__label {
          font-size: 13px;
          line-height: 28px;
        }

        .el-input,
        .el-input-number {
          .el-input__inner {
            height: 32px;
            line-height: 32px;
            font-size: 13px;
          }
        }

        .el-date-editor {
          .el-input__inner {
            height: 32px;
            line-height: 32px;
            font-size: 13px;
          }
        }
      }

      .calculated-field {
        .el-input-number {
          .el-input__inner {
            background: linear-gradient(135deg, #e8f4fd 0%, #f0f9ff 100%);
            color: #1890ff;
            font-weight: 600;
            border: 1px solid #b3d8ff;
            height: 32px;
            line-height: 32px;
            font-size: 13px;
          }
        }
      }
    }

    .upload-section {
      margin-top: 12px;
      padding-top: 12px;
      border-top: 1px solid #f0f2f5;

      .upload-title {
        margin-bottom: 8px;
        font-weight: 600;
        color: #606266;
        font-size: 14px;
        display: flex;
        align-items: center;
        gap: 6px;

        i {
          color: #909399;
        }
      }

      .upload-dragger {
        .el-upload-dragger {
          width: 100%;
          height: 100px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          background: #fafbfc;
          transition: all 0.3s ease;

          &:hover {
            border-color: #409eff;
            background: #f0f9ff;
          }

          .el-icon-upload {
            font-size: 24px;
            color: #c0c4cc;
            margin: 15px 0 8px;
          }

          .el-upload__text {
            color: #606266;
            font-size: 12px;
            line-height: 1.4;

            em {
              color: #409eff;
              font-style: normal;
              text-decoration: underline;
            }
          }
        }

        .el-upload__tip {
          color: #909399;
          font-size: 11px;
          margin-top: 6px;
          text-align: center;
        }
      }
    }
  }

  // 空状态样式
  .empty-state {
    text-align: center;
    padding: 40px 12px;
    color: #909399;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .empty-icon {
      font-size: 48px;
      color: #dcdfe6;
      margin-bottom: 12px;

      i {
        font-size: inherit;
      }
    }

    .empty-text {
      font-size: 16px;
      color: #606266;
      margin-bottom: 6px;
      font-weight: 500;
    }

    .empty-desc {
      font-size: 12px;
      color: #909399;
    }
  }

  // 新增发票按钮区域
  .add-invoice-section {
    text-align: center;
    padding: 12px 0;
    border-top: 1px solid #f0f2f5;
    margin-top: auto;

    .add-invoice-btn-new {
      min-width: 160px;
      height: 36px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 18px;
      background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
      border: none;
      box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
      }

      &:active {
        transform: translateY(0);
      }

      i {
        margin-right: 6px;
        font-size: 14px;
      }
    }
  }

  // 汇总信息样式
  .invoice-summary {
    background: linear-gradient(135deg, #f0f9ff 0%, #e8f4fd 100%);
    border: 1px solid #b3d8ff;
    border-radius: 8px;
    padding: 16px;
    margin: 12px 0;
    box-shadow: 0 1px 4px rgba(64, 158, 255, 0.1);

    .summary-title {
      font-size: 14px;
      font-weight: 600;
      color: #303133;
      margin-bottom: 12px;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 6px;

      i {
        color: #409eff;
        font-size: 16px;
      }
    }

    .summary-content {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      text-align: center;

      .summary-item {
        padding: 12px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 6px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: translateY(-1px);
        }

        .summary-label {
          font-size: 12px;
          color: #606266;
          margin-bottom: 6px;
          font-weight: 500;
        }

        .summary-value {
          font-size: 16px;
          font-weight: 700;
          color: #1890ff;
          text-shadow: 0 1px 2px rgba(24, 144, 255, 0.1);
        }
      }
    }
  }

  // 底部按钮样式
  .drawer-footer {
    display: flex;
    justify-content: center;
    gap: 12px;
    padding: 16px 12px;
    border-top: 1px solid #f0f2f5;
    background: linear-gradient(180deg, #fafbfc 0%, #f5f7fa 100%);
    margin: 0 -12px -12px -12px;

    .el-button {
      min-width: 100px;
      height: 36px;
      font-size: 14px;
      font-weight: 600;
      border-radius: 18px;
      transition: all 0.3s ease;

      &:not(.el-button--primary) {
        background: #fff;
        border-color: #dcdfe6;
        color: #606266;

        &:hover {
          border-color: #c0c4cc;
          color: #409eff;
        }
      }

      &.el-button--primary {
        background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
        border: none;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
        }

        &:disabled {
          background: #c0c4cc;
          transform: none;
          box-shadow: none;
        }
      }

      i {
        margin-right: 4px;
      }
    }
  }
}
</style>
