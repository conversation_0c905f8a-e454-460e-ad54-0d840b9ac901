<template>
  <basic-container>
    <Title style="margin-bottom: 10px"
      >详情
      <template #foot
        ><el-button
          @click="approve()"
          type="primary"
          v-if="form.leaderId == $store.getters.userInfo.user_id"
          >批示</el-button
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        >
      </template></Title
    >
    <avue-form :option="option" @submit="handleSubmit" v-model="form">
      <template #filesIds>
        <File :fileList="form.attachList || []"></File>
      </template>
    </avue-form>
    <Title style="margin: 20px 0">批示记录</Title>
    <avue-crud :option="approveOption" :data="form.customerFollowCommandVoS"></avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { followType } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed, onMounted } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';

const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

let userInfo = computed(() => store.getters.userInfo);
const option = ref({
  labelWidth: 140,
  detail: true,
  submitBtn: false,
  emptyBtn: false,
  group: [
    {
      column: [
        {
          type: 'input',
          label: '客户名称',
          span: 12,
          display: true,
          component: 'wf-customer-select',
          prop: 'customerId',
          required: true,
          rules: [
            {
              required: true,
              message: '请选择客户',
            },
          ],
        },
        {
          type: 'select',
          label: '跟进类型',
          span: 12,
          display: true,
          prop: 'followType',
          dicData: followType,
          props: {
            value: 'value',
            label: 'label',
          },
          control: val => {
            console.log(val);
            return {
              logicId_business: {
                display: val == 1,
              },
              logicId_project: {
                display: val == 2,
              },
              logicId_order: {
                display: val == 3,
              },
            };
          },
        },
        {
          label: '关联商机',
          prop: 'logicId_business',
          component: 'wf-business-select',
          display: false,
        },
        {
          label: '关联项目',
          prop: 'logicId_project',
          display: false,
        },
        {
          label: '关联订单',
          prop: 'logicId_order',
          display: false,
        },
        {
          type: 'select',
          label: '跟进方式',
          span: 12,
          display: true,
          dicUrl: '/blade-system/dict/dictionary?code=followType',
          prop: 'followWay',
          props: {
            label: 'dictValue',
            value: 'id',
          },
          rules: [
            {
              required: true,
              message: '跟进方式必须填写',
            },
          ],
        },

        {
          type: 'date',
          label: '实际跟进时间',
          span: 12,
          display: true,
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          format: 'YYYY-MM-DD HH:mm:ss',
          prop: 'followTime',
          rules: [
            {
              required: true,
              message: '联系方式必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '跟进联系人',
          component: 'wf-contact-select',
          span: 12,
          display: true,
          prop: 'followPerson',
        },
        {
          type: 'select',
          label: '跟进目的',
          span: 12,
          display: true,
          prop: 'commonTerm',
          dicUrl: '/blade-system/dict/dictionary?code=commonTerms',
          props: {
            label: 'dictValue',
            value: 'id',
          },
        },
        // {
        //   type: 'input',
        //   label: '拜访地点',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'address',
        // },

        {
          type: 'textarea',
          label: '跟进记录',
          cascader: [],

          span: 24,
          display: true,

          prop: 'followContent',
        },
        // {
        //   type: 'textarea',
        //   label: '工作成效',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'jobPerformance',
        // },
        // {
        //   type: 'textarea',
        //   label: '客户情况',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'customerSituation',
        // },
        // {
        //   type: 'textarea',
        //   label: '工作计划与目标',
        //   cascader: [],
        //   span: 24,
        //   display: true,
        //   prop: 'plansOjbectives',
        // },
        // {
        //   type: 'textarea',
        //   label: '需要协助事宜',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'needHelpThing',
        // },
        {
          label: '附件',
          prop: 'filesIds',
          type: 'upload',
          dataType: 'string',
          slot: true,
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
          propsHttp: {
            res: 'data',
            url: 'link',
          },
          span: 24,
        },
      ],
    },
    {
      column: [
        {
          label: '回访提醒',
          type: 'switch',
          prop: 'isReturnVisitRemind',
          dicData: [
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ],
          control: v => {
            return {
              remindTime: {
                display: !!v,
              },
              messageRemind: {
                display: !!v,
              },
              remindHourAndMinut: {
                display: !!v,
              },
            };
          },
        },
        {
          label: '回访时间',
          prop: 'remindTime',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          rules: [
            {
              required: true,
              message: '请选择回访时间',
            },
          ],
        },
        {
          label: '提醒人',
          component: 'wf-user-select',
          prop: 'messageRemind',
          params: {
            checkType: 'checkbox',
          },
        },
        {
          label: '提醒时间',
          labelTip: '回访时间前',
          labelTipPlacement: 'top',
          prop: 'remindHourAndMinut',
          value: '00:30',
          format: '回访开始前HH时mm分',
          valueFormat: 'HH:mm',
          type: 'time',
        },
      ],
    },
  ],
});
const approveOption = ref({
  header: false,
  addBtn: false,
  align: 'center',
  menu: false,
  column: [
    {
      label: '批示内容',
      prop: 'remark',
    },
    {
      label: '批示时间',
      prop: 'createTime',
    },
    {
      label: '批示人',
      prop: 'createName',
    },
  ],
});
onMounted(() => {
  if (route.query.id) {
    getDetail();
  }
});
watchEffect(() => {
  console.log(route);
  if(route.query.id && route.path.indexOf('follow') > -1){
    getDetail()
  }
})
function getDetail() {
  axios.get('/api/vt-admin/customerFollow/detail?id=' + route.query.id).then(res => {
    const { remindStartTime, remindEndTime } = res.data.data;
    form.value = {
      ...res.data.data,
      remindTime: [remindStartTime, remindEndTime],
      logicId_business: res.data.data.logicId,
      logicId_order: res.data.data.logicId,
      logicId_project: res.data.data.logicId,
    };
  });
}
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    remindStartTime: form.remindTime[0],
    remindEndTime: form.remindTime[1],
    remindHour: form.remindHourAndMinut.split(':')[0],
    remindMintue: form.remindHourAndMinut.split(':')[1],
  };
  if (form.id) {
    axios
      .post('/api/vt-admin/customerFollow/update', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          router.go(-1);
        }
      })
      .catch(() => {
        done();
      });
  } else {
    axios
      .post('/api/vt-admin/customerFollow/save', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          router.go(-1);
        }
      })
      .catch(() => {
        done();
      });
  }
}
function approve() {
  proxy.$refs.dialogForm.show({
    title: '批示',
    option: {
      column: [
        {
          label: '批示内容',
          type: 'textarea',
          prop: 'remark',
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        followId: form.value.id,
        ...res.data,
      };
      axios.post('/api/vt-admin/customerFollowCommand/save', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
</script>

<style lang="scss" scoped></style>
