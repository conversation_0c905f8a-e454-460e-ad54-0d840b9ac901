<template>
  <div style="width: 100%">
    <el-autocomplete
      v-model="value"
      style="width: 100%"
      :fetch-suggestions="querySearch"
      :trigger-on-focus="false"
      clearable
      v-if="dataType === 'name'"
      value-key="customerName"
      class="inline-input w-50"
      placeholder="请输入客户名称"
      @select="handleUserSelectConfirm"
      @blur="handleBlur"
    />
    <el-select v-else style="width: 100%" filterable remote placeholder="请输入客户" :remote-method="querySearch" v-model="value" clearable @change="handleBlur">
      <el-option :label="item.customerName" :value="item.id" v-for="item in customerData"></el-option>
    </el-select>
  </div>
</template>
<script>
export default {
  name: 'customer-drop',
};
</script>
<script setup>
import axios from 'axios';
import { watch } from 'vue';

const props = defineProps({
  modelValue: {
    type: String,
  },
  url: {
    type: String,
    default: '/api/vt-admin/customer/pageByCustomerName',
  },
  dataType:{
    type: String,
    default: 'name'
  }
});
watch(
  () => props.modelValue,
  val => {
    value.value = val;
  }
);
const emits = defineEmits(['update:modelValue']);
let value = ref('');
let customerData = ref([]);
function querySearch(value, cb) {
  if (!value) {
    customerData.value = [];
    return [];
  }
  axios.get(props.url, { params: { customerName: value, size: 100 } }).then(res => {
    if(cb){
      cb(res.data.data.records);
    }
    customerData.value = res.data.data.records;
  });
}
function handleUserSelectConfirm(value) {
  emits('update:modelValue', value.customerName);
}
function handleBlur() {
  emits('update:modelValue', value.value);
}
</script>

<style lang="scss" scoped></style>
