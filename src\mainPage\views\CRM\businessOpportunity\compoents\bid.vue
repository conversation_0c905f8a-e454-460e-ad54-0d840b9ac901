<template>
  <basic-container>
    <Title style="margin-bottom: 10px">
      投标信息录入
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" @submit="handleSubmit" v-model="form"></avue-form>
  </basic-container>
</template>

<script setup>
let baseUrl = '/api/blade-system/region/lazy-tree';
import { followData } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

let userInfo = computed(() => store.getters.userInfo);
const option = ref({
  labelWidth: 120,
  column: [
    {
      label: '投标信息',
      prop: 'bidInfo',
    },
  ],
});
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    businessOpportunityId:route.query.businessId
  };
  axios
    .post('/api/vt-admin/businessOpportunityBiding/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        router.$avueRouter.closeTag();
        router.go(-1);
      }
    })
    .catch(() => {
      done();
    });
}
</script>

<style lang="scss" scoped></style>
