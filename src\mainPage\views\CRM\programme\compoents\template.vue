<template>
  <div
    class="wrap"
    :style="{ background: props.detail || form.isFold ? 'var(--el-color-info-light-9)' : '' }"
  >
    <div :style="{ height: isFold ? 0 : 'auto' }" style="overflow: hidden; transition: all 0.3s">
      <!-- <el-row :gutter="10">
        <el-col :span="5">
          <el-card
            :body-style="{ padding: 0 }"
            style="
              height: 100%;
              overflow-y: auto;
              font-size: 14px;
              padding: 5px 0 0 5px;
              box-sizing: border-box;
            "
          >
            <div style="font-size: 14px; font-weight: bolder">所有汇总:</div>
            <div style="display: flex; gap: 20px">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <div style="color: #666">
                  成本金额： <span style="color: #000">{{ allModuleTotal.costTotal }}</span>
                </div>

                <div style="color: #666">
                  客户报价： <span>{{ allModuleTotal.total }}</span>
                </div>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <div style="color: #666">
                  毛利润 ：<span>{{
                    (allModuleTotal.total - allModuleTotal.costTotal).toFixed(2)
                  }}</span>
                </div>
                <div style="color: #666">
                  毛利润率：
                  <span>{{
                    (
                      (allModuleTotal.total - allModuleTotal.costTotal) /
                      allModuleTotal.total
                    ).toFixed(2)
                  }}</span>
                </div>
              </div>
            </div>
          </el-card></el-col
        >
        <el-col :span="6">
          <el-card
            :body-style="{ padding: 0 }"
            style="
              height: 100%;
              font-size: 14px;
              padding: 5px 0 0 5px;
              box-sizing: border-box;
              overflow-y: auto;
            "
          >
            <div v-if="form.currentModule > 0">
              <div style="font-size: 14px; font-weight: bolder">
                当前模块汇总:<el-text type="primary">{{
                  moduleDTOList.find((item, index) => index == form.currentModule).moduleName
                }}</el-text>
                <el-icon
                  v-if="$route.query.type != 'detail'"
                  @click="addModule(moduleDTOList[form.currentModule], 'edit')"
                  style="margin-left: 5px; cursor: pointer"
                  ><edit
                /></el-icon>
                <el-icon
                  v-if="$route.query.type != 'detail'"
                  @click="copyModule(moduleDTOList[form.currentModule], 'edit')"
                  style="margin-left: 5px; cursor: pointer"
                  title="复制"
                  ><CopyDocument
                /></el-icon>
                <el-icon
                  @click="delModule"
                  v-if="$route.query.type != 'detail'"
                  style="margin-left: 5px; cursor: pointer; color: var(--el-color-danger)"
                  ><delete
                /></el-icon>
              </div>
              <div style="display: flex; gap: 20px">
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    align-items: flex-start;
                  "
                >
                  <div style="color: #666">
                    设备金额：
                    <span style="color: #000">{{ currentModuleTotal.totalProductPrice }}</span>
                  </div>
                  <div style="color: #666">
                    人工金额：
                    <span style="color: #000">{{ currentModuleTotal.totalLaborCost }}</span>
                  </div>

                  <div style="color: #666" v-if="!form.isHasTax">
                    税金：
                    <span style="color: #000">{{ currentModuleTotal.taxPrice }}</span>
                  </div>
                  <div style="color: #666">
                    报价金额：
                    <span style="color: #000">{{ currentModuleTotal.total }}</span>
                  </div>
                  <div style="color: #666">
                    毛利润 ：<span style="color: black">{{
                      (currentModuleTotal.total - currentModuleTotal.costTotal).toFixed(2)
                    }}</span>
                  </div>
                </div>
                <div style="display: flex; flex-direction: column; justify-content: space-between">
                  <div style="color: #666">
                    设备成本金额：
                    <span style="color: #000">{{ currentModuleTotal.totalCostPrice }}</span>
                  </div>
                  <div style="color: #666">
                    人工成本金额：
                    <span style="color: #000">{{ currentModuleTotal.totalRgcbPrice }}</span>
                  </div>

                  <div style="color: #666">
                    报价成本金额：
                    <span style="color: #000">{{ currentModuleTotal.costTotal }}</span>
                  </div>
                  <div style="color: #666">
                    毛利润率：
                    <span style="color: black">{{
                      isNaN(
                        (
                          (currentModuleTotal.total - currentModuleTotal.costTotal) /
                          currentModuleTotal.total
                        ).toFixed(2)
                      )
                        ? '--'
                        : (
                            (currentModuleTotal.total - currentModuleTotal.costTotal) /
                            currentModuleTotal.total
                          ).toFixed(2)
                    }}</span>
                  </div>
                </div>
                <div
                  style="
                    display: flex;
                    flex-direction: column;
                    justify-content: space-between;
                    gap: 10px;
                  "
                ></div>
              </div>
            </div> </el-card
        ></el-col>
        <el-col :span="5">
          <el-card
            :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box; font-size: 14px"
          >
            <div style="font-size: 14px; font-weight: bolder">
              产品参考信息:
              <el-text type="primary" style="overflow: hidden; white-space: nowrap">{{
                referInfo.productName
              }}</el-text>
            </div>
            <div style="display: flex; gap: 10px">
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                <div style="color: #666">
                  最低销售价： <span style="color: #000">{{ referInfo.minSealPrice }}</span>
                </div>
                <div style="color: #666">
                  最近设备销售价： <span style="color: #000">{{ referInfo.preSealPrice }}</span>
                </div>
             

                <div style="color: #666">
                  参考销售价： <span style="color: #000">{{ referInfo.referSealPrice }}</span>
                </div>
              </div>
              <div
                style="
                  display: flex;
                  flex-direction: column;
                  justify-content: space-between;
                  gap: 10px;
                "
              >
                
              </div>
            </div>
          </el-card></el-col
        >
        <el-col :span="4">
          <el-card
            :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box"
          >
            <div
              style="
                font-size: 14px;
                font-weight: bolder;
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              快捷配置:
              <el-switch
                v-model="form.isLock"
                size="small"
                v-if="$route.query.type != 'detail'"
                active-text="锁定"
                inactive-text="解锁"
              />
              <el-button type="primary" text size="small" @click="colmunDrawer = !colmunDrawer"
                >显示设置</el-button
              >
            </div>
            <el-row>
              <el-form>
                <el-form-item style="margin: 0" label="设备利润比:">
                  <el-col :span="12">
                    <el-popconfirm
                      hide-icon
                      @confirm="setPrice(form.productTax)"
                      title="修改将会重新计算所有产品设备单价，是否确认?"
                    >
                      <template #reference>
                        <el-input
                          size="small"
                          :disabled="form.isLock || $route.query.type == 'detail'"
                          v-model="form.productTax"
                          placeholder="请输入"
                        ></el-input>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-form-item>
                <el-form-item style="margin: 0" label="人工利润比:">
                  <el-col :span="12">
                    <el-popconfirm
                      hide-icon
                      @confirm="setLaborPrice(form.laborTax)"
                      title="修改将会重新计算所有产品人工单价，是否确认?"
                    >
                      <template #reference>
                        <el-input
                          size="small"
                          :disabled="form.isLock || $route.query.type == 'detail'"
                          v-model="form.laborTax"
                          placeholder="请输入"
                        ></el-input>
                      </template>
                    </el-popconfirm>
                  </el-col>
                </el-form-item>
              </el-form>
            </el-row> </el-card
        ></el-col>
        <el-col :span="4">
          <el-card
            :body-style="{ padding: 0 }"
            style="height: 100%; padding: 5px 0 0 5px; box-sizing: border-box"
          >
            <div
              style="
                font-size: 14px;
                font-weight: bolder;
                display: flex;
                justify-content: space-between;
                align-items: center;
              "
            >
              导出配置:
              <el-switch
                style="margin-right: 10px"
                v-model="form.isHasTax"
                size="small"
                v-if="$route.query.type != 'detail'"
                active-text="含税"
                inactive-text="未税"
                :active-value="1"
                :inactive-value="0"
              />
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              设备税率：
              <el-input-number
                size="small"
                style="width: 60%"
                step="0.01"
                :disabled="form.isHasTax || $route.query.type == 'detail'"
                v-model="form.productRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              人工税率：
              <el-input-number
                size="small"
                style="width: 60%"
                step="0.01"
                :disabled="form.isHasTax || $route.query.type == 'detail'"
                v-model="form.labourRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              延保税率：
              <el-input-number
                size="small"
                step="0.01"
                style="width: 60%"
                :disabled="form.isHasTax || $route.query.type == 'detail'"
                v-model="form.warrantyRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
            <div style="display: flex; align-items: center; font-size: 14px">
              其他税率：
              <el-input-number
                size="small"
                step="0.01"
                style="width: 60%"
                :disabled="form.isHasTax || $route.query.type == 'detail'"
                v-model="form.otherRate"
                placeholder="请输入"
              ></el-input-number>
            </div>
          </el-card>
        </el-col>
      </el-row> -->
      <el-row style="height: 70px" v-if="form.templateType != 2 && form.templateType != 3">
        <el-col :span="24">
          <el-card :body-style="{ padding: '10px' }" :key="randomKey">
            <el-radio-group
              @change="
                () => {
                  setTableData();
                  setCateGoryList();
                }
              "
              style="margin-right: 10px"
              v-model="form.currentModule"
              size="large"
              ref="sort-buttons"
            >
              <el-radio-button
                v-for="(item, index) in moduleDTOList"
                :key="item.uuid"
                :label="index"
              >
                <div style="display: flex; align-items: center">
                  {{ item.moduleName }}
                  <el-icon
                    v-if="index != 0 && !props.detail"
                    @click="addModule(moduleDTOList[form.currentModule], 'edit')"
                    style="margin-left: 5px; cursor: pointer"
                    ><edit
                  /></el-icon>
                  <el-icon
                    v-if="index != 0 && !props.detail"
                    @click="delModule"
                    style="margin-left: 5px; cursor: pointer; color: var(--el-color-danger)"
                    ><delete
                  /></el-icon></div
              ></el-radio-button>
              <el-button
                type=""
                size="large"
                v-if="$route.query.type != 'detail' && form.templateType == 0 && !props.detail"
                @click="addModule"
                style="width: 50px; margin-left: 5px; border-radius: 1px"
                icon="plus"
              ></el-button>
              <el-button
                type=""
                size="large"
                title="从模板库选择"
               v-if="$route.query.type != 'detail' && form.templateType == 0 && !props.detail"
                @click="addTemplate(1)"
                style="width: 50px; margin-left: 5px; border-radius: 1px"
                icon="CopyDocument"
              ></el-button>
            </el-radio-group>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-row :gutter="10" :style="{ height: isFold ? 'calc(105%)' : 'calc(100% - 90px)' }">
      <div
        style="
          display: flex;
          cursor: pointer;
          justify-content: center;
          background-color: #ccc;
          width: 100%;
          margin: 0 5px;
        "
        v-if="!props.detail"
        @click="isFold = !isFold"
      >
        <el-icon :style="{ transform: `rotate(${isFold ? '-90deg' : '90deg'})` }"
          ><DArrowLeft
        /></el-icon>
      </div>
      <el-col
        :span="form.currentModule == 0 || form.templateType == 2 || form.templateType == 3 ? 0 : 3"
      >
        <el-card style="height: 100%; position: relative">
          <div ref="category_box">
            <el-tag effect='plain'
              plain
              size="large"
              @click="handleScroll(index)"
              style="width: 100%; margin-bottom: 5px; cursor: pointer"
              v-for="(item, index) in categoryList"
              :key="item"
            >
              <el-icon v-if="$route.query.type != 'detail' && !props.detail" class="sort">
                <sort></sort>
              </el-icon>
              {{ item }}</el-tag
            >
          </div>
          <div style="display:flex;justify-content: space-around">
            <el-button
            type=""
            v-if="$route.query.type != 'detail' && !props.detail"
            style="width: 50%"
            @click="addCategory"
            icon="plus"
          ></el-button>
          <el-button
              type=""
              title="从模板库选择"
              v-if="$route.query.type != 'detail' && !props.detail"
              style="width: 50%"
              @click="addTemplate(2)"
              icon="CopyDocument"
            ></el-button>
          </div>
          <div
            style="
              height: 50%;
              width: auto;
              box-shadow: var(--el-box-shadow-light);
              position: absolute;
              bottom: 0;
              left: 5px;
              box-sizing: border-box;
              right: 5px;
              padding: 5px;
            "
          >
            <div style="height: 20px; overflow: hidden">
              <el-text type="primary">{{ currentProduct.customProductName }}</el-text>
            </div>
            <textarea
              style="
                height: calc(100% - 20px);
                overflow: auto;
                font-size: 14px;
                border: none;
                resize: none;
              "
              :disabled="$route.query.type == 'detail'"
              v-model="currentProduct.customProductDescription"
            >
            </textarea>
          </div>
        </el-card>
      </el-col>
      <el-col
        :span="
          form.currentModule == 0 || form.templateType == 2 || form.templateType == 3 ? 24 : 21
        "
        style="height: 100%"
      >
        <el-card
          ref="tableCard"
          class="myCard"
          :body-style="{ padding: 0 }"
          style="height: 100%; overflow: scroll"
        >
          <div v-if="form.currentModule == 0">
            <el-table class="avue_table" :data="allData" border>
              <el-table-column
                label="序号"
                width="80"
                type="index"
                align="center"
              ></el-table-column>
              <el-table-column label="模块名称" prop="moduleName" align="center"></el-table-column>
            </el-table>
          </div>
          <table style="text-align: center; position: relative; width: 100%" border="1" v-else>
            <colgroup>
              <col width="40px" />
              <col width="50px" />
              <col width="150px" />
              <col width="80px" />

              <col width="200px" />
              <col width="150px" />
              <col width="80px" class="center" />
              <col width="80px" />
            </colgroup>
            <thead style="position: sticky; top: -1px; z-index: 100; background-color: #fff">
              <tr style="font-weight: bolder; color: black">
                <td>序号</td>
                <td>分类</td>
                <td>产品名称</td>
                <td>品牌</td>
                <td>规格型号</td>
                <td>产品描述</td>

                <td class="center">单位</td>
                <td class="center">数量</td>
              </tr>
            </thead>

            <tbody :ref="index + '-cateGory'" v-for="(item, index) in tableData" :key="randomKey">
              <tr class="category" v-show="form.templateType != 3">
                <td colspan="24">
                  {{ item.classify }}
                  <el-icon
                    v-if="$route.query.type != 'detail' && !props.detail"
                    @click="editCategory(item.classify)"
                    style="margin-left: 5px; cursor: pointer"
                    ><edit
                  /></el-icon>
                  <el-button
                    v-if="currentIndex == item.classify"
                    @click="currentIndex = null"
                    type="primary"
                    text
                    >退出编辑模式</el-button
                  >
                  <productSelectDrop
                    v-if="$route.query.type != 'detail' && !props.detail"
                    @select="
                      id => {
                        form.currentclassify = item.classify;
                        handleProductSelectConfirm(id);
                      }
                    "
                    style="margin-left: 5px"
                  ></productSelectDrop>

                  <el-button
                    class="delete_category"
                    type="danger"
                    size="small"
                    v-if="$route.query.type != 'detail' && !props.detail"
                    @click="delCategory(item.classify)"
                    style="margin-left: 10px"
                    circle
                    title="删除"
                    icon="delete"
                  ></el-button>
                  <el-button
                    class="delete_category"
                    type="primary"
                    size="small"
                    v-if="$route.query.type != 'detail' && !props.detail"
                    @click="copyCategory(item.classify)"
                    style="margin-left: 10px"
                    circle
                    title="复制"
                    icon="CopyDocument"
                  ></el-button>
                </td>
              </tr>
              <tr
                class="cell_hover"
                @click="setReferPrice(i)"
                v-for="(i, ins) in item.productList"
                :key="item.uuid"
              >
                <td class="index_product">
                  <span class="index_product_span">{{ customIndex(ins, index) }}</span>
                  <div style="display: flex; align-items: center; gap: 5px">
                    <el-button
                      @click="deleteProduct(i)"
                      class="delete_product"
                      type="danger"
                      size="small"
                      title="删除"
                      v-if="$route.query.type != 'detail' && !props.detail"
                      circle
                      icon="delete"
                    ></el-button>
                    <el-icon
                      title="拖动排序"
                      v-if="$route.query.type != 'detail' && !props.detail"
                      class="sort delete_product"
                    >
                      <sort></sort>
                    </el-icon>
                  </div>
                </td>

                <td :class="{ active: currentIndex === item }" @click="handleRowClick(item)">
                  <div
                    v-if="!i.productId"
                    style="display: flex; justify-content: center; align-items: center"
                  >
                    <el-tag effect='plain' style="margin-right: 5px" v-if="i.categoryName">{{
                      i.categoryName
                    }}</el-tag>
                    <el-button
                      @click="relationCategory(i)"
                      class="delete_product"
                      type="primary"
                      size="small"
                      title="关联分类"
                      v-if="$route.query.type != 'detail' && !props.detail"
                      circle
                      :icon="i.categoryId ? 'edit' : 'plus'"
                    ></el-button>
                  </div>
                </td>
                <!-- 名称 -->
                <td :class="{ active: currentIndex === item }" @click="handleRowClick(item)">
                  <div
                    style="white-space: wrap; overflow: auto; height: 100%"
                    @focus="i.height = 100"
                    :style="{
                      'white-space': i.height == 20 || !i.height ? 'wrap' : 'normal',
                      overflow: i.height == 20 || !i.height ? 'hidden' : 'auto',
                      height: i.height + 'px',
                    }"
                    @input="
                      e => {
                        i.customProductName = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                  >
                    {{ i.customProductName }}
                  </div>
                </td>
                <!-- 品牌 -->
                <td>
                  <el-popover
                    placement="bottom-start"
                    :offset="10"
                    :show-after="150"
                    :disabled="i.productId"
                    style="padding: 0"
                    trigger="focus"
                  >
                    <template #default>
                      <ul
                        class="el-scrollbar__view el-select-dropdown__list"
                        style="max-height: 200px; overflow-y: auto"
                      >
                        <li
                          @click.capture="handleProductBrandClick(i, item)"
                          v-for="item in brandList"
                          class="el-select-dropdown__item"
                        >
                          {{ item }}
                        </li>
                      </ul>
                    </template>
                    <template #reference>
                      <div
                        style="white-space: wrap; overflow: auto; height: 100%"
                        @input="
                          e => {
                            i.productBrand = e.target.innerText;
                          }
                        "
                        @focus="getBrand(i.categoryId)"
                        :contenteditable="
                          $route.query.type == 'detail' || i.productId ? 'false' : 'true'
                        "
                      >
                        {{ i.productBrand }}
                      </div>
                    </template>
                  </el-popover>
                </td>
                <!-- 型号 -->
                <td
                  :class="{ active: currentIndex === item.classify }"
                  @click="handleRowClick(item)"
                >
                  <el-popover
                    placement="bottom-start"
                    :offset="10"
                    width="auto"
                    :disabled="i.productId"
                    :show-after="150"
                    style="padding: 0"
                    trigger="focus"
                  >
                    <template #default>
                      <ul
                        class="el-scrollbar__view el-select-dropdown__list"
                        style="max-height: 200px; overflow-y: auto"
                      >
                        <li
                          @click="handleSpecificationClick(i, item)"
                          v-for="item in productSpecificationList"
                          class="el-select-dropdown__item"
                          style="height: 50px"
                        >
                          <div style="height: 25px; line-height: 25px">
                            <span>名称：</span>{{ item.productName }}
                          </div>
                          <div style="height: 25px; line-height: 25px">
                            <span>型号：</span>{{ item.productSpecification }}
                          </div>
                        </li>
                      </ul>
                    </template>
                    <template #reference>
                      <div
                        style="white-space: wrap; overflow: auto"
                        :style="{
                          'white-space': i.height == 20 || !i.height ? 'wrap' : 'normal',
                          overflow: i.height == 20 || !i.height ? 'hidden' : 'auto',
                          height: i.height + 'px',
                        }"
                        @focus="getProductSpecificationList(i)"
                        @input="
                          e => {
                            i.customProductSpecification = e.target.innerText;
                          }
                        "
                        :contenteditable="
                          $route.query.type == 'detail' || i.productId ? 'false' : 'true'
                        "
                      >
                        {{ i.customProductSpecification }}
                      </div>
                    </template>
                  </el-popover>
                </td>
                <!-- 描述 -->
                <td
                  :class="{ active: currentIndex === item.classify }"
                  @click="handleRowClick(item)"
                >
                  <el-input
                    @blur="handleBlur($event, item)"
                    v-if="currentIndex == item.classify"
                    v-model="i.customProductDescription"
                    type="textarea"
                    placeholder=""
                  ></el-input>
                  <textarea
                    style="
                      white-space: nowrap;
                      overflow: auto;
                      outline: none;
                      border: none;
                      resize: none;
                      width: 100%;
                    "
                    @focus="i.height = 100"
                    :style="{
                      height: i.height + 'px',
                      'white-space': i.height == 20 || !i.height ? 'nowrap' : 'normal',
                      overflow: i.height == 20 || !i.height ? 'hidden' : 'auto',
                    }"
                    :disabled="$route.query.type == 'detail'"
                    v-model="i.customProductDescription"
                    v-else
                  >
                  </textarea>
                </td>

                <!-- 单位 -->
                <td class="center">
                  <div
                    @input="
                      e => {
                        i.customUnit = e.target.innerText;
                      }
                    "
                    :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                  >
                    {{ i.customUnit || i.unitName }}
                  </div>
                </td>
                <!-- 数量 -->
                <td
                  :class="{
                    active: currentIndex === item.classify,
                    error: i.number <= 0 || !isNumber(i.number),
                  }"
                  @click="handleRowClick(item)"
                  class="center"
                >
                  <el-tooltip
                    :content="!isNumber(i.number) ? '格式有误' : '数量不能小于等于0'"
                    effect="light"
                    placement=""
                    :disabled="!(i.number <= 0 || !isNumber(i.number))"
                  >
                    <div
                      @input="
                        e => {
                          i.number = e.target.innerText;
                        }
                      "
                      :contenteditable="$route.query.type == 'detail' ? 'false' : 'true'"
                    >
                      {{ i.number }}
                    </div>
                  </el-tooltip>
                </td>
              </tr>
              <tr>
                <td colspan="24">
                  <el-button
                    type="primary"
                    icon="plus"
                    title="从产品库选择产品"
                    v-if="$route.query.type != 'detail' && !props.detail"
                    @click="
                      $refs['product-select'].visible = true;
                      form.currentclassify = item.classify;
                    "
                    style="margin: 2px 0; margin-left: 3px"
                    plain
                    size="default"
                    >库</el-button
                  >
                  <el-button
                    type="primary"
                    icon="plus"
                    title="添加一个空产品"
                    v-if="$route.query.type != 'detail' && !props.detail"
                    @click="addEmptyProduct(item)"
                    plain
                    size="default"
                    >空</el-button
                  >
                </td>
              </tr>
              <tr v-if="index == tableData.length - 1" style="border: none">
                <td colspan="24" style="height: 200px"></td>
              </tr>
            </tbody>
          </table> </el-card
      ></el-col>
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleProductSelectConfirm"
      @openIframe="handleOpenIframe"
    >
    </wf-product-select>
    <templateSelect
      ref="templateDialog"
      :templateType="templateType"
      :level="templateType"
      :businessType="$route.query.businessType"
      @change="handleTemplateChange"
    ></templateSelect>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import productSelectDrop from '../../quotation/compoents/productSelectDrop.vue';
import { deepClone } from '@/utils/util.js';
const { proxy } = getCurrentInstance();
import Sortable from 'sortablejs';
import { randomLenNum } from '@/utils/util';
import templateSelect from './templateSelect.vue';
let currentIndex = ref(null);
const route = useRoute;
let form = ref({
  currentModule: 0,
  currentclassify: null,
  isHasTax: 1,
});
let tableData = ref([]); //表格临时数据
let categoryList = ref([]);
let moduleDTOList = ref([
  { moduleName: '汇总', uuid: randomLenNum(10) },
  //   { moduleName: '默认子项', id: null, detailDTOList: [] },
]); // 所有数据的操作都会存在这里
const props = defineProps({
  detail: {
    default: false,
  },
  data: {
    type: Object,
    default: () => ({}),
  },
});
watch(
  () => props.data,
  () => {
    if (props.data) {
      form.value = props.data;
      tableData.value = [];

      categoryList.value = [];
      if (form.value.templateType == 1 || form.value.templateType == 0) {
        //处理单系统 类型
        if (form.value.moduleTemplateVOS.length < 1 && !props.detail) {
          // addModule();
        } else {
          moduleDTOList.value = [
            { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
            ...props.data.moduleTemplateVOS?.map(item => {
              return {
                ...item,
                uuid: item.id || randomLenNum(10),
                detailDTOList: item.optionModuleDetailTemplateVOS.map(item => {
                  return {
                    ...item,
                    uuid: item.id || randomLenNum(10),
                    number: isNaN(item.number) || !item.number ? '' : parseFloat(item.number),
                  };
                }),
              };
            }),
          ];

          proxy.$nextTick(() => {
            form.value.currentModule = 1;
            setTableData();
            setCateGoryList();
          });
        }
      }
      if (form.value.templateType == 2) {
        //处理单分类 类型
        if (!form.value.optionModuleClassifyTemplateVO && !props.detail) {
          moduleDTOList.value = [
            { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
            { moduleName: '默认子项', uuid: randomLenNum(10), detailDTOList: [] },
          ];
          form.value.currentModule = 1;
          addCategory();
        } else {
          moduleDTOList.value = [
            { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
            {
              moduleName: '默认子项',
              uuid: randomLenNum(10),
              detailDTOList: [
                {
                  ...form.value.optionModuleClassifyTemplateVO,
                  uuid: randomLenNum(10),
                  optionModuleDetailTemplateVOS: [],
                  detailType: 1,
                },
                ...form.value.optionModuleClassifyTemplateVO.optionModuleDetailTemplateVOS.map(
                  item => {
                    return {
                      ...item,
                      detailType: 0,
                      uuid: item.id || randomLenNum(10),
                      number: isNaN(item.number) || !item.number ? '' : parseFloat(item.number),
                    };
                  }
                ),
              ],
            },
          ];

          form.value.currentModule = 1;
          setTableData();
          setCateGoryList();
        }
      }
      if (form.value.templateType == 3) {
        if (!form.value.detailTemplateVOS) {
          moduleDTOList.value = [
            { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
            {
              moduleName: '默认子项',
              uuid: randomLenNum(10),
              detailDTOList: [
                {
                  classify: '默认分类',
                  uuid: randomLenNum(10),
                  detailType: 1,
                  number: isNaN(item.number) || !item.number ? '' : parseFloat(item.number),
                },
              ],
            },
          ];

          form.value.currentModule = 1;
          setTableData();
          setCateGoryList();
        } else {
          moduleDTOList.value = [
            { moduleName: '汇总', uuid: randomLenNum(10), detailDTOList: [] },
            {
              moduleName: '默认子项',
              uuid: randomLenNum(10),
              detailDTOList: [
                {
                  classify: '默认分类',
                  classifySort: 0,
                  uuid: randomLenNum(10),
                  detailType: 1,
                },
                ...form.value.detailTemplateVOS.map(item => {
                  return {
                    ...item,
                    detailType: 0,
                    classify: '默认分类',
                    uuid: item.id || randomLenNum(10),
                    number: isNaN(item.number) || !item.number ? '' : parseFloat(item.number),
                  };
                }),
              ],
            },
          ];
          form.value.currentModule = 1;
          setTableData();
          setCateGoryList();
        }
      }

      //    setTableData()
    }
  },
  { immediate: true }
);

function setCateGoryList() {
  let arr = [];
  if (form.value.currentModule == 0 || !form.value.currentModule) return [];
  moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
    if (arr.includes(item.classify)) return;
    arr.push(item.classify);
  });
  categoryList.value = arr;
}
function handleBlur(value, item) {
  //   currentIndex.value = null;
}

function setTableData(c) {
  if (form.value.currentModule == 0) {
    tableData.value = [];
    return;
  }
  setCategotyAndProductDrag();
  const currentData = moduleDTOList.value[form.value.currentModule];
  tableData.value = currentData.detailDTOList.reduce((pre, cur) => {
    if (pre.findIndex(item => item.classify === cur.classify) === -1) {
      pre.push({
        ...cur,
        classify: cur.classify,
        productList: [cur],
      });
    } else {
      pre.find(item => item.classify === cur.classify).productList.push(cur);
    }
    return pre;
  }, []);
  proxy.$nextTick(() => {
    for (let i = 0; i < tableData.value.length; i++) {
      tableData.value[i].productList = tableData.value[i].productList.filter(
        item => item.detailType == 0
      );
      setProductDrag(i);
    }
  });
  if (typeof c == 'function') {
    c();
  }
}
let allData = computed(() => {
  return moduleDTOList.value
    .filter((item, index) => index != 0)
    .map(item => {
      const list = item.detailDTOList.filter(item => item.detailType == 0);
      const {
        totalProductPrice,
        totalLaborCost,
        totalYbPrice,
        totalQtPrice,
        totalRgcbPrice,
        totalCostPrice,
        totalQtcbPrice,
        totalYbcbPrice,
      } = {
        totalProductPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.sealPrice;
          }, 0)
          .toFixed(2),
        totalRgcbPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.rgcbdj;
          }, 0)
          .toFixed(2),
        totalCostPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * (cur.specialCostPrice || cur.costPrice);
          }, 0)
          .toFixed(2),
        totalLaborCost: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.laborCost;
          }, 0)
          .toFixed(2),
        totalYbPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.ybhsdj;
          }, 0)
          .toFixed(2),
        totalQtPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.qthsdj;
          }, 0)
          .toFixed(2),
        totalYbcbPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.ybcbdj;
          }, 0)
          .toFixed(2),
        totalQtcbPrice: list
          .reduce((pre, cur) => {
            return pre + cur.number * cur.qtcbdj;
          }, 0)
          .toFixed(2),
      };
      const { taxPrice } = {
        // 税金
        taxPrice: (
          totalProductPrice * 1 * form.value.productRate +
          totalLaborCost * 1 * form.value.labourRate +
          totalYbPrice * 1 * form.value.warrantyRate +
          totalQtPrice * 1 * form.value.otherRate
        ).toFixed(2),
      };

      return {
        // 设备金额
        totalProductPrice,
        // 人工金额
        totalLaborCost,
        // 延保金额
        totalYbPrice,
        // 其他金额
        totalQtPrice,
        // 人工成本
        totalRgcbPrice,
        // 设备成本
        totalCostPrice,
        // 延保成本
        totalYbcbPrice,
        // 其他成本
        totalQtcbPrice,
        taxPrice,
        // 报价合计
        total: (
          totalProductPrice * 1 +
          totalLaborCost * 1 +
          totalYbPrice * 1 +
          totalQtPrice * 1 +
          (form.value.isHasTax ? 0 : taxPrice * 1)
        ).toFixed(2),

        //成本合计
        costTotal:
          totalRgcbPrice * 1 + totalCostPrice * 1 + totalYbcbPrice * 1 + totalQtcbPrice * 1,
        moduleName: item.moduleName,
      };
    });
});
let isFold = ref(false);

function addModule(row = {}, type) {
  proxy.$refs.dialogForm.show({
    title: type ? '编辑' : '新增',
    option: {
      column: [
        {
          type: 'input',
          label: '模块名称',
          span: 24,
          prop: 'moduleName',
          value: row.moduleName,
          rules: [
            {
              required: true,
              message: '请输入模块名称',
              trigger: 'blur',
            },
          ],
        },
        {
          type: 'textarea',
          label: '备注',
          span: 24,
          value: row.remark,
          prop: 'remark',
        },
      ],
    },
    callback(res) {
      if (type) {
        row.remark = res.data.remark;
        row.moduleName = res.data.moduleName;
      } else {
        moduleDTOList.value.push({
          moduleName: res.data.moduleName,
          remark: res.remark,
          uuid: randomLenNum(10),
          detailDTOList: [],
        });
        form.value.currentModule = moduleDTOList.value.length - 1;
        setTableData();
        setCateGoryList();
      }
      res.close();
    },
  });
}
// 添加分类
function addCategory() {
  const currentData = moduleDTOList.value[form.value.currentModule];
  proxy.$refs.dialogForm.show({
    title: '新增',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          rules: [
            {
              required: true,
              message: '请输入分类名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      currentData.detailDTOList.push({
        classify: res.data.classify,
        detailType: 1,
        classifySort: categoryList.value.length,
      });
      setTableData();
      setCateGoryList();
      res.close();
    },
  });
}
async function handleProductSelectConfirm(ids) {
  console.log(ids);
  const res = await axios.get('/api/vt-admin/product/detailByIds', {
    params: {
      idList: ids,
    },
  });

  const data = res.data.data
    .map(item => {
      return {
        customProductName: item.productName,
        customProductSpecification: item.productSpecification,
        customProductDescription: item.description,
        productId: item.id,
        customUnit: item.unitName,
        customProductBrand: item.productBrand,
        classify: form.value.currentclassify,
        ...item,
        id: null,
        detailType: 0,
        source: 0,
        number: '',
        rgcbdj: '',
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        uuid: randomLenNum(10),
        classifySort: categoryList.value.findIndex(item => item == form.value.currentclassify),
      };
    })
    .reverse();

  moduleDTOList.value[form.value.currentModule].detailDTOList.push(...data);
  setTableData();
  setCateGoryList();
}
// 删除分类
function delCategory(classify) {
  proxy
    .$confirm('是否删除该分类?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      moduleDTOList.value[form.value.currentModule].detailDTOList = moduleDTOList.value[
        form.value.currentModule
      ].detailDTOList.filter(item => item.classify != classify);
      setTableData();
      setCateGoryList();
    })
    .catch(() => {});
}
// 复制分类
function copyCategory(classify) {
  proxy
    .$confirm('是否复制该分类?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      const randomLenNumValue = randomLenNum(5);
      const data = [];
      moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
        if (item.classify == classify) {
          const itemCopy = {
            ...item,
            classify: item.classify + `--${randomLenNumValue}`,
            classifySort: categoryList.value.length,
            uuid: randomLenNum(10),
            id: null,
          };
          data.push(itemCopy);
        }
      });
      moduleDTOList.value[form.value.currentModule].detailDTOList.push(...data);
      setTableData();
      setCateGoryList();
    })
    .catch(() => {});
}

function editCategory(classify) {
  proxy.$refs.dialogForm.show({
    title: '编辑',
    option: {
      column: [
        {
          type: 'input',
          label: '分类名称',
          span: 24,
          prop: 'classify',
          value: classify,
          rules: [
            {
              required: true,
              message: '请输入模块名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      moduleDTOList.value[form.value.currentModule].detailDTOList.forEach(item => {
        if (item.classify == classify) {
          item.classify = res.data.classify;
        }
      });
      setTableData();
      setCateGoryList();
      res.close();
    },
  });
}
// 删除模块
function delModule() {
  proxy
    .$confirm('是否删除该模块?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      moduleDTOList.value.splice(form.value.currentModule, 1);
      form.value.currentModule = 1;
      setTableData();
    })
    .catch(() => {});
}

function handleScroll(index) {
  console.log(proxy.$refs[index + '-cateGory'][0]);
  proxy.$refs[index + '-cateGory'][0].scrollIntoView({
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest',
  });
}

// 列显示隐藏
let colmunDrawer = ref(false);
let columnHideData = ref([
  // {
  //   value :'产品名称',
  //   isShow:true,
  // },
  // {
  //   value :'品牌',
  //   isShow:true,
  // },
  // {
  //   value :'规格型号',
  //   isShow:true,
  // },
  // {
  //   value :'详细描述',
  //   isShow:true,
  // },
  // {
  //   value :'产品图片',
  //   isShow:true,
  // },
  // {
  //   value :'单位',
  //   isShow:true,
  // },
  // {
  //   value :'数量',
  //   isShow:true,
  // },
  {
    value: '设备单价',
    isShow: true,
    width: 80,
  },

  {
    value: '设备金额',
    isShow: true,
    width: 80,
  },

  {
    value: '人工单价',
    isShow: true,
    width: 80,
  },

  {
    value: '人工金额',
    isShow: true,
    width: 80,
  },

  {
    value: '其他单价',
    isShow: false,
    width: 80,
  },

  {
    value: '其他金额',
    isShow: false,
    width: 80,
  },

  {
    value: '延保单价',
    isShow: false,
    width: 80,
  },

  {
    value: '延保金额',
    isShow: false,
    width: 80,
  },

  {
    value: '备注',
    isShow: true,
    width: 150,
    align: 'left',
  },

  {
    value: '设备成本单价',
    isShow: true,
    width: 100,
  },
  {
    value: '设备成本金额',
    isShow: true,
    width: 100,
  },
  {
    value: '人工成本单价',
    isShow: true,
    width: 100,
  },
  {
    value: '人工成本金额',
    isShow: true,
    width: 100,
  },
  {
    value: '其他成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '其他成本金额',
    isShow: false,
    width: 100,
  },
  {
    value: '延保成本单价',
    isShow: false,
    width: 100,
  },
  {
    value: '延保成本金额',
    isShow: false,
    width: 100,
  },

  {
    value: '专项成本',
    isShow: true,
    width: 100,
  },
  {
    value: '专项供应商',
    isShow: true,
    width: 100,
  },
]);
// const tableWidth = computed(() => {
//   const value = columnHideData.value
//     .filter(item => item.isShow)
//     .reduce((pre, cur) => {
//       return pre + cur.width;
//     }, 0);
//   return `${value}px`;
// });

function isTrue(value) {
  return columnHideData.value.find(item => item.value == value)?.isShow;
}

// 设置参考信息
let referInfo = ref({});
let currentProduct = ref({});
let preItem = ref({});
function setReferPrice(item) {
  preItem.value.height = 20;
  item.height = 100;
  preItem.value = item;
  referInfo.value = {
    productName: item.customProductName,
    preSealPrice: item.preSealPrice,
    minSealPrice: item.minSealPrice,
    referSealPrice: item.referSealPrice,
    customProductDescription: item.customProductDescription,
  };
  currentProduct.value = item;
}

onMounted(() => {
  if (proxy.$route.query.type != 'detail') {
    setSort();
  }

  form.value.currentModule = 0;
});
// 拖拽排序
const setSort = () => {
  // 设置 子系统拖拽
  console.log(proxy.$refs);
  const el = proxy.$refs['sort-buttons'].$el;

  new Sortable(el, {
    // handle: '.move1',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      console.log(evt);

      const targetRow = moduleDTOList.value.splice(evt.oldIndex, 1);
      console.log(form.value.currentModule, evt.newIndex, evt.oldIndex);
      // if (!targetRow[0]) return;
      moduleDTOList.value.splice(evt.newIndex, 0, targetRow[0]);
      if (form.value.currentModule == evt.oldIndex) {
        form.value.currentModule = evt.newIndex;
      } else if (form.value.currentModule == evt.newIndex) {
        form.value.currentModule = evt.oldIndex;
      }
      setTableData();
    },
  });
};
function setCategotyAndProductDrag(params) {
  // 设置分类拖拽
  const el = proxy.$refs.category_box;

  new Sortable(el, {
    handle: '.sort',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      const targetRow = tableData.value.splice(evt.oldIndex, 1);
      tableData.value.splice(evt.newIndex, 0, targetRow[0]);
      moduleDTOList.value[form.value.currentModule].detailDTOList = tableData.value.reduce(
        (acc, cur, index) => {
          cur.productList.forEach(item => {
            item.classifySort = index;
          });
          acc.push({ ...cur, productList: null, classifySort: index });
          acc.push(...cur.productList);
          return acc;
        },
        []
      );
      
      console.log(moduleDTOList.value);
      setTableData();
      setCateGoryList();
    },
  });
}
function setProductDrag(ins) {
  console.log(proxy.$refs, `${ins}-cateGory`);
  // 设置产品拖拽
  const el = proxy.$refs[`${ins}-cateGory`][0];
  console.log(el);
  new Sortable(el, {
    handle: '.sort',
    animation: 180,
    delay: 0,
    put: true,
    onEnd: evt => {
      const newRow = tableData.value[ins].productList[evt.newIndex - 1];
      
      const targetRow = tableData.value[ins].productList[evt.oldIndex - 1];
      const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
        item => item.uuid == targetRow.uuid
      );
      // const row = moduleDTOList[form.value.currentModule].detailDTOList[index]
      const newIndex = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
        item => item.uuid == newRow.uuid
      );
      const row = moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      moduleDTOList.value[form.value.currentModule].detailDTOList.splice(newIndex, 0, row[0]);
      setTableData(setRandomKey);
    },
  });
}
// 自定义序号
function customIndex(index, parentIndex) {
  console.log(index, parentIndex);
  let i = 0;
  if (parentIndex == 0) {
    i = index + 1;
  } else {
    let sum = 0;
    for (let i = 0; i < parentIndex; i++) {
      sum += tableData.value[i].productList.filter(item => item.detailType == 0).length;
    }
    // 返回序号
    i = sum + index + 1;
  }

  return i;
}

function deleteProduct(i) {
  proxy
    .$confirm('此操作将删除该产品, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      if (i.id) {
        const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
          item => item.id == i.id
        );
        moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      } else {
        const index = moduleDTOList.value[form.value.currentModule].detailDTOList.findIndex(
          item => item.uuid == i.uuid
        );
        moduleDTOList.value[form.value.currentModule].detailDTOList.splice(index, 1);
      }
      setTableData();
    })
    .catch(() => {});
}

function getData() {
  let value;
  if (form.value.templateType != 2 && form.value.templateType != 3) {
    value = {
      ...form.value,
      moduleDTOS: moduleDTOList.value
        .filter((item, index) => index != 0)
        .map((item, index) => {
          return {
            ...item,
            classifyDTOS: item.detailDTOList.reduce((pre, cur, index) => {
              if (cur.detailType == 1) {
                const detailDTOS = item.detailDTOList
                  .filter(item => item.classify == cur.classify && item.detailType != 1)
                  .map(item => {
                    return {
                      ...item,
                      sortNumber: index,
                    };
                  });
                pre.push({
                  ...cur,
                  classifySort: index,
                  detailDTOS,
                });
              }
              return pre;
            }, []),
            sortNumber: index,
          };
        }),
    };
  } else {
    if (form.value.templateType == 3) {
      value = {
        ...form.value,

        detailDTOS: moduleDTOList.value[1].detailDTOList
          .filter(item => item.detailType != 1)
          .map((item, index) => {
            return {
              ...item,
              sortNumber: item.index,
            };
          }),
      };
    } else {
      value = {
        ...form.value,
        classifyDTO: {
          ...moduleDTOList.value[1].detailDTOList.find(item => item.detailType == 1),
          detailDTOS: moduleDTOList.value[1].detailDTOList
            .filter(item => item.detailType != 1)
            .map((item, index) => {
              return {
                ...item,
                sortNumber: item.index,
              };
            }),
        },
      };
    }
  }
  return value;
}
function handleRowClick(item) {
  return;
  if (proxy.$route.query.type == 'detail') return;
  currentIndex.value = item.classify;
}

let randomKey = ref(null);
function setRandomKey() {
  randomKey.value = randomLenNum(10);
}

function isNumber(str) {
  return /^[0-9]*(\.[0-9]+)?$/.test(str);
}
function copyModule() {
  const { moduleName, detailDTOList } = moduleDTOList.value[form.value.currentModule];
  moduleDTOList.value.push({
    moduleName: moduleName + '复制',
    detailDTOList: deepClone(
      detailDTOList.map(item => {
        return {
          uuid: randomLenNum(10),
          ...item,
          id: null,
        };
      })
    ),
  });
}

function addEmptyProduct(item) {
  form.value.currentclassify = item.classify;
  const data = {
    detailType: 0,
    classify: form.value.currentclassify,
    id: null,
    detailType: 0,
    costPrice: 0,
    source: 3,
    sealPrice: '',
    rgcbdj: 0,
    ybcbdj: 0,
    qtcbdj: 0,
    ybhsdj: 0,
    qthsdj: 0,
    sealPrice: 0,
    laborCost: 0,
    remark: '',
    uuid: randomLenNum(10),
  };

  moduleDTOList.value[form.value.currentModule].detailDTOList.push(data);
  setTableData();
  setCateGoryList();
}
function relationCategory(i) {
  proxy.$refs.dialogForm.show({
    title: '关联产品三级分类',
    option: {
      column: [
        {
          label: '分类名称',
          prop: 'categoryId',
          type: 'tree',
          span: 24,
          dicUrl: '/api/vt-admin/productCategory/tree',
          parent: false,
          value: i.categoryId,
          props: {
            label: 'categoryName',
            value: 'id',
            children: 'children',
          },
          expandOnClickNode: true,
          rules: [
            {
              required: true,
              message: '请选择分类名称',
              trigger: 'blur',
            },
          ],
        },
      ],
    },
    callback(res) {
      i.categoryId = res.data.categoryId;
      axios.get('/api/vt-admin/productCategory/detail?id=' + i.categoryId).then(r => {
        i.categoryName = r.data.data.categoryName;
        res.close();
      });
    },
  });
}
let brandList = ref([]);

function getBrand(id) {
  axios.get('/api/vt-admin/product/getBrandListByCategory?categoryId=' + id).then(res => {
    brandList.value = res.data.data;
  });
}
let productSpecificationList = ref([]);
function getProductSpecificationList(i) {
  axios
    .get('/api/vt-admin/product/getSpecificationListByCategory', {
      params: {
        categoryId: i.categoryId,
        productBrand: i.productBrand,
      },
    })
    .then(res => {
      productSpecificationList.value = res.data.data;
    });
}
function handleSpecificationClick(i, item) {
  i.customProductName = item.productName;
  i.customProductSpecification = item.productSpecification;
  i.customProductDescription = item.description;
  // i.productId = item.id;
  i.customUnit = item.unitName;
  i.productBrand = item.productBrand;
}
function handleProductBrandClick(i, item) {
  console.log(i, item);
  i.customProductName = '';
  i.customProductSpecification = '';
  i.customProductDescription = '';
  // i.productId = item.id;
  i.customUnit = '';
  i.productBrand = item;
}
let templateType = ref(null)
function addTemplate(val) {
  templateType.value = val;
  proxy.$nextTick(() => {
    proxy.$refs.templateDialog.open();
  });
}
// 添加模板
async function handleTemplateChange(id, done) {
  const res = await axios.get('/api/vt-admin/optionTemplate/detailForAdd', {
    params: {
      id,
    },
  });

  const currentData = moduleDTOList.value[form.value.currentModule];
  if (templateType.value == 1) {
    // 子系统模块
    const { moduleName, remark, optionModuleDetailTemplateVOS } =
      res.data.data.moduleTemplateVOS[0];
    if (moduleDTOList.value.some(item => item.moduleName == moduleName))
      return proxy.$message.warning(`已存在模块：${moduleName},请先修改`);
    moduleDTOList.value.push({
      moduleName: moduleName,
      remark: remark,
      uuid: randomLenNum(10),
      detailDTOList: optionModuleDetailTemplateVOS.map(item => {
        return {
          ...item,
          ...item.productVO,
          productBrand: item.productBrand,
          sealPrice: '',
          rgcbdj: '',
          ybcbdj: '',
          qtcbdj: '',
          ybhsdj: '',
          qthsdj: '',
          source: 1,
          sealPrice: '',
          laborCost: '',
          id: null,
        };
      }),
    });
    done();
  } else if (templateType.value == 2) {
    //子分类模块
    const { classify, optionModuleDetailTemplateVOS } = res.data.data
      .optionModuleClassifyTemplateVO || {
      classify: '',
      optionModuleDetailTemplateVOS: [],
    };

    if (categoryList.value.some(item => item == classify))
      return proxy.$message.warning(`已存在分类：${classify},请先修改`);
    console.log(classify, optionModuleDetailTemplateVOS);
    // 1。添加分类
    currentData.detailDTOList.push({
      classify: classify,
      detailType: 1,
      classifySort: categoryList.value.length,
    });
    // 2.添加产品
    const data = optionModuleDetailTemplateVOS.map(item => {
      return {
        ...item.productVO,
        productId: item.id,
        classify: classify,
        ...item,
        id: null,
        detailType: 0,
        sealPrice: '',
        rgcbdj: '',
        source: 1,
        ybcbdj: '',
        qtcbdj: '',
        ybhsdj: '',
        qthsdj: '',
        sealPrice: '',
        laborCost: '',
        remark: '',
        uuid: randomLenNum(10),
        classifySort: categoryList.value.length,
      };
    });
    done();
    currentData.detailDTOList.push(...data);
    setTableData();
    setCateGoryList();
  }
}
defineExpose({
  getData,
});
</script>

<style lang="scss" scoped>
.wrap {
  height: 100%;
  width: calc(100%);
  // background: #eee;
  box-sizing: border-box;
  padding: 15px;
  margin-bottom: 0;
  border-radius: 5px;
  .barbox {
    display: flex;
    align-items: center;
    padding-left: 10px;
  }
  .box {
    height: 100%;
    width: 100%;
  }
}
table {
  width: 100%;
  border-collapse: collapse;
  //   margin: 25px 0;
  //   height: 100%;
  font-size: 0.9em;
  min-width: 400px;
  color: #111;
  border-radius: 5px 5px 0 0;
  border-color: #ccc;
  table-layout: auto;
  // overflow: hidden;
  // box-shadow: 0 0 20px rgba(0, 0, 0, 0.15);
}
th,
td {
  //   padding: 5px;
  text-align: left;
  height: 25px;
  white-space: nowrap;
  word-break: keep-all;
  overflow: hidden;
  text-overflow: ellipsis;
  border-bottom: 1px solid #dddddd;
  // border-bottom: 1px solid #dddddd;
}
.active {
  white-space: wrap;
  overflow: visible;
  text-overflow: clip;
  height: auto;
  line-height: 25px;
  border-bottom: 1px solid #dddddd;
}
thead {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
  // border-bottom: 1px solid #dddddd;
  td {
    // word-break: unset;
    white-space: unset;
  }
}
th {
  // background-color: #009879;
  color: #ffffff;
  text-align: center;
}
tr {
  // background-color: #f3f3f3;
  height: 25px;
}
.category {
  background-color: var(--el-color-primary-light-8);
  .delete_category {
    display: none;
  }
  &:hover .delete_category {
    display: inline-block;
  }
}
.cell_hover:hover {
  background-color: var(--el-color-info-light-8);
}

.index_product {
  &:hover .delete_product {
    display: inline-block;
  }
  &:hover .index_product_span {
    display: none;
  }
  .delete_product {
    display: none;
    cursor: pointer;
  }
}
.left {
  text-align: left;
}
.center {
  text-align: center;
}
.right {
  text-align: right;
}
.error {
  background-color: var(--el-color-danger-light-8);
}
.select_main {
  height: 10px;
  // border: 1px solid red
}
:deep(.el-popover.el-popper) {
  padding: 0;
}
.el-select-dropdown__item {
  padding: 0 !important;
}
</style>
