<template>
  <div style="width: 100%; display: flex">
    <el-select
      v-model="name"
      remote
      style="width: 100%"
      reserve-keyword
      @change="handleChange"
      :placeholder="placeholder || '请输入名字'"
      :loading="loading"
    >
      <el-option
        v-for="item in invoiceList"
        :key="item.id"
        :label="item.invoiceCompanyName"
        :value="item.id"
      >
        <el-form inline label-position="left">
          <el-form-item label="客户公司名称：">
            <el-tag effect='plain'>{{ item.invoiceCompanyName }}</el-tag>
          </el-form-item>
          <el-form-item label="纳税人识别号：">
            <el-tag effect='plain'>{{ item.ratepayerIdentifyNumber }}</el-tag>
          </el-form-item>
          <el-form-item label="开户银行：">
            <el-tag effect='plain'>{{ item.bankName }}</el-tag>
          </el-form-item>
          <el-form-item label="初期末开票余额：">
            <el-text type="primary">{{
              parseFloat(item.endTermNoInvoiceAmount).toLocaleString()
            }}</el-text>
          </el-form-item>
        </el-form>
      </el-option>
    </el-select>
    <el-button icon="plus" :size="size" type="primary" @click="drawer = true" v-if="this.id"
      >新增</el-button
    >
  </div>
  <el-drawer append-to-body title="新增开票信息" size="50%" v-model="drawer">
    <avue-form :option="addOption" v-model="addForm" @submit="submit"></avue-form>
  </el-drawer>
</template>
<script>
//   import { getUser } from '../../../api/process/user';

//   import NfUserSelect from '../../nf-user-select/index.vue';

export default {
  name: 'user-drop',
  components: {},
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      default: () => {
        return 'default';
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    userUrl: {
      type: String,
      default: () => {
        return '/vt-admin/customerInvoiceInfo/page';
      },
    },
    tenantId: String,
    change: Function,
    id: String,
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          this.name = val;
        } else {
          this.name = '';
        }
      },
      immediate: true,
    },
    userUrl: {
      handler(val) {
        if (val && this.id) {
          this.getInvoiceList();
        }
      },
      immediate: true,
    },
    id: {
      handler(val) {
        if (val) {
          this.getInvoiceList();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      invoiceList: [],
      loading: false,
      drawer: false,
      addForm: {},
      addOption: {
        labelWidth: 140,
        // height:'auto',
        // calcHeight:30,
        size: 'large',
        border: true,
        column: [
          {
            label: '客户公司名称',
            prop: 'invoiceCompanyName',
            span: 24,
            rules: [{ required: true, message: '请输入开票公司名称', trigger: 'blur' }],
          },
          {
            label: '纳税人识别号',
            prop: 'ratepayerIdentifyNumber',
            span: 24,
            rules: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
          },
          {
            label: '开户银行',
            prop: 'bankName',
            span: 24,
            rules: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
          },
          {
            label: '银行账号',
            prop: 'bankAccount',
            span: 24,
            rules: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
          },
          {
            label: '注册地址',
            span: 24,
            prop: 'bankAddress',
          },
          {
            label: '电话',
            prop: 'phone',
            span: 24,
          },
          {
            label: '期初末开票余额(元)',
            prop: 'endTermNoInvoiceAmount',
            type: 'number',
            span: 24,
          },
        ],
      },
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['user-select'].visible = true;
    },
    // remoteMethod(value) {
    //   if (value === '') return (this.nameList = []);
    //   this.loading = true;
    //   axios.get(`${userUrl}?customerId=` + value).then(res => {
    //     this.nameList = res.data.data.records;
    //     this.loading = false;
    //   });
    // },
    handleChange(val) {
      console.log(val);
      this.$emit('update:modelValue', val);
    },
    getInvoiceList() {
      if (!this.id || !this.userUrl) return;
      console.log(this.userUrl);
      axios
        .get(`${this.userUrl}?customerId=${this.id}`, {
          params: {
            size: 500,
          
          },
        })
        .then(res => {
          this.invoiceList = res.data.data.records;
          this.loading = false;
        });
    },
    // handleUserSelectConfirm(id) {
    //   console.log(id);
    //   this.$emit('update:modelValue', id);
    //   if (this.change && typeof this.change == 'function') this.change({ value: id });
    // },
    // handleBlur(e) {
    //   let value = e.target.value; // 输入框值
    //   if (value) {
    //     // 只有输入才有这个值，下拉框选择的话 这个值为空
    //     this.name = value;
    //     this.$emit('update:modelValue', value);
    //     if (this.change && typeof this.change == 'function') this.change({ value: value });
    //   }
    // },
    submit(form, done, loading) {
      const data = {
        ...form,
        customerId: this.id,
      };
      axios
        .post('/api/vt-admin/customerInvoiceInfo/save', data)
        .then(res => {
          if (res.data.code == 200) {
            this.$message.success(res.data.msg);
            this.drawer = false;
            this.getInvoiceList();
            done();
          }
        })
        .catch(err => {
          done();
        });
    },
  },
};
</script>
<style lang="scss"></style>
