<template>
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @row-del="rowDel"
        @search-reset="reset"
        @search-change="searchChange"
        @current-change="onLoad"
        @refresh-change="onLoad"
        @keyup.enter="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
       
        <template #orderStatus="{ row }">
          <el-tag effect="plain" size="small" v-if="row.orderStatus == 0" type="warning"
            >待采购</el-tag
          >
          <el-tag effect="plain" size="small" type="warning" v-else-if="row.orderStatus == 2"
            >询价中</el-tag
          >
          <el-tag effect="plain" size="small" v-if="row.orderStatus == 1" type="warning"
            >采购中</el-tag
          >
          <el-tag effect="plain" size="small" type="success" v-else-if="row.orderStatus == 3"
            >采购完成</el-tag
          >
        </template>
        <template #menu="{ row }">
            <el-button type="primary" icon="view" @click="viewProduct(row)" text>明细</el-button>
        </template>
       
        <template #number="{ row }">
          <el-link type="primary" @click="viewProduct(row)" size="large" style="font-size: 20px">{{
            row.number
          }}</el-link>
        </template>
      </avue-crud>
      <dialogForm ref="dialogForm"></dialogForm>
  
   
   
      <el-drawer title="查看产品" v-model="productDrawer" direction="rtl" size="60%">
        <h3>自服务产品</h3>
        <avue-crud :option="productOption" :data="productData.filter(item => item.isSelfService == 1)"></avue-crud>
        <h3>其它</h3>
        <avue-crud :option="productOption" :data="productData.filter(item => item.isSelfService == 0)"></avue-crud>
      </el-drawer>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted, onActivated } from 'vue';
  import { useRoute, useRouter } from 'vue-router';

  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: false,
    editBtn: false,
    // selection:true,
    delBtn: false,
    calcHeight: 30,
    searchMenuSpan: 4,
    searchIcon: true,
    searchIndex: 4,
    searchSpan: 4,
    menuWidth: 100,
    border: true,
    column: [
      {
        label: '订单编号',
        prop: 'orderNo',
        width: 200,
        overHidden: true,
        search: true,
      },
      {
        label: '报价名称',
        prop: 'offerName',
        overHidden: true,
        search: true,
      },
      {
        label: '客户名称',
        prop: 'customerName',
        overHidden: true,
        search: true,
        component:'wf-customer-drop',
      },
      {
        label: '业务员',
        prop: 'businessName',
        component: 'wf-user-drop',
        search: true,
        width:80
        // hide: true,
      },
    //   {
    //     label: '交付时间',
    //     prop: 'contractDeliveryDate',
    //     valueFormat: 'YYYY-MM-DD',
    //     // search: true,
    //     width: 120,
    //   },
  
      {
        label: '采购数量',
        prop: 'number',
        width: 90,
      },
  
      {
        label: '下单时间',
        prop: 'orderDate',
        type: 'date',
        component: 'wf-daterange-search',
        search: true,
        overHidden: true,
        format: 'YYYY-MM-DD HH:mm',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        searchSpan: 5,
        search: true,
        width: 150,
      },
    //   {
    //     label: '采购人',
    //     prop: 'purchaseName',
    //     component: 'wf-user-select',
    //     searchSpan: 3,
    //     width:80
    //     // search: true,
    //   },
      {
        label: '采购类型',
        prop: 'purchaseType',
        type: 'select',
        width: 100,
        dicData: [
          {
            value: 0,
            label: '订单采购',
          },
          {
            value: 1,
            label: '项目采购',
          },
          {
            value: 2,
            label: '储备采购',
          },
        ],
  
        search: true,
        searchSpan: 3,
        width: 100,
        // search: true,
      },
    
    ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  
  const tableUrl = '/api/vt-admin/purchaseOrder/pageForSelf';
  let route = useRoute();
  let params = ref({
    orderStatus: route.query.type == 19 ? 0 : route.query.type == 20 ? 1 : '',
  });
  watch(
    () => route.query.ids,
    val => {
      params.value = {
        orderStatus: route.query.type == 19 ? 0 : route.query.type == 20 ? 1 : '',
      };
      onLoad();
    }
  );
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  
  onMounted(() => {
    onLoad();
  });
  onActivated(() => {
    onLoad();
  });
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    const { pageSize: size, currentPage: current } = page.value;
    axios
      .get(tableUrl, {
        params: {
          size,
          current,
          ...params.value,
          customerId: route.query.id || null,
          orderStartDate: params.value.orderDate && params.value.orderDate[0],
          orderEndDate: params.value.orderDate && params.value.orderDate[1],
          orderDate: null,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data.records;
        page.value.total = res.data.data.total;
      });
  }
  let router = useRouter();
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  function toDetail(row, activeName = null) {
    router.push({
      path: '/SRM/procure/compoents/orderDetail',
      query: {
        id: row.id,
        activeName,
      },
    });
  }

  

  

  function reset() {
    page.value.currentPage = 1;
    params.value.orderStatus = null;
    onLoad();
  }
 
  
  
  let productDrawer = ref(false);
  let productOption = {
    menu: false,
    selection: false,
    tip: false,
    border: true,
    header: false,
    column: [
      {
        label: '产品名称',
        prop: 'customProductName',
        overHidden: true,
        cell: false,
        formatter: row => {
          return row.customProductName || row.productVO?.productName;
        },
      },
      {
        label: '规格型号',
        prop: 'customProductSpecification',
        overHidden: true,
        cell: false,
        span: 24,
        type: 'input',
        formatter: row => {
          return row.customProductSpecification || row.productVO?.productSpecification;
        },
      },
  
      {
        label: '品牌',
        cell: false,
        prop: 'productBrand',
        overHidden: true,
        formatter: row => {
          return row.productBrand || row.productVO?.productBrand;
        },
      },
      {
        label: '单位',
        type: 'select',
        cell: false,
        width: 80,
        props: {
          label: 'dictValue',
          value: 'id',
          desc: 'desc',
        },
        rules: [
          {
            required: true,
            message: '请选择单位',
            trigger: 'blur',
          },
        ],
  
        prop: 'customUnit',
        dicUrl: '/blade-system/dict/dictionary?code=unit',
        remote: false,
        formatter: row => {
          return row.customUnit || row.productVO?.unitName;
        },
      },
      {
        label: '数量',
        prop: 'number',
        type: 'number',
        rules: [
          {
            required: true,
            message: '请输入数量',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '备注',
        prop: 'remark',
        type: 'inpput',
      },
    ],
  };
  let productData = ref([]);
  function viewProduct(row) {
    axios
      .get('/api/vt-admin/purchaseOrder/detailForSelf', {
        params: {
          id: row.id,
        },
      })
      .then(res => {
        productData.value = res.data.data.detailList
        productDrawer.value = true;
      });
  }
  </script>
  
  <style lang="scss" scoped>
  :deep(.el-table--small .cell) {
    padding: 0!important;
  }
  </style>
  