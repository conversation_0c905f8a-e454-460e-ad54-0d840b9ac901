<template>
  <div style="width: 100%">
    <el-select
      v-model="name"
      remote
      style="width: 100%"
      reserve-keyword
      @change="handleChange"
      :placeholder="placeholder || '请选择发票'"
      :loading="loading"
    >
      <el-option
        v-for="item in invoiceList"
        :key="item.id"
        :label="item.invoiceCode"
        :value="item.id"
      >
        <el-form inline label-position="left">
          <el-form-item label="发票代码：">
            <el-tag effect='plain'>{{ item.invoiceCode }}</el-tag>
          </el-form-item>
          <el-form-item label="发票号码：">
            <el-tag effect='plain'>{{ item.invoiceNumber }}</el-tag>
          </el-form-item>
          <el-form-item label="发票金额：">
            <el-tag effect='plain'>{{ item.invoicePrice }}</el-tag>
          </el-form-item>
        </el-form>
      </el-option>
    </el-select>
  </div>
</template>
<script>
//   import { getUser } from '../../../api/process/user';

//   import NfUserSelect from '../../nf-user-select/index.vue';

export default {
  name: 'user-drop',
  components: {},
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: {
      // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio';
      },
    },
    size: {
      type: String,
      default: () => {
        return 'small';
      },
    },
    readonly: {
      type: Boolean,
      default: false,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    placeholder: String,
    userUrl: {
      type: String,
      default: () => {
        return '/vt-admin/sealContractInvoice/page';
      },
    },
    tenantId: String,
    change: Function,
    id: String,
  },
  watch: {
    modelValue: {
      handler(val) {
        if (val) {
          this.name = val;
        } else {
          this.name = '';
        }
      },
      immediate: true,
    },
    userUrl: {
      handler(val) {
        console.log(val,this.id);
        if (val && this.id) {
          this.getInvoiceList();
        }
      },
      immediate: true,
    },
    id: {
      handler(val) {
        if (val) {
          this.getInvoiceList();
        }
      },
      immediate: true,
    },
  },
  data() {
    return {
      name: '',
      invoiceList: [],
      loading: false,
    };
  },
  methods: {
    handleSelect() {
      if (this.readonly || this.disabled) return;
      else this.$refs['user-select'].visible = true;
    },
    handleChange(val) {
      console.log(val);
      this.$emit('update:modelValue', val);
    },
    // remoteMethod(value) {
    //   if (value === '') return (this.nameList = []);
    //   this.loading = true;
    //   axios.get(`${userUrl}?customerId=` + value).then(res => {
    //     this.nameList = res.data.data.records;
    //     this.loading = false;
    //   });
    // },
    getInvoiceList() {
      axios.get(`${this.userUrl}?sealContractId=${this.id}&size=500`,{
        params:{
          isPlan: 0,
        }
      }).then(res => {
        this.invoiceList = res.data.data.records;
        this.loading = false;
      });
    },
    // handleUserSelectConfirm(id) {
    //   console.log(id);
    //   this.$emit('update:modelValue', id);
    //   if (this.change && typeof this.change == 'function') this.change({ value: id });
    // },
    // handleBlur(e) {
    //   let value = e.target.value; // 输入框值
    //   if (value) {
    //     // 只有输入才有这个值，下拉框选择的话 这个值为空
    //     this.name = value;
    //     this.$emit('update:modelValue', value);
    //     if (this.change && typeof this.change == 'function') this.change({ value: value });
    //   }
    // },
  },
};
</script>
<style lang="scss"></style>
