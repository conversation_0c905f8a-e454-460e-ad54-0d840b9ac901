<template>
    <el-row style="padding: 10px;" :gutter="10">
        <el-col :span="24">
          <objectNumberStatisic></objectNumberStatisic>
        </el-col>
    </el-row>
  </template>
  
  <script setup>
  import  objectNumberStatisic from '@/views/desk/components/objectNumberStatisic.vue'
  let form = ref({
    selectType: '0',
  });
  // const store = useStore();
  // const permission = computed(() => {
  //   return store.getters.permission;
  // });
  
  provide('form', form);
  
  
  </script>
  
  <style lang="scss" scoped></style>
  