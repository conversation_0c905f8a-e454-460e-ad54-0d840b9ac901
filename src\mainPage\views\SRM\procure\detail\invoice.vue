<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    :before-open="beforeOpen"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #invoiceFiles="{ row }"> <File :fileList="row.attachList || []"></File></template>
    <template #planName-form>
      <wfPlanDrop v-model="form.contractPlanId" :id="props.id"></wfPlanDrop>
    </template>
    <template #menu="{ row, $index }">
      <el-button type="primary" text icon="edit" @click="$refs.crud.rowEdit(row, $index)"
        >编辑</el-button
      >
      <!-- <el-button type="primary" text icon="delete" @click="$refs.crud.rowDel">删除</el-button> -->
      <!-- <el-button
        type="primary"
        text
        icon="tools"
        @click="authentication(row)"
        v-if="!row.authenticationDate"
        >认证</el-button
      > -->
    </template>
    <template #invoicePrice="{ row }">
        <el-link type="primary" @click="viewDetail(row)">{{ row.invoicePrice }}</el-link>
      </template>
      <template #paymentStatus="{ row }">
        <el-tag effect='plain'
          :type="
            ['danger', 'warning', 'success'][row.paymentStatus == null ? 0 : row.paymentStatus]
          "
        >
          {{ row.$paymentStatus }}
        </el-tag>
      </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-dialog title="金额详情" v-model="detailDrawer" width="80%">
      <avue-crud :option="detailOption" :data="detailForm.detailEntityList"></avue-crud>
    </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted,watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfPlanDrop from '../compoents/wf-plan-drop.vue';
import { dateFormat } from '@/utils/date';
const props = defineProps({
  contractCode: String,
  supplierName: String,
  id: String,
});
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  border: true,
  column: [
    {
      type: 'input',
      label: '合同编号',
      span: 24,
      overHidden: true,
      component: 'wf-purchaseContract-select',
      prop: 'purchaseContractId',
      formatter(row, column, cellValue, index) {
        return row.contractCode;
      },
      change: val => {
        const { value } = val;
        setSupplierName(value);
      },
    },
    // {
    //   type: 'input',
    //   label: '供应商名称',
    //   span: 12,
    //   display: false,
    //   placeholder: '',
    //   prop: 'supplierName',
    //   search: true,
    //   disabled: true,
    //   overHidden: true,
    // },
    {
      type: 'input',
      label: '公司名称',
      span: 24,
      display: true,
      overHidden: true,
      search: true,
      prop: 'companyName',

      // hide: true,
    },
    {
      type: 'input',
      label: '付款计划',
      span: 12,
      display: true,
      prop: 'planName',
    },
    {
      type: 'select',
      label: '付款状态',
      addDisplay: false,
      editDisplay: false,
      span: 12,

      prop: 'paymentStatus',
      dicData: [
        {
          value: null,
          label: '未关联',
        },
        {
          value: 0,
          label: '未付款',
        },
        {
          value: 1,
          label: '部分付款',
        },
        {
          value: 2,
          label: '已付款',
        },
      ],
    },
    {
      type: 'date',
      label: '登记日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      prop: 'createTimeBf',
      formatter(row, column, cellValue, index) {
        return dateFormat(new Date(row.createTime), 'yyyy-MM-dd');
      },
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      component: 'wf-daterange-search',
      search: true,
      display: false,
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDateRange',
      hide: true,
    },
    {
      type: 'date',
      label: '发票日期',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',

      searchSpan: 6,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),

      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },
    {
      type: 'input',
      label: '发票号码',
      span: 12,
      display: true,
      overHidden: true,
      prop: 'invoiceNumber',
    },
    // {
    //   type: 'textarea',
    //   label: '发票内容',
    //   span: 24,
    //   overHidden: true,
    //   display: true,
    //   prop: 'invoiceContent',
    // },
    {
      type: 'input',
      label: '发票金额',
      span: 24,
      overHidden: true,
      display: true,
      prop: 'invoicePrice',
    },
    {
      type: 'input',
      label: '金额信息',
      span: 24,
      display: true,
      hide: true,
      prop: 'detailEntityList',
      type: 'dynamic',
      children: {
        align: 'center',
        headerAlign: 'center',
        rowAdd: done => {
          done();
        },
        rowDel: (row, done) => {
          done();
        },
        column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width:80
          },
          {
            label: '数量',
            prop: 'number',width:80
          },
          {
            label: '单价',
            prop: 'price',width:100
          },
          {
            label: '金额',
            prop: 'productTotalPrice',width:120
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width:100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width:120
          },
          {
            label: '小计',
            prop: 'totalPrice',   width:120
          },
        ],
      },
    },
    {
      type: 'select',
      label: '发票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      search: true,
      display: true,
      overHidden: true,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [
        {
          required: true,
          message: '请选择发票类型',
        },
      ],
    },
    {
      label: '上传发票',
      prop: 'invoiceFiles',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      rules: [
        {
          required: true,
          message: '请上传发票',
        },
      ],
      tip: '先上传发票可以自动填充上面部分类容',
      uploadAfter: (res, done) => {
        console.log(res);
        done();
        const { id } = res;
        axios
          .get('/api/vt-admin/purchaseContractInvoiceDetail/analysisInvoice', {
            params: {
              id,
            },
          })
          .then(res => {
            const {
              date: invoiceDate,
              buyerName: companyName,
              totalAmount: invoicePrice,
              number: invoiceNumber,
            } = res.data.data;
            const list = res.data.data.detailList.map(item => {
              const {
                totalPrice,
                amount: productTotalPrice,
                taxAmount: taxPrice,
                taxRate,
                count: number,
                name: productName,
                model: specification,
                unit: unitName,
                price: price,
              } = item;
              return {
                totalPrice,
                productTotalPrice,
                taxPrice,
                taxRate,
                number,
                productName,
                specification,
                unitName,
                price,
              };
            });
            form.value.detailEntityList = list;
            form.value.invoiceDate = invoiceDate;
            // form.value.companyName = companyName;
            form.value.invoicePrice = invoicePrice;
            form.value.invoiceNumber = invoiceNumber;
          });
      },
      uploadPreview: file => {
        return 
       },
      action: '/blade-resource/attach/upload',
    },
    {
      type: 'date',
      label: '认证日期',
      addDisplay: false,
      editDisplay: false,
      span: 12,

      search: false,
      searchSpan: 6,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'authenticationDate',
      component: 'wf-daterange-search',
      type: 'date',
      html: true,
      formatter(row, column, cellValue, index) {
        let text;
        if (row.authenticationDate) {
          text = `<div style="color:var(--el-color-success)">${row.authenticationDate}</div>`;
        } else {
          text = `<div style="color:var(--el-color-warning)">未认证</div>`;
        }
        return text;
      },
    },
    {
      type: 'textarea',
      label: '备注',
      overHidden: true,
      span: 24,
      display: true,
      prop: 'remark',
    },
  ],
});
let form = ref({
  contractCode: props.contractCode,

  supplierName: props.supplierName,
});
function beforeOpen(done) {
  form.value = {
    contractCode: props.contractCode,
    purchaseContractId: props.id,
    supplierName: props.supplierName,
    companyName: props.supplierName,
  };
  done();
}
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseContractInvoice/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseContractInvoice/update';
const tableUrl = '/api/vt-admin/purchaseContractInvoice/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
watch(
  () => props.id,
  () => {
    onLoad();
  })
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(`${tableUrl}?purchaseContractId=${props.id}`, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records.map(item => {
        return {
          ...item,
          invoiceFiles: item.attachList.map(item1 => {
            return {
              value: item1.id,
              label: item1.originalName,
            };
          }),
        };
      });
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    invoiceFiles: form.invoiceFiles.map(item => item.value).join(','),
    purchaseContractId: props.id,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    invoiceFiles: row.invoiceFiles.map(item => item.value).join(','),
    purchaseContractId: props.id,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
// function authentication(row) {
//   proxy.$refs.dialogForm.show({
//     title: row.name,
//     option: {
//       column: [
//         {
//           type: 'date',
//           label: '认证日期',
//           span: 12,
//           display: true,
//           format: 'YYYY-MM-DD',
//           valueFormat: 'YYYY-MM-DD',
//           value: dateFormat(new Date(), 'yyyy-MM-dd'),
//           prop: 'authenticationDate',
//         },
//       ],
//     },
//     callback(res) {
//       axios
//         .post('/api/vt-admin/purchaseContractInvoice/authentication', {
//           id: row.id,
//           ...res.data,
//         })
//         .then(e => {
//           proxy.$message.success('操作成功');
//           res.close();
//           onLoad();
//         });
//     },
//   });
// }
let detailDrawer = ref(false);
let detailForm = ref({});
let detailOption = ref({
  border: true,
  header: false,
  menu: false,
  align: 'center',
  column: [
          {
            label: '货物、应税劳务及服务',
            prop: 'productName',
          },
          {
            label: '规格型号',
            prop: 'specification',
          },
          {
            label: '单位',
            prop: 'unitName',
            width:80
          },
          {
            label: '数量',
            prop: 'number',width:80
          },
          {
            label: '单价',
            prop: 'price',width:100
          },
          {
            label: '金额',
            prop: 'productTotalPrice',width:120
          },
          {
            label: '税率',
            type: 'select',
            cell: true,
            props: {
              label: 'dictValue',
              value: 'dictKey',
            },
            width:100,
            prop: 'taxRate',
            dicUrl: '/blade-system/dict/dictionary?code=tax',
          },
          {
            label: '税额',
            prop: 'taxPrice',
            width:120
          },
          {
            label: '小计',
            prop: 'totalPrice',   width:120
          },
        ],
});
function viewDetail(row) {
  axios
    .get('/api/vt-admin/purchaseContractInvoice/detail', {
      params: {
        id: row.id,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      detailDrawer.value = true;
    });
}
</script>

<style lang="scss" scoped></style>
