<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    ></avue-crud>
  </basic-container>
</template>

<script setup>
import { getCurrentInstance } from 'vue';
let option = ref({
  addBtn: false,
  delBtn: false,
  column: [
    {
      label: '业务板块',
      prop: 'businessTypeName',
      disabled: true,
    },
    {
      label: '编号',
      prop: 'code',
    
    },
    {
      label: '负责人',
      prop: 'userId',
      component: 'wf-user-select',
      formatter: (row, column, cellValue) => {
        return row.userName;
      },
    },
    {
      label: '人员配置',
      prop: 'userIds',
      component: 'wf-user-select',
      checkType:'checkbox',
      formatter: (row, column, cellValue) => {
        return row.userNames;
      },
    },
  ],
});
let {proxy} = getCurrentInstance()
let tableData = ref([]);
let loading = ref(false);
function onLoad() {
  loading.value = true;
  axios.get('/api/vt-admin/businessTypeLeader/list').then(res => {
    loading.value = false;
    tableData.value = res.data.data;
  });
}
function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post('/api/vt-admin/businessTypeLeader/save', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done();
        }
      })
      .catch(err => {
        done();
      });
  }
</script>

<style></style>
