<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @keyup.enter="onLoad"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button
          type="primary"
          v-if="row.reportStatus == 0 && $store.getters.permission.reporting_success"
          @click="completeReporting(row)"
          text
          icon="CircleCheckFilled"
          >完成报备</el-button
        >
        <el-button
          type="primary"
          v-if="row.reportStatus == 0 && $store.getters.permission.reporting_fail"
          @click="notReport(row)"
          text
          icon="CircleCloseFilled"
          >报备失败</el-button
        >
      </template>
      <template #reportStatus="{ row }">
        <el-tag effect='plain' v-if="row.reportStatus == 1" size="small" type="success">报备中</el-tag>
        <!-- <el-tag effect='plain' v-if="row.reportStatus == 0" size="small" type="info">{{row.$reportStatus}}</el-tag> -->
       
          <el-tag effect='plain' v-if="row.reportStatus == 2" size="small" type="danger">{{
            row.$reportStatus
          }}</el-tag>
       
        <el-tag effect='plain' v-if="row.reportStatusType == 0" style="margin-left: 5px" size="small" type="success"
          >成交</el-tag
        >
        <el-tag effect='plain' v-if="row.reportStatusType == 1" style="margin-left: 5px" size="small" type="danger"
          >失单</el-tag
        >
        <el-tag effect='plain'
          style="margin-left: 5px"
          v-if="row.reportStatusType == 2 "
          size="small"
          type="warning"
          >过期</el-tag
        >
      </template>
      <template #reportFiles="{row}">
        <File :fileList="row.attachList"></File>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted ,onActivated} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { followType } from '@/const/const.js';
let store = useStore();
let permission = computed(() => store.getters.permission);
console.log(permission.value);
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  labelWidth:150,
  searchSpan: 4,
  menuWidth: 300,
  menu: false,
  border: true,
  column: [
    {
      label: '商机名称',
      prop: 'businessOpportunityId',
      width: 150,
      component: 'wf-business-select',
      overHidden: true,
      search: false,
      hide: true,
    },
    {
      label: '商机名称',
      prop: 'businessOpportunityName',
      width: 150,
      overHidden: true,
      search: true,
      display: false,
      rules: [
        {
          required: true,
          message: '请选择商机名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '客户名称',
      prop: 'customerName',
      width: 200,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      search: true,
    },

    {
      label: '厂家名称',
      prop: 'manufacturer',
      width: 250,
      overHidden: true,
      search: true,
    },

    {
      // type: 'tree',
      label: '业务板块',
      multiple: true,
      width: 100,
      span: 12,
      parent: false,
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },

      filterable: true,
      prop: 'businessTypeName',
      checkStrictly: true,
      // props: {
      //   labelText: '标题',
      //   label: 'categoryName',
      //   value: 'id',
      //   children: 'children',
      // },
    },

    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,
    },
    {
      label: '报备方式',
      prop: 'reportMethod',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '报备方式不能为空',
          trigger: 'change',
        },
      ],
      dicUrl: '/blade-system/dict/dictionary?code=reportingMethods',
      overHidden: true,
    },
    {
      label: '报备平台',
      prop: 'reportPlatform',
      overHidden: true,
    },
    {
      label: '报备人',
      prop: 'reportName',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      component: 'wf-user-drop',
      search: true,
    },
    {
      label: '报备时间',
      prop: 'reportTime',
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      overHidden: true,
    },
    {
      label: '报备有效期(天)',
      prop: 'reportValidity',
      overHidden: true,
      type:'number',
      hide: true,
    },
    {
      label: '离有效期还剩(天)',
      prop: 'days',
      overHidden: true,
      addDisplay: false,
      editDisplay: false,
      html:true,
      formatter: row => {
        if (row.days == null || (row.reportStatus == 2 && row.reportStatusType != 2)) {
          return '';
        }
            return `<div ><span style="color: ${
              row.days > 7
                ? 'var(--el-color-success)'
                : row.days <= 7 && row.days >= 0
                ? 'var(--el-color-warning)'
                : 'var(--el-color-danger)'
            }" class='days'>${row.days}</span><span>${
              row.days || row.days == 0 ? '天' : ''
            }</span></div>`;
          },
   
    },
    {
      label: '报备状态',
      prop: 'reportStatus',
      overHidden: true,
      search: true,  width:140,
      type: 'select',
      addDisplay: false,
      editDisplay: false,
      dicData: [
        // {
        //   value: 0,
        //   label: '未报备',
        // },
        {
          value: 1,
          label: '报备中',
        },
        {
          value: 2,
          label: '报备失效',
        },
      ],
    },
    {
      label: '附件',
      prop: 'reportFiles',
      type: 'upload',
      dataType: 'object',
      overHidden:true,
      loadText: '附件上传中，请稍等',
      span: 24,
      slot: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const tableUrl = '/api/vt-admin/businessOpportunityReport/page';
const updateUrl = '/api/vt-admin/businessOpportunityReport/update';
const addUrl = '/api/vt-admin/businessOpportunityReport/save';

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
onActivated(() => {
  onLoad();
})
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType:2,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
function completeReporting(row) {
 

  proxy.$confirm('是否完成报备?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(() => {
      const data = {
       
        id: row.id,
        reportStatus: 1,
      };
      submit(data);
    })
    .catch(() => {
      //
  })
}
function notReport(row) {
  proxy.$refs.dialogForm.show({
    title: '报备失败',

    option: {
      labelWidth: 120,
      column: [
        {
          label: '原因',
          prop: 'reportReason',
          type: 'textarea',
          span: 24,
          overHidden: true,
        },
      ],
    },
    callback(res) {
      const data = {
        ...res.data,
        id: row.id,
        reportStatus: 2,
      };
      res.close();
      submit(data);
    },
  });
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function submit(data) {
  axios
    .post('/api/vt-admin/businessOpportunityReport/complete', {
      ...data,
    })
    .then(res => {
      onLoad();
    });
}
</script>

<style lang="scss" scoped>
:deep(.days) {
  font-size: 25px;
  font-weight: bolder;
}
</style>
