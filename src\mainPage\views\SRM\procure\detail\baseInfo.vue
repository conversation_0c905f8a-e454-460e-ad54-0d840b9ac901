<template>
  <!-- 基本信息 -->
  <div>
    <avue-form :option="detailOption" @submit="handleSubmit" :modelValue="props.form">
      <template #files>
        <File :fileList="form.attachList || []"></File>
      </template>
    </avue-form>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, nextTick, watch } from 'vue';
import { useRoute } from 'vue-router';
let baseUrl = '/api/blade-system/region/lazy-tree';
import { businessOpportunityData } from '@/const/const';
const props = defineProps({
  form: Object,
  isEdit: Boolean,
});
const form1 = ref({});

watchEffect(() => {
  if (props.isEdit) {
    form1.value = { ...props.form };
  }
});
const route = useRoute();
let detailOption = ref({
  labelWidth: 150,
  submitBtn: false,
  detail: true,
  emptyBtn: false,
  group: [
    {
      label: '基本信息',
      column: [
        {
          label: '订单编号',
          prop: 'orderNo',
          width: 250,
          overHidden: true,
          search: true,
        },
        {
          label: '报价名称',
          prop: 'offerName',
          search: true,
        },

        {
          label: '采购数量',
          prop: 'number',
        },
        {
          label: '采购金额',
          prop: 'totalPrice',
        },

        {
          label: '下单时间',
          prop: 'orderDate',
          type: 'date',
          searchRange: true,
          searchSpan: 5,
          search: true,
        },
        {
      label: '业务员',
      prop: 'businessUserName',
      search: true,
      width: 120,
    },
    {
      label: '客户名称',
      prop: 'customerName',
      search: true,
      width: 120,
    },
        {
          label: '采购人',
          prop: 'purchasePeople',
          component: 'wf-user-select',
          searchSpan: 3,
          search: true,
        },
        {
          label: '订单状态',
          prop: 'orderStatus',
          type: 'select',
          dicData: [
            {
              value: 0,
              label: '待采购',
            },
            {
              value: 1,
              label: '采购中',
            },
            {
              value: 2,
              label: '询价中',
            },
            {
              value: 3,
              label: '采购完成',
            },
          ],
          slot: true,
          search: true,
        },
        {
          label: '附件',
          prop: 'files',
          span: 24,
        },
        {
          label: '备注',
          prop: 'remark',
          span: 24,
        },
      ],
    },
    {
      label: '送货信息',
      column: [
        {
          label: '收货人',
          prop: 'deliveryUser',
          span: 24,
        },
        {
          label: '收货地址',
          prop: 'deliveryAddress',
          type: 'input',
          span: 24,
        },
        {
          label: '联系方式',
          prop: 'deliveryContact',

          span: 24,
        },
      ],
    },
  ],
});
const emit = defineEmits(['getDetail']);
let { proxy } = getCurrentInstance();
function handleSubmit(form, done, loading) {
  const data = {
    ...form,
    id: route.query.id,
    provinceCode: form.province_city_area[0],
    cityCode: form.province_city_area[1],
    areaCode: form.province_city_area[2],
  };
  axios
    .post('/api/vt-admin/businessOpportunity/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        emit('getDetail');
      }
    })
    .catch(() => {
      done();
    });
}
</script>

<style lang="scss" scoped></style>
