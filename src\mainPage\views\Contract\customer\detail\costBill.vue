<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="reset"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
   
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>


</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useStore } from 'vuex';

import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date';
import InvoiceApply from '../../invoiceApply.vue';
const store = useStore();
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
  form: Object,
  isNeedInvoice: String,
  contractType: Number,
  contractTotalPrice: [String, Number],
});
let { proxy } = getCurrentInstance();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,

  editBtn: false,
  delBtn: false,
  viewBtn: false,
  size: 'default',
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  addTitle: '开票申请',
  addBtnText: '发票新增',
  menuWidth: 270,
  border: true,
  column: [
  

    {
      type: 'date',
      label: '开票日期',
      span: 12,
      width: 100,
      display: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'invoiceDate',
    },

    {
      type: 'input',
      label: '开票金额',
      width: 110,
      
      span: 12,
      display: true,
      prop: 'invoicePrice',
    },
    {
      type: 'select',
      label: '开票类型',
      dicUrl: '/blade-system/dict/dictionary?code=invoiceType',
      cascader: [],
      span: 12,
      width: 130,
      // search: true,
      value: '',
      display: true,
      value: props.invoiceType,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      prop: 'invoiceType',
      rules: [{ required: true, message: '请选择开票类型', trigger: 'blur' }],
    },
    {
      label: '税率',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },

      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      rules: [
        {
          required: true,
          message: '请选择税率',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '开票公司',
      type: 'select',

      prop: 'billingCompany',

      props: {
        label: 'companyName',
        value: 'id',
      },
      dicFormatter: res => {
        return res.data.records;
      },

      overHidden: true,
      cell: false,
      dicUrl: '/api/vt-admin/company/page?size=100',
    },
    {
      label: '操作人',
      type: 'input',
      value: store.getters.userInfo.nick_name,
      prop: 'createName',
      width: 80,
      readonly: true,
    },
    {
      type: 'date',
      label: '操作时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      readonly: true,
      width: 100,
      value: dateFormat(new Date(), 'yyyy-MM-dd'),
      valueFormat: 'YYYY-MM-DD',
      prop: 'createTime',
    },
  
    // 发票附件
    {
      type: 'upload',
      label: '发票附件',
      span: 24,
      display: true,
      prop: 'invoiceAttachment',
       type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      overHidden: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },

    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
    },
    {
      type: 'select',
      label: '状态',
      width: 80,
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票 ',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
        {
          label: '申请作废',
          value: 4,
        },
      ],
      cascader: [],
      span: 12,
      addDisplay: false,
      editDisplay: false,
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
      prop: 'invoiceStatus',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '';
let params = ref({});
let tableData = ref([]);

let route = useRoute();
let loading = ref(false);

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}


</script>

<style lang="scss" scoped>
.cost-bill-form {
  padding: 20px;

  .full-width {
    .el-form-item__content {
      width: 100%;
    }
  }

  .invoice-items-table {
    margin-bottom: 10px;

    .el-input-number {
      width: 100%;
    }

    .el-select {
      width: 100%;
    }
  }

  .table-actions {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .total-info {
      font-weight: bold;
      color: #409eff;

      span {
        display: inline-block;
        padding: 5px 10px;
        background-color: #f0f9ff;
        border-radius: 4px;
        border: 1px solid #d1ecf1;
      }
    }
  }
}

.drawer-footer {
  text-align: right;
  padding: 20px;
  border-top: 1px solid #e4e7ed;

  .el-button {
    margin-left: 10px;
  }
}

// 抽屉内容区域样式调整
:deep(.el-drawer__body) {
  padding: 0;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-table th) {
  background-color: #fafafa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 8px 0;
}
</style>
