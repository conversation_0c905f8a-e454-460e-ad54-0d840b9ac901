<template>
  <basic-container>
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="报价视图" name="first">
        <OfferDetail></OfferDetail>
      </el-tab-pane>
      <el-tab-pane label="方案视图" name="second" v-if="$route.query.businessOpportunityId">
        <OptionDetail :id="id" :isAdmin="$route.query.isHistory"></OptionDetail>
      </el-tab-pane>
    </el-tabs>
  </basic-container>
</template>

<script setup>
import { useRoute } from 'vue-router';
import OfferDetail from './OfferDetail.vue';
import OptionDetail from '@/views/CRM/programme/compoents/detail.vue';
let route = useRoute();
let activeName = ref(route.query.businessOpportunityId ? 'second' : 'first');
let id = ref(route.query.businessOpportunityId);
</script>
<style lang="scss" scoped></style>
