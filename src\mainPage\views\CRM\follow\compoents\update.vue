<template>
  <basic-container>
    <Title style="margin-bottom: 10px">
      {{ route.query.id ? '编辑' : '新增' }}
      <template #foot
        ><el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <avue-form :option="option" ref="followForm" @submit="handleSubmit" v-model="form"> </avue-form>
  </basic-container>
</template>

<script setup>
import { followType } from '@/const/const';
import axios from 'axios';
import { useStore } from 'vuex';
import { computed, onMounted } from 'vue';
import { ref, getCurrentInstance } from 'vue';
import { useRouter, useRoute } from 'vue-router';
const form = ref({});
const router = useRouter();
const route = useRoute();
const store = useStore();
const { proxy } = getCurrentInstance();

let userInfo = computed(() => store.getters.userInfo);

const option = ref({
  labelWidth: 120,
  // detail:true,
  group: [
    {
      column: [
        {
          type: 'input',
          label: '客户名称',
          span: 12,
          display: true,
          component: 'wf-customer-select',
          prop: 'customerId',
          value: route.query.customerId || '',
          params: {
            Url: '/vt-admin/customer/page?type=0',
          },
          change: val => {
            form.value.followPerson = '';
          },
          control: val => {
            if (val) {
              const contactPerson = proxy.findObject(option.value.group[0].column, 'followPerson');

              contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val;
              proxy.$refs.followForm.clearValidate();
            }
            return {
              followPerson: {
                disabled: !val,
              },
            };
          },
          required: true,
          rules: [
            {
              required: true,
              message: '请选择客户',
            },
          ],
        },
        {
          type: 'select',
          label: '跟进类型',
          span: 12,
          display: true,
          prop: 'followType',
          // disabled: true,
          value: route.query.type || route.query.type == 0 ? route.query.type * 1 : '',
          dicData: followType,
          control: val => {
            console.log(val);
            return {
              logicId_business: {
                display: val == 1,
              },
              logicId_project: {
                display: val == 2,
              },
              logicId_order: {
                display: val == 3,
              },
            };
          },
          props: {
            value: 'value',
            label: 'label',
          },
        },
        {
          label: '关联商机',
          prop: 'logicId_business',
          component: 'wf-business-select',
          value: route.query.logicId || '',
          display: false,
        },
        {
          label: '关联项目',
          prop: 'logicId_project',
          display: false,
        },
        {
          label: '关联订单',
          prop: 'logicId_order',
          component: 'wf-order-select',
          value: route.query.logicId || '',
          display: false,
        },
        {
          type: 'select',
          label: '跟进方式',
          span: 12,
          display: true,
          dicUrl: '/blade-system/dict/dictionary?code=followType',
          prop: 'followWay',

          props: {
            label: 'dictValue',
            value: 'id',
          },
          rules: [
            {
              required: true,
              message: '跟进方式必须填写',
            },
          ],
        },

        {
          type: 'datetime',
          label: '实际跟进时间',
          span: 12,
          display: true,
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          format: 'YYYY-MM-DD HH:mm',
          prop: 'followTime',
          rules: [
            {
              required: true,
              message: '联系方式必须填写',
            },
          ],
        },
        {
          type: 'input',
          label: '跟进联系人',
          component: 'wf-contact-select',
          span: 12,
          disabled: false,
          params: {
            Url: '/vt-admin/customerContact/page',
          },
          display: true,
          prop: 'followPerson',
        },
        {
          type: 'select',
          label: '跟进目的',
          span: 12,

          display: true,
          prop: 'commonTerm',
          dicUrl: '/blade-system/dict/dictionary?code=commonTerms',
          props: {
            label: 'dictValue',
            value: 'id',
          },
        },
        {
          type: 'textarea',
          label: '跟进记录',
          cascader: [],

          span: 24,
          display: true,

          prop: 'followContent',
        },
        // {
        //   type: 'textarea',
        //   label: '工作成效',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'jobPerformance',
        // },
        // {
        //   type: 'textarea',
        //   label: '客户情况',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'customerSituation',
        // },
        // {
        //   type: 'textarea',
        //   label: '工作计划与目标',
        //   cascader: [],
        //   span: 24,
        //   display: true,
        //   prop: 'plansOjbectives',
        // },
        // {
        //   type: 'textarea',
        //   label: '需要协助事宜',
        //   cascader: [],

        //   span: 24,
        //   display: true,

        //   prop: 'needHelpThing',
        // },
        {
          label: '附件',
          prop: 'filesIds',
          type: 'upload',
          dataType: 'object',
          loadText: '附件上传中，请稍等',
          span: 12,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    {
      column: [
        {
          label: '回访提醒',
          type: 'switch',
          prop: 'isReturnVisitRemind',
          dicData: [
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ],
          control: v => {
            return {
              remindTimes: {
                display: !!v,
              },
              messageRemind: {
                display: !!v,
              },
              remindHourAndMinut: {
                display: !!v,
              },
            };
          },
        },
        {
          label: '回访时间',
          prop: 'remindTimes',
          type: 'datetimerange',
          format: 'YYYY-MM-DD HH:mm',
          display: false,
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          startPlaceholder: '开始时间',
          endPlaceholder: '结束时间',
          disabledDate(time) {
            return time.getTime() < Date.now() - 1000 * 60 * 60 * 24;
          },
          rules: [
            {
              required: true,
              message: '请选择回访时间',
            },
          ],
        },
        {
          label: '提醒人',
          component: 'wf-user-select',
          prop: 'messageRemind',
          value: userInfo.value.user_id,
          display: false,
        },
        {
          label: '提醒时间',
          labelTip: '回访时间前',
          labelTipPlacement: 'top',
          prop: 'remindHourAndMinut',
          display: false,
          value: '00:30',
          format: '回访开始前HH时mm分',
          valueFormat: 'HH:mm',
          type: 'time',
        },
      ],
    },
  ],
});
onMounted(() => {
  if (route.query.id) {
    proxy.$nextTick(() => {
      getDetail();
    });
  }
  if (route.query.type == 1) {
    const logic = proxy.findObject(option.value.group[0].column, 'logicId_business');
    logic.display = true;
  }
  if (route.query.type == 2) {
    const logic = proxy.findObject(option.value.group[0].column, 'logicId_project');
    logic.display = true;
  }
  if (route.query.type == 3) {
    const logic = proxy.findObject(option.value.group[0].column, 'logicId_order');
    logic.display = true;
  }
});
function getDetail() {
  axios.get('/api/vt-admin/customerFollow/detail?id=' + route.query.id).then(res => {
    const { remindStartTime, remindEndTime, logicId, attachList = [] } = res.data.data;
    form.value = {
      ...res.data.data,
      remindTimes: [remindStartTime, remindEndTime],
      logicId_business: logicId,
      logicId_order: logicId,
      logicId_project: logicId,
      filesIds: attachList.map(item => {
        return { value: item.id, label: item.originalName };
      }),
    };
  });
}
function handleSubmit(form, done, loading) {
  const { logicId_business, logicId_order, logicId_project } = form;

  const data = {
    ...form,
    logicId: logicId_business || logicId_order || logicId_project,
    filesIds: form.filesIds.map(item => item.value).join(','),
    remindStartTime: form.remindTimes[0],
    remindEndTime: form.remindTimes[1],
    remindHour: form.remindHourAndMinut.split(':')[0],
    remindMintue: form.remindHourAndMinut.split(':')[1],
  };
  if (form.id) {
    axios
      .post('/api/vt-admin/customerFollow/update', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          router.$avueRouter.closeTag();
        router.go(-1);
        }
      })
      .catch(() => {
        done();
      });
  } else {
    axios
      .post('/api/vt-admin/customerFollow/save', data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          router.$avueRouter.closeTag();
        router.go(-1);
        }
      })
      .catch(() => {
        done();
      });
  }
}
</script>

<style lang="scss" scoped></style>
