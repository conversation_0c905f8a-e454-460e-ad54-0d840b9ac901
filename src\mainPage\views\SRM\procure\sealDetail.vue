<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu-left>
        <div style="display: flex">
          <div style="display: flex; align-items: center; gap: 20px">
            <span style="font-weight: bolder">出货数量：</span>
            <el-text type="primary" size="large"
              >{{ (totalNumber * 1).toLocaleString() }}</el-text
            >
            <span style="font-weight: bolder">出货金额：</span>
            <el-text type="primary" size="large">￥{{ (totalPrice * 1).toLocaleString() }}</el-text>
          </div>
        </div>
      </template>
      <template #productSerialNumber="{ row }">
        <el-popover placement="left" :width="200" trigger="click">
          <div style="max-height: 400px; overflow-y: auto">
            <div v-for="item in row.productSerialNumber.split(',')" :key="item">
              <div v-for="item1 in item.split('\n')" :key="item1">
                <el-text type="primary">{{ item1 }}</el-text>
              </div>
            </div>
          </div>
          <template #reference>
            <el-button type="primary" text sizi="small" v-if="row.productSerialNumber"
              >查看</el-button
            >
          </template>
        </el-popover>
      </template>
      <template #contractCode="{ row, size }">
        <el-link :size="size" style="font-size: 12px;" type="primary" @click="toContract(row)">{{ row.contractCode }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  size: 'small',
  border: true,
  column: [
    {
      label: '关键字',
      prop: 'keys',
      search: true,
      hide: true,
      placeholder: '输入名称，型号，品牌，模糊查询',
      component: 'wf-product-drop',
    },
    {
      label: '产品名称',
      prop: 'productName',
      //   search:true,
      overHidden: true,
      cell: false,
      formatter: row => {
        return row.customProductName || row.productVO?.productName;
      },
    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      overHidden: true,
      cell: false,
      span: 24,
      // search:true,component:'wf-product-drop',
      type: 'input',
      formatter: row => {
        return row.customProductSpecification || row.productVO?.productSpecification;
      },
    },

    {
      label: '品牌',
      cell: false,
      prop: 'productBrand',
      width: 80,
      overHidden: true,
      formatter: row => {
        return row.productBrand || row.productVO?.productBrand;
      },
    },
    {
      label: '单位',
      type: 'select',
      cell: false,
      width: 60,
      props: {
        label: 'dictValue',
        value: 'id',
        desc: 'desc',
      },
      rules: [
        {
          required: true,
          message: '请选择单位',
          trigger: 'blur',
        },
      ],

      prop: 'customUnit',
      dicUrl: '/blade-system/dict/dictionary?code=unit',
      remote: false,
      formatter: row => {
        return row.customUnit || row.productVO?.unitName;
      },
    },
    {
      label: '数量',
      prop: 'number',
      width: 60,
    },

    // {
    //   label: '进货信息',
    //   prop: 'inInfo',
    //   type: 'select',
    //   search: true,
    //   children: [

    //   ],
    // },
    // {
    //   label: '出货信息',
    //   prop: 'outInfo',
    //   type: 'select',
    //   search: true,
    //   children: [
    //
    //   ],
    // },
    {
      label: '客户名称',
      prop: 'customerName',
      search: true,
      component: 'wf-customer-drop',
    },
    {
      label: '合同编号',
      prop: 'contractCode',
    },
    {
      label: '业务员',
      prop: 'businessName',
      width: 80,
      searchSpan: 3,
      search: true,
      component: 'wf-user-drop',
    },
    //   {
    //     label: '出货单价',
    //     prop: 'sealPrice',
    //   },
    {
      label: '出货时间',
      prop: 'signDate',
      searchSpan: 5,
      search: true,
      component: 'wf-daterange-search',
    },
    //   {
    //     label: '订单号',
    //     prop: 'orderNo',
    //     search: true,
    //     overHidden: true,
    //   },
    {
      label: '序列号',
      prop: 'productSerialNumber',
      search: true,
      width: 60,
      // overHidden:true
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 20,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/purchaseOrder/purchaseOutStorageDetailList';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let totalNumber = ref(0);
let totalPrice = ref(0);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        orderStartDate: params.value.signDate && params.value.signDate[0],
        orderEndDate: params.value.signDate && params.value.signDate[1],
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
  // 获取统计数据
  axios
    .get('/api/vt-admin/purchaseOrder/purchaseInStorageDetailListStatistics', {
      params: {
        size,
        current,
        ...params.value,
        orderStartDate: params.value.signDate && params.value.signDate[0],
        orderEndDate: params.value.signDate && params.value.signDate[1],
      },
    })
    .then(res => {
      totalNumber.value = res.data.data.totalNumber;
      totalPrice.value = res.data.data.totalPrice;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function toContract(row) {
  router.push({
    path:'/Contract/customer/compoents/detail',
    query: {
      id: row.sealContractId,
    },
  })
}
</script>

<style lang="scss" scoped></style>
