<template>
  <el-row style="padding: 10px" :gutter="10">
    <el-col :span="12">
      <businessStatisic></businessStatisic>
    </el-col>
    <el-col :span="12">
      <reNewAmount></reNewAmount>
    </el-col>
  </el-row>
</template>

<script setup>
import businessStatisic from '@/views/desk/components/businessStatisic.vue';
import reNewAmount from '@/views/desk/components/renewAmountStatistics.vue';
let form = ref({
  selectType: '0',
});
// const store = useStore();
// const permission = computed(() => {
//   return store.getters.permission;
// });

provide('form', form);
</script>

<style lang="scss" scoped></style>
