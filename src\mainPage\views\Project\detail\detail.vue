<template>
  <basic-container style="height: 100%">
    <div class="header">
      <div>
        <h3 v-if="form.type == 0" style="margin-bottom: 10px" class="title">
          {{ form.projectName }}
        </h3>
        <div v-else style="display: flex; justify-content: space-between; align-items: center">
          <h3 style="margin-bottom: 10px; font-size: 18px" class="title">{{ form.projectName }}</h3>
          <div>
            <el-button type="primary" @click="openDetail" v-if="$store.getters.permission['project:viewOption']" plain>查看方案</el-button>
          <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        >
          </div>
        </div>

        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="项目总额">
            <el-tag effect="plain" size="large">{{ form.contractTotalPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="原项目总额" v-if="form.changePrice > 0">
            <el-tag effect="plain" size="large">{{ form.oldContractTotalPrice || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="签证总额" v-if="form.changePrice > 0">
            <el-tag effect="plain" size="large">{{ form.changePrice || '---' }}</el-tag>
          </el-form-item>

          <el-form-item label="项目进度">
            <el-tag effect="plain" size="large">{{ form.projectSchedule || `---` }}</el-tag>
          </el-form-item>
          <el-form-item label="项目经理">
            <el-tag effect="plain" size="large">{{ form.technologyUserName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="业务">
            <el-tag effect="plain" size="large">{{ form.businessUserName || '---' }}</el-tag>
            <!-- <el-icon style="cursor: pointer; margin-left: 5px" @click="edit">
              <Edit></Edit>
            </el-icon> -->
          </el-form-item>
          <el-form-item label="售前技术">
            <el-tag effect="plain" size="large">{{ form.preSaleTechnology || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="协助人员">
            <el-tag effect="plain" size="large">{{
              form.technologyAssistUserName || '---'
            }}</el-tag>
          </el-form-item>
          <el-form-item  v-if="form.paymentReminders && form.paymentReminders.length > 0" label="计划收款提醒">
            <div class="payment-reminders">
              <div class="payment-list">
                <div 
                  v-for="(payment, index) in form.paymentReminders" 
                  :key="index"
                  class="payment-item"
                  :class="getPaymentStatusClass(payment)"
                >
                  <div class="payment-name">{{ payment.planName || '未命名收款' }}</div>
                  <div class="payment-time">{{ payment.planCollectionDate || '未设置时间' }}</div>
                  <div class="payment-price">¥{{ formatPrice(payment.noPaymentPrice) }}</div>
                  <div class="payment-days">
                    <span v-if="payment.daysLeft >= 0">
                      距离{{ payment.daysLeft }}天
                    </span>
                    <span v-else class="overdue">
                      逾期{{ Math.abs(payment.daysLeft) }}天
                    </span>
                    <div class="payment-rule" >
                      <el-tooltip
                        :content="getHitRuleText(payment)"
                        placement="top"
                        :show-after="200"
                      >
                        <el-icon style="color: var(--el-color-primary); font-size: 12px; cursor: pointer; margin-left: 4px">
                          <InfoFilled />
                        </el-icon>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </div>
             
            </div>
          </el-form-item>
          
        </el-form>
      </div>
    </div>

    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <BaseInfo
            :form="form"
            v-loading="loading"
            :isEdit="isEdit"
            @getDetail="getDetail"
          ></BaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      :is="tabArray.find(item => item.name == activeName).component"
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :id="form.businessOpportunityId"
      :detail="true"
      :humanInfo="{
        humanIds: form.humanIds,
        humanResourceEntities: form.humanResourceEntities && form.humanResourceEntities.sort((a, b) => a.isAllSettlement - b.isAllSettlement),
        technologyUserInfo: { id: form.projectLeader, name: form.technologyUserName },
        interiorUsers: form.interiorUsers,
        interiorUserList: form.interiorUserList,
      }"
      @success="getDetail"
      :projectPrice="form.projectPrice"
      :isView="true"
      :deepenStatus="form.deepenStatus"
      :projectId="form.id"
      :projectName="form.projectName"
      :selectType="route.query.type == 0 ? 0 : 1"
      :customerId="form.customerId"
      :sealContractId="form.sealContractId"
      :isCompletePlan="form.isCompletePlan"
      :startDate="form.startDate"
      :isNeedPurchase="form.isNeedPurchase"
      :height="500"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer
      title="方案详情"
      width="90%"
      size="90%"
      overflow
      top="0vh"
      append-to-body
      v-model="dialogVisible"
      style="height: 100vh"
    >
      <div style="background-color: var(--el-color-info-light-9)">
        <programmeDetail
          :businessOpportunityId="form.businessOpportunityId"
          :dialogView="true"
        ></programmeDetail>
      </div>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, computed } from 'vue';
import { InfoFilled } from '@element-plus/icons-vue';
import { useRoute, useRouter } from 'vue-router';
import programmeDetail from '@/views/CRM/programme/compoents/updateVersion3.vue';
import { useStore } from 'vuex';

import BaseInfo from './baseInfo.vue';
// 干系人
import Contact from './contact.vue';
// 项目计划
import ProjectPlan from './projectPlan.vue';
// 项目费用
import Cost from './cost.vue';
// 项目文档
import ProjectFile from './projectFile.vue';
// 项目工时
import ProjectHours from './projectHours.vue';
// 材料清单表
import ProjectProduct from './projectProduct.vue';
// 项目请购
import projectPurchase from './projectPurchase.vue';
// 项目签证
import projectChange from './projectChange.vue';
// 项目报警
import ProjectWarn from './projectWarn.vue';
// 深化设计
import ProjectDeep from '../compoents/deepSign.vue';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);

watchEffect(() => {
  if (route.query.id && route.fullPath.indexOf('/Project/detail/detail') > -1) {
    getDetail();
  }
});
let { proxy } = getCurrentInstance();
const roleArr = [
  'project:contact',
  'project:deep',
  'project:plan',
  'project:hours',
  'project:product',
  'project:change',
  'project:purchase',
  'project:cost',
  'project:file',
];
const conpletArr = [
  'Contact',
  'ProjectDeep',
  'ProjectPlan',
  'Cost',
  'ProjectFile',
  'ProjectHours',
  'ProjectProduct',
  'projectChange',
  'projectPurchase',

  'ProjectWarn',
];
console.log(conpletArr, 'conpletArr');
const tabArray = [
  {
    label: '干系人',
    name: 'Contact',
    component: Contact,
  },
  {
    label: '深化设计',
    name: 'ProjectDeep',
    component: ProjectDeep,
  },
  {
    label: '项目计划',
    name: 'ProjectPlan',
    component: ProjectPlan,
  },
  {
    label: '项目工时',
    name: 'ProjectHours',
    component: ProjectHours,
  },
  {
    label: '材料清单表',
    name: 'ProjectProduct',
    component: ProjectProduct,
  },
  {
    label: '项目签证',
    name: 'projectChange',
    component: projectChange,
  },
  {
    label: '请购单',
    name: 'projectPurchase',
    component: projectPurchase,
  },

  // {
  //   label: '人力资源库',
  //   name: 'follow',
  //   component: BaseInfo,
  // },
  {
    label: '项目费用',
    name: 'Cost',
    component: Cost,
  },
  // {
  //   label: '项目分析',
  //   name: 'Process',
  //   component: BaseInfo,
  // },
  // {
  //   label: '项目总结',
  //   name: 'Process',
  //   component: BaseInfo,
  // },
  {
    label: '项目文档',
    name: 'ProjectFile',
    component: ProjectFile,
  },
  {
    label: '项目报警',
    name: 'ProjectWarn',
    component: ProjectWarn,
  },
].filter((item, index) => {
  console.log(proxy.$store.getters.permission);
  if(route.query.type != 0) return true
  if (['Contact', 'projectChange', 'ProjectFile'].includes(item.name)) {
    return proxy.$store.getters.permission[roleArr[index]];
  } else {
    return (
      proxy.$store.getters.permission[roleArr[index]] &&
      (route.query.type != 0 || route.query.isMy != 1)
    );
  }
});
console.log(tabArray, 'tabArray');
onMounted(() => {
  getDetail();
});
function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/project/detail', {
      params: {
        id: route.query.id,
      },
    })
    .then(res => {
      loading.value = false;
      const { provinceCode, cityCode, areaCode } = res.data.data;
      
      
      
      // 处理收款提醒数据（优先使用真实数据，如果没有则使用模拟数据）
      const paymentReminders = res.data.data.planCollectionRemindNum || [];
      
      // 模拟数据（用于测试显示效果）
      const mockPaymentReminders = [
        {
          planName: '首期款',
          planCollectionDate: '2024-01-15',
          overDays: null,
          planCollectionDays: 5
        },
        {
          planName: '中期款',
          planCollectionDate: '2024-01-20',
          overDays: null,
          planCollectionDays: 10
        },
        {
          planName: '尾款',
          planCollectionDate: '2024-01-10',
          overDays: 3,
          planCollectionDays: 0
        },
        {
          planName: '质保金',
          planCollectionDate: '2024-01-05',
          overDays: 8,
          planCollectionDays: 0
        },
        {
          planName: '违约金',
          planCollectionDate: '2024-02-01',
          overDays: null,
          planCollectionDays: 22
        }
      ];
      
      // 使用真实数据或模拟数据
      const paymentData = paymentReminders||[]
      
      const processedPaymentReminders = paymentData.map(payment => {
        // 根据overDays字段计算daysLeft：如果overDays不为null（已逾期），则为负数；否则使用planCollectionDays
        const daysLeft = payment.overDays !== null ? -payment.overDays : payment.planCollectionDays;
        return {
          ...payment,
          daysLeft
        };
      });
      
      form.value = {
        ...res.data.data,
        paymentReminders: processedPaymentReminders
      };
      console.log(form.value, 'form.value');
      
    });
}

let currentIndex = ref(0);

function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}

const store = useStore();
const tag = computed(() => store.getters.tag);
const activeName = ref('baseInfo');

function openDetail() {
  console.log(form.value);
  dialogVisible.value = true;
  // router.push({
  //   path: '/CRM/programme/compoents/updateVersion3',
  //   query: {
  //     id: form.value.businessOpportunityId,
  //     type: 'detail',
  //     name: form.value.businessOpportunityName,
  //     // businessOpportunityId: row.businessOpportunityId,
  //   },
  // });
}
let dialogVisible = ref(false);

// 计算距离收款时间的天数
function calculateDaysLeft(paymentTime) {
  if (!paymentTime) return null;
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const paymentDate = new Date(paymentTime);
  paymentDate.setHours(0, 0, 0, 0);
  
  const timeDiff = paymentDate.getTime() - today.getTime();
  const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
  
  return daysDiff;
}

// 获取收款状态样式类
function getPaymentStatusClass(payment) {
  if (!payment.daysLeft && payment.daysLeft !== 0) return '';
  
  if (payment.daysLeft < 0) {
    // 逾期：根据逾期天数显示不同严重程度
    const overdueDays = Math.abs(payment.daysLeft);
    if (overdueDays >= 30) return 'payment-overdue-critical';
    if (overdueDays >= 15) return 'payment-overdue-serious';
    return 'payment-overdue-warning';
  } else if (payment.daysLeft <= 7) {
    // 即将到期：7天内
    return 'payment-urgent';
  } else if (payment.daysLeft <= 30) {
    // 近期到期：30天内
    return 'payment-upcoming';
  }
  // 正常状态
  return 'payment-normal';
}

// 获取命中规则文本
function getHitRuleText(payment) {
  if (!payment.remindTriggeringCondition || payment.remindTriggeringCondition == 0) {
    return '固定收款时间';
  }
  switch (payment.remindTriggeringCondition) {
    case 1:
      return payment.conditionStageName ? `${payment.conditionStageName}完成后${payment.conditionValue || 0}天` : '项目计划完成后多少天';
    case 2:
      return `项目采购金额达${payment.conditionPercentage || 0}%后${payment.conditionValue || 0}天`;
    case 3:
      return `验收完成后${payment.conditionValue || 0}天`;
    case 4:
      return `签订时间${payment.conditionValue || 0}天后`;
    default:
      return '---';
  }
}

// 格式化金额显示
function formatPrice(price) {
  if (!price && price !== 0) return '0';
  const num = Number(price);
  if (isNaN(num)) return '0';
  return num.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  // display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}

// 收款提醒样式
.payment-reminders {
  min-width: 450px;
  max-width: 100%;
}

.payment-list {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 5px;
  align-items: flex-start;
}

.payment-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 6px;
  border-radius: 4px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 1px solid #e9ecef;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  min-width: 90px;
  flex: 0 0 auto;
  line-height: 1.2;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
}
  
  .payment-name {
  font-weight: 600;
  font-size: 14px;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 1px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}
  
  .payment-time {
    font-size: 12px;
    color: #6c757d;
    margin-bottom: 1px;
    text-align: center;
  }
    
    .payment-price {
    font-size: 12px;
    color: #28a745;
    font-weight: 600;
    text-align: center;
  }
  
  .payment-days {
  font-size: 13px;
  font-weight: 500;
  text-align: center;
  
  .overdue {
    font-weight: 700;
  }
  
  & span {
    font-size: 13px;
  }
}
  
  .payment-rule {
     display: inline-flex;
     align-items: center;
     vertical-align: middle;
   }


// 正常状态 - 绿色
.payment-normal {
  border-left: 4px solid #28a745;
  background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
  
  .payment-days {
    color: #28a745;
  }
}

// 即将到期 - 橙色
.payment-upcoming {
  border-left: 4px solid #fd7e14;
  background: linear-gradient(135deg, #fff9f5 0%, #ffffff 100%);
  
  .payment-days {
    color: #fd7e14;
  }
}

// 紧急 - 深橙色
.payment-urgent {
  border-left: 4px solid #e55100;
  background: linear-gradient(135deg, #fff3e0 0%, #ffffff 100%);
  
  .payment-days {
    color: #e55100;
    font-weight: 600;
  }
}

// 逾期警告 - 红色
.payment-overdue-warning {
  border-left: 4px solid #dc3545;
  background: linear-gradient(135deg, #fff5f5 0%, #ffffff 100%);
  
  .payment-days {
    color: #dc3545;
    
    .overdue {
      color: #dc3545;
    }
  }
}

// 逾期严重 - 深红色
.payment-overdue-serious {
  border-left: 4px solid #bd2130;
  background: linear-gradient(135deg, #ffebee 0%, #ffffff 100%);
  animation: pulse-warning 2s infinite;
  
  .payment-days {
    color: #bd2130;
    
    .overdue {
      color: #bd2130;
      font-weight: 700;
    }
  }
}

// 逾期严重 - 深红色
.payment-overdue-critical {
  border-left: 4px solid #721c24;
  background: linear-gradient(135deg, #ffcdd2 0%, #ffffff 100%);
  animation: pulse-critical 1.5s infinite;
  
  .payment-days {
    color: #721c24;
    
    .overdue {
      color: #721c24;
      font-weight: 700;
    }
  }
}

// 脉冲动画效果
@keyframes pulse-warning {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
  }
  50% {
    box-shadow: 0 2px 16px rgba(220, 53, 69, 0.3);
  }
}

@keyframes pulse-critical {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(114, 28, 36, 0.2);
  }
  50% {
    box-shadow: 0 2px 20px rgba(114, 28, 36, 0.5);
  }
}

.no-payment {
  text-align: center;
  color: #6c757d;
  font-style: italic;
}
</style>
