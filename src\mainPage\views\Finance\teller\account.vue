<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <!-- <el-button type="primary" text icon="view" @click="handleView(row)">明细</el-button> -->
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" :title="`账户明细[${currentRow.abbreviation}]`" size="90%">
      <accountOutAndIn :accountId="currentRow.id"></accountOutAndIn>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import accountOutAndIn from './accountOutAndIn.vue';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 160,
  labelWidth: 120,
  border: true,
  column: [
    {
      label: '账号类型',
      prop: 'accountType',
      type: 'select',
      width: 100,
      dicData: [
        {
          value: 0,
          label: '企业账户',
        },
        {
          value: 1,
          label: '个人账户',
        },
        {
          value: 2,
          label: '其它账户',
        },
      ],
    },
    {
      label: '账户简称',
      prop: 'abbreviation',
      rules: [{ required: true, message: '请输入账户简称', trigger: 'blur' }],
    },
    {
      label: '开户名',
      prop: 'accountName',
      rules: [{ required: true, message: '请输入开户名', trigger: 'blur' }],
    },
    {
      label: '开户银行',
      prop: 'bankName',
      rules: [{ required: true, message: '请输入开户行', trigger: 'blur' }],
    },
    {
      label: '开户银行账号',
      prop: 'accountNumber',
      width: 180,
      rules: [{ required: true, message: '请输入开户行账号', trigger: 'blur' }],
    },

    {
      label: '持有人名字	',
      prop: 'accountHolder',
      width: 100,
    },
    {
      label: '持有人联系电话',
      prop: 'holderPhone',
      width: 130,
    },
    {
      label: '账号状态',
      prop: 'accountStatus',
      addDisplay: false,
      width: 90,
      type: 'switch',
      dicData: [
        {
          value: 0,
          label: '正常',
        },
        {
          value: 1,
          label: '异常',
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/companyAccount/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/companyAccount/update';
const tableUrl = '/api/vt-admin/companyAccount/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let currentRow = ref('');
let drawer = ref(false);
function handleView(row) {
  currentRow.value = row;
  drawer.value = true;
}
</script>

<style lang="scss" scoped></style>
