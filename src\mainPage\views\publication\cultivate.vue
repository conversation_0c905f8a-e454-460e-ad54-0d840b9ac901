<template>
  <div class="train-page">
    <div class="train-page-left">
      <!-- <div
        class="train-page-left__add"
        style="display: flex; justify-content: space-between; align-items: center"
      >
        <el-input placeholder="输入关键字进行过滤" v-model="filterText" size="small"> </el-input>
        <el-button
          circle
          type="primary"
          size="small"
          v-if="$store.getters.permission['cultivate:addCategory']"
          icon="el-icon-plus"
          @click="handleAddForm(1)"
        ></el-button>
      </div> -->

      <el-tree
        :data="treeData"
        :props="treeProps"
        :default-expand-all="true"
        node-key="id"
        ref="tree"
        @node-click="handleTreeClick"
        :expand-on-click-node="false"
        :filter-node-method="filterNode"
      >
        <template #default="{ node, data }">
          <span class="custom-tree-node" slot-scope="{ node, data }">
            <el-tooltip
              class="item"
              effect="dark"
              :content="node.label"
              placement="top-start"
              :disabled="isShowTooltip"
            >
              <span @mouseenter="mouseTootip($event)" style="font-size: 14px">{{
                node.label
              }}</span>
            </el-tooltip>
          </span>

          <el-dropdown v-if="node.level == 1" @command="addCommand" trigger="click">
            <el-button type="primary" size="small" icon="more" text></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="0"
                  v-if="$store.getters.permission['cultivate:addCategory']"
                  >新增</el-dropdown-item
                >
                <el-dropdown-item
                  command="1"
                  v-if="
                    $store.getters.permission['cultivate:deleteCategory'] ||
                    data.createUser == $store.getters.user_id
                  "
                  >编辑</el-dropdown-item
                >
                <el-dropdown-item
                  command="2"
                  v-if="
                    $store.getters.permission['cultivate:deleteCategory'] ||
                    data.createUser == $store.getters.user_id
                  "
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
          <el-dropdown v-if="node.level == 2" @command="addCommand" trigger="click">
            <el-button type="primary" size="small" icon="more" text></el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item
                  command="3"
                  v-if="
                    $store.getters.permission['cultivate:editCourse'] ||
                    data.createUser == $store.getters.user_id
                  "
                  >编辑</el-dropdown-item
                >
                <el-dropdown-item
                  command="4"
                  v-if="
                    $store.getters.permission['cultivate:deleteCourse'] ||
                    data.createUser == $store.getters.user_id
                  "
                  >删除</el-dropdown-item
                >
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </template>
      </el-tree>
      <el-button
        type="primary"
        @click="handleAddForm(1)"
        size="small"
        style="width: 100%; margin-top: 10px"
        plain
        icon="plus"
      ></el-button>
    </div>
    <div class="train-page-right">
      <!-- <div class="title">
        {{ selectObj['name'] ? selectObj['name'] : '全部分类' }}
        <div class="btns">
          <template v-if="show == 'first'">
            <el-button @click="handleEditForm(1)" size="default"  v-if="$store.getters.permission['cultivate:editCategory']">编辑分类</el-button>
            <el-button @click="handleRemove(1)" size="default"  v-if="$store.getters.permission['cultivate:deleteCategory']" type="danger">删除分类</el-button>
          </template>
          <template v-if="show == 'second'">
            <el-button @click="handleEditForm(2)" size="default"  v-if="$store.getters.permission['cultivate:editCourse']">编辑教程</el-button>
            <el-button @click="handleRemove(2)" size="default" v-if="$store.getters.permission['cultivate:deleteCourse']" type="danger">删除教程</el-button>
          </template>
          <template v-if="show == 'three'">
            <el-button @click="handleEditForm(3)" size="default" v-if="$store.getters.permission['cultivate:editClassFile']">编辑课件</el-button>
            <el-button @click="handleRemove(3)" size="default"  v-if="$store.getters.permission['cultivate:deleteClassFile']" type="danger">删除课件</el-button>
          </template>
        </div>
      </div> -->
      <div class="train-page-main">
        <!-- 空数据 -->
        <!-- <el-empty description="请选择最后一级目录" v-if="show == 'first'"></el-empty> -->
        <!-- 课件列表 -->
        <div v-show="show == 'second'" style="margin-bottom: 10px">
          <el-button
            @click="handleAddForm(3)"
            v-if="$store.getters.permission['cultivate:addClassFile']"
            size="default"
            icon="el-icon-plus"
            type="primary"
          >
            新增教程
          </el-button>
          <span
            style="margin-left: 10px"
            v-if="
              $store.getters.userInfo.role_name.indexOf('admin') > -1 ||
              $store.getters.userInfo.role_name.indexOf('manager') > -1
            "
          >
            <span style="color: #666; font: 14px">该教程岗位可见权限：</span>
            {{ selectObj.postLimitsName || '全部' }} -

            <span style="color: #666; font: 14px">该教程部门可见权限：</span>
            {{ selectObj.deptLimitsName || '全部' }}
          </span>
        </div>
        <div v-show="show == 'second' || show == 'first'">
          <el-table class="avue-crud" border size="default" :data="tableData">
            <el-table-column width="150" label="教程名称" prop="name">
              <template #default="{ row }">
                <el-link
                  @click="handleOpenLessonDetail(row['id'])"
                  size="default"
                  :underline="false"
                  type="primary"
                >
                  <span class="hover_under">{{ row['name'] }} </span></el-link
                >
              </template>
            </el-table-column>
            <el-table-column
              label="教程说明"
              prop="remarks"
              show-overflow-tooltip
            ></el-table-column>
            <el-table-column
              label="创建者"
              width="80"
              align="center"
              prop="createName"
            ></el-table-column>
            <el-table-column
              label="创建时间"
              prop="createdTime"
              align="center"
              width="120"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.createdTime.split(' ')[0] }}
              </template>
            </el-table-column>

            <el-table-column
              label="教程类型"
              prop="type"
              align="center"
              show-overflow-tooltip
              width="90"
            ></el-table-column>
            <el-table-column label="教程大小" prop="size" align="center" width="90">
              <template #default="{ row }">
                <span>{{ sizeFilter(row['size']) }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="教程附件"
              prop="size"
              align="left"
              width="300"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <File :fileList="row.attachList"></File>
              </template>
            </el-table-column>
            <el-table-column align="center" width="200" label="操作">
              <template #default="{ row, $index }">
                <el-popover
                  placement="top-start"
                  :width="500"
                  trigger="hover"
                  :disabled="!(!row.attachList || row.attachList.length > 1)"
                >
                  <template #default>
                    <file :fileList="row.attachList" />
                  </template>
                  <template #reference>
                    <el-button
                      type="primary"
                      icon="view"
                      style="margin-left: 0"
                      @click="handleStart(row)"
                      text
                      >预览</el-button
                    >
                  </template>
                </el-popover>

                <el-button
                  @click="handleTableEdit(3, row)"
                  type="primary"
                  text
                  style="margin-left: 0"
                  icon="edit"
                  v-if="
                    $store.getters.permission['cultivate:editClassFile'] ||
                    row.createUser == $store.getters.user_id
                  "
                  >编辑</el-button
                >
                <el-button
                  @click="handleTableDelete(3, row)"
                  type="primary"
                  text
                  style="margin-left: 0"
                  icon="delete"
                  v-if="
                    $store.getters.permission['cultivate:deleteClassFile'] ||
                    row.createUser == $store.getters.user_id
                  "
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="pagebox">
            <el-pagination
              :current-page="page.current"
              :page-size="page.size"
              @current-change="handleCurrentChange"
              layout="total, prev, pager, next, jumper"
              :total="page.total"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- 新增/编辑 -->
    <el-dialog
      :title="getDialogTitle()"
      v-model="formDialog"
      append-to-body
      :destroy-on-close="true"
      :close-on-click-modal="false"
      width="600px"
      class="avue-dialog avue-dialog--top"
    >
      <el-form :model="form" ref="formRef" label-width="120px" size="default">
        <template v-if="dialogStatus == 1">
          <!-- 分类表单 -->
          <el-form-item
            :rules="[{ required: true, message: '请输入分类名', trigger: 'blur' }]"
            label="分类名："
            prop="name"
          >
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <!-- <el-form-item
           
            label="可见岗位："
            prop="post"
          >
            <el-select style="width: 100%" v-model="form.postLimits">
              <el-option
                :label="item.postName"
                :value="item.id"
                v-for="item in postData"
              ></el-option>
            </el-select>
          </el-form-item> -->
        </template>
        <template v-else-if="dialogStatus == 2">
          <!-- 教程表单 -->
          <el-form-item
            :rules="[{ required: true, message: '请输入教程名称', trigger: 'blur' }]"
            label="教程名称："
            prop="name"
          >
            <el-input
              style="width: 100%"
              placeholder="请输入目录名称"
              v-model="form.name"
            ></el-input>
          </el-form-item>
          <el-form-item label="可见岗位：" prop="postLimits">
            <el-select style="width: 100%" multiple v-model="form.postLimits">
              <el-option
                :label="item.postName"
                :value="item.id"
                v-for="item in postData"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="可见部门：" prop="deptLimits">
            <avue-input-tree
              leaf-only
              style="width: 100%"
              multiple
              :check-strictly="true"
              :props="props"
              v-model="form.deptLimits"
              placeholder="请选择部门"
              :dic="deptData"
            ></avue-input-tree>
          </el-form-item>
        </template>
        <template v-else>
          <!-- 课件表单 -->
          <el-form-item
            :rules="[{ required: true, message: '请输入教程名称', trigger: 'blur' }]"
            label="教程名称："
            prop="name"
          >
            <el-input v-model="form.name"></el-input>
          </el-form-item>
          <el-form-item label="教程说明：" prop="remarks">
            <el-input type="textarea" v-model="form.remarks"></el-input>
          </el-form-item>
          <el-form-item
            :rules="[
              {
                required: true,
                validator: validatePass,
                trigger: 'change',
              },
            ]"
            label="教程附件："
            prop="path"
          >
            <el-upload
              action="/api/blade-resource/attach/upload"
              :headers="uploadHeader"
              :file-list="form['pathList']"
              :on-remove="pathRemove"
              :on-exceed="pathExceed"
              :on-change="pathSuccess"
            >
              <el-button size="default" type="primary">上传文件</el-button>
              <div slot="tip" class="el-upload__tip">
                <!-- 支持扩展名：.rar .zip .doc .docx .pdf .jpg... -->
              </div>
            </el-upload>
          </el-form-item>
          <!-- <el-form-item label="上传附件：" prop="enclosureList">
            <el-upload
              action="/api/blade-resource/attach/upload"
              :headers="uploadHeader"
              :file-list="form['enclosureList']"
              :on-remove="enclosureRemove"
              :on-success="enclosureSuccess"
              limit="5"
            >
              <el-button size="default" type="primary">上传文件</el-button>
              <div slot="tip" class="el-upload__tip">
                支持扩展名：.rar .zip .doc .docx .pdf .jpg...
              </div>
            </el-upload>
          </el-form-item> -->
        </template>
      </el-form>

      <span slot="footer" class="avue-dialog__footer">
        <el-button
          size="default"
          :icon="form['id'] ? 'el-icon-circle-check' : 'el-icon-circle-plus-outline'"
          type="primary"
          @click="handleFormSubmit"
        >
          {{ form['id'] ? '修改' : '保存' }}
        </el-button>
        <el-button size="default" icon="el-icon-circle-close" @click="handleFormCancel"
          >取消</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex';

import config from '@/config/website';
import { Base64, encode } from 'js-base64';
import { getToken } from '@/utils/auth';
import { loadFile } from '@/utils/file';
import {
  addCate,
  updateCate,
  removeCate,
  addTou,
  updateTou,
  removeTou,
  addLesson,
  updateLesson,
  removeLesson,
  tutorialMoveUp,
  tutorialMoveDown,
  cateMoveUp,
  cateMoveDown,
  lessonMoveUp,
  lessonMoveDown,
  treeData as getTree,
  cateList,
  tutorialList,
  lessonList,
  lessonDetail,
} from '@/api/publication/cultivate';
import { getPostList } from '@/api/system/post';
import axios from 'axios';
export default {
  data() {
    var _welf = this;
    return {
      validatePass: (rule, value, callback) => {
        console.log(_welf.form['pathList']);
        if (!_welf.form['files']) {
          callback(new Error('请上传课件内容'));
        } else {
          callback();
        }
      },
      // 树数据
      treeData: [],
      treeProps: {
        label: 'name',
        children: 'childrens',
      },
      filterText: '',
      page: {
        current: 1,
        size: 10,
        total: 0,
      },
      isShowTooltip: false,
      // 选择的对象
      selectObj: {},
      tableData: [],
      // 表格加载状态
      tableLoading: false,
      // 显示状态
      show: 'first',
      // 二级分类表格
      threeOption: {
        size: 'default',
        submitBtn: false,
        emptyBtn: false,
        labelWidth: '120',
        detail: true,
        column: [
          {
            label: '课件名称',
            prop: 'name',
            row: true,
            span: 24,
          },
          {
            label: '课件说明',
            prop: 'remarks',
            type: 'textarea',
            row: true,
            span: 24,
          },
          {
            label: '附件下载',
            prop: 'enclosureList',
            row: true,
            span: 24,
          },
        ],
      },
      // 表单数据
      form: {
        path: '',
      },
      formDialog: false,
      dialogStatus: 1,
      // 上传
      uploadHeader: {},
      // 表格操作
      tableFlag: false,
      postData: [],
      deptData: [],
      props: {
        label: 'title',
        value: 'value',
        children: 'children',
      },
    };
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
  },
  watch: {
    filterText(val) {
      this.$refs.tree.filter(val);
    },
  },
  mounted() {
    // 加载树结构
    this.initTreeData().then(() => {
      this.show = 'first';
      if (this.$route.params.id) {
        const obj = this.findNode(this.treeData, item => {
          if (item['publishTrainId']) {
            if (item['id'] == this.$route.params.id) {
              return true;
            }
          }
        });
        if (obj && obj['id']) {
          this.selectObj = obj;

          this.initThreeList();
        }

        this.$refs['tree'].setCurrentKey(this.selectObj.id || this.treeData[0]?.childrens[0]?.id);
        if (this.selectObj.id) {
          this.handleTreeClick(this.selectObj);
        }
        if (this.treeData[0]?.childrens[0]?.id) {
          this.handleTreeClick(this.treeData[0]?.childrens[0]);
        }
      }
    });
    // 加载一级列表
    this.initFirstList();
    this.initThreeList();
    this.show = 'second';
    this.uploadHeader = {
      Authorization: `Basic ${Base64.encode(`${config.clientId}:${config.clientSecret}`)}`,
    };
    this.uploadHeader[config.tokenHeader] = 'bearer ' + getToken();

    // 加载岗位列表
    this.getPostList();
    // 加载部门列表
    this.getDeptList();
  },
  methods: {
    // 查询树
    initTreeData() {
      return new Promise(resolve => {
        getTree().then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.treeData = data.data.map(item => {
              return {
                ...item,
                childrens: item.childrens.map(item => {
                  return {
                    ...item,
                    postLimits: !item.postLimits ? [''] : item.postLimits?.split(','),
                    deptLimits: !item.deptLimits ? [''] : item.deptLimits?.split(','),
                  };
                }),
              };
            });

            this.$nextTick(() => {
              this.$refs['tree'].setCurrentKey(
                this.selectObj.id || this.treeData[0]?.childrens[0]?.id
              );
              if (this.selectObj.id) {
                this.handleTreeClick(this.selectObj);
              }
              if (this.treeData[0]?.childrens[0]?.id) {
                this.handleTreeClick(this.treeData[0]?.childrens[0]);
              }
            });
            resolve();
          }
        });
      });
    },
    // 树节点点击
    handleTreeClick(data, node) {
      console.log(node, 'node');
      // 将当前对象数据放到选择对象体中
      if (!data['publishTrainId'] && !data['publishCourseId']) {
        this.selectObj = data;
        // 一级分类
        this.show = 'first';
        this.initThreeList({
          publishTrainId: data.id,
        });
      } else {
        // 二级分类/三级分类
        this.show = data['publishTrainId'] ? 'second' : 'three';
        if (data['publishTrainId']) {
          this.selectObj = data;
          this.initThreeList();
        } else {
          this.queryLessonDetail(data['id']);
        }
      }
    },
    // 加载一级分类列表
    initFirstList() {
      cateList(this.page.current, this.page.size).then(e => {
        let data = e.data;
        if (data.code == 200) {
          if (!this.show) {
            this.tableData = data.data.records;
            this.page.total = data.data.total;
          }
        }
      });
    },
    // 加载二级分类列表
    initSecList() {
      tutorialList(this.selectObj['id'], this.page.current, this.page.size).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.tableData = data.data.records;
          this.page.total = data.data.total;
        }
      });
    },
    // 加载三级分类列表
    initThreeList(params) {
      lessonList(
        params || {
          publishCourseId: this.selectObj.id,
        },
        this.page.current,
        this.page.size
      ).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.tableData = data.data.records;
          this.page.total = data.data.total;
        }
      });
    },
    // 查询课件详情
    queryLessonDetail(id) {
      lessonDetail(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.selectObj = data.data;
        }
      });
    },
    // 预览图片
    handlePreviewPic(path) {
      if (!path) {
        return;
      }
      this.$ImagePreview(
        [
          {
            thumbUrl: path,
            url: path,
          },
        ],
        0
      );
    },
    // 开始学习
    handleStart(row) {
      if (row.attachList && row.attachList.length == 1) {
        const path = row.attachList[0]?.link;
        window.open(
          config.previewFileUrl + 'onlinePreview?url=' + encodeURIComponent(encode(path))
        );
      }
    },

    // 拿到弹窗表单
    getDialogTitle() {
      let str1 = '新增';
      let str2 = this.dialogStatus == 1 ? '目录' : this.dialogStatus == 2 ? '目录' : '教程';
      if (this.form['id']) {
        str1 = '编辑';
      }
      return str1 + str2;
    },
    // 新增表单
    handleAddForm(type) {
      this.dialogStatus = type;
      if (type == 3) {
        this.form = {
          pathList: [],
        };
      } else {
        this.form = {};
      }
      this.formDialog = true;
    },
    // 编辑表单
    handleEditForm(type) {
      this.dialogStatus = type;
      this.form = { ...this.selectObj };
      if (type == 3) {
        this.form['pathList'] = [
          { name: this.selectObj['fileName'], path: this.selectObj['path'] },
        ];
      }
      this.formDialog = true;
    },
    // 发起编辑/新增
    handleFormSubmit() {
      this.$refs.formRef.validate(async valid => {
        if (valid) {
          if (this.form['id']) {
            // 编辑
            if (this.dialogStatus == 1) {
              // 分类
              this.onCateUpdate();
            } else if (this.dialogStatus == 2) {
              // 教程
              this.onTouUpdate();
            } else {
              // 课件
              this.onLessonUpdate();
            }
          } else {
            // 新增
            if (this.dialogStatus == 1) {
              // 分类
              this.onCateAdd();
            } else if (this.dialogStatus == 2) {
              // 教程
              this.onTouAdd();
            } else {
              // 课件
              this.onLessonAdd();
            }
          }
        }
      });
    },
    // 删除
    handleRemove(type) {
      this.$confirm('确定删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        if (type == 1) {
          // 删除分类
          this.onCateRemove();
        } else if (type == 2) {
          // 删除教程
          this.onTouRemove();
        } else {
          // 删除课件
          this.onLessonRemove();
        }
      });
    },
    // 取消弹窗
    handleFormCancel() {
      this.formDialog = false;
      this.tableFlag = false;
      this.form = {};
      this.dialogStatus = 1;
    },
    // 分类新增
    onCateAdd() {
      return new Promise(resolve => {
        addCate(this.form).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.handleFormCancel();
            this.$message.success('新增分类成功');
            this.initTreeData();
            if (!this.show) {
              this.initFirstList();
            }
            resolve();
          }
        });
      });
    },
    // 分类编辑
    onCateUpdate() {
      return new Promise(resolve => {
        updateCate(this.form).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.$message.success('编辑分类成功');
            if (this.tableFlag) {
              this.initFirstList();
            } else {
              this.selectObj.name = this.form.name;
            }
            this.initTreeData();
            this.handleFormCancel();
            resolve();
          }
        });
      });
    },
    handleCurrentChange(val) {
      this.page.current = val;
      if (this.show == '') {
        this.initFirstList();
      } else if (this.show == 'first') {
        this.initSecList();
      } else if (this.show == 'second') {
        this.initThreeList();
      }
      // this.onLoad(() => {
      //   this.handleCheckTable();
      // });
    },
    // 分类删除
    onCateRemove(data) {
      return new Promise(resolve => {
        let obj = data ? data : this.selectObj;
        removeCate(obj).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.$message.success('删除分类成功');
            if (this.tableFlag) {
              this.initFirstList();
            } else {
              this.show = '';
              this.selectObj = {};
              this.initFirstList();
            }
            this.initTreeData();
            this.handleFormCancel();
            resolve();
          }
        });
      });
    },
    // 教程新增
    onTouAdd() {
      return new Promise(resolve => {
        let obj = {
          ...this.form,
          postLimits: this.form.postLimits.join(','),
          deptLimits: this.form.deptLimits.join(','),
        };
        obj['publishTrainId'] = this.selectObj['id'];
        addTou(obj).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.handleFormCancel();
            this.$message.success('新增教程成功');
            this.initTreeData();
            this.initSecList();
            resolve();
          }
        });
      });
    },
    // 教程编辑
    onTouUpdate() {
      return new Promise(resolve => {
        const formData = {
          ...this.form,
          postLimits: this.form.postLimits.join(','),
          deptLimits: this.form.deptLimits.join(','),
        };
        updateTou(formData).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.$message.success('编辑教程成功');
            if (this.tableFlag) {
              this.initSecList();
            } else {
              this.selectObj.name = this.form['name'];
              this.selectObj.path = this.form['path'];
              this.selectObj.remarks = this.form['remarks'];
            }
            this.initTreeData();
            this.handleFormCancel();
            resolve();
          }
        });
      });
    },
    // 教程删除
    onTouRemove(data) {
      return new Promise(resolve => {
        let obj = data ? data : this.selectObj;
        removeTou(obj).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.$message.success('删除教程成功');
            if (this.tableFlag) {
              this.initSecList();
            } else {
              this.show = '';
              this.selectObj = {};
              this.initFirstList();
            }
            this.initTreeData();
            this.handleFormCancel();
            resolve();
          }
        });
      });
    },
    // 课件新增
    onLessonAdd() {
      return new Promise(resolve => {
        let obj = { ...this.form };
        obj['publishCourseId'] = this.selectObj['id'];
        addLesson(obj).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.handleFormCancel();
            this.$message.success('新增课件成功');

            this.initThreeList();
            resolve();
          }
        });
      });
    },
    // 课件编辑
    onLessonUpdate() {
      return new Promise(resolve => {
        updateLesson(this.form).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.$message.success('编辑课件成功');
            this.initThreeList();
            // this.initTreeData();
            this.handleFormCancel();
            resolve();
          }
        });
      });
    },
    // 课件删除
    onLessonRemove(data) {
      return new Promise(resolve => {
        let obj = data ? data : this.selectObj;
        removeLesson(obj).then(e => {
          let data = e.data;
          if (data.code == 200) {
            this.$message.success('删除教程成功');
            if (this.tableFlag) {
              this.initThreeList();
            } else {
              this.show = '';
              this.selectObj = {};
              this.initFirstList();
            }

            this.handleFormCancel();
            resolve();
          }
        });
      });
    },
    // 教程缩略图上传前
    touBeforeUpload(file) {
      const isJPG = file.type === 'image/jpeg' || file.type === 'image/png';
      const isLt2M = file.size / 1024 / 1024 < 1;

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 1MB!');
      }
      return isJPG && isLt2M;
    },
    // 教程缩略图上传成功
    handleTouSuccess(file) {
      let { link = null } = file.data;
      this.form.path = link;
    },
    // 课件附件移除
    enclosureRemove(file, fileList) {
      this.form.enclosureList = fileList;
    },
    // 课件附件上传成功
    enclosureSuccess(file) {
      let data = file.data;
      data['name'] = data['originalName'];
      data['path'] = data['link'];
      if (!this.form.enclosureList) {
        this.form.enclosureList = [data];
      } else {
        this.form.enclosureList.push(data);
      }
    },
    // 课件移除
    pathRemove(file, fileList) {
      this.pathSuccess({ response: true }, fileList);
    },
    // 课件上传成功
    pathSuccess(file, fileList) {
      console.log(file, fileList, 'file');
      if (!file.response) {
        return;
      }

      let data = fileList.map(item => {
        return {
          name: item.response.data.originalName,
          path: item.response.data?.link,
          size: item.response.data.attachSize,
          type: item.response.data.extension,
          id: item.response.data.id,
        };
      });

      // this.form['fileName'] = data.map(item => item.name).join(',');
      // this.form['path'] = data.map(item => item.path).join(',');
      this.form.files = data.map(item => item.id).join(',');
      this.form['size'] = data.reduce((pre, cur) => {
        return pre + cur.size;
      }, 0);
      this.form['type'] = data.map(item => item.type).join(',');
      this.$refs['formRef'].validateField('path', Error => {
        if (!Error) {
          return true;
        } else {
          return false;
        }
      });
    },
    // 表格编辑
    async handleTableEdit(type, data) {
      this.dialogStatus = type;
      if (type == 3) {
        // const res = await lessonDetail(data['id']);
        //  let  data = res.data.data;
        data['pathList'] =
          data.attachList &&
          data.attachList.map(item => {
            return {
              name: item.originalName,
              path: item.link,
              response: {
                data: item,
              },
            };
          });

        this.form = { ...data };
      } else {
        this.form = { ...data };
      }
      this.tableFlag = true;
      this.formDialog = true;
    },
    // 表格删除
    handleTableDelete(type, data) {
      this.$confirm('确定删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.tableFlag = true;
        if (type == 1) {
          // 删除分类
          this.onCateRemove(data);
        } else if (type == 2) {
          // 删除教程
          this.onTouRemove(data);
        } else {
          // 删除课件
          this.onLessonRemove(data);
        }
      });
    },
    // // 课件列表点击
    // handleOpenLessonDetail(id) {
    //   this.show = 'three';
    //   this.queryLessonDetail(id);
    // },
    // 课件超出限制
    pathExceed() {
      this.$message.error('只能上传一个课件内容');
    },
    // 教程上移
    handleTutorialMoveUp(id) {
      tutorialMoveUp(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.$message.success(data.msg);
          this.initSecList();
        }
      });
    },
    // 教程下移
    handleTutorialMoveDown(id) {
      tutorialMoveDown(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.$message.success(data.msg);
          this.initSecList();
        }
      });
    },
    // 分类上移
    handleCateMoveUp(id) {
      cateMoveUp(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.$message.success(data.msg);
          this.initFirstList();
        }
      });
    },
    // 分类下移
    handleCateMoveDown(id) {
      cateMoveDown(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.$message.success(data.msg);
          this.initFirstList();
        }
      });
    },
    // 课件上移
    handleLessonMoveUp(id) {
      lessonMoveUp(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.$message.success(data.msg);
          this.initThreeList();
        }
      });
    },
    // 课件下移
    handleLessonMoveDown(id) {
      lessonMoveDown(id).then(e => {
        let data = e.data;
        if (data.code == 200) {
          this.$message.success(data.msg);
          this.initThreeList();
        }
      });
    },
    // 鼠标移入判断长短
    mouseTootip(event) {
      const ev = event.target;
      const ev_weight = ev.scrollWidth; // 文本的实际宽度   scrollWidth：对象的实际内容的宽度，不包边线宽度，会随对象中内容超过可视区后而变大。
      const content_weight = ev.clientWidth; // 文本的可视宽度 clientWidth：对象内容的可视区的宽度，不包滚动条等边线，会随对象显示大小的变化而改变。
      // const content_weight = this.$refs.tlp.$el.parentNode.clientWidth; // 文本容器宽度(父节点)
      if (ev_weight > content_weight) {
        // 实际宽度 > 可视宽度  文字溢出
        this.isShowTooltip = false;
      } else {
        // 否则为不溢出
        this.isShowTooltip = true;
      }
    },
    findNode(tree, func) {
      for (const node of tree) {
        if (func(node)) return node;
        if (node.childrens) {
          const res = this.findNode(node.childrens, func);
          if (res) return res;
        }
      }
      return null;
    },
    sizeFilter(size) {
      if (size) {
        size *= 1000;
        if (size < 0.1 * 1024) {
          //小于0.1KB，则转化成B
          size = size.toFixed(2) + 'B';
        } else if (size < 0.1 * 1024 * 1024) {
          // 小于0.1MB，则转化成KB
          size = (size / 1024).toFixed(2) + 'KB';
        } else if (size < 0.1 * 1024 * 1024 * 1024) {
          // 小于0.1GB，则转化成MB
          size = (size / (1024 * 1024)).toFixed(2) + 'MB';
        } else {
          // 其他转化成GB
          size = (size / (1024 * 1024 * 1024)).toFixed(2) + 'GB';
        }
        // 转成字符串
        let sizeStr = size + '',
          // 获取小数点处的索引
          index = sizeStr.indexOf('.'),
          // 获取小数点后两位的值
          dou = sizeStr.substr(index + 1, 2);

        // 判断后两位是否为00，如果是则删除00
        if (dou == '00') return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);

        return size;
      }
      return '0KB';
    },
    handleLoad(url, name) {
      loadFile(url, name);
    },
    filterNode(value, data) {
      console.log(value, data, '-------------');
      if (!value) return true;
      return data.name.indexOf(value) !== -1;
    },
    addCommand(val) {
      console.log(val);
      switch (val) {
        case '0': //新增分类
          this.handleAddForm(2);
          break;
        case '1':
          this.handleEditForm(1);
          break;
        case '2':
          this.handleRemove(1);
          break;
        case '3':
          this.handleEditForm(2);
          break;
        case '4':
          this.handleRemove(2);
          break;
        default:
          break;
      }
    },
    getPostList() {
      axios.get('/api/blade-system/post/list?size=1000').then(res => {
        this.postData = res.data.data.records;
        this.postData.unshift({ id: '', postName: '全部' });
      });
    },
    getDeptList() {
      axios.get(`/api/blade-system/dept/tree?tenantId=${this.userInfo.tenant_id}`).then(res => {
        this.deptData = res.data.data[0].children;
        this.deptData.unshift({ value: '', title: '全部' });
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.train-page {
  width: calc(100% - 12px);
  margin: 10px auto 0;
  height: 100%;
  // padding: 0 10px;
  box-sizing: border-box;
  border-radius: 10px;
  // box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

  display: flex;
  justify-content: space-between;
  align-items: flex-start;

  .train-page-left {
    width: 250px;
    height: 100%;
    overflow: auto;
    background-color: #fff;
    border-radius: 5px;
    padding: 16px;
    box-sizing: border-box;

    .train-page-left__add {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      align-items: center;
    }
  }

  .train-page-right {
    width: calc(100% - 266px);
    height: 100%;
    overflow: auto;
    background-color: #fff;
    border-radius: 5px;

    .title {
      width: 100%;
      height: 50px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding-left: 16px;
      box-sizing: border-box;

      .btns {
        width: 50%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 16px;
        box-sizing: border-box;
      }
    }
    .train-page-main {
      width: 100%;
      height: calc(100% - 50px);
      padding: 16px;
      box-sizing: border-box;

      .startBox {
        width: 100%;
        margin: 15px auto 0;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 130px;
  height: 130px;
  line-height: 130px;
  text-align: center;
  border: 1px dashed #d9d9d9;
  border-radius: 5px;
  box-sizing: border-box;
  overflow: hidden;
}
.avatar {
  width: 130px;
  height: 130px;
  display: block;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
  .item {
    display: inline-block;
    width: calc(100% - 20px);
  }
}
.hover_under:hover {
  text-decoration: underline;
}
.pagebox {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  height: 80px;
  padding: 25px 0 0;
  background: #fff;
  box-sizing: border-box;
}
</style>
