diff --git a/node_modules/mockjs/dist/mock.js b/node_modules/mockjs/dist/mock.js
index 35d5b9a..56b928d 100644
--- a/node_modules/mockjs/dist/mock.js
+++ b/node_modules/mockjs/dist/mock.js
@@ -8309,7 +8309,7 @@ return /******/ (function(modules) { // webpackBootstrap
 	            // 创建原生 XHR 对象，调用原生 open()，监听所有原生事件
 	            var xhr = createNativeXMLHttpRequest()
 	            this.custom.xhr = xhr
-
+				MockXMLHttpRequest.prototype.upload = xhr.upload
 	            // 初始化所有事件，用于监听原生 XHR 对象的事件
 	            for (var i = 0; i < XHR_EVENTS.length; i++) {
 	                xhr.addEventListener(XHR_EVENTS[i], handle)
