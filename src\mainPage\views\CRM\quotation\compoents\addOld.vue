<template>
  <basic-container class="mybasic_container">
    <el-affix target=".mybasic_container" :offset="100">
      <div style="background-color: #fff; padding-top: 15px">
        <Title
          >{{ isEdit ? '新增报价' : '编辑报价' }}
          <template #foot>
            <el-button type="primary" @click="submit" v-if="form.isOnline == 0" plain
              >保存</el-button
            >
            <el-button type="primary" @click="submit('confirm')">保存并提交</el-button>

            <el-button
              @click="
                $router.$avueRouter.closeTag();
                $router.back();
              "
              >关闭</el-button
            ></template
          ></Title
        >
      </div>
    </el-affix>

    <!-- <input type="file" @change="handleChange" ref="file" /> -->
    <avue-form :option="option" ref="addForm" style="margin-top: 5px" v-model="form"></avue-form>
    <div style="padding-left: 75px">
      <el-form>
        <el-form-item label="" style="margin-bottom: 18px">
          <el-button
            type="primary"
            icon="plus"
            size="small"
            v-if="!form.isHasOption"
            @click.stop="addProduct"
            plain
            >新增</el-button
          >
          <productSelectDrop
            @select="handleUserSelectConfirm"
            style="margin-left: 5px"
          ></productSelectDrop>
        </el-form-item>
      </el-form>
      <el-table
        :data="form.detailList"
        border
        class="avue-crud"
        ref="tableRef"
        show-summary
        row-key="uuid"
        :row-class-name="tableRowClassName"
        :summary-method="productSum"
      >
        <el-table-column label="基本信息" align="center">
          <el-table-column label="" width="40">
            <div>
              <i
                style="font-size: 20px; cursor: move"
                class="element-icons el-icon-yidong_huaban move3"
                title="拖拽排序"
              ></i>
            </div>
          </el-table-column>
          <el-table-column
            label="设备名称"
            class-name="allowDrag"
            show-overflow-tooltip
            min-width="200"
            prop="customProductName"
          >
            <template #default="{ row }" v-if="form.isHasCustom">
              <el-popover
                placement="top"
                title="原名称:"
                width="400"
                :disabled="row.productName == row.customProductName"
                effect="dark"
                trigger="hover"
                :content="row.productName"
              >
                <template #reference>
                  <span
                    v-if="!row.editName"
                    style="cursor: pointer"
                    @click="handleFocus(row, 'editName')"
                    >{{ row.customProductName }}</span
                  >
                  <el-input
                    type="textarea"
                    v-model="row.customProductName"
                    placeholder=""
                    v-else
                    :ref="`editName${row.uuid}`"
                    style="margin: 5px 0"
                    @blur="row.editName = false"
                    :rows="5"
                  ></el-input>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            label="规格型号"
            prop="customProductSpecification"
            show-overflow-tooltip
            min-width="200"
            class-name="allowDrag"
          >
            <template #default="{ row }" v-if="form.isHasCustom">
              <el-popover
                placement="top"
                title="原规格型号:"
                effect="dark"
                width="400"
                :disabled="row.productSpecification == row.customProductSpecification"
                trigger="hover"
                :content="row.productSpecification"
              >
                <template #reference>
                  <span
                    v-if="!row.editSpecification"
                    style="cursor: pointer"
                    @click="handleFocus(row, 'editSpecification')"
                    >{{ row.customProductSpecification }}</span
                  >
                  <el-input
                    type="textarea"
                    v-model="row.customProductSpecification"
                    placeholder=""
                    :ref="`editSpecification${row.uuid}`"
                    style="margin: 5px 0"
                    @blur="row.editSpecification = false"
                    :rows="5"
                  ></el-input>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column
            label="产品描述"
            prop="customProductDescription"
            show-overflow-tooltip
            min-width="200"
            class-name="allowDrag"
          >
            <template #default="{ row }">
              <el-popover
                placement="top"
                title="原描述:"
                effect="dark"
                :disabled="row.customProductDescription == row.description"
                width="400"
                trigger="hover"
                v-if="form.isHasCustom"
                :content="row.description"
              >
                <template #reference>
                  <span
                    v-if="!row.editDescription"
                    style="cursor: pointer"
                    @click="handleFocus(row, 'editDescription')"
                    >{{ row.customProductDescription }}</span
                  >
                  <el-input
                    type="textarea"
                    v-model="row.customProductDescription"
                    placeholder=""
                    :ref="`editDescription${row.uuid}`"
                    style="margin: 5px 0"
                    @blur="row.editDescription = false"
                    :rows="5"
                  ></el-input>
                </template>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column width="110" label="产品图片" class-name="allowDrag" #default="{ row }">
            <el-image
              style="width: 80px"
              :preview-src-list="[row.coverUrl]"
              :src="row.coverUrl"
            ></el-image>
          </el-table-column>
          <el-table-column
            label="品牌"
            class-name="allowDrag"
            width="90"
            show-overflow-tooltip
            prop="productBrand"
          ></el-table-column>
          <el-table-column
            width="80"
            label="单位"
            class-name="allowDrag"
            prop="unitName"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="报价信息" align="center">
          <el-table-column label="数量" width="80" #default="{ row }" prop="number">
            <el-input v-if="isEdit" v-model="row.number" size="small" style="width: 80%"></el-input>
            <span v-else>{{ row.number }}</span>
          </el-table-column>
          <el-table-column label="单价" width="110" #default="{ row, $index }" prop="sealPrice">
            <el-form @submit.prevent v-if="isEdit" :ref="'sealPrice' + $index" :model="row">
              <el-form-item
                prop="sealPrice"
                :rules="{
                  required: true,
                  message: '请输入单价',
                  trigger: 'blur',
                }"
              >
                <el-input
                  v-model="row.sealPrice"
                  placeholder="请输入单价"
                  size="small"
                  style="width: 100%"
                ></el-input>
              </el-form-item>
            </el-form>

            <span v-else>{{ row.sealPrice }}</span>
          </el-table-column>
          <el-table-column
            label="金额"
            width="120"
            class-name="allowDrag"
            #default="{ row }"
            prop="totalPrice"
          >
            {{ parseFloat(row.number * row.sealPrice).toLocaleString() || '---' }}
          </el-table-column>
          <el-table-column
            label="专项成本"
            #default="{ row, $index }"
            width="120"
            v-if="form.isHasSpecialPrice"
            prop="specialCostPrice"
          >
            <el-input
              v-if="isEdit"
              v-model="row.specialCostPrice"
              placeholder="请输入专项成本"
              size="small"
              style="width: 100%"
            ></el-input>
            <span v-else>{{ row.specialCostPrice }}</span>
          </el-table-column>
          <el-table-column
            label="专项供应商"
            #default="{ row, $index }"
            width="120"
            v-if="form.isHasSpecialPrice"
            prop="specialSupplierId"
          >
            <WfSupplierSelect
              v-if="isEdit"
              v-model="row.specialSupplierId"
              placeholder="请选择专项供应商"
              size="small"
              style="width: 100%"
            ></WfSupplierSelect>
            <span v-else>{{ row.specialSupplierName }}</span>
          </el-table-column>
        </el-table-column>
        <el-table-column label="参考信息" align="center">
          <el-table-column
            label="最近销售价"
            class-name="allowDrag"
            width="100"
            #default="{ row }"
            prop="preSealPrice"
          >
            <span>{{
              parseFloat(row.preSealPrice).toLocaleString() == 'NaN'
                ? '--'
                : parseFloat(row.preSealPrice).toLocaleString()
            }}</span>
          </el-table-column>
          <el-table-column
            label="成本价"
            width="120"
            class-name="allowDrag"
            #default="{ row }"
            prop="costPrice"
          >
            <div v-if="row.priceWarnType == 0 || !row.priceWarnType">
              <div>
                {{
                  parseFloat(row.costPrice).toLocaleString() == 'NaN'
                    ? '--'
                    : parseFloat(row.costPrice).toLocaleString()
                }}
              </div>
            </div>
            <div v-else>
              <div v-if="row.priceWarnType == 1" style="color: var(--el-color-success)">
                {{
                  parseFloat(row.costPrice).toLocaleString() == 'NaN'
                    ? '--'
                    : parseFloat(row.costPrice).toLocaleString()
                }}
              </div>
              <div v-else-if="row.priceWarnType == 2" style="color: var(--el-color-warning)">
                {{
                  parseFloat(row.costPrice).toLocaleString() == 'NaN'
                    ? '--'
                    : parseFloat(row.costPrice).toLocaleString()
                }}
              </div>
              <div v-else style="color: var(--el-color-danger)">
                {{
                  parseFloat(row.costPrice).toLocaleString() == 'NaN'
                    ? '--'
                    : parseFloat(row.costPrice).toLocaleString()
                }}
              </div>
            </div>
          </el-table-column>
          <el-table-column
            label="参考销售价"
            class-name="allowDrag"
            width="100"
            #default="{ row }"
            prop="referSealPrice"
          >
            <span>{{
              parseFloat(row.referSealPrice).toLocaleString() == 'NaN'
                ? '--'
                : parseFloat(row.referSealPrice).toLocaleString()
            }}</span>
          </el-table-column>
          <el-table-column
            label="最低销售价"
            class-name="allowDrag"
            width="100"
            #default="{ row }"
            prop="minSealPrice"
          >
            <span>{{
              parseFloat(row.minSealPrice).toLocaleString() == 'NaN'
                ? '--'
                : parseFloat(row.minSealPrice).toLocaleString()
            }}</span>
          </el-table-column>
          <el-table-column
            label="市场价"
            class-name="allowDrag"
            #default="{ row }"
            width="100"
            prop="marketPrice"
          >
            <span>{{
              parseFloat(row.marketPrice).toLocaleString() == 'NaN'
                ? '--'
                : parseFloat(row.marketPrice).toLocaleString()
            }}</span>
          </el-table-column>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          prop=""
          width="120"
          fixed="right"
          class-name="allowDrag"
          #default="{ row, index }"
          v-if="isEdit"
        >
          <el-button tabindex="-1" type="primary" text icon="delete" @click="deleteProduct(row)"
            >删除</el-button
          >
        </el-table-column>
      </el-table>
    </div>
    <!-- 产品选择弹窗 -->
    <wf-product-select
      ref="product-select"
      check-type="box"
      @onConfirm="handleUserSelectConfirm"
    ></wf-product-select>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { getCurrentInstance, onBeforeUnmount, onMounted } from 'vue';
import { useRoute, useRouter, onBeforeRouteLeave } from 'vue-router';
import Sortable from 'sortablejs';
import WfProductSelect from '@/components/Y-UI/wf-product-select.vue';
import WfSupplierSelect from '@/views/plugin/workflow/components/custom-fields/wf-supplier-select/index.vue';
import productSelectDrop from './productSelectDrop.vue';
import { randomLenNum } from '@/utils/util';
let { proxy } = getCurrentInstance();
// 设置拖拽排序

const setSort = () => {
  const el = proxy.$refs.tableRef.$el.querySelector('tbody');
  new Sortable(el, {
    handle: '.move3',
    animation: 180,
    delay: 0,
    onEnd: e => {
      const targetRow = form.value.detailList.splice(e.oldIndex, 1);
      form.value.detailList.splice(e.newIndex, 0, targetRow[0]);
      console.log(form.value.detailList);
    },
  });
};
onMounted(() => {
  setSort();
  if (route.query.roleType == 'assist') {
    console.log(111);
    setBusinessUrl();
    setCustomerUrl();
  }
});
let route = useRoute();
let router = useRouter();
let form = ref({
  detailList: [],
});

let isEdit = ref(true);
let option = ref({
  submitBtn: false,
  labelWidth: 140,
  detail: false,
  emptyBtn: false,
  column: [
    {
      label: '报价名称',
      prop: 'offerName',
      rules: [
        {
          required: true,
          message: '请填写方案名称',
        },
        {
          validator: (rule, value, callback) => {
            const reg = /^[^/\\?？\[\]]*$/;
            console.log(value, rule, reg);
            if (!reg.test(value)) {
              callback(new Error('不能包含特殊字符"/\?？[]"'));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联商机',
      prop: 'businessOpportunityId',
      component: 'wf-business-select',
      params: {
        isNew: true,
      },
      change: val => {
        if (val.value) {
          getBusinessDetail(val.value);
        }
      },
      control: val => {
        return {
          businessTypeId: {
            display: !val,
          },
        };
      },
    },
    {
      type: 'select',
      dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
      props: {
        label: 'dictValue',
        value: 'id',
      },
      label: '业务板块',
      // multiple: true,
      span: 12,
      parent: true,
      rules: [
        {
          required: true,
          message: '请选择业务板块',
        },
      ],
      display: true,
      filterable: true,
      prop: 'businessTypeId',
      checkStrictly: true,
    },
    {
      label: '对应客户',
      prop: 'customerId',
      component: 'wf-customer-select',
      change: val => {
        const contactPerson = proxy.findObject(option.value.column, 'contactPerson');

        if (!val.value) {
          contactPerson.disabled = true;
          return;
        }
        contactPerson.disabled = false;
        contactPerson.params.Url = '/vt-admin/customerContact/page?customerId=' + val.value;
        setBaseInfo(val.value);
      },
      params: {},
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
    },
    {
      label: '协作业务员',
      placeholder: '请选择协作业务员',
      type: 'select',
      dicData: [],
      display: false,
      prop: 'businessPerson',
      rules: [
        {
          required: true,
          message: '请选择协作业务员',
          trigger: 'change',
        },
      ],
    },
    {
      label: '关联联系人',
      prop: 'contactPerson',
      component: 'wf-contact-select',
      disabled: true,
      rules: [
        {
          required: true,
          message: '请选择客户',
        },
      ],
      params: {
        // checkType: 'box',
        Url: '/vt-admin/customerContact/page',
      },
    },

    // {
    //   label: '报价人员',
    //   component: 'wf-user-select',
    //   prop: 'offerPeople',
    // },
    // {
    //   label: '报价日期',
    //   prop: 'offerDate',
    //   type: 'date',
    //   readonly: true,
    //   format: 'YYYY-MM-DD',
    //   valueFormat: 'YYYY-MM-DD',
    //   value: dateFormat(new Date(), 'yyyy-MM-dd'),
    // },
    {
      label: '报价有效期（天）',
      prop: 'offerValidity',
      type: 'number',
    },
    {
      label: '线下报价',
      prop: 'isOnline',
      type: 'radio',
      span: 4,
      // display: !route.query.id,
      value: 0,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
    {
      label: '自定义',
      prop: 'isHasCustom',
      readonly: true,
      type: 'switch',
      labelTip: '打开后点击文字即可编辑',
      span: 4,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      label: '专项报价',
      prop: 'isHasSpecialPrice',
      type: 'radio',
      span: 4,
      value: 0,
      dicData: [
        {
          label: '是',
          value: 1,
        },
        {
          label: '否',
          value: 0,
        },
      ],
    },
  ],
});
watchEffect(() => {
  if (route.query.id) {
    getDetail(route.query.id);
  }
  if (route.query.businessId) {
    getBusinessDetail(route.query.businessId);
  }
});
function addProduct() {
  proxy.$refs['product-select'].visible = true;
}
function handleUserSelectConfirm(ids) {
  ids.split(',').forEach(item => {
    axios.get('/api/vt-admin/product/detail?id=' + item).then(r => {
      form.value.detailList.push({
        ...r.data.data,
        productId: r.data.data.id,
        preSealPrice: r.data.data.sealPrice,
        unitPrice: r.data.data.purchasePrice,
        sealPrice: '',
        number: 1,
        id: null,
        uuid: randomLenNum(10, true),
        customProductName: r.data.data.productName,
        customProductSpecification: r.data.data.productSpecification,
        customProductDescription: r.data.data.description,
      });
    });
  });
}
function submit(type) {
  if (form.value.detailList.length == 0) {
    return proxy.$message.warning('请至少添加一个产品');
  }

  if (type == 'confirm') {
    let res = [];

    form.value.detailList.forEach((item, index) => {
      proxy.$refs['sealPrice' + index].validate(valid => {
        console.log(valid);
        res.push(valid);
      });
    });

    setTimeout(() => {
      if (!res.some(i => !i)) {
        proxy.$refs.addForm.validate((valid, done) => {
          if (valid) {
            proxy
              .$confirm('确认此次操作吗？', '提示')
              .then(() => {
                sumitForm(type);
              })
              .catch(err => {
                done();
              });
          }
        });
      }
    }, 0);
  } else {
    proxy.$refs.addForm.validate((valid, done) => {
      if (valid) {
        proxy
          .$confirm('确认此次操作吗？', '提示')
          .then(() => {
            sumitForm(type, done);
          })
          .catch(err => {
            done();
          });
      }
    });
  }
}
function sumitForm(type, done) {
  console.log(type,form.value);
  if (route.query.id || form.value.id) {
    axios
      .post('/api/vt-admin/offer/businessUpdateOffer', {
        ...form.value,
        detailList: form.value.detailList.map((i, index) => {
          return {
            ...i,
            sortNumber: index,
          };
        }),
        id: route.query.id || form.value.id ,
        offerPrice: offerPrice(),
        offerStatus: type == 'confirm' ? 1 : 0,
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        if (type == 'confirm') {
          isEdit.value = false;
          router.$avueRouter.closeTag();
          router.go(-1);
        }
        done();
      });
  } else {
    let url;
    if (form.value.businessOpportunityId) {
      url = '/api/vt-admin/businessOpportunity/noOptionAddOffer';
    } else {
      url = '/api/vt-admin/offer/noBusinessAddOffer';
    }
    axios
      .post(url, {
        ...form.value,
        detailList: form.value.detailList.map((i, index) => {
          return {
            ...i,
            sortNumber: index,
          };
        }),
        offerPeople: proxy.$store.getters.userInfo.user_id,
        offerStatus: type == 'confirm' ? 1 : 0,
        offerPrice: offerPrice(),
      })
      .then(res => {
        proxy.$message.success(res.data.msg);
        if (type == 'confirm') {
          isEdit.value = false;
          router.$avueRouter.closeTag();
          router.go(-1);
        } else {
          getDetail(res.data.data);
        }
        done();
      });
  }
}
function offerPrice() {
  return form.value.detailList.reduce((prev, curr) => {
    const value = Number(curr.sealPrice) * Number(curr.number);
    if (!Number.isNaN(value)) {
      return prev + value;
    } else {
      return prev;
    }
  }, 0);
}
function setBaseInfo(id) {
  axios
    .get('/api/vt-admin/customer/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const mainPerson = res.data.data.customerContactVO?.id;
      form.value.contactPerson = mainPerson;
      const businessPersonRef = proxy.findObject(option.value.column, 'businessPerson');
      if (res.data.data.businessPerson.split(',').length > 1 && route.query.roleType == 'assist') {
        businessPersonRef.display = true;
        const dicData = res.data.data.businessPerson.split(',').map((item, index) => {
          return {
            value: item,
            label: res.data.data.businessPersonName.split(',')[index],
          };
        });
        businessPersonRef.dicData = dicData;
      } else if (
        res.data.data.businessPerson.split(',').length == 1 &&
        route.query.roleType == 'assist'
      ) {
        form.value.businessPerson = res.data.data.businessPerson;
        businessPersonRef.display = false;
      } else {
        businessPersonRef.display = false;
      }
    });
}
function getDetail(id) {
  axios
    .get('/api/vt-admin/offer/detail', {
      params: {
        id: id,
      },
    })
    .then(res => {
      const {
        detailVOList,
        id,
        customerId,
        businessOpportunityId,
        contactPerson,
        offerName,
        isHasOption,
        offerValidity,
        offerDate,
        businessTypeId,
        isHasSpecialPrice,
        isHasCustom,
        isOnline
      } = res.data.data;
      form.value.detailList = detailVOList.map(item => {
        delete item.product.id;
        return {
          number: item.number,
          ...item.product,
          unitPrice: item.unitPrice,
          productId: item.productId,
          specialCostPrice: item.specialCostPrice,
          specialSupplierId: item.specialSupplierId,
          preSealPrice: item.product.sealPrice,
          sealPrice: item.sealPrice,
          id: item.id,
          uuid: randomLenNum(10, true),

          customProductName: item.customProductName,
          customProductSpecification: item.customProductSpecification,
          customProductDescription: item.customProductDescription,
        };
      });
      form.value.businessOpportunityId = businessOpportunityId;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = offerName;
      form.value.isHasOption = isHasOption;
      form.value.offerValidity = offerValidity;
      form.value.offerDate = offerDate;
      form.value.businessTypeId = businessTypeId;
      form.value.isHasSpecialPrice = isHasSpecialPrice;
      form.value.isHasCustom = isHasCustom;
      form.value.id = id;
      form.value.isOnline = isOnline;
    });
}
function getBusinessDetail(id) {
  axios
    .get('/api/vt-admin/businessOpportunity/detail', {
      params: {
        id,
      },
    })
    .then(res => {
      const {
        productVOList,
        // id: businessOpportunityId,
        customerId,
        id: businessOpportunityId,
        contactPerson,
        name,
      } = res.data.data;
      form.value.detailList = productVOList.map(item => {
        return {
          number: item.number,
          ...item.productVO,
          productId: item.productId,
          preSealPrice: item.productVO.sealPrice,
          unitPrice: item.productVO.purchasePrice,
          id: item.id,
        };
      });
      form.value.businessOpportunityId = businessOpportunityId;
      form.value.customerId = customerId;
      form.value.contactPerson = contactPerson;
      form.value.offerName = name;
    });
}
function productSum(param) {
  const { columns, data } = param;
  const sums = [];
  columns.forEach((column, index) => {
    if (index === 0) {
      sums[index] = '总计';
      return;
    }

    if (column.property == 'number') {
      const values = data.map(item => Number(item[column.property]));

      sums[index] = `${values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0)}`;
    } else if (column.property == 'prePrice') {
      // const values = data.map(item => Number(item[column.property]));
      // sums[index] = `￥ ${values.reduce((prev, curr) => {
      //   const value = Number(curr);
      //   if (!Number.isNaN(value)) {
      //     return prev + curr;
      //   } else {
      //     return prev;
      //   }
      // }, 0)}`
    } else if (column.property == 'totalPrice') {
      const values = data.map(item => Number(item.number * item.sealPrice));
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    } else if (column.property == 'costPrice') {
      const values = data.map(item =>
        Number(
          item.number *
            (form.value.isHasSpecialPrice == 1 && item.specialCostPrice
              ? item.specialCostPrice * 1
              : item.costPrice * 1)
        )
      );
      const value = values.reduce((prev, curr) => {
        const value = Number(curr);
        if (!Number.isNaN(value)) {
          return prev + curr;
        } else {
          return prev;
        }
      }, 0);
      sums[index] = `￥ ${parseFloat(value).toLocaleString()}`;
    }
  });

  return sums;
}
function deleteProduct(row) {
  proxy
    .$confirm('确认删除此产品吗？', '提示', {
      type: 'warning',
    })
    .then(() => {
      form.value.detailList = form.value.detailList.filter(item => item.id != row.id);
    });
}

function tableRowClassName({ row }) {
  if (row.isNew === 1) {
    return 'warning-row';
  } else {
    return '';
  }
}
function setBusinessUrl(params) {
  const businessOpportunity = proxy.findObject(option.value.column, 'businessOpportunityId');
  businessOpportunity.params.Url = '/vt-admin/businessOpportunity/page?selectType=3';
}
function setCustomerUrl() {
  const customer = proxy.findObject(option.value.column, 'customerId');
  customer.params.Url = '/vt-admin/customer/page?type=2';
}
onBeforeUnmount(() => {});
function handleFocus(row, value) {
  row[value] = true;
  console.log(proxy.$refs);
  proxy.$nextTick(() => {
    proxy.$refs[`${value}${row.uuid}`].focus();
  });
}
onBeforeRouteLeave((to, from) => {
  if (isEdit.value) {
    const answer = window.confirm('确定离开当前页面吗？');
    // 取消导航并停留在同一页面上
    if (!answer) {
      proxy.$store.commit('ADD_TAG', {
        name: from.name || from.name,
        path: from.path,
        fullPath: from.fullPath,
        params: from.params,
        query: from.query,
        meta: from.meta,
      });
      return false;
    }
  }
});
</script>

<style scoped>
.el-table .el-form-item {
  margin-bottom: 0;
}
</style>
