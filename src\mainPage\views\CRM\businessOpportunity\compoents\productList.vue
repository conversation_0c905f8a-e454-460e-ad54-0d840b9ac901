<template>
  <el-drawer title="查看关联产品" size="60%" v-model="drawer">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #cycle-form>
        <div style="width: 100%; display: flex; align-items: center">
          <el-input
            v-model.number="form.cycleNumber"
            placeholder="请输入"
            style="width: 20%; margin-right: 5px"
          />
          <el-radio v-model="form.cycleType" :label="0">天</el-radio>
          <el-radio v-model="form.cycleType" :label="1">月</el-radio>
          <el-radio v-model="form.cycleType" :label="2">年</el-radio>
        </div>
      </template>
    </avue-crud>
  </el-drawer>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  labelWidth: 130,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
  {
      label: '产品',
      prop: 'customProductName',
      disabled: true,
      display: false,
      span: 24,
      overHidden: true,
      formatter: row => {
        return row.customProductName ||  row.productVO?.productName;
      },
    },
    {
      label: '品牌',
      prop: 'customProductBrand',
      disabled: true,
      display: false,
      overHidden: true,
      // search: true,
      span: 24,
      type: 'input',
      formatter: row => {
        return row.productBrand || row.productVO?.productBrand;
      },

    },
    {
      label: '规格型号',
      prop: 'customProductSpecification',
      disabled: true,
      overHidden: true,
      display: false,
      // search: true,
      span: 24,
      type: 'input',
      formatter: row => {
        return row.customProductSpecification ||  row.productVO?.productSpecification;
      },
    },
    {
      label:'单位',
      prop: 'customUnit',
      disabled: true,
      display: false,
      formatter: row => {
        return row.customUnit ||  row.productVO?.unitName;
      },
    },
    {
      label: '数量',
      prop: 'number',

      type: 'number',
      span: 12,
      cell: false,
    },

    {
      label: '单价',
      prop: 'sealPrice',
      type: 'number',
      span: 12,
      cell: false,
    },
    {
      label: '金额',
      prop: 'hsze',
      type: 'number',
      editDisplay: false,
      span: 12,
      cell: false,
      formatter: row => {
        return (row.number * row.sealPrice).toFixed(2);
      },
    },
    {
      type: 'date',
      label: '开始使用日期',
      span: 24,
      width: 130,

      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'useDate',
    },
    {
      type: 'date',
      label: '到期时间',
      span: 24,
      width: 130,
      editDisplay: false,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'overDate',
      rules: [
        {
          required: true,
          message: '请选择到期时间',
          trigger: 'blur',
        },
      ],
    },
    {
      type: 'input',
      label: '使用周期',
      width: 110,
      span: 24,
      display: true,
      prop: 'cycle',
      formatter: row => {
        return row.cycleNumber + `${['天', '月', '年'][row.cycleType]}`;
      },
    },
    {
      type: 'select',
      label: '提醒时间',
      width: 110,
      span: 24,
      display: true,
      props: {
        value: 'value',
        label: 'label',
      },
      dicData: (() => {
        let arr = [];
        for (let i = 1; i <= 31; i++) {
          arr.push({ label: '到期' + i + '天前', value: i });
        }
        return arr;
      })(),
      prop: 'beforeDays',
    },
    {
      label: '备注',
      prop: 'remark',
      span: 24,
      overHidden: true,
      type: 'textarea',
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/api/vt-admin/businessOpportunityProduct/remove?ids=';
const updateUrl = '/api/vt-admin/businessOpportunityProduct/update';
const tableUrl = '/api/vt-admin/businessOpportunityProduct/pageForRenew';
let params = ref({});
let tableData = ref([]);
let businessOpportunityId = ref('');
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);

function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        businessOpportunityId: businessOpportunityId.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    hsze: row.number * row.sealPrice,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}

let drawer = ref(false);
function open(params) {
  drawer.value = true;
  onLoad();
}
defineExpose({
  open,
  businessOpportunityId
});
</script>

<style lang="scss" scoped></style>
