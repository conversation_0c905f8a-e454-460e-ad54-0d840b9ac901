<template>
  <div style="margin-bottom: 10px">
    <el-card>
      <template #header>
        <div style="display: flex; justify-content: space-between; align-items: center">
          <div>跟进数量统计</div>
          <div style="display: flex; gap: 20px">
            <el-date-picker
              v-model="query.year"
              value-format="YYYY"
              @change="getDetail"
              type="year"
              :clearable="false"
              placeholder=""
            ></el-date-picker>
            <!-- <el-radio-group @change="getDetail" v-model="query.type1">
              <el-radio-button label="0">月</el-radio-button>
              <el-radio-button label="1">季度</el-radio-button>
              <el-radio-button label="2">年</el-radio-button>
            </el-radio-group> -->
            <wfUserSelectDrop
              v-model="query.userId"
              @change="getDetail"
              v-if="form.selectType != 0"
              style="width: 100px"
            ></wfUserSelectDrop>
          </div>
        </div>
      </template>
      <!-- 图表 -->
      <div style="height: 400px">
        <div ref="chartRef" style="height: 100%"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { inject } from 'vue';
import wfUserSelectDrop from './userSelect.vue';
import * as echarts from 'echarts';
import { ref, onMounted } from 'vue';
import moment from 'moment';
const chartRef = ref(null);
const radio2 = ref('月');
onMounted(() => {
  getDetail();
});
let query = ref({
  year: moment(new Date()).format('YYYY'),
});
const form = inject('form');
watch(form.value, () => {
 
  query.value.userId = form.value.userId;
  query.value.year = form.value.year;
   getDetail();
});
function initChart() {
  const chart = echarts.init(chartRef.value);
  let option = {
    color: ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399', '#f3d19e'],

    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: true,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 10,
      gap: 10,
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: detailForm.value.x, //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '跟进数量', //名称
        type: 'value', //连续类型
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    series: Object.keys(detailForm.value.y).map(key => {
      return {
        name: key, //名称
        type: 'bar', //类型
        stack: 'ad',
        barWidth: 18, //宽度
        data: detailForm.value.y[key], //数值
        z: 1,
        label: {
          show: true,
        
          formatter: function (params) {
            const a = params.data;
          
            if (a > 0) {
              
              return a;
            } else {
              return '';
            }
          },
        },
      };
    }),
  };
  chart.setOption(option);
}

let detailForm = ref({ y: [] });
function getDetail() {
  axios
    .get('/api/vt-admin/statistics/followNumberStatistics', {
      params: {
        ...form.value,
        ...query.value,
      },
    })
    .then(res => {
      detailForm.value = res.data.data;
      initChart();
    });
}
</script>

<style lang="scss" scoped>
.el-card {
  height: 500px;
}
</style>
