import { createApp } from 'vue';
import website from './config/website';
import axios from './axios';
import router from './router/';
import store from './store';
import i18n from './lang/';
import { language, messages } from './lang/';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import ElementPlus from 'element-plus';
import 'element-plus/dist/index.css';
import Avue from '@smallwei/avue';
import '@smallwei/avue/lib/index.css';
import AvueUeditor from 'avue-plugin-ueditor';
import crudCommon from '@/mixins/crud.js';
import { getScreen } from './utils/util';
import './permission';
import error from './error';
import dialogForm from 'components/DialogForm/index.vue';
import avueUeditor from 'components/avue-ueditor/main.vue';
import basicBlock from 'components/basic-block/main.vue';
import basicContainer from 'components/basic-container/main.vue';
import thirdRegister from './components/third-register/main.vue';
import NfDesignBase from '@saber/nf-design-base-elp';
import flowDesign from './components/flow-design/main.vue';
import title from './components/title/index.vue';
import file from './components/file/index.vue';
import form from './components/DialogForm/plugin';
import Sheet from './views/CRM/compoents/sheet.vue';
import print from 'vue3-print-nb';
import '@/assets/icon/iconfont.css';
import uploadService from './components/GlobalUploader/uploadService';

import App from './App.vue';
import 'animate.css';
import dayjs from 'dayjs';
import 'styles/common.scss';
// 业务组件
import tenantPackage from './views/system/tenantpackage.vue';

// 字典
import constData from '@/const/const.js';
window.$crudCommon = crudCommon;
window.axios = axios;
const app = createApp(App);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

// 挂载字典
app.config.globalProperties.$const = constData;

// 注册组件
app.component('dialogForm', dialogForm);
app.component('avueUeditor', avueUeditor);
app.component('basicContainer', basicContainer);
app.component('basicBlock', basicBlock);
app.component('thirdRegister', thirdRegister);
app.component('flowDesign', flowDesign);
app.component('tenantPackage', tenantPackage);
app.component('Title', title);
app.component('File', file);
app.component('Sheet', Sheet);
app.use(AvueUeditor);
app.config.globalProperties.$dayjs = dayjs;
app.config.globalProperties.website = website;

app.config.globalProperties.getScreen = getScreen;
// 将上传服务挂载到全局属性
app.config.globalProperties.$upload = uploadService;
app.use(error);
app.use(i18n);
app.use(store);
app.use(router);
app.use(print);
app.use(ElementPlus, {
  locale: messages[language],
});
app.use(Avue, {
  axios,
  calcHeight: 10,
  locale: messages[language],
});
app.use(NfDesignBase);
app.use(form);
app.directive('drag', {
  mounted(el) {
    // 获取可移动元素的父节点
    let parentNode = el.parentNode;
    // 设置父节点定位
    parentNode.style.position = 'relative';
    el.style.position = 'absolute';

    // 设置鼠标hover效果:移动上前去显示可移动的提示效果，并且禁用页面可选择，离开恢复正常
    el.onmouseover = () => {
      el.style.cursor = 'move';
    };
    el.onmouseout = () => {
      el.style.cursor = 'none';
    };
    // 防止选中移动块上的文字等
    parentNode.onmouseover = () => {
      document.onselectstart = () => {
        return false;
      };
    };
    parentNode.onmouseout = () => {
      document.onselectstart = () => {
        return true;
      };
    };

    el.onmousedown = event => {
      //event的兼容,同时得到clientX,的值
      var event = event || window.event;
      let x = event.clientX - el.offsetLeft;
      let y = event.clientY - el.offsetTop; //得到小段的偏移
      // 将移动事件绑定到 document 上，防止拖动过快脱离开
      document.onmousemove = event => {
        let xx = event.clientX - x; //当移动的时候，用它的鼠标值减去偏移量
        let yy = event.clientY - y;
        if (xx <= 0) {
          //判定边界值 0，就在最边上了,
          xx = 0;
        }
        if (xx >= parentNode.offsetWidth - el.offsetWidth) {
          //大于整个盒子的宽度-小盒子的宽度
          xx = parentNode.offsetWidth - el.offsetWidth;
        }
        if (yy <= 0) {
          yy = 0;
        }
        if (yy >= parentNode.offsetHeight - el.offsetHeight) {
          yy = parentNode.offsetHeight - el.offsetHeight;
        }

        el.style.left = xx + 'px';
        el.style.top = yy + 'px';
      };
      el.onmouseup = () => {
        // 取消事件
        document.onmousemove = null;
        el.onmouseup = null;
      };
    };
  },
});

// import NfDesign from '@nutflow/nf-design-elp';
// import NfForm from '@nutflow/nf-form-elp';
// import NfFormDesign from '@nutflow/nf-form-design-elp';
import NfCustomFields from '@/views/plugin/workflow/components/custom-fields'; // 自定义字段
// app.use(NfDesign, { locale: messages[language] }).use(NfForm).use(NfFormDesign).use(NfCustomFields);
app.use(NfCustomFields);

app.mount('#app');

export default app;
