<template>
    <basic-container block>
      <el-container style="height: 100%;">
        <el-aside
          width="150px"
          style="margin-right: 20px; height: calc(100% - 20px); overflow: hidden"
        >
          <avue-tree
            :option="treeOption"
            :data="treeData"
            node-key="id"
            style="height: 680px"
            ref="tree"
            @del="treeDel"
            :beforeOpen="beforeOpen"
            @check-change="treeCheck"
            :permission="getPermission"
            @node-click="handleNodeClick"
            v-model="treeForm"
          >
          </avue-tree>
        </el-aside>
        <el-main>
          <div class="calendar-box">
            <div class="calendar-box-left">
              <MCalendarWeek type="all" ref="calendarRef" :userId="currentNode.id" @calendarChange="handlePlanChange" />
            </div>
            <div class="calendar-box-right">
              <MToDoListWeek :data="planList" type="all" :date="planDate" />
            </div>
          </div>
        </el-main>
      </el-container>
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ElMessage } from 'element-plus';
  import { ref, getCurrentInstance, onMounted, onActivated, nextTick } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import MCalendarWeek from '@/views/home/<USER>';
  import MToDoListWeek from '@/views/home/<USER>';
  onMounted(() => {
    getTreeData();
  });
  let treeData = ref([]);
  let tree = ref(null);
  function getTreeData() {
    axios
      .get('/api/blade-system/user/under-list', {
        params: {
          size: 5000,
        },
      })
      .then(res => {
        treeData.value = res.data.data;
        nextTick(() => {
          tree.value.setCurrentKey(treeData.value[0].id);
          currentNode.value = treeData.value[0];
          queryPlanList();
        });
      });
  }
  
  let treeOption = ref({
    defaultExpandAll: true,
    menu: false,
    addBtn: false,
    // multiple: true,
    draggable: true,
  
    props: {
      label: 'realName',
      children: 'children',
      value: 'id',
    },
  });
  let currentNode = ref({});
  let treeForm = ref({});
  function handleNodeClick(node) {
    currentNode.value = node;
    queryPlanList();
  }
  // 计划
  let planDate = ref([]);
  let planList = ref([]);
  function handlePlanChange(e) {
    planDate.value = e;
    queryPlanList();
  }
  // 查询日历待办列表
  function queryPlanList() {
    if(!currentNode.value.id) return 
    axios({
      url: '/api/vt-admin/weekPlan/list',
      method: 'get',
      params: {
        startDate: planDate.value[0],
        endDate: planDate.value[1],
        userId: currentNode.value.id,
      },
    }).then(e => {
      let data = e.data.data;
      planList.value = data;
    });
  }
  </script>
  
  <style lang="scss" scoped>
  .calendar-box {
    width: 100%;
    height: calc(100% - 40px);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  
    .calendar-box-left {
      width: 600px;
      height: 100%;
    }
    .calendar-box-right {
      width: calc(100% - 325px);
      height: 100%;
  
      padding: 10px 0 10px 16px;
      box-sizing: border-box;
    }
  }
  </style>
  