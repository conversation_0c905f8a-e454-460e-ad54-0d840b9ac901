<template>
  <div>
    <el-input v-model="name"
              :size="size"
              suffix-icon="el-icon-user"
              :placeholder="placeholder || '合同选择'"
              readonly
              :disabled="disabled"
              @click="handleSelect"></el-input>
    <!-- 商机选择弹窗 -->
    <wf-order-select ref="order-select"
                    :userUrl="Url"
                    :check-type="checkType"
                    :default-checked="modelValue"
                    @onConfirm="handleUserSelectConfirm"></wf-order-select>
  </div>

</template>
<script>
import { getUser } from "../../../api/process/user"

import WfOrderSelect from '@/components/Y-UI/wf-order-select.vue'

export default {
  name: 'order-select',
  components: { WfOrderSelect },
  emits: ['update:modelValue'],
  props: {
    modelValue: [String, Number],
    checkType: { // radio单选 checkbox多选
      type: String,
      default: () => {
        return 'radio'
      }
    },
    size: {
      type: String,
      // default: () => {
      //   return 'small'
      // }
    },
    readonly: {
      type: Boolean,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    placeholder: String,
    Url: {
      type: String,
      default: () => {
        return '/vt-admin/purchaseOrder/page'
      }
    },
    change: Function
  },
  watch: {
    modelValue: {
      handler (val) {
        if (val) {
          const name = []
          const checks = (val + '').split(',')
          const asyncList = []
          checks.forEach(c => {
            asyncList.push(this.getDetail(c))
          })
          Promise.all(asyncList).then(res => {
            res.forEach(r => {
              const data = r.data.data
              if (data) name.push(data.orderNo) 
            })
            this.name = name.join(',')
          })
        } else this.name = ''
      },
      immediate: true
    }
  },
  data () {
    return {
      name: '',
    }
  },
  methods: {
    handleSelect () {
      if (this.readonly || this.disabled) return
      else this.$refs['order-select'].visible = true
    },
    handleUserSelectConfirm (id) {
      this.$emit('update:modelValue', id)
      if (this.change && typeof this.change == 'function') this.change({ value: id })
    },
    getDetail(c) {
     
     return axios.get('/api/vt-admin/purchaseOrder/detail?id=' + c, {
       id: c,
     });
   },
  }
}
</script>
<style lang="scss">
</style>