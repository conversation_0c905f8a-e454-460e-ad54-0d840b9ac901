<template>
  <el-dialog title="产品选择" v-model="dialogVisible" width="60%">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      @selection-change="handleSelectionChange"
      v-model="form"
    >
    </avue-crud>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleConfirm">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, computed, watch,defineEmits } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
const props = defineProps(['type','id']);

const emit = defineEmits('onConfirm')
let option = ref({
 
  align: 'center',
  addBtn: false,
  menu:false,
  selection:true,
  editBtn: true,
  delBtn: true,
 
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  border: true,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
     
      rules: [
        {
          required: true,
          message: '请输入产品名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '品牌',
      prop: 'productBrand',
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden:true
     
    },
    {
      label: '数量',
      prop: 'number',
    },
  ],
});
let form = ref({});

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
const url = computed(() => {
  const obj = {
    0: '/api/vt-admin/purchaseOrder/detailForInquiry',
  };
  return obj[props.type];
});
function onLoad() {
  loading.value = true;
  
  axios
    .get(url.value, {
      params: {
        ...params.value,
        id:props.id
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.detailList.map(item => {
        return {
          ...item.productVO,
          ...item
        }
      });
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
let dialogVisible = ref(false)
function open() {
  dialogVisible.value = true;
  onLoad()
}
let selectionList = ref([]);
function handleSelectionChange(list) {
  selectionList.value = list;
}
function handleConfirm() {
  if (selectionList.value.length == 0) {
    return proxy.$message.error('请选择产品');
  }
  emit('onConfirm', selectionList.value)
  dialogVisible.value = false
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
