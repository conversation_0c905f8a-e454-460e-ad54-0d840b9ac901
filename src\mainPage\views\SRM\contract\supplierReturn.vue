<template>
  <basic-container :shadow="purchaseContractId ? 'never' : 'always'" block>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      :before-open="beforeOpen"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #productNames="{ row }">
        <div style="text-align: left">
          <el-tag
            effect="plain"
            style="margin-right: 5px"
            type="primary"
            v-for="(item, index) in row.productNames?.split(',').filter(item => !!item)"
            :key="index"
            >{{ item }}</el-tag
          >
        </div>
      </template>
      <template #menu="{ row }">
        <el-button
          type="primary"
          @click="$refs.crud.rowEdit(row, index)"
          text
          icon="edit"
          v-if="row.returnStatus == 0"
          >编辑</el-button
        >
        <el-button
          type="primary"
          v-if="row.returnStatus == 0"
          text
          icon="back"
          @click="confirmReturn(row)"
          >确认退货</el-button
        >

        <!-- <el-button type="primary" @click="$refs.crud.rowDel(row,index)" text icon="delete">删除</el-button> -->
      </template>
      <template #returnCode="{ row }">
        <el-link type="primary" @click="$refs.crud.rowView(row)">{{ row.returnCode }}</el-link>
      </template>
      <template #returnStatus="{ row }">
        <el-tag
          :type="row.returnStatus == 0 ? 'info' : row.$returnStatus == 1 ? 'warning' : 'success'"
          effect="plain"
          >{{ row.$returnStatus }}</el-tag
        >
      </template>
      <template #refundFile-form="{ type }">
        <File :fileList="form.attachList"></File>
      </template>
      <template #purchaseContractId="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.purchaseContractCode }}</el-link>
      </template>
      <template #operate-form="{ row, type }">
        <el-button type="primary" v-if="type != 'view'" @click="replacePro(row)" text
          >替换</el-button
        >
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <productSelect
      :purchase-contract-id="purchaseContractId || form.purchaseContractId"
      @confirm="handleConfirm"
      ref="productSelectRef"
    ></productSelect>
    <wfProductSelect
      ref="wfProductSelectRef"
      @onConfirm="handleConfirmForReplace"
    ></wfProductSelect>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import productSelect from '../procure/compoents/productSelect.vue';
import { ElMessage } from 'element-plus';
import { dateFormat } from '@/utils/date';
import wfProductSelect from '@/components/Y-UI/wf-product-select.vue';
let productSelectRef = ref();
const props = defineProps(['purchaseContractId']);

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 120,
  labelWidth: 120,
  border: true,
  dialogClickModal: true,
  dialogType: 'drawer',
  dialogWidth: '80%',
  menuWidth: 180,
  column: [
    {
      label: '退货单编号',
      prop: 'returnCode',
      overHidden: true,
      width: 180,
      disabled: true,
      search: true,
      searchLabelWidth: 120,
    },
    {
      label: '合同编号',
      prop: 'contractCode',
      hide: true,
      display: false,
      search: !props.purchaseContractId,
    },
    {
      type: 'input',
      label: '关联采购合同',
      display: true,
      width: 180,
      rules: [
        {
          required: true,
          message: '请选择关联采购合同',
        },
      ],
      display: !props.purchaseContractId,
      hide: !!props.purchaseContractId,
      component: 'wf-purchaseContract-select',
      overHidden: true,
      prop: 'purchaseContractId',

      formatter: (row, value) => {
        return row.purchaseContractCode;
      },
    },
    {
      label: '供应商名称',
      prop: 'supplierName',
      overHidden: true,
      disabled: true,
      component: 'wf-supplier-drop',
      display: false,
      search: !props.purchaseContractId,
      hide: !!props.purchaseContractId,
      searchLabelWidth: 120,
      width: 180,
    },
    {
      label: '退货产品',
      prop: 'productNames',

      display: false,
    },
    {
      label: '退货产品',
      prop: 'detailDTOList',
      type: 'dynamic',
      hide: true,
      rules: [
        {
          required: true,
          message: '请选择退货产品',
        },
      ],
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        rowAdd: done => {
          productSelectRef.value.open();
          // done();
        },
        rowDel: (row, done) => {
          done();
        },

        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            bind: 'productVO.productName',
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            bind: 'productVO.productSpecification',
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
            bind: 'productVO.productBrand',
            cell: false,
            width: 100,
          },
          {
            label: '单位',
            prop: 'unitName',
            width: 80,
            cell: false,
            bind: 'productVO.unitName',
          },
          {
            label: '数量',
            prop: 'number',
            width: 130,
            type: 'number',
            span: 12,
            cell: true,
            change: val => {
              form.value.refundPrice = totalAmount();
            },
          },
          {
            label: '单价',
            prop: 'purchasePrice',
            type: 'number',
            span: 12,
            width: 100,
            cell: false,
            blur: setReturnPrice,
          },
          {
            label: '金额',
            prop: 'totalPrice',
            type: 'number',
            span: 12,
            width: 100,
            cell: false,
            formatter: row => {
              return row.number * row.purchasePrice;
            },
          },
          {
            label: '退货单价',
            prop: 'returnPrice',
            type: 'number',
            span: 12,
            width: 120,
            cell: true,
            blur: setReturnPrice,
          },
          {
            label: '退货金额',
            prop: 'refundTotal',
            type: 'number',
            span: 12,
            width: 120,
            cell: false,
            formatter: row => {
              return row.number * row.returnPrice;
            },
          },
          {
            label: '序列号',
            prop: 'serialNumber',
            span: 12,
            cell: true,

            width: 200,
            type: 'textarea',
          },
          {
            label: '操作',
            prop: 'operate',
            cell: true,
            width: 120,
          },
        ],
      },
    },
    {
      label: '换货产品',
      prop: 'replaceDTOList',
      type: 'dynamic',
      hide: true,
      span: 24,
      children: {
        align: 'center',
        headerAlign: 'center',
        size: 'small',
        addBtn: false,

        rowDel: (row, done) => {
          done();
          setTimeout(() => {
            setReturnPrice();
          }, 100);
        },
        size: 'small',
        column: [
          {
            label: '产品名称',
            prop: 'productName',
            overHidden: true,
            bind: 'productVO.productName',
            cell: false,
          },

          {
            label: '规格型号',
            prop: 'productSpecification',
            overHidden: true,
            bind: 'productVO.productSpecification',
            // search: true,
            cell: false,
            span: 24,
            type: 'input',
          },
          {
            label: '品牌',
            prop: 'productBrand',
            overHidden: true,
            bind: 'productVO.productBrand',
            cell: false,
            width: 100,
          },
          {
            label: '单位',
            prop: 'unitName',
            cell: false,
            width: 80,
            bind: 'productVO.unitName',
          },
          {
            label: '数量',
            prop: 'number',
            width: 120,
            type: 'number',
            span: 12,
           
            cell: true,
            rules: [
              {
                required: true,
                message: '请输入数量',
              },
            ],
          },

          
        ],
      },
    },
    {
      label: '退货金额',
      prop: 'refundPrice',
      type: 'number',
      span: 12,
      width: 100,
    },
    {
      label: '退货原因',
      prop: 'returnReason',
      type: 'textarea',
      span: 24,
      overHidden: true,
    },

    {
      label: '退货状态',
      prop: 'returnStatus',
      display: false,
      search: true,
      width: 100,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '退货中',
        },
        {
          value: 1,
          label: '已退货',
        },
      ],
    },
    {
      label: '创建人',
      prop: 'createName',
      width: 100,
      display: false,
    },
    // {
    //   label: '联系电话',
    //   prop: 'followPerson',
    // },
  ],
  group: [
    {
      label: '退货信息',
      prop: 'rereturnInfo',
      addDisplay: false,
      editDisplay: false,
      column: [
        {
          label: '退货时间',
          prop: 'returnDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          span: 24,
          valueFormat: 'YYYY-MM-DD',
        },
        {
          label: '备注',
          type: 'textarea',
          span: 24,
        },
      ],
    },
    // {
    //   label: '收款信息',
    //   prop: 'refundInfo',
    //   addDisplay: false,
    //   editDisplay: false,
    //   column: [
    //     {
    //       label: '收款金额',
    //       prop: 'refundPrice',
    //       rules: [
    //         {
    //           required: true,
    //           message: '请输入收款金额',
    //         },
    //       ],
    //     },
    //     {
    //       label: '收款时间',
    //       prop: 'refundDate',
    //       type: 'date',
    //       format: 'YYYY-MM-DD',
    //       valueFormat: 'YYYY-MM-DD',
    //     },
    //     {
    //       label: '收款附件',
    //       prop: 'refundFile',
    //       type: 'upload',
    //       span: 24,
    //       dragFile: true,
    //       // rules: [
    //       //   {
    //       //     required: true,
    //       //     validator: validatorPath,
    //       //     trigger: "change",
    //       //   },
    //       // ],
    //       dataType: 'object',
    //       propsHttp: {
    //         res: 'data',
    //         url: 'id',
    //         name: 'originalName',
    //       },
    //       action: '/api/blade-resource/attach/upload',
    //     },
    //   ],
    // },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/purchaseReturn/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/purchaseReturn/update';
const tableUrl = '/api/vt-admin/purchaseReturn/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        purchaseContractId: props.purchaseContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    refundFile: null,
    purchaseContractId: props.purchaseContractId || form.purchaseContractId,
    detailDTOList: [
      ...form.detailDTOList.map(item => {
        return {
          ...item,
         
          id: null,
        };
      }),
      ...form.replaceDTOList.map(item => {
        return {
          ...item,
          id: null,
        };
      }),
    ],
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
    refundFile: null,
    detailDTOList: [
      ...row.detailDTOList.map(item => {
        return {
          ...item,
          
          id: null,
        };
      }),
      ...row.replaceDTOList.map(item => {
        return {
          ...item,
          id: null,
        };
      }),
    ],
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function handleConfirm(list) {
  console.log(list);

  form.value.detailDTOList = list.map(item => {
    return {
      ...item,
      type: 0,
      purchasePrice: item.unitPrice,
      contractDetailId: item.id,
      returnPrice: item.unitPrice,
    };
  });
}
function totalAmount(val) {
  //  console.log(val);
  // form.value.invoicePrice =  form.value.detailDTOList.reduce((total, item) => (total += item.totalPrice * 1), 0) * (1 - (val * 1) / 100)
  return form.value.detailDTOList
    .reduce((total, item) => (total += item.number * 1 * (item.returnPrice * 1)), 0)
    .toFixed(2);
}
function beforeOpen(done, type) {
  if (['edit', 'view'].includes(type)) {
    axios
      .get('/api/vt-admin/purchaseReturn/detail', { params: { id: form.value.id } })
      .then(res => {
        form.value = {
          ...res.data.data,
          detailDTOList: res.data.data.detailVOList
            .filter(item => item.type == 0)
            .map(item => {
              return {
                ...item,
                number: item.number * 1,
                id: item.detailId,
              };
            }),
          replaceDTOList: res.data.data.detailVOList
            .filter(item => item.type == 1)
            .map(item => {
              return {
                ...item,
               
                id: null,
              };
            }),
        };
      });
  }

  done();
}

function toDetail(row) {
  router.push({
    path: '/procure/contractDetail',
    query: {
      id: row.purchaseContractId,
    },
  });
}
function confirmReturn(row) {
  axios.get('/api/vt-admin/purchaseReturn/detail', { params: { id: row.id } }).then(res => {
    const detailDTOList = res.data.data.detailVOList.map(item => {
      return {
        ...item,
        number: item.number * 1,
        id: item.contractDetailId,
      };
    });
    proxy.$refs.dialogForm.show({
      title: '确认退货',
      width: '50%',
      option: {
        column: [
          // {
          //   label: '关联产品',
          //   prop: 'detailDTOList',
          //   type: 'dynamic',
          //   labelWidth: 0,
          //   value: detailDTOList,
          //   hide: true,

          //   span: 24,
          //   children: {
          //     align: 'center',
          //     headerAlign: 'center',
          //     rowAdd: done => {
          //       productSelectRef.value.open();
          //       // done();
          //     },
          //     rowDel: (row, done) => {
          //       done();
          //     },

          //     column: [
          //       {
          //         label: '产品名称',
          //         prop: 'productName',
          //         overHidden: true,
          //         bind: 'productVO.productName',
          //         cell: false,
          //       },

          //       {
          //         label: '规格型号',
          //         prop: 'productSpecification',
          //         overHidden: true,
          //         bind: 'productVO.productSpecification',
          //         // search: true,
          //         cell: false,
          //         span: 24,
          //         type: 'input',
          //       },
          //       {
          //         label: '品牌',
          //         prop: 'productBrand',
          //         overHidden: true,
          //         bind: 'productVO.productBrand',
          //         cell: false,
          //         width: 120,
          //       },
          //       {
          //         label: '数量',
          //         prop: 'number',
          //         type: 'number',
          //         span: 12,
          //         cell: true,
          //         width: 120,
          //       },
          //       {
          //         label: '单价',
          //         prop: 'purchasePrice',
          //         type: 'number',
          //         span: 12,
          //         cell: false,
          //         width: 100,
          //       },
          //       {
          //         label: '金额',
          //         prop: 'totalPrice',
          //         type: 'number',
          //         span: 12,
          //         width: 100,
          //         cell: false,
          //         formatter: row => {
          //           return row.number * row.purchasePrice;
          //         },
          //       },
          //       {
          //         label: '序列号',
          //         prop: 'serialNumber',
          //         span: 12,
          //         cell: true,
          //         row: 1,
          //         width: 200,
          //         type: 'textarea',
          //       },
          //     ],
          //   },
          // },
          {
            label: '退货时间',
            prop: 'returnDate',
            type: 'date',
            format: 'YYYY-MM-DD',
            span: 24,
            valueFormat: 'YYYY-MM-DD',
            value: dateFormat(new Date(), 'yyyy-MM-dd'),
          },
          {
            label: '备注',
            type: 'textarea',
            span: 24,
            prop: 'remark',
          },
        ],
      },
      callback(res) {
        axios
          .post('/api/vt-admin/purchaseReturn/confirmReturn', {
            ...row,
            ...res.data,
          })
          .then(ref => {
            ElMessage.success('操作成功');
            onLoad();
            res.close();
          });
      },
    });
  });
}
function refundPrice(row) {
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          label: '收款金额',
          prop: 'refundPrice',
          value: row.refundPrice,
          rules: [
            {
              required: true,
              message: '请输入收款金额',
            },
          ],
        },
        {
          label: '收款时间',
          prop: 'refundDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
        },
        {
          label: '收款附件',
          prop: 'refundFile',
          type: 'upload',
          span: 24,
          dragFile: true,
          // rules: [
          //   {
          //     required: true,
          //     validator: validatorPath,
          //     trigger: "change",
          //   },
          // ],
          dataType: 'object',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/api/blade-resource/attach/upload',

          viewDisplay: false,
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/purchaseReturn/refundPrice', {
          id: row.id,
          ...res.data,
          refundFile: res.data.refundFile && res.data.refundFile.map(item => item.value).join(','),
        })
        .then(r => {
          ElMessage.success('操作成功');
          res.close();
          onLoad();
        });
    },
  });
}
function handleConfirmForReplace(ids) {
  
  axios.get('/api/vt-admin/product/detail?id=' + ids).then(res => {
    form.value.replaceDTOList.push({
      contractDetailId: contractDetailId.value,
      number: contractDetailNumber.value,
      type: 1,
      productId: ids,
      productVO: res.data.data,
    });
    setReturnPrice();
  });
}
function setReturnPrice() {
  // 计算退货产品的总金额
  const returnTotal =
    form.value.detailDTOList?.reduce(
      (total, item) => (total += item.number * item.returnPrice),
      0
    ) || 0;
 
  // 设置form的refundPrice为退货产品的总金额 - 换货产品的总金额
  form.value.refundPrice = (replaceTotal).toFixed(2);
}
let contractDetailId = ref(null);
let contractDetailNumber = ref(null);
let wfProductSelectRef = ref(null);
function replacePro(row) {
  contractDetailId.value = row.contractDetailId;
  contractDetailNumber.value = row.number;
  wfProductSelectRef.value.visible = true;
}
</script>

<style lang="scss" scoped></style>
