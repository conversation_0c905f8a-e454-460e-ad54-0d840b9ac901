<template>
  <div style="height: 100%">
    <el-row style="height: 100%">
      <el-col :span="9" style="height: 100%">
        <basic-container style="height: 100%">
          <el-button style="margin-bottom: 5px" icon="upload" @click="upload" type="primary"
            >导入</el-button
          >
          <!-- <el-button style="margin-bottom: 5px" icon="download" @click="download" type="primary"
            >下载模板</el-button
          > -->
          <el-input placeholder="输入关键字进行过滤" v-model="filterText" @input="getTreeData">
          </el-input>
          <avue-tree :option="treeOption" style="max-height: 660px;overflow-y: auto;" :data="treeData" @node-click="nodeClick"> </avue-tree>
        </basic-container>
      </el-col>

      <el-col :span="15" style="height: 100%">
        <basic-container style="height: 100%">
          <Title>{{ categoryName }}</Title>
          <div style="margin: 20px 0" v-if="!hasChildren">
            <el-form>
              <el-form-item>
                <el-button icon="plus" @click="addParams" type="primary">新增参数</el-button>
              </el-form-item>
              <el-table class="avue-crud" :row-key="row => row.id" :data="tableData" border>
                <el-table-column type="index" width="50" />
                <el-table-column label="#" width="50" type="expand">
                  <template #default="scope">
                    <div style="margin-left: 100px; display: flex">
                      <draggable
                        v-model="scope.row.valuesEntityList"
                        :animation="100"
                        @sort="
                          a => {
                            onMoveCallback(a, scope.row);
                          }
                        "
                      >
                        <transition-group>
                          <el-tag effect='plain'
                            style="margin-right: 5px"
                            v-for="element in scope.row.valuesEntityList"
                            :key="element.id"
                            closable
                            @close="handleClose(element)"
                          >
                            {{ element.value }}
                          </el-tag>
                        </transition-group>
                      </draggable>

                      <el-input
                        class="input-new-tag"
                        v-if="scope.row.inputVisible"
                        style="width: 200px"
                        v-model="scope.row.inputValue"
                        :ref="'saveTagInput' + scope.$index"
                        size="small"
                        @keyup.enter.native="addTag(scope.row)"
                        @blur="addTag(scope.row)"
                      >
                      </el-input>

                      <el-button
                        v-else-if="scope.row.type != 2"
                        class="button-new-tag"
                        size="small"
                        @click="showInput(scope.row, scope.$index)"
                        >+ 添加新标签</el-button
                      >
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="参数名称" prop="propertyName" />
                <el-table-column label="参数类型" prop="type" width="120">
                  <template #default="scope">
                    <el-tag effect='plain' type="success" v-if="scope.row.type == 0">单选</el-tag>
                    <el-tag effect='plain' type="success" v-else-if="scope.row.type == 1">多选</el-tag>
                    <el-tag effect='plain' type="success" v-else>输入</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="是否使用" prop="isUse" width="120">
                  <template #default="scope">
                    <el-switch
                      :active-value="1"
                      :inactive-value="0"
                      disabled
                      v-model="scope.row.isUse"
                    ></el-switch
                  ></template>
                </el-table-column>

                <el-table-column label="操作" align="center" width="200">
                  <template #default="scope">
                    <el-button
                      text
                      icon="edit"
                      size="small"
                      type="primary"
                      @click="editParams(scope.row)"
                      >编辑</el-button
                    >
                    <el-button
                      text
                      icon="delete"
                      size="small"
                      type="danger"
                      @click="delParams(scope.row)"
                      >删除</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </el-form>
          </div>
          <el-empty v-else description="请先选中最后一级"></el-empty>
        </basic-container>
      </el-col>
    </el-row>

    <dialogForm ref="dialogForm"></dialogForm>
    <el-dialog
      title="导入参数"
      v-model="dialogVisible"
      width="30%"
      class="avue-dialog avue-dialog--top"
    >
      <el-upload
        class="upload-demo"
        drag
        ref="upload"
        :on-success="handleSuccess"
        :on-error="handleError"
        :on-exceed="handleExceed"
        :limit="1"
        action="/api/vt-admin/productCategoryProperty/importProperty"
        :headers="{
          [website.tokenHeader]: $store.getters.token,
          Authorization: `Basic ${Base64.encode(`${website.clientId}:${website.clientSecret}`)}`,
        }"
        multiple
        :auto-upload="false"
      >
        <i class="el-icon-upload"></i>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <div><el-link type="primary" slot="tip" @click.stop="download">下载模板</el-link></div>
      </el-upload>
      <div class="avue-dialog__footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button @click="handleSubmit" type="primary">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import axios from 'axios';

import { ref, getCurrentInstance, onMounted, reactive } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Base64 } from 'js-base64';
import { VueDraggableNext as draggable } from 'vue-draggable-next';
import website from '@/config/website';
import { ElNotification,ElMessage } from 'element-plus';
import { h } from 'vue';
import progress from '@/components/progress/index.vue';
let form = ref({
  hasChildren: true,
});

let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let treeOption = ref({
  //   defaultExpandAll: true,
  menu: false,
  filter: false,
  addBtn: false,
  props: {
    labelText: '标题',
    label: 'categoryName',
    value: 'id',
    children: 'children',
  },
  lazy: true,
  treeLoad: function (node, resolve) {
    axios
      .get('/api/vt-admin/productCategory/list', {
        params: {
          parentId: node.data.id,
        },
      })
      .then(res => {
        resolve(
          res.data.data.map(item => {
            return {
              ...item,
              leaf: !item.hasChildren,
            };
          })
        );
      });
  },
});

const updateUrl = '/api/vt-admin/product/update';

let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  getTreeData();
});
let loading = ref(false);

let router = useRouter();
let treeData = ref([]);
function getTreeData(value) {
  axios
    .get('/api/vt-admin/productCategory/list', {
      params: {
        categoryName: value,
      },
    })
    .then(res => {
      treeData.value = res.data.data.map(item => {
        return {
          ...item,
          leaf: !item.hasChildren,
        };
      });
    });
}
let filterText = ref('');
let categoryId = ref('');
let categoryName = ref('');
let hasChildren = ref(true);
function nodeClick(val, accountName) {
  categoryId.value = val.id;
  categoryName.value = val.categoryName;
  hasChildren.value = val.hasChildren;
  getTableData();
}
function getTableData() {
  axios.get('/api/vt-admin/productCategory/detail?id=' + categoryId.value).then(res => {
    tableData.value = res.data.data.propertyVOList.map(item => {
      return {
        ...item,
        inputVisible: false,

        inputValue: '',
      };
    });
  });
}

function showInput(row, index) {
  row.inputVisible = true;
  // 输入框自动获得焦点 $nextTick 当页面上的元素发生刷新（重新渲染）时的回调函数
  proxy.$nextTick(_ => {
    setTimeout(() => {
      console.log(proxy.$refs);
      proxy.$refs['saveTagInput' + index].focus();
    }, 200);
  });
}
function addParams() {
  // 新增参数
  proxy.$refs.dialogForm.show({
    title: '新增参数',
    option: {
      column: [
        {
          label: '参数名称',
          type: 'input',
          prop: 'propertyName',
          rules: [{ required: true, message: '请输入参数名称' }],
        },
        {
          label: '参数类型',
          type: 'radio',
          prop: 'type',
          rules: [{ required: true, message: '请选择参数类型' }],
          dicData: [
            {
              label: '单选',
              value: 0,
            },
            {
              label: '多选',
              value: 1,
            },
            {
              label: '输入',
              value: 2,
            },
          ],
        },
        {
          label: '是否使用',
          type: 'switch',
          prop: 'isUse',
          value: 1,
          dicData: [
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ],
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/productCategoryProperty/save', {
          productCategoryId: categoryId.value,
          propertyName: res.data.propertyName,
          isUse: res.data.isUse,
          type: res.data.type,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getTableData();
        });
    },
  });
}
function editParams(row) {
  proxy.$refs.dialogForm.show({
    title: 'bianji参数',
    option: {
      column: [
        {
          label: '参数名称',
          type: 'input',
          value: row.propertyName,
          prop: 'propertyName',
          rules: [{ required: true, message: '请输入参数名称' }],
        },
        {
          label: '参数类型',
          type: 'radio',
          prop: 'type',
          value: row.type,
          dicData: [
            {
              label: '单选',
              value: 0,
            },
            {
              label: '多选',
              value: 1,
            },
            {
              label: '输入',
              value: 2,
            },
          ],
        },
        {
          label: '是否使用',
          type: 'switch',
          prop: 'isUse',
          value: row.isUse,
          dicData: [
            {
              label: '否',
              value: 0,
            },
            {
              label: '是',
              value: 1,
            },
          ],
        },
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/productCategoryProperty/update', {
          id: row.id,
          type: res.data.type,
          isUse: res.data.isUse,
          propertyName: res.data.propertyName,
        })
        .then(e => {
          proxy.$message.success('操作成功');
          res.close();
          getTableData();
        });
    },
  });
}
function addTag(row) {
  // 新增tag
  if (!row.inputValue) {
    row.inputVisible = false;
    return;
  }
  axios
    .post('/api/vt-admin/productCategoryPropertyValues/save', {
      propertyId: row.id,
      value: row.inputValue,
    })
    .then(res => {
      proxy.$message.success('操作成功');
      getTableData();
      // row.inputVisible = false;
    });
}
function handleClose(row) {
  // 删除标签
  proxy
    .$confirm('确定删除此标签吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios.post('/api/vt-admin/productCategoryPropertyValues/remove?ids=' + row.id).then(res => {
        proxy.$message.success('删除成功');
        getTableData();
      });
    });
}
function delParams(row) {
  proxy
    .$confirm('确定删除此参数吗', '提示', {
      type: 'warning',
    })
    .then(res => {
      axios.post('/api/vt-admin/productCategoryProperty/remove?ids=' + row.id).then(res => {
        proxy.$message.success('删除成功');
        getTableData();
      });
    });
}
let drag = ref(false);
let topList = ref([]);
function onMoveCallback(e, row) {
  const data = row.valuesEntityList.map((item, idnex) => {
    return {
      ...item,
      sort: idnex,
    };
  });
  axios
    .post('/api/vt-admin/productCategoryProperty/updateSort', {
      id: row.id,
      valuesDTOList: data,
    })
    .then(res => {
      proxy.$message.success('排序成功');
      getTableData();
    });
}
let dialogVisible = ref(false);
function upload() {
  dialogVisible.value = true;

}

function download() {
  let a = document.createElement('a'); // 创建a标签
  a.href = '/template/paramsTemplate.xlsx'; // 文件路径
  a.download = '参数导入模板.xlsx'; // 文件名称
  a.style.display = 'none'; // 隐藏a标签
  document.body.appendChild(a);
  // 定时器(可选)
  setTimeout(() => {
    a.click(); // 模拟点击(要加)
    document.removeChild(a); //删除元素(要加)
    setTimeout(() => {
      self.URL.revokeObjectURL(a.href); // 用来释放文件路径(可选)
    }, 200);
  }, 66);
}
function handleSuccess(res) {
  proxy.$message({
    message:res.data.failMessage? res.data.failMessage.split(';').join('<br>') : res.data.message,
    dangerouslyUseHTMLString: true,
    type:res.data.failMessage?'warning':'success'
  });
  notice.value.close()
  proxy.$refs.upload.clearFiles();
  // dialogVisible.value = false
}
let notice = ref(null)
function handleError(res) {
  console.log(res);
  proxy.$message.error(res.data.msg);
  proxy.$refs.upload.clearFiles();
  notice.value.close()
}
function handleExceed(params) {
  proxy.$message.warning(`当前限制选择 1 个文件，本次选择了 ${params.length} 文件`);
}
function handleSubmit(params) {
 proxy.$nextTick(() => {
  proxy.$refs.upload.submit()
   notice.value = ElNotification({
    title: '导入中',
    position: 'bottom-right',
    duration: 0,
    message: h(progress, {
     
      // 事件要以onXxx的形式书写
      onFinish: status => {
        if (status.value == 'ok') {
          notice.close(); // 关闭ElNotification
        }
      },
    }),
  });
 })
}
</script>

<style lang="scss" scoped></style>
