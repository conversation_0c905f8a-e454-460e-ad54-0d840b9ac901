<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @on-load="onLoad"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="onLoad"
    @search-change="searchChange"
    @current-change="onLoad"
    @refresh-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #invoiceId-form>
      <wfCustomerInvoiceDrop
        v-model="form.invoiceId"
        :id="props.sealContractId"
      ></wfCustomerInvoiceDrop>
    </template>
    <template #menu="{ row, $index }">
      <!-- <el-button text type="primary" icon="pointer" @click="collection(row)">收款</el-button> -->
      <el-button
        text
        type="primary"
        icon="edit"
        v-if="row.collectionStatus == 0"
        @click="$refs.crud.rowEdit(row, $index)"
        >编辑</el-button
      >
      <el-button text type="primary" icon="pointer" @click="collectionCord(row)"
        >收款记录</el-button
      >
    </template>
    <template #collectionStatus="{ row }">
      <el-tag effect='plain' size="small" type="success" v-if="row.collectionStatus == 2">已收款</el-tag>
      <el-tag effect='plain' size="small" type="warning" v-if="row.collectionStatus == 1">部分收款</el-tag>
      <el-tag effect='plain' size="small" type="warning" v-if="row.collectionStatus == 0">未收款</el-tag>
    </template>
    <template #invoiceStatus="{ row }">
      <el-tag effect='plain'
        :type="row.invoiceStatus == 3 ? 'danger' : row.invoiceStatus == 0 ? 'primary' : 'success'"
        >{{ row.$invoiceStatus }}</el-tag
      >
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  <el-dialog title="收款记录" v-model="dialogVisible" class="avue-dialog avue-dialog--top">
    <!-- <slot ></slot> -->
    <collectionList :planCollectionId="currentId"></collectionList>
    <div class="avue-dialog__footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <!-- <el-button @click="$refs.dialogForm.submit()" type="primary">确 定</el-button> -->
    </div>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import wfCustomerInvoiceDrop from '../compoents/wf-customerInvoice-drop.vue';
import { dateFormat } from '@/utils/date';
import collectionList from '../compoents/collectionList.vue';
const props = defineProps({
  contractCode: String,
  supplierName: String,
  sealContractInvoiceId: String,
  customerId: String,
  sealContractId: String,
  offerId: String,
});
let route = useRoute();
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  labelWidth: 140,
  menuWidth: 270,
  border: true,
  column: [
    // {
    //   type: 'input',
    //   label: '客户名称',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'a170080949446133484',
    // },
    // {
    //   type: 'input',
    //   label: '项目名称',
    //   span: 12,
    //   display: true,
    //   hide: true,
    //   prop: 'a170080951774565537',
    // },
    {
      type: 'input',
      label: '关联发票',
      span: 12,
      hide: true,
      display: true,
      prop: 'invoiceId',
    },
    // {
    //   type: 'input',
    //   label: '关联标的',
    //   span: 12,
    //   hide: true,
    //   display: true,
    //   prop: 'a170080947476223862',
    // },
    {
      type: 'input',
      label: '计划名称',
      span: 12,
      display: true,
      prop: 'planName',
      required: true,
      width: 300,
      rules: [
        {
          required: true,
          message: '计划名称必须填写',
        },
      ],
    },
    {
      type: 'select',
      label: '关联标的',
      span: 12,
      display: true,
      prop: 'contractObjectId',
      required: true,
      dicUrl:
        '/api/vt-admin/sealContractObject/page?sealContractId=' + route.query.id + '&size=5000',
      width: 300,
      dicFormatter: res => {
        console.log(res);
        return res.data.records;
      },
      props: {
        value: 'id',
        label: 'objectName',
      },
    },
    {
      type: 'date',
      label: '计划收款时间',
      span: 12,
      display: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      prop: 'planCollectionDate',
      disabled: false,
      readonly: false,
      required: true,
      rules: [
        {
          required: true,
          message: '计划收款时间必须填写',
        },
      ],
    },
    {
      type: 'number',
      label: '计划收款金额',
      controls: true,
      span: 24,
      display: true,
      prop: 'planCollectionPrice',
    },
    {
      type: 'number',
      label: '收款比例',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'collectionRate',
      formatter: row => {
        return row.collectionRate + '%';
      },
    },
    {
      type: 'number',
      label: '实际收款金额',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollection',
    },
    {
      type: 'number',
      label: '实际回款比例',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'actualCollectionRate',
      formatter: row => {
        return row.actualCollectionRate + '%';
      },
    },
    {
      type: 'input',
      label: '逾期',
      controls: true,
      span: 24,
      addDisplay: false,
      editDisplay: false,
      prop: 'overDays',
      html: true,
      formatter: row => {
        if (row.overDays) {
          return `<span style="color:var(--el-color-danger)">${row.overDays}天</span>`;
        } else {
          return `---`;
        }
      },
    },
    {
      label: '收款状态',
      prop: 'collectionStatus',
      addDisplay: false,
      editDisplay: false,
    },
    {
      type: 'select',
      label: '发票状态',
      span: 12,
      addDisplay: false,
      width: 100,
      editDisplay: false,
      prop: 'invoiceStatus',
      dicData: [
        {
          label: '待开票',
          value: 0,
        },
        {
          label: '已开票',
          value: 1,
        },
        {
          label: '已邮寄',
          value: 2,
        },
        {
          label: '已作废',
          value: 3,
        },
      ],
      cascader: [],
      props: {
        label: 'label',
        value: 'value',
        desc: 'desc',
      },
    },
    {
      type: 'textarea',
      label: '备注',
      span: 24,
      display: true,
      prop: 'remark',
      showWordLimit: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/sealContractPlanCollection/save';
const delUrl = '';
const updateUrl = '/api/vt-admin/sealContractPlanCollection/update';
const tableUrl = '/api/vt-admin/sealContractPlanCollection/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

onMounted(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        sealContractId: props.sealContractId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    sealContractId: props.sealContractId,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}

function searchChange(params, done) {
  onLoad();
  done();
}
function collection(row) {
  proxy.$refs.dialogForm.show({
    title: '收款',
    option: {
      column: [
        {
          type: 'number',
          label: '收款金额',
          controls: true,
          span: 12,
          display: true,
          prop: 'actualPrice',
        },
        {
          type: 'datetime',
          label: '收款时间',
          span: 12,
          display: true,
          value: dateFormat(new Date(), 'yyyy-MM-dd hh:mm:ss'),
          format: 'YYYY-MM-DD HH:mm',
          valueFormat: 'YYYY-MM-DD HH:mm:ss',
          prop: 'actualDate',
          disabled: false,
          readonly: false,
          required: true,
          rules: [
            {
              required: true,
              message: '收款时间必须填写',
            },
          ],
        },
        {
          type: 'select',
          label: '收款账号',
        
          cascader: [],
          span: 12,
          // search: true,
          display: true,
          dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
          rules: [
            {
              required: true,
              message: '请选择收款账号',
              trigger: 'blur',
            },
          ],
          prop: 'collectionAccount',
        },
        {
          label: '备注',
          prop: 'remark',
          type: 'textarea',
          span: 24,
        },
        {
          label: '收款凭证',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          // listType: 'picture-img',
          loadText: '图片上传中，请稍等',
          span: 24,
          slot: true,
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
          },
          action: '/blade-resource/attach/upload',
        },
      ],
    },
    callback(res) {
      const data = {
        planCollectionId: row.id,
        ...res.data,
        collectionFiles: res.data.files.map(item => item.value).join(','),
      };
      axios.post('/api/vt-admin/sealContractPlanCollection/collection', data).then(r => {
        proxy.$message.success(r.data.msg);
        res.close();
        onLoad();
      });
    },
  });
}
let dialogVisible = ref(false);
let currentId = ref('');
function collectionCord(row) {
  currentId.value = row.id;
  dialogVisible.value = true;
}
</script>

<style lang="scss" scoped></style>
