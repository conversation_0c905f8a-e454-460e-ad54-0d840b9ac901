<template>
  <div>
    <el-table class="avue-crud" :data="tableData" border v-loading="loading" align="center">
      <el-table-column label="产品名称" prop="productName" show-overflow-tooltip></el-table-column>
      <el-table-column
        label="规格型号"
        prop="productSpecification"
        show-overflow-tooltip
      ></el-table-column>
      <el-table-column label="产品图片" #default="{ row }">
        <el-image
          style="width: 80px"
          :preview-src-list="[row.coverUrl]"
          :src="row.coverUrl"
        ></el-image>
      </el-table-column>
      <el-table-column
        label="产品描述"
        show-overflow-tooltip
        width="200"
        prop="description"
      ></el-table-column>
      <el-table-column label="品牌" align="center" prop="productBrand"></el-table-column>
      <el-table-column label="单位" align="center" width="100" prop="unitName"></el-table-column>
      <el-table-column label="数量" align="center" width="100" #default="{ row }" prop="number">
      </el-table-column>
      <el-table-column
        label="已开票数量"
        align="center"
        width="100"
        #default="{ row }"
        prop="invoiceNumber"
      >
      </el-table-column>
      <!-- <el-table-column label="入库数量" #default="{ row }" prop="arriveNumber"> </el-table-column> -->
      <el-table-column label="单价" align="center" width="100" #default="{ row }" prop="unitPrice">
        <span>{{ row.unitPrice }}</span>
      </el-table-column>
      <el-table-column label="金额" align="center" width="100" #default="{ row }" prop="totalPrice">
      </el-table-column>
      <el-table-column label="发票状态" width="100" #default="{ row }" prop="isInvoice">
        <el-tag
          effect="plain"
          :type="row.isInvoice == 0 ? 'danger' : row.isInvoice == 1 ? 'success' : 'warning'"
          >{{ ['未收票', '已收票', '部分收票', '无需收票'][row.isInvoice] }}</el-tag
        >
      </el-table-column>
      <el-table-column label="采购类型" prop="purchaseType" show-overflow-tooltip width="150">
        <template #default="{ row }">
          <el-tag type="primary" v-if="row.purchaseType == 1" effect="plain">库存采购</el-tag>
          <el-tag type="success" v-else effect="plain">报价采购</el-tag>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import { computed, getCurrentInstance } from 'vue';
import { dateFormat } from '@/utils/date';
const props = defineProps({
  // 父组件传递过来的值
  id: String,
});

const { proxy } = getCurrentInstance();
onMounted(() => {
  onLoad();
});
watch(
  () => props.id,
  () => {
    onLoad();
  },
  {
    immediate: true,
  }
);
let loading = ref(false);
let tableData = ref([]);
function onLoad() {
  loading.value = true;
  axios
    .get('/api/vt-admin/purchaseContract/detail', {
      params: {
        id: props.id,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.detailVOList.map(item => {
        return {
          ...item.productVO,
          ...item,
        };
      });
    });
}
</script>

<style lang="scss" scoped>
.bottom_box {
  height: 0px;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
  position: fixed;
  z-index: 2000;
  bottom: 0;
  left: 0;
  transition: height 0.2s linear;
  display: flex;
  justify-content: center;
  align-items: center;
}
.bottom_box.active {
  height: 60px;
}
</style>
