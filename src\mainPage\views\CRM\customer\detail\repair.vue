<template>
    <div>
        <repairRegister v-if="info.type == 0 || info.type == 2" :customer-id="info.customerId"></repairRegister>
        <repairAll v-else :customer-id="info.customerId"></repairAll>
    </div>
</template>

<script setup>
import repairRegister from '../../repair/repairRegister.vue';
import repairAll from '../../repair/repairAll.vue';
const props = defineProps(['info'])
</script>

<style lang="scss" scoped>

</style>