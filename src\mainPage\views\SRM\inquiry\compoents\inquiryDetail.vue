<template>
  <avue-crud
    :option="option"
    :data="tableData"
    v-model:page="page"
    v-model:search="params"
    @row-update="rowUpdate"
    @row-save="rowSave"
    :table-loading="loading"
    ref="crud"
    @keyup.enter="onLoad"
    @row-del="rowDel"
    @search-reset="reset"
    @search-change="searchChange"
    @refresh-change="onLoad"
    @current-change="onLoad"
    @size-change="onLoad"
    v-model="form"
  >
    <template #inquiryPrice="{ row }">
      <el-popover v-if="row.isStop == 1" placement="bottom" :content="row.remark" trigger="hover">
        <template #reference>
          <el-tag effect='plain' type="danger">停产</el-tag>
        </template>
      </el-popover>
      <span v-else>{{ row.inquiryPrice }}</span>
    </template>
  </avue-crud>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { deepClone } from '@/utils/util';
const props = defineProps({
  data: {
    type: Array,
    default: '',
  },
});
let tableData = ref([]);
watch(
  () => props.data,
  (newVal, oldVal) => {
    tableData.value = deepClone(props.data);
  },
  {
    deep: true,
    immediate: true,
  }
);

let option = ref({
  align: 'center',
  addBtn: false,
  header: false,
  editBtn: false,
  delBtn: false,
  menu: false,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 100,
  border: true,
  column: [
    {
      label: '产品名称',
      prop: 'productName',
      overHidden: true,

      rules: [
        {
          required: true,
          message: '请输入产品名称',
          trigger: 'blur',
        },
      ],
    },
    {
      label: '品牌',
      prop: 'productBrand',
      width: 150,
    },
    {
      label: '规格型号',
      prop: 'productSpecification',
      overHidden: true,
     
    },
    {
      label: '数量',
      prop: 'number',
      width: 80,
    },
    {
      label: '单价',
      prop: 'inquiryPrice',
      type: 'number',
      controls: false,
      cell: false,
      span: 12,
      width: 100,
      
    },
    {
      label: '金额',
      prop: 'totalPrice',
      type: 'number',
      width: 110,
      controls: false,
      cell: false,
      span: 12,
      formatter: row => {
        return (row.inquiryPrice * row.number).toFixed(2);
      },
    },
    {
      label: '是否含税',
      prop: 'isHasTax',
      type: 'switch',
      value: 1,
      width: 100,
      cell: false,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
      span: 12,
      formatter:row => {
        row.isHasTax = !row.isHasTax ? 0 : 1;
        return row.isStop == 1?'':['否','是'][row.isHasTax]
      }
    },
    {
      label: '税率',
      type: 'select',
      props: {
        label: 'dictValue',
        value: 'dictKey',
      },
      width: 80,
      cell: false,
      prop: 'taxRate',
      dicUrl: '/blade-system/dict/dictionary?code=tax',
      formatter:row => {
        return row.isStop == 1?'':row.taxRate
      }
    },
    {
      label: '质保期（年）',
      prop: 'guaranteePeriod',
      type: 'number',
      width: 120,
      controls: false,
      cell: false,
      
      span: 12,formatter:row => {
        return row.isStop == 1?'':row.guaranteePeriod
      }
    },
    {
      label: '供货周期（天）',
      prop: 'deliveryCycle',
      type: 'number',
      width: 150,
      controls: false,
      cell: false,
      span: 12,
      formatter: row => {
        return row.deliveryCycle === 0 && !row.isStop ? '现货' : row.deliveryCycleDays? row.deliveryCycleDays + '天' : '';
      },
    },
    {
      label: '报价时间',
      prop: 'offerDate',
      type: 'date',
      span: 12,
      width: 120,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      cell: false,
      formatter:row => {
        return row.isStop == 1?'':row.offerDate
      }
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '/vt-admin/purchaseInquiryDetail/remove?ids=';
const updateUrl = '';
const tableUrl = '/vt-admin/purchaseInquiry/detail';
let params = ref({});

let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        id: props.id,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.detailVOS;
      //   page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let dialogVisible = ref(false);
function open() {
  dialogVisible.value = true;
}
defineExpose({
  open,
});
</script>

<style lang="scss" scoped></style>
