<template>
  <basic-container>
    <el-container style="gap: 0">
      <el-aside
        style="
          width: 200px;
          border-right: 1px solid var(--el-border-color);
          padding-right: 15px;
          margin-right: 15px;
          background: var(--el-bg-color-overlay);
        "
      >
        <div style="display: flex; flex-direction: column; gap: 10px">
          <div
            v-for="(item, index) in typeList"
            :key="item.id || item.value"
            style="
              background-color: var(--el-color-primary-light-8);
              padding: 10px 15px;
              border-radius: 15px;
              cursor: pointer;
              margin-right: 10px;
              font-size: 14px;
            "
            @click="handleClick(item)"
            :style="{
              backgroundColor:
                activeName == item.value
                  ? ' var(--el-color-primary-light-8)'
                  : ' var(--el-color-info-light-8)',
            }"
          >
            <el-text
              style="font-weight: bold"
              :style="{
                color:
                  activeName == item.value ? ' var(--el-color-primary)' : ' var(--el-color-info)',
              }"
              class="mx-1"
              >{{ item.label }}</el-text
            >
          </div>
        </div>
      </el-aside>
      <el-main style="position: relative; padding-left: 15px">
        <div
          style="
            position: absolute;
            left: -1px;
            top: 0;
            bottom: 0;
            width: 1px;
            background: var(--el-border-color);
          "
        ></div>
        <myFlow :height="700" ref="flow" :option="option" v-model="form">
          <template #header="{ node }">
            <div
              style="
                background: linear-gradient(
                  145deg,
                  var(--el-color-primary-light-3),
                  var(--el-color-primary)
                );
                padding: 8px 12px;
                border-radius: 6px 6px 0 0;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
                color: white;
                font-weight: 500;
              "
            >
              {{ node.name }}
            </div>
          </template>
          <template #="{ node }">
            <div
              style="
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 8px;
                height: 80px;
                padding: 12px;
                background: var(--el-bg-color);
                border-radius: 0 0 6px 6px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
                transition: all 0.3s ease;
              "
            >
              <div
                v-if="node.id == 2 || node.id == 3"
                style="display: flex; flex-direction: column; align-items: center; gap: 8px"
              >
                <el-avatar
                  :size="48"
                  style="
                    border: 2px solid var(--el-color-primary);
                    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                  "
                  >姚</el-avatar
                >
                <el-button type="primary" text>修改</el-button>
              </div>
              <div
                v-if="node.id == 1"
                style="
                  padding: 6px 12px;
                  background: var(--el-color-success-light-9);
                  border-radius: 16px;
                  color: var(--el-color-success);
                "
              >
                ▶ 开始
              </div>
              <div
                v-if="node.id == 4"
                style="
                  padding: 6px 12px;
                  background: var(--el-color-danger-light-9);
                  border-radius: 16px;
                  color: var(--el-color-danger);
                "
              >
                ⏹ 结束
              </div>
            </div>
          </template>
        </myFlow>
      </el-main>
    </el-container>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ElMessage } from 'element-plus';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import myFlow from './flow/index.vue';
import wfUserSelect from '@/views/plugin/workflow/components/nf-user-select/index.vue';
let questionTypeData = ref([]);
let activeName = ref(0);

let form = ref({});

let typeList = ref([
  {
    label: '报销流程',
    value: 0,
  },

  //   {
  //     label: '更多服务',
  //     value: 6,
  //   },
]);
onMounted(() => {
  onLoad();
});
let tableUrl = '/api/vt-admin/itServer/page';
function onLoad() {
  axios.get(tableUrl, {}).then(res => {
    res.data.data.records.forEach(item => {
      if (!item.type && item.type != 0) return;
      const label = typeList.value[item.type]?.label;
      const value = typeList.value[item.type]?.value;
      typeList.value[item.type] = item;
      typeList.value[item.type].label = label;
      typeList.value[item.type].value = value;
    });
    form.value = {
      ...typeList.value[activeName.value],
    };
  });
}
function handleClick(item) {
  console.log(item);

  activeName.value = item.value;
  form.value = {
    ...item,
    content: item.content,
  };
}
function submit(form, done) {
  console.log(form);

  axios
    .post('/api/vt-admin/itServer/save', {
      ...form,
      type: activeName.value,
      id: null,
    })
    .then(res => {
      done();
      ElMessage.success('保存成功');
      onLoad();
    })
    .catch(err => {
      done();
    });
}

const count = ref(0);

const option = ref({
  name: '流程A',
  nodeList: [
    {
      id: '1',
      name: '开始节点',
      left: 494,
      top: 10,
    },
    {
      id: '2',
      name: '主管审批',
      left: 494,
      top: 180,
    },
    {
      id: '3',
      name: '总经理审批',
      left: 494,
      top: 357,
    },
    {
      id: '4',
      name: '结束',
      left: 494,
      top: 530,
    },
  ],
  lineList: [
    { from: '1', to: '2' },
    { from: '2', to: '3' },
    { from: '3', to: '4' },
  ],
});

const nodeList = computed(() => option.value.nodeList);

onMounted(() => {
  form.value = nodeList.value[count.value].id;
});

const handleNext = () => {
  count.value++;
  if (count.value >= nodeList.value.length) {
    count.value = 0;
  }
  form.value = nodeList.value[count.value].id;
};

function getInfo() {
  console.log(form.value, option.value);
}
</script>

<style lang="scss" scoped></style>
