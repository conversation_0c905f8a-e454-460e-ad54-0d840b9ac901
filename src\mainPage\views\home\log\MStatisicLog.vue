<template>
  <div class="wrap">
    <el-radio-group style="margin-bottom: 5px" size="small" v-model="displayType">
      <el-radio-button label="table">
        <el-icon><Grid /></el-icon>
      </el-radio-button>
      <el-radio-button label="chart">
        <el-icon><PieChart /></el-icon>
      </el-radio-button>
    </el-radio-group>

    <div ref="chartsRefPie1" v-if="displayType == 'chart'" style="width: 100%; height: 100%"></div>
    <el-table border v-else size="small" :data="statisicData">
      <el-table-column header-align="center" align="center" prop="name" label="类型" width="80">
        <template #default="{row}">
            <el-tag :type="row.name == '已完成'?'success':'danger'" effect="dark" size="small">{{row.name}}</el-tag>
        </template>
      </el-table-column>
      <el-table-column   prop="prop" label="人员">
        <template #default="{ row }">
          <el-link size="small" type="primary"  style="font-size: 12px;" @click="$emit('change',item)" v-for="(item, index) in row.data" :key="item.id">
            {{ item.realName }}
            <span style="color: black" v-if="index !== row.data.length - 1">,</span>
          </el-link>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script setup>
import axios from 'axios';
import { nextTick, onMounted, watch } from 'vue';
import * as echarts from 'echarts';
const props = defineProps(['logDate']);

let statisicData = ref({});
watch(
  () => props.logDate,
  () => {
    getStatisicData();
  },
  { immediate: true }
);
function getStatisicData() {
  axios
    .get('/api/vt-admin/businessWorkLog/statisticEveryDay', {
      params: {
        date: props.logDate,
      },
    })
    .then(res => {
      statisicData.value = [
        {
          name: '未完成',
          value: res.data.data.noCompleteList.length,
          key: 'noCompleteList',
          data: res.data.data.noCompleteList,
        },
        {
          name: '已完成',
          value: res.data.data.completeList.length,
          key: 'completeList',
          data: res.data.data.completeList,
        },
      ];
      nextTick(() => {
        initChart();
      });
    });
}
let chartsRefPie1 = ref(null);
function initChart() {
  // 初始化饼状图
  const chart = echarts.init(chartsRefPie1.value);

  // 配置饼状图选项
  const option = {
    tooltip: {
      trigger: 'item',
      // 格式化提示框内容，显示所有完成或未完成的人的名字
      formatter: function (params) {
        const names = params.data.data.map(item => item.realName);
        let formattedNames = '';
        for (let i = 0; i < names.length; i++) {
          formattedNames += names[i];
          if ((i + 1) % 8 === 0 && i !== names.length - 1) {
            formattedNames += '<br>';
          } else if (i !== names.length - 1) {
            formattedNames += ', ';
          }
        }
        return `${params.data.name}: ${formattedNames}`;
      },
    },
    series: [
      {
        name: '统计数据',
        type: 'pie',
        // 增大饼状图半径，使图形变大
        radius: '70%',
        data: statisicData.value, // 替换为你的数据数组

        label: {
          show: true,
          fontSize: 11,
          formatter: params => {
            const names = params.data.data.map(item => item.realName);
            let formattedNames = '';
            const maxCount = 10; // 最多显示10个名字

            for (let i = 0; i < Math.min(names.length, maxCount); i++) {
              formattedNames += names[i];
              if ((i + 1) % 2 === 0 && i !== Math.min(names.length, maxCount) - 1) {
                formattedNames += '\n';
              } else if (i !== Math.min(names.length, maxCount) - 1) {
                formattedNames += ', ';
              }
            }

            // 如果名字数量超过10个，添加等表示剩下的
            if (names.length > maxCount) {
              formattedNames += ' 等';
            }

            return `${params.name}:\n ${formattedNames}`;
          },
        },
      },
    ],
  };

  // 使用配置项显示图表
  chart.setOption(option);
}

let displayType = ref('table');
watch(
  () => displayType.value,
  () => {
    if (displayType.value === 'chart') {
      nextTick(() => {
        initChart();
      });
    }
  }
);
</script>

<style lang="scss" scoped>
.wrap {
  padding: 0 10px;
  height: calc(100% - 85px);
  //   border: 1px solid #ccc;
  box-sizing: border-box;
}
</style>
