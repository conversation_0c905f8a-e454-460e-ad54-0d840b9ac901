<template>
  <div class="m-calendar">
    <div class="m-calendar-title">
      <el-icon @click="handlePrevYear" class="calendaricon"><DArrowLeft /></el-icon>
      <el-icon @click="handlePrevMonth" class="calendaricon"><ArrowLeft /></el-icon>
      <!-- <i @click="handlePrevYear" class="calendaricon el-icon-d-arrow-left"></i>
        <i @click="handlePrevMonth" class="calendaricon el-icon-arrow-left"></i> -->
      <span>{{ year }}年{{ month }}月</span>
      <el-icon @click="handleNextMonth" class="calendaricon"><ArrowRight /></el-icon>
      <el-icon @click="handleNextYear" class="calendaricon"><DArrowRight /></el-icon>
      <!-- <i @click="handleNextMonth" class="calendaricon el-icon-arrow-right"></i>
        <i @click="handleNextYear" class="calendaricon el-icon-d-arrow-right"></i> -->
    </div>
    <div class="m-calendar-con">
      <div class="m-calendar-con-week">
        <div class="m-calendar-con-week-item">一</div>
        <div class="m-calendar-con-week-item">二</div>
        <div class="m-calendar-con-week-item">三</div>
        <div class="m-calendar-con-week-item">四</div>
        <div class="m-calendar-con-week-item">五</div>
        <div class="m-calendar-con-week-item">六</div>
        <div class="m-calendar-con-week-item">日</div>
      </div>
      <div class="m-calendar-con-days">
        <div
          class="m-calendar-con-day"
          v-for="(item, index) in dayArr"
          :class="{
            active: Math.floor(index / 7) == Math.floor(selectIndex / 7),
            active_borderRadiusLeft: index % 7 == 0,
            active_borderRadiusRight: index % 7 == 6,
          }"
          :key="index"
          :style="{
            color: item.type == 'pre' || item.type == 'next' ? '#ddd' : '#000',
           
          }"
          @click="handleDayClick(index)"
        >
          <span>{{ item['name'] }}</span>
          <el-icon
            v-if="
              index % 7 == 6 && statisicData && statisicData[Math.floor(index / 7)]?.weekStatus == 2
            "
            style="position: absolute; right: 0; top: 0; color: var(--el-color-success)"
            ><SuccessFilled
          /></el-icon>
          <el-icon
            v-if="
              index % 7 == 6 && statisicData && statisicData[Math.floor(index / 7)]?.weekStatus == 0
            "
            style="position: absolute; right: 0; top: 0; color: var(--el-color-danger)"
            ><CircleCloseFilled
          /></el-icon>
          <el-icon
            v-if="
              index % 7 == 6 && statisicData && statisicData[Math.floor(index / 7)]?.weekStatus == 1
            "
            style="position: absolute; right: 0; top: 0; color: var(--el-color-warning)"
            ><WarningFilled
          /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
// import axios from "axios";
import axios from 'axios';
import moment from 'moment';
const fullMonth = [1, 3, 5, 7, 8, 10, 12];

export default {
  name: 'MCalendar',
  props: ['type', 'userId'],
  data() {
    return {
      // 时间戳
      time: 0,
      // 当前时间戳的年份
      year: 0,
      // 当前时间戳的月份
      month: 0,
      // 日历天的数组
      dayArr: [],
      // 时间戳的日子
      day: 0,
      // 任务日程状态数组
      todoStatusList: [],
      // 选中的日期
      selectIndex: 0,
      statisicData: [],
    };
  },
  watch:{
    userId(){
        this.getStatisic()
    }
  },
  created() {
    let time = new Date();
    this.time = time.getTime();

    this.initCalendar();
    this.getStatisic()
    this.handleOutYearMonthDay();
  },
  methods: {
    // 获取当前日期星期几
    getTodayWeek() {
      let time = `${this.year}-${this.month.toString().padStart(2, 0)}-01`;
      let day = new Date(time).getDay() - 1;
      if (day == -1) {
        return 6;
      }
      return day;
    },
    // 获取当前月份有多少天
    getToMonDays() {
      if (fullMonth.includes(this.month)) {
        return 31;
      }
      if (this.month != 2) {
        return 30;
      }
      // 闰年2月判断
      if ((this.year % 4 == 0 && this.year % 100 != 0) || this.year % 400 == 0) {
        return 29;
      }
      return 28;
    },
    //   获取某个月上月有多少天
    getDaysInLastMonth(year, month) {
      // 月份是从0开始的，所以1月是0，12月是11
      // 计算上一个月的年份和月份

      let lastMonthYear = year;
      let lastMonthMonth = month - 1;
      if (lastMonthMonth == 0) {
        lastMonthMonth = 12; // 上个月是前一年的12月
        lastMonthYear--; // 上个月是前一年的某个月
      }

      // 创建一个Date对象，设置为上个月的某一天（通常是1号）
      let date = new Date(lastMonthYear, lastMonthMonth, 1);

      // 将日期设置为上个月最后一天（即当前月的0号）
      date.setDate(0);

      // 获取上个月的最后一天，也就是上个月有多少天
      
      let daysInLastMonth = date.getDate();

      return daysInLastMonth;
    },
    // 获取上个月的YYYY-MM
    getDateInLastMonth(year, month) {
      
      // 月份是从0开始的，所以1月是0，12月是11
      // 计算上一个月的年份和月份
        
      let lastMonthYear = year;
      let lastMonthMonth = month - 1;
      if (lastMonthMonth == 0) {
        lastMonthMonth = 12; // 上个月是前一年的12月
        lastMonthYear--; // 上个月是前一年的某个月
      }
    
      return `${lastMonthYear}-${lastMonthMonth < 10 ? '0' + lastMonthMonth : lastMonthMonth}`;
    },
    // 获取下个月的YYYY-MM
    getDateInNextMonth(year, month) {
      // 月份是从0开始的，所以1月是0，12月是11
      // 计算上一个月的年份和月份
      
      let nextMonthYear = year;
      let nextMonthMonth = month + 1;
      if (nextMonthMonth == 13) {
        nextMonthMonth = 1; // 上个月是前一年的12月
        nextMonthYear++; // 上个月是前一年的某个月
      }

      return `${nextMonthYear}-${nextMonthMonth < 10 ? '0' + nextMonthMonth : nextMonthMonth}`;
    },
    // 天数选择
    handleDayClick(index) {
      
      if (index < this.getTodayWeek() || index > this.getToMonDays() + this.getTodayWeek() - 1) {
        return;
      }
      this.selectIndex = index;
      this.day = this.dayArr[index]['name'];
      this.time = new Date(
        `${this.year}-${this.month.toString().padStart(2, '0')}-${this.day
          .toString()
          .padStart(2, '0')}`
      ).getTime();
      console.log(`${this.year}-${this.month.toString().padStart(2, '0')}-${this.day
          .toString()
          .padStart(2, '0')}`);
      
      
      // 抛出日期
      this.handleOutYearMonthDay();
    },
    // 初始化/更新日历
    async initCalendar() {
      
      this.year = new Date(this.time).getFullYear();
      this.month = new Date(this.time).getMonth() + 1;
      this.day = new Date(this.time).getDate();
      // 拿到所有任务状态
      const todoStatusList = await this.queryTodoStatusList();

      let arr = [];
      for (let i = 1; i <= this.getToMonDays(); i++) {
        let date = `${this.year}-${this.month.toString().padStart(2, '0')}-${i
          .toString()
          .padStart(2, '0')}`;
        for (let j = 0; j < todoStatusList.length; j++) {
          if (date == todoStatusList[j]['scheduleDate']) {
            // 有状态推送状态
            arr.push({
              name: i,
              status: todoStatusList[j]['status'],
              date: `${this.year}-${this.month < 10 ? '0' + this.month : this.month}`,
            });
            break;
          }
        }
        if (!arr[i - 1]) {
          arr.push({
            name: i,
            date: `${this.year}-${this.month < 10 ? '0' + this.month : this.month}`,
          });
        }
      }
      if (this.getTodayWeek() == 0) {
        this.dayArr = arr;
      } else {
        // const preDays = this.getToMonDays
        
        const lastDate = this.getDateInLastMonth(this.year, this.month);
        const nextDate = this.getDateInNextMonth(this.year, this.month);
        const preArr = [
          ...Array(this.getTodayWeek())
            .fill('')
            .map((item, index, arr) => {
              return {
                name: this.getDaysInLastMonth(this.year, this.month) - arr.length + index + 1,
                type: 'pre',
                date: lastDate,
              };
            }),
        ];
        console.log(this.getTodayWeek(), this.day - 1, '数据');
        this.dayArr = [...preArr, ...arr];
        this.dayArr = [
          ...this.dayArr,
          ...Array(42 - this.dayArr.length)
            .fill('')
            .map((item, index) => {
              return {
                type: 'next',
                name: index + 1,
                date: nextDate,
              };
            }),
        ];
      }

      // 设置选中
      this.selectIndex = this.getTodayWeek() + this.day - 1;
      // 刷新当前任务列表
      this.handleOutYearMonthDay();
      
    },
    // 查询日程任务状态数组
    queryTodoStatusList() {
      return new Promise(resolve => {
        let dateArr = moment(this.time).format('YYYY-MM').split('-');
        // 通过年/月拿到当前月份的任务状态数组
        axios
          .get('/api/vt-admin/schedule/statisticsByYearAndDate', {
            params: {
              year: dateArr[0],
              month: dateArr[1],
            },
          })
          .then(e => {
            resolve(e.data.data);
          });
      });
    },
    // 上一年
    handlePrevYear() {
      if (this.time && this.time > 31536000000) {
        this.time -= 31536000000;
        // 更新日历
        this.initCalendar();
        this.getStatisic();
      }
    },
    // 下一页
    handleNextYear() {
      if (this.time) {
        this.time += 31536000000;
        // 更新日历
        this.initCalendar();
        this.getStatisic();
      }
    },
    // 上一月
    handlePrevMonth() {
      if (this.time && this.time > 2592000000) {
        const lastMonthDay = this.getDaysInLastMonth(this.year, this.month);
        
        this.time -= lastMonthDay * 24 * 60 * 60 * 1000;
        // 更新日历
        this.initCalendar();
        this.getStatisic();
      }
    },
    // 下一月
    handleNextMonth() {
      if (this.time) {
        console.log(this.time);
        this.time += 2592000000;
        console.log(this.time);
        // 更新日历
        this.initCalendar();
        this.getStatisic();
      }
    },
    // 对外抛出日期变化
    handleOutYearMonthDay() {
      const startIndex = Math.floor(this.selectIndex / 7) * 7;
      const endIndex =
        Math.ceil(this.selectIndex / 7) * 7 == startIndex
          ? startIndex + 6
          : Math.ceil(this.selectIndex / 7) * 7 - 1;
      console.log(this.dayArr[startIndex], this.dayArr[endIndex]);
      const { name, date } = this.dayArr[startIndex];
      const { name: name1, date: date1 } = this.dayArr[endIndex];
      const startDate = `${date}-${name < 10 ? '0' + name : name}`;
      const endDate = `${date1}-${name1 < 10 ? '0' + name1 : name1}`;

      this.$emit('calendarChange', [startDate, endDate]);
    },
    getStatisic() {
        if(!this.userId) return 
      axios
        .get('/api/vt-admin/weekPlan/monthlyPlanSituation', {
          params: {
            month: `${this.year}-${this.month < 10 ? '0' + this.month : this.month}`,
            userId: this.userId,
          },
        })
        .then(res => {
          this.statisicData = res.data.data;
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.m-calendar {
  width: 100%;
  height: 100%;
  padding-right: 7px;
  box-sizing: border-box;

  .m-calendar-title {
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 16px;
    color: #303133;

    .calendaricon {
      margin: 0 10px;
      cursor: pointer;
    }
  }
  .m-calendar-con {
    width: 100%;
    height: calc(100% - 40px);

    .m-calendar-con-week {
      width: 100%;
      height: 24px;
      display: flex;

      .m-calendar-con-week-item {
        flex: 1;
        width: 100%;
        color: #606266;
        line-height: 24px;
        height: 24px;
        text-align: center;
      }
    }
    .m-calendar-con-days {
      width: 100%;
      height: calc(100% - 24px);
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      align-content: flex-start;
      overflow-x: hidden;
      overflow-y: auto;

      .m-calendar-con-day {
        width: calc(100% / 7);
        height: 16.5%;
        // border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        position: relative;
        border-bottom: 1px dashed #ccc;
        font-size: 14px;
        box-sizing: border-box;
        cursor: pointer;

        .complete {
          display: inline-block;
          width: 5px;
          height: 5px;
          background-color: #ececec;
          border-radius: 100%;

          position: absolute;
          bottom: 2px;
          left: 50%;
          transform: translateX(-50%);
        }

        .nocomplete {
          width: 5px;
          height: 5px;
          // border-radius: 100%;
          position: absolute;
          bottom: 2px;
          left: 50%;
          transform: translateX(-50%);
          background-color: #fa3534;
          font-size: 10px;
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
        }

        &:hover {
          background-color: #f5f7fa;
        }

        &.active {
          color: #fff !important;

          background-color: var(--el-color-primary);
        }
        // &.active_borderRadiusLeft{
        //     border-top-left-radius: 50%;
        //     border-bottom-left-radius: 50%;
        // }
        // &.active_borderRadiusRight{
        //     border-top-right-radius: 50%;
        //     border-bottom-right-radius: 50%;
        // }
      }
    }
  }
}
</style>
