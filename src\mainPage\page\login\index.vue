<template>
  <div class="login-container" ref="login" @keyup.enter="handleLogin">
    <img class="logo" :src="logoUrl" alt="未配置企业logo" />
    <div
      style="
        position: fixed;
        width: 100vw;
        height: 100vh;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 9;
      "
    >
      <el-carousel
        height="100vh"
        :interval="5000"
        arrow="never"
        loop
        autoplay
        indicator-position="none"
      >
        <el-carousel-item v-for="item in urlList" :key="item">
          <div
            class="login_bg"
            :style="{ 'background-image': `url(${item})`, height: '100vh' }"
          ></div>
        </el-carousel-item>
      </el-carousel>
      <div style="position: fixed; bottom: 30px; z-index: 9; left: 50%">
        <a
          href="https://beian.miit.gov.cn/"
          target="_blank"
          style="text-decoration: none"
          rel="noopener"
        >
          <span style="color: #fff; font-size: 14px; vertical-align: middle"
            >粤ICP备17073149号-2</span
          >
        </a>
      </div>
    </div>
    <div class="login-weaper animate__animated animate__backInRight">
      <!-- <div class="login-left">
        <div class="login-time">
          {{ time }}
        </div>
        <img class="img" src="/img/logo.png" alt="">
        <img style="width: 300px;"  src="/img/logo1.png" alt="">
      </div> -->
      <div class="login-border">
        <div class="login-main">
          <h4 class="login-title">
            {{ $t('login.title') }}{{ website.title }}
            <!-- <top-lang></top-lang> -->
          </h4>
          <userLogin v-if="activeName === 'user'"></userLogin>
          <codeLogin v-else-if="activeName === 'code'"></codeLogin>
          <thirdLogin v-else-if="activeName === 'third'"></thirdLogin>
          <!-- <div class="login-menu">
            <a href="#" @click.stop="activeName = 'user'">{{ $t('login.userLogin') }}</a>
            <a href="#" @click.stop="activeName='code'">{{ $t('login.phoneLogin') }}</a>
            <a href="#" @click.stop="activeName = 'third'">{{ $t('login.thirdLogin') }}</a>
            <a :href="website.ssoUrl + website.redirectUri">{{ $t('login.ssoLogin') }}</a>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import userLogin from './userlogin.vue';
import codeLogin from './codelogin.vue';
import thirdLogin from './thirdlogin.vue';
import { mapGetters } from 'vuex';
import { validatenull } from '@/utils/validate';
import topLang from '@/page/index/top/top-lang.vue';
import { getQueryString, getTopUrl } from '@/utils/util';
import website from '@/config/website';

export default {
  name: 'login',
  components: {
    userLogin,
    codeLogin,
    thirdLogin,
    topLang,
  },
  data() {
    return {
      website: website,
      time: '',
      activeName: 'user',
      socialForm: {
        tenantId: '000000',
        source: '',
        code: '',
        state: '',
      },
      urlList: [],
      logoUrl:'',
      logo:'',
    };
  },
  watch: {
    $route() {
      this.handleLogin();
    },
  },
  created() {
    this.handleLogin();
    this.getTime();
  },
  mounted() {},
  computed: {
    ...mapGetters(['tagWel']),
  },
  props: [],
  methods: {
    getTime() {
      setInterval(() => {
        this.time = this.$dayjs().format('YYYY-MM-DD HH:mm:ss');
      }, 1000);
    },
    handleLogin() {
      const topUrl = getTopUrl();
      const redirectUrl = '/oauth/redirect/';
      const ssoCode = '?code=';
      this.socialForm.source = getQueryString('source');
      this.socialForm.code = getQueryString('code');
      this.socialForm.state = getQueryString('state');
      if (validatenull(this.socialForm.source) && topUrl.includes(redirectUrl)) {
        let source = topUrl.split('?')[0];
        source = source.split(redirectUrl)[1];
        this.socialForm.source = source;
      }
      if (
        topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.source) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: '第三方系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('LoginBySocial', this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(redirectUrl)[0];
            this.$router.push(this.tagWel);
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      } else if (
        !topUrl.includes(redirectUrl) &&
        !validatenull(this.socialForm.code) &&
        !validatenull(this.socialForm.state)
      ) {
        const loading = this.$loading({
          lock: true,
          text: '单点系统登录中,请稍后',
          background: 'rgba(0, 0, 0, 0.7)',
        });
        this.$store
          .dispatch('LoginBySso', this.socialForm)
          .then(() => {
            window.location.href = topUrl.split(ssoCode)[0];
            this.$router.push(this.tagWel);
            loading.close();
          })
          .catch(() => {
            loading.close();
          });
      }
    },
  },
 
};
</script>

<style lang="scss">
@import '@/styles/login.scss';
.login_bg {
  background-size: 100vw 100vh;
}
</style>
