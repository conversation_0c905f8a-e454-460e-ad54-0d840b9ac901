<template>
  <div class="demo-date-picker" style="width: 100%;">
    <div class="block" style="display: flex; align-items: center">
      <el-date-picker
        v-model="dateRange[0]"
        @change="handleChage($event, 0)"
        :format="format"
      
        :valueFormat="valueFormat"
        :type="type"
        placeholder="开始日期"
      />
      -
      <el-date-picker
        v-model="dateRange[1]"
        :type="type"
        :format="format"
        :valueFormat="valueFormat"
        @change="handleChage($event, 1)"
        placeholder="结束日期"
      />
      <el-dropdown @command="handleQuickFilter" style="margin-left: 3px;">
        <el-button   size="small" circle>
          <el-icon><arrow-down /></el-icon>
        </el-button>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="thisMonth">本月</el-dropdown-item>
            <el-dropdown-item command="lastMonth">上月</el-dropdown-item>
            <el-dropdown-item command="last3Months">最近三月</el-dropdown-item>
            <el-dropdown-item command="last6Months">最近半年</el-dropdown-item>
            <el-dropdown-item command="thisYear">今年</el-dropdown-item>
            <el-dropdown-item command="lastYear">去年</el-dropdown-item>
            <el-dropdown-item command="last2Years">最近两年</el-dropdown-item>
            <el-dropdown-item command="last3Years">最近三年</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
   
  </div>
</template>
<script>
import moment from 'moment';
import { ArrowDown } from '@element-plus/icons-vue';
export default {
  name: 'daterange-search',
  components: {
    ArrowDown,
  },
  emits: ['update:modelValue'],

  props: {
    modelValue: Array,
   
    type: {
      type: String,
      default: 'date',
    },
    format: {
      type: String,
      default: 'YYYY-MM-DD',
    },
    valueFormat: {
      type: String,
      default: 'YYYY-MM-DD',
    },
  },
  data() {
    return {
      dateRange: [null, null],
      timeRange: [null, null],

    };
  },
  computed: {},
  watch: {
    modelValue: {
      handler(newVal, oldVal) {
       
        this.dateRange = newVal
        if (newVal.length == 0 ) {
          this.dateRange = [null, null];
        }

      },
      immediate: true,
      deep:true
    },
  },
  mounted() {},
  methods: {
    handleChage(val, index,type) {
     
     
        this.$emit('update:modelValue', this.dateRange);
     
     
    },
    handleDateChange(val){
      this.dateRange[1] = val
     
    },
    handleQuickFilter(value) {
      if (!value) return;
      
      const now = moment();
      let startDate, endDate;
      
      switch (value) {
        case 'thisMonth':
          startDate = now.clone().startOf('month');
          endDate = now.clone().endOf('month');
          break;
        case 'lastMonth':
          startDate = now.clone().subtract(1, 'month').startOf('month');
          endDate = now.clone().subtract(1, 'month').endOf('month');
          break;
        case 'last3Months':
          startDate = now.clone().subtract(3, 'months').startOf('month');
          endDate = now.clone().endOf('month');
          break;
        case 'last6Months':
          startDate = now.clone().subtract(6, 'months').startOf('month');
          endDate = now.clone().endOf('month');
          break;
        case 'thisYear':
          startDate = now.clone().startOf('year');
          endDate = now.clone().endOf('year');
          break;
        case 'lastYear':
          startDate = now.clone().subtract(1, 'year').startOf('year');
          endDate = now.clone().subtract(1, 'year').endOf('year');
          break;
        case 'last2Years':
          startDate = now.clone().subtract(2, 'years').startOf('year');
          endDate = now.clone().endOf('year');
          break;
        case 'last3Years':
          startDate = now.clone().subtract(3, 'years').startOf('year');
          endDate = now.clone().endOf('year');
          break;
        default:
          return;
      }
      
      this.dateRange = [
        startDate.format(this.valueFormat),
        endDate.format(this.valueFormat)
      ];
      
      this.$emit('update:modelValue', this.dateRange);
    }
  },
};
</script>
<style lang="" scoped></style>
