<template>
    <basic-container>
      <avue-crud
        :option="option"
        :data="tableData"
        v-model:page="page"
        v-model:search="params"
        @on-load="onLoad"
        @row-update="rowUpdate"
        @row-save="rowSave"
        :table-loading="loading"
        ref="crud"
        @tree-load="treeLoad"
        lazy
        :before-open="beforeOpen"
        @row-del="rowDel"
        @search-reset="onLoad"
        @search-change="searchChange"
        @current-change="onLoad"
        @keyup.enter="onLoad"
        @refresh-change="onLoad"
        @size-change="onLoad"
        v-model="form"
      >
        <template #menu="{ row }">
          <el-button text size="small" type="primary" icon="CirclePlus" @click="addItem(row)"
            >新增子项</el-button
          >
        </template>
      </avue-crud>
      <!-- <dialogForm ref="dialogForm"></dialogForm> -->
    </basic-container>
  </template>
  
  <script setup>
  import axios from 'axios';
  import { ref, getCurrentInstance, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { followType } from '@/const/const.js';
  let option = ref({
    height: 'auto',
    align: 'center',
    addBtn: true,
    lazy: true,
    editBtn: true,
    index: true,
    delBtn: true,
    calcHeight: 30,
    searchMenuSpan: 6,
    // searchSpan: 4,
    menuWidth: 270,
    border: true,
    column: [
      {
        label: '上级分类',
        type: 'tree',
        prop: 'parentId',
        dicUrl: tableUrl,
        hide: true,
        accordion: true,
        filter: true,
        props: {
          value: 'id',
          label: 'categoryName',
        },
        lazy: true,
        treeLoad: (node, resolve) => {
          axios
            .get(tableUrl, {
              params: {
                parentId: node.data.id,
              },
            })
            .then(res => {
              resolve(
                res.data.data.map(item => {
                  return {
                    ...item,
                    leaf: !item.hasChildren,
                  };
                })
              );
            });
        },
      },
      {
        label: '上级分类',
        prop: 'parentName',
        disabled: true,
        hide:true,
      },
      {
        label: '分类名称',
        prop: 'categoryName',
        // width: 250,
        overHidden: true,
        align: 'left',
        search: true,
        rules: [
          {
            required: true,
            message: '请填写分类名称',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '分类编号',
        prop: 'categoryCode',
        // width: 250,
        overHidden: true,
        // search: true,
        rules: [
          {
            required: true,
            message: '请填写分类编号',
            trigger: 'blur',
          },
        ],
      },
      {
        label: '备注',
        prop: 'remark',
        // width: 250,
        overHidden: true,
        // search: true,
      },
    ],
  });
  let form = ref({});
  let page = ref({
    pageSize: 10,
    currentPage: 1,
    total: 0,
  });
  
  const addUrl = '/api/vt-admin/productCategory/save';
  const delUrl = '/api/vt-admin/productCategory/remove?ids=';
  const updateUrl = '/api/vt-admin/productCategory/update';
  const tableUrl = '/api/vt-admin/productCategory/list';
  let params = ref({});
  let tableData = ref([]);
  let { proxy } = getCurrentInstance();
  let route = useRoute();
  
  let loading = ref(false);
  function onLoad() {
    loading.value = true;
    axios
      .get(tableUrl, {
        params: {
          ...params.value,
        },
      })
      .then(res => {
        loading.value = false;
        tableData.value = res.data.data;
      });
  }
  let router = useRouter();
  
  function rowSave(form, done, loading) {
    const data = {
      ...form,
      // parentId: parentId.value,
    };
    axios
      .post(addUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          onLoad();
          done(res.data.data);
          // done();
          parentId.value = 0;
        }
      })
      .catch(err => {
        done();
        parentId.value = 0;
      });
  }
  function rowUpdate(row, index, done, loading) {
    const data = {
      ...row,
    };
    axios
      .post(updateUrl, data)
      .then(res => {
        if (res.data.code == 200) {
          proxy.$message.success(res.data.msg);
          // onLoad();
          done(data);
        }
      })
      .catch(err => {
        done();
      });
  }
  function rowDel(form, index, done) {
    if(form.hasChildren) return proxy.$message.info('请先删除子项')
    proxy
      .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
      .then(() => {
        console.log(222);
        axios.post(delUrl + form.id).then(res => {
          proxy.$message({
            type: 'success',
            message: '删除成功',
          });
          done(form);
        });
      })
      .catch(() => {});
  }
  
  function searchChange(params, done) {
    onLoad();
    done();
  }
  let parentId = ref(0);
  function addItem(row) {
    parentId.value = row.id;
    form.value.parentId = row.id;
    form.value.parentName = row.categoryName;
    // form.value.parentId = row.categoryName;
    proxy.$refs.crud.rowAdd();
  }
  
  function treeLoad(tree, treeNode, resolve) {
    loading.value = true;
    axios
      .get(tableUrl, {
        params: {
          ...params.value,
          parentId: tree.id,
        },
      })
      .then(res => {
        loading.value = false;
        resolve(res.data.data);
      });
  }
  function beforeOpen(done, type) {
    console.log(form);
    const cateGoryRef = proxy.findObject(option.value.column, 'parentId');
    const parentNameRef = proxy.findObject(option.value.column, 'parentName');
    if (type == 'edit') {
      // form.value.parentName = form.value.categoryName;
      cateGoryRef.display = true
      parentNameRef.display = false;
    }
    if (type == 'add') {
      if (form.value.parentId) {
        // form.value.parentName = form.value.categoryName;
        console.log(cateGoryRef);
        cateGoryRef.display = false;
        parentNameRef.display = true;
      } else {
        cateGoryRef.display = true;
        parentNameRef.display = false;
      }
    }
    done();
  }
  </script>
  
  <style lang="scss" scoped></style>
  