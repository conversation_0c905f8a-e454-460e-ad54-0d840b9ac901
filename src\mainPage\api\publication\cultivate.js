import axios from "../../axios";

/**
 * 新增分类
 * @param {*} data
 * @returns
 */
export const addCate = (data) => {
  return axios({
    url: "/api/vt-admin/publishTrain/add",
    method: "post",
    data,
  });
};

/**
 * 编辑分类
 * @param {*} data
 * @returns
 */
export const updateCate = (data) => {
  return axios({
    url: "/api/vt-admin/publishTrain/update",
    method: "post",
    data,
  });
};

/**
 * 删除分类
 * @param {*} data
 * @returns
 */
export const removeCate = (data) => {
  return axios({
    url: "/api/vt-admin/publishTrain/remove",
    method: "post",
    data,
  });
};

/**
 * 新增教程
 * @param {*} data
 * @returns
 */
export const addTou = (data) => {
  return axios({
    url: "/api/vt-admin/PublishTrainCourse/add",
    method: "post",
    data,
  });
};

/**
 * 编辑教程
 * @param {*} data
 * @returns
 */
export const updateTou = (data) => {
  return axios({
    url: "/api/vt-admin/PublishTrainCourse/update",
    method: "post",
    data,
  });
};

/**
 * 删除教程
 * @param {*} data
 * @returns
 */
export const removeTou = (data) => {
  return axios({
    url: "/api/vt-admin/PublishTrainCourse/remove",
    method: "post",
    data,
  });
};

/**
 * 新增课件
 * @param {*} data
 * @returns
 */
export const addLesson = (data) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/add",
    method: "post",
    data,
  });
};

/**
 * 编辑课件
 * @param {*} data
 * @returns
 */
export const updateLesson = (data) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/update",
    method: "post",
    data,
  });
};

/**
 * 删除课件
 * @param {*} data
 * @returns
 */
export const removeLesson = (data) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/remove",
    method: "post",
    data,
  });
};

/**
 * 分类上移
 * @param {*} id
 * @returns
 */
export const cateMoveUp = (id) => {
  return axios({
    url: "/api/vt-admin/publishTrain/moveUp",
    method: "post",
    data: {
      id,
    },
  });
};

/**
 * 分类下移
 * @param {*} id
 * @returns
 */
export const cateMoveDown = (id) => {
  return axios({
    url: "/api/vt-admin/publishTrain/moveDown",
    method: "post",
    data: {
      id,
    },
  });
};

/**
 * 教程上移
 * @param {*} id
 * @returns
 */
export const tutorialMoveUp = (id) => {
  return axios({
    url: "/api/vt-admin/PublishTrainCourse/moveUp",
    method: "post",
    data: {
      id,
    },
  });
};

/**
 * 教程下移
 * @param {*} id
 * @returns
 */
export const tutorialMoveDown = (id) => {
  return axios({
    url: "/api/vt-admin/PublishTrainCourse/moveDown",
    method: "post",
    data: {
      id,
    },
  });
};

/**
 * 课件上移
 * @param {*} id
 * @returns
 */
export const lessonMoveUp = (id) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/moveUp",
    method: "post",
    data: {
      id,
    },
  });
};

/**
 * 课件下移
 * @param {*} id
 * @returns
 */
export const lessonMoveDown = (id) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/moveDown",
    method: "post",
    data: {
      id,
    },
  });
};
/**
 * 培训中心-树数据
 * @returns
 */
export const treeData = () => {
  return axios({
    url: "/api/vt-admin/publishTrain/findByTree",
    method: "post",
  });
};

/**
 * 培训中心-教程分类列表
 * @param {*} current
 * @param {*} size
 * @returns
 */
// export const cateList = () => {
//   return axios({
//     url: "/api/vt-admin/publishTrain/list",
//     method: "post",
//   });
// };
export const cateList = (current, size) => {
  return axios({
    url: "/api/vt-admin/publishTrain/page",

    method: "get",
    params: {
      current, size
    }
  });
};

/**
 * 培训中心-教程列表
 * @param {*} current
 * @param {*} size
 * @param {*} publishTrainId
 * @returns
 */
export const tutorialList = (publishTrainId, current, size) => {
  return axios({
    url: "/api/vt-admin/PublishTrainCourse/page",
    method: "get",
    params: {
      publishTrainId, current, size
    },
  });
};
// export const tutorialList = (publishTrainId) => {
//   return axios({
//     url: "/api/vt-admin/PublishTrainCourse/list",
//     method: "post",
//     data: {
//       publishTrainId,
//     },
//   });
// };

/**
 * 培训中心-课件列表
 * @param {*} params
 * @returns
 */
export const lessonList = (params, current, size) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/page",
    method: "get",
    params: params,
  });
};
// export const lessonList = (publishCourseId) => {
//   return axios({
//     url: "/api/vt-admin/PublishTrainBreak/list",
//     method: "post",
//     data: {
//       publishCourseId,
//     },
//   });
// };

/**
 * 培训中心-课件详情
 * @param {*} id
 * @returns
 */
export const lessonDetail = (id) => {
  return axios({
    url: "/api/vt-admin/PublishTrainBreak/getId?id=" + id,
    method: "get",
  });
};
