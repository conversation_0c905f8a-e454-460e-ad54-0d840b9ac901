<template>
  <div class="list">
    <div class="list-title">
      {{ todoDate }}

      <el-icon @click="handleAddTask" style="font-size: 18px; cursor: pointer"
        ><CirclePlus
      /></el-icon>
    </div>
    <div v-for="(item, index) in list" :key="item['id']" class="todo-item">
      <el-input
        v-if="!item['status']"
        type="textarea"
        autosize
        v-model="item.scheduleName"
        @focus="item['focus'] = true"
        @blur="handleTodoItemBlur(index)"
        :ref="'input' + index"
      >
      </el-input>
      <div v-else class="complete">{{ item.scheduleName }}</div>
      <div v-if="!item['focus']" class="control">
        <div
          v-if="!item['status']"
          @click="handleCompleteTodoItem(index, true)"
          class="control-item enter"
        >
          <el-icon><CircleCheck /></el-icon>
        </div>
        <div @click.stop="handleCompleteTodoItem(index, false)" class="control-item enter" v-else>
          <el-icon><RefreshLeft /></el-icon>
        </div>
        <div @click.stop="handleDelTodoItem(index)" class="control-item close">
          <el-icon><CircleClose /></el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'MToDoList',
  props: {
    data: {
      type: Array,
    },
    date: {
      type: String,
    },
  },
  data() {
    return {
      list: [],
      todoDate: '',
    };
  },
  watch: {
    data: function (val) {
      this.list = val.map(item => {
        return {
          ...item,
          focus: false,
        };
      });
    },
    date: function (val) {
      this.todoDate = val;
    },
  },
  created() {
    this.todoDate = this.date;
    this.list = this.data.map(item => {
      return {
        ...item,
        focus: false,
      };
    });
  },
  methods: {
    // 新增日程
    handleAddTodo(index) {
      if (
        index == this.list.length - 1 &&
        this.list[index]['scheduleName'] !== null &&
        this.list[index]['scheduleName'] !== undefined &&
        this.list[index]['scheduleName'] !== ''
      ) {
        this.list.push({
          // 日期
          scheduleDate: this.todoDate,
          // 内容
          scheduleName: '',
          // 未完成
          status: 0,
        });
      }
      this.$nextTick(() => {
        let str = this.list[index]['scheduleName'];
        this.list[index].scheduleName = str.substring(0, str.length - 1);
        this.$refs['input' + (index + 1)][0].focus();
      });
    },
    // 删除日程
    handleDelTodoItem(index) {
      axios.post('/api/vt-admin/schedule/realRemove?id=' + this.list[index]['id']).then(e => {
        if (e.data.code == 200) {
          this.list.splice(index, 1);
          this.handleRefreshCalendar();
        }
      });
    },
    /**
     * 任务item取消焦点
     */
    handleTodoItemBlur(index) {
      this.list[index]['focus'] = false;
      if (
        this.list[index]['scheduleName'] === null ||
        this.list[index]['scheduleName'] === undefined ||
        this.list[index]['scheduleName'] === ''
      ) {
        if (this.list[index]['id']) {
          this.handleDelTodoItem(index);
        } else {
          this.list.splice(index, 1);
        }
        return;
      }
      // 新增/修改
      axios.post('/api/vt-admin/schedule/submit', this.list[index]).then(e => {
        if (e.data.code == 200) {
          this.handleRefreshCalendar();
        }
      });
    },
    // 新增默认数据
    handleFirstAdd() {
      if (this.list.length > 0) {
        for (let i = 0; i < this.list.length; i++) {
          if (this.list[i]['status'] == 0) {
            return;
          }
        }
      }
      this.list.unshift({
        // 日期
        scheduleDate: this.todoDate,
        // 内容
        scheduleName: '',
        // 完成状态
        status: 0,
      });
      this.$nextTick(() => {
        this.$refs['input0'][0].focus();
      });
    },
    // 完成日程
    handleCompleteTodoItem(index, flag) {
      this.list[index].status = flag ? 1 : 0;
      // 切换状态
      axios.get('/api/vt-admin/schedule/switchStatus?id=' + this.list[index]['id']).then(e => {
        if (e.data.code == 200) {
          this.handleRefreshCalendar();
        }
      });
    },
    // 刷新日历数据状态
    handleRefreshCalendar() {
      this.$parent.$refs['calendarRef'].initCalendar();
    },
    // 新增任务
    handleAddTask() {
      this.list.push({
        // 日期
        scheduleDate: this.todoDate,
        // 内容
        scheduleName: '',
        // 完成状态
        status: 0,
      });
      this.$nextTick(() => {
        this.$refs[`input${this.list.length - 1}`][0].focus();
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.list {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  box-shadow: 0 0 10px 2px rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  position: relative;
  // padding-top: 40px;
  // box-sizing: border-box;

  .list-title {
    width: 100%;
    height: 40px;
    line-height: 40px;
    background-color: #409eff;
    color: #fff;
    font-size: 16px;
    padding-left: 15px;
    box-sizing: border-box;
    position: sticky;
    top: 0;
    left: 0;
    z-index: 5;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-right: 15px;
    box-sizing: border-box;
  }
}

 ::v-deep .el-textarea__inner {
  box-shadow: none !important;
  resize: none !important;
}
.todo-item {
  width: 100%;
  padding: 5px 0;
  box-sizing: border-box;
  border-bottom: 1px solid #ebeef5;
  position: relative;

  .complete {
    width: 100%;
    padding: 5px 15px;
    box-sizing: border-box;
    color: #606266;
    text-decoration: line-through;
    font-size: 14px;
  }

  .control {
    height: 100%;

    position: absolute;
    right: 0;
    top: 0;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    visibility: hidden;

    padding-right: 10px;
    box-sizing: border-box;

    .control-item {
      width: 20px;
      height: 20px;
      border-radius: 100%;
      display: flex;
      justify-content: center;
      align-items: center;

      color: #fff;

      cursor: pointer;

      &.enter {
        background-color: var(--el-color-primary);
        margin-right: 10px;
      }
      &.close {
        background-color: var(--el-color-danger);
      }
    }
  }

  &:hover {
    .control {
      visibility: visible;
    }
  }
}
// /deep/ .el-card__body {
//   padding: 5px 10px;
// }
</style>
