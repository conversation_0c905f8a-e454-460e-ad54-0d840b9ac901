<template>
  <avue-crud
    :option="option"
    :data="modelValue"
    v-model:page="page"
    v-model:search="params"
    :table-loading="loading"
    @row-click='rowClick'
    ref="crud"
    @selection-change="selectionChange"
  >
    <template #menu-left>
      <el-button v-if="!isSelectFromSheet" type="primary" @click="editCategory">编辑分类</el-button>
    </template>
  </avue-crud>
  <dialogForm ref="dialogForm"></dialogForm>
  
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  selection: true,
  editBtn: true,
  index:true,
  delBtn: true,
  reserveSelection:true,
  calcHeight: 180,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  border: true,
  column: {
    serialNumber: {
      label: '序号',
      overHidden: true,
    },
    customProductName: {
      label: '产品名称',
      overHidden: true,
    },
    productBrand: {
      label: '品牌',
      overHidden: true,
    },
    customProductSpecification: {
      label: '规格型号',
      overHidden: true,
      span: 24,
    },
    customProductDescription: {
      label: '产品描述',
      overHidden: true,
      span: 24,
      type: 'textarea',
    },
    customUnit: {
      label: '单位',
      overHidden: true,
      label: '单位',
      type: 'select',
      width: 80,
      props: {
        label: 'dictValue',
        value: 'dictValue',
        desc: 'desc',
      },

      dicUrl: '/blade-system/dict/dictionary?code=unit',
    },
    number: {
      label: '数量',
      overHidden: true,
    },
    sealPrice: {
      label: '产品单价',
      overHidden: true,
      width:100,
    },
    laborCost: {
      label: '人工单价',
      overHidden: true,width:100,
    },
    ybhsdj: {
      label: '延保单价',
      overHidden: true,width:100,
    },
    qthsdj: {
      label: '其他单价',
      overHidden: true,width:100,
    },
    costPrice: {
      label: '产品成本单价',
      overHidden: true,
    },
    // totalPrice: {
    //   label: '金额',
    //   overHidden: true,
    //   editDisplay: false,
    // },
    rgcbdj: {
      label: '人工成本单价',
      overHidden: true,
    },
    ybcbdj: {
      label: '延保成本单价',
      overHidden: true,
    },
    qtcbdj: {
      label: '其他成本单价',
      overHidden: true,
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const props = defineProps(['modelValue', 'categoryTreeData','isSelectFromSheet']);
const emit = defineEmits(['update:modelValue', 'confirm']);
let params = ref({});
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);

let router = useRouter();
let selectList = ref([]);
function editCategory() {
  if(selectList.value.length == 0){
    proxy.$message.warning('请选择要编辑的产品');
    return;
  }

  proxy.$refs.dialogForm.show({
    title: '编辑产品',
    submitText: '添加至方案',
    option: {
      column: [
        {
          label: '分类',
          prop: 'classifyName',
          span: 24,
          type: 'tree',
          parent: false,
          dicData: props.categoryTreeData,
          props: {
            label: 'label',
            value: 'value',
            children: 'children',
          },
        },
      ],
    },
    callback(res) {
      const ids = selectList.value.map(item => item.id);
      const data = props.modelValue.map(item => {
        return {
          ...item,
          classifyName: ids.includes(item.id) ? res.data.classifyName : item.classifyName,
          source:2
        };
      });
      console.log(data,selectList.value);
      emit('update:modelValue', data);
      emit('confirm', data.filter(item => ids.includes(item.id)));
      res.close();
    },
  });
}
function selectionChange(list) {
  selectList.value = list;
}
function clearSelect() {
  console.log(22);
  proxy.$refs.crud.toggleSelection()
}
function rowClick(row) {
  proxy.$refs.crud.toggleSelection([row])
}
function searchChange(params, done) {
  onLoad();
  done();
}
defineExpose({
  selectList,
  clearSelect,
})
</script>

<style lang="scss" scoped></style>
