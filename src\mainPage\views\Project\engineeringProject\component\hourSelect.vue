<template>
  <el-dialog title="工时选择" v-model="dialog" width="60%">
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
      @selectionChange="handleChange"
    >
    </avue-crud>
    <template #footer>
      <el-button @click="dialog = false">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确认</el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const props = defineProps(['projectId', 'humanId']);
const emits = defineEmits(['success'])
let option = ref({
  // height: 'auto',
  align: 'center',
  addBtn: false,
  addRowBtn: false,
  editBtn: true,
  delBtn: true,
  menu:false,
  calcHeight: 30,
  searchMenuSpan: 4,
  cellBtn: true,
  searchSpan: 4,
  labelWidth: 120,
  menuWidth: 270,
  selection:true,
  // showSummary: true,
  selectable: row => {
    return row.isCheck == 0;
  },
  border: true,
  column: {
    index: {
      type: 'input',
      width: 80,
      label: '序号',
    },
    userName: {
      label: '工人/团队姓名',
      hide: true,
      width: 120,
      overHidden: true,
      click: () => {
        proxy.$refs.humanSelectRef.open();
      },
    },

    // customSalary: {
    //   label: '自定义工价',
    //   width: 120,
    //   // hide: true,
    //   cell: true,
    //   //   addDisplay: false,
    // },
    hours: {
      label: '工时（人·天）',
      width: 120,
      //   addDisplay: false,
      cell: true,
      type: 'number',
    },
    customSalary: {
      label: '工价（人·天）',
      type: 'select',
      width: 200,
      cell: true,
      dicData: [],
      allowCreate: true,
      filterable: true,
      // hide: true,

      props: {
        value: 'salary',
        label: 'salary',
      },
    },
    type: {
      label: '工人类型',
      width: 120,
      type: 'radio',
      dicData: [
        {
          value: 0,
          label: '外包',
        },
        {
          value: 1,
          label: '点工',
        },
      ],
      //   addDisplay: false,
      hide: true,
    },
    startTime: {
      label: '工作开始时间',

      type: 'date',
      width: 185,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    endTime: {
      label: '工作结束时间',
      width: 185,
      type: 'date',
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      cell: true,
    },
    planName: {
      label: '关联计划',
      width: 150,
      type: 'input',
      clearable: false,
      overHidden: true,
      click: () => {
        planDialogVisible.value = true;
      },
      span: 24,
      cell: true,
    },
    workContent: {
      label: '工作内容',
      overHidden: true,
      type: 'textarea',
      span: 24,
      cell: true,
      row: 2,
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let dialog = ref(false);
const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/projectHours/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
let selectList = ref([])
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        projectId: props.projectId,
        humanId: props.humanId,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
function handleChange(list) {
    selectList.value = list
}

function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
function open(params) {
    dialog.value = true
}

function handleConfirm() {
    emits('success',selectList.value)
    dialog.value = false
}
defineExpose({
    open
})
</script>

<style lang="scss" scoped></style>
