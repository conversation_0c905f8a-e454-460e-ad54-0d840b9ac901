<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @row-del="rowDel"
      @search-reset="onLoad"
      @search-change="searchChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @keyup.enter="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="toDetail(row)">详情</el-button>
        <!-- <el-button text type="primary" icon="el-icon-check" @click="confirm(row)">确认</el-button> -->
        <el-button type="primary" text icon="view" @click="toDetail(row)">查看询价单</el-button>
      </template>
      <template #orderNo="{ row }">
        <el-link type="primary" @click="toDetail(row)">{{ row.orderNo }}</el-link>
      </template>
      <template #auditStatus="{ row }">
        <div v-if="row.auditStatus == 1 || row.auditStatus == 2">
          <el-tag effect='plain' v-if="row.auditStatus == 1" size="small" type="success">审核成功</el-tag>
          <el-tooltip
            class="box-item"
            effect="dark"
            :content="row.auditReason"
            placement="top-start"
            v-if="row.auditStatus == 2"
          >
            <el-tag effect='plain' size="small" type="danger">审核失败</el-tag>
          </el-tooltip>
        </div>
        <div v-else>
            <el-tag effect='plain' v-if="row.auditType == 1" size="small" type="info">待采购主管审核</el-tag>
        
        <el-tag effect='plain' v-if="row.auditType == 2" size="small" type="info">待总经理审核</el-tag>
        </div>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted,onActivated } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
import { dateFormat } from '@/utils/date.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: false,
  editBtn: false,
  delBtn: false,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 290,
  border: true,
  column: [
    {
      label: '订单编号',
      prop: 'orderNo',
      width: 200,
      overHidden: true,
      search: true,
    },
    {
      label: '报价名称',
      prop: 'offerName',
      search: true,
    },

    {
      label: '采购数量',
      prop: 'number',
    },

    {
      label: '下单时间',
      prop: 'orderDate',
      type: 'date',
      component: 'wf-daterange-search',
        search:true,
      format: 'YYYY-MM-DD HH:mm',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      searchSpan: 5,
      search: true,
    },
    {
      label: '采购人',
      prop: 'purchaseName',
      component: 'wf-user-select',
      searchSpan: 3,
      width: 100,
      // search: true,
    },
    {
      label: '审核状态',
      prop: 'auditStatus',
      type: 'select',
      width: 120,
      dicData: [
        {
          value: 0,
          label: '待审核',
        },
        {
          value: 1,
          label: '审核成功',
        },
        {
          value: 1,
          label: '审核失败',
        },
      ],
      slot: true,
      search: true,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const tableUrl = '/api/vt-admin/purchaseOrder/getAuditPage';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
onMounted(() => {
  onLoad();
});
onActivated(() => {
  onLoad();
});
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        orderStartDate: params.value.orderDate && params.value.orderDate[0],
        orderEndDate: params.value.orderDate && params.value.orderDate[1],
        orderDate: null,
        createTime:null,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function searchChange(params, done) {
  onLoad();
  done();
}
function toDetail(row, activeName = null) {
  router.push({
    path: '/SRM/procure/compoents/inquirySheet',
    query: {
      id: row.id,
      type:1
    },
  });
}
function addContract(row) {
  proxy.$refs.dialogForm.show({
    title: row.name,
    tip: '确定前请核对产品供应商信息,确认后会自动生成与供应商的合同',
    option: {
      column: [
        {
          label: '确认时间',
          span: 12,
          prop: 'confirmDate',
          type: 'date',
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          value: dateFormat(new Date(), 'yyyy-MM-dd'),
          disabled: true,
        },
        {
          label: '确认人',
          span: 12,
          component: 'wf-user-select',
          value: proxy.$store.getters.userInfo.user_id,
          prop: 'createUser',
          disabled: true,
        },
        {
          label: '附件',
          prop: 'files',
          type: 'upload',
          dataType: 'object',
          cell: true,
          span: 24,
          loadText: '附件上传中，请稍等',
          span: 12,
          width: 150,
          slot: true,
          value: [],
          // align: 'center',
          propsHttp: {
            res: 'data',
            url: 'id',
            name: 'originalName',
            // home: 'https://www.w3school.com.cn',
          },
          action: '/blade-resource/attach/upload',
        },
        {
          label: '备注',
          span: 24,
          prop: 'remark',
          type: 'textarea',
        },
      ],
    },
    callback(res) {
      console.log(res.data);
      const data = {
        ...res.data,
        files: res.data.files.map(e => e.value).join(','),
        id: row.id,
      };
      axios.post('/api/vt-admin/vt-admin/purchaseContract/generateContract', data).then(e => {
        proxy.$message.success('操作成功');
        res.close();
        onLoad();
      });
    },
  });
  // proxy
  //   .$confirm('确认一键生成所有合同', '提示', {
  //     type: 'warning',
  //   })
  //   .then(res => {
  //     axios
  //       .post('/api/vt-admin/vt-admin/purchaseContract/generateContract?id=' + row.id)
  //       .then(res => {
  //         proxy.$message.success('生成合同成功');
  //         onLoad();
  //       });
  //   });
}
function confirm(row) {
  proxy.$refs.dialogForm.show({
    title: '审核',
    option: {
      column: [
        {
          label: '审核结果',
          type:'radio',
          value:1,
          dicData: [
            {
              value: 1,
              label: '通过',
            },
            {
              value: 2,
              label: '不通过',
            },
          ],
          prop: 'auditStatus',
          control:(val) => {
            return {
              auditReason:{
                display:val == 2
              }
            }
          }
        },
        {
          label: '审核原因',
          prop: 'auditReason',
          type:'textarea',
          span:24
        }
      ],
    },
    callback(res) {
      axios
        .post('/api/vt-admin/purchaseOrder/audit',{
          id:row.id,
          ...res.data
        })
        .then(e => {
          proxy.$message.success('操作成功');
          proxy.$store.dispatch('getMessageList');
          res.close();
          onLoad();
        });
    },
  });
}
</script>

<style lang="scss" scoped></style>
