<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #isOpen="{ row }">
        <el-tag :type="row.isOpen == 1 ? 'success' : 'danger'" effect="plain">{{
          row.$isOpen
        }}</el-tag>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { followType } from '@/const/const.js';
let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: true,
  delBtn: true,
  calcHeight: 30,
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  addBtn: false,
  border: true,
  column: [
    {
      label: '风控类型',
      prop: 'paramType',
      overHidden: true,
      search: true,
      type: 'select',
      dicData: [
        {
          value: 0,
          label: '客户应收款额度预警',
        },
        {
          value: 1,
          label: '业务员应收款额度预警',
        },
      ],
      control: val => {
        return {
          customerId: {
            display: val == 0,
          },
          userId: {
            display: val == 1,
          },
        };
      },
    },
    {
      label: '客户名称',
      prop: 'customerId',
      width: 250,
      component: 'wf-customer-select',
      params: {
        Url: '/api/vt-admin/customer/page?type=5',
      },
      formatter: val => {
        return val.customerName;
      },
      search: true,
      overHidden: true,
    },
    // {
    //   label: '业务员',
    //   prop: 'userId',
    //   width: 250,
    //   component: 'wf-user-select',
    //   params: {
    //     Url: '/api/blade-system/search/user?roleKeys=sales_leader,sales_person,manager',
    //   },
    //   overHidden: true,
    //   formatter: val => {
    //     return val.userName;
    //   },
    // },
    // {
    //   label: '业务员',
    //   prop: 'userName',
    //   display: false,
    //   hide: true,
    //   search: true,
    //   component: 'wf-user-drop',
    // },
    {
      label: '风控金额',
      tip: '指客户未回款金额或者业务员未收款金额',
      prop: 'accountReceivable',
      type: 'number',
    },
    {
      label: '已使用额度',
      prop: 'hasUsePrice',
      type: 'number',
      display: false,
    },
    {
      label: '剩余额度',
      prop: 'surplusPrice',
      type: 'number',
      display: false,
    },
    {
      label: '是否生效',
      prop: 'isOpen',
      type: 'switch',
      value: 1,
      dicData: [
        {
          value: 0,
          label: '否',
        },
        {
          value: 1,
          label: '是',
        },
      ],
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/alarmParam/save';
const delUrl = '/api/vt-admin/alarmParam/remove?ids=';
const updateUrl = '/api/vt-admin/alarmParam/edit';
const tableUrl = '/api/vt-admin/alarmParam/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
        selectType: 0,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
</script>

<style lang="scss" scoped></style>
