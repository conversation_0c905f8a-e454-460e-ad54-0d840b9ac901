<template>
  <basic-container shadow="never" style="height: 100%">
    <Title
      >产品详情
      <template #foot>
        <el-button
          @click="
            $router.$avueRouter.closeTag();
            $router.back();
          "
          >关闭</el-button
        ></template
      ></Title
    >
    <div class="header">
      <div>
        <h3 style="margin-bottom: 10px" class="title">{{ form.name }}</h3>
        <div class="right"></div>
        <el-form inline label-position="top">
          <el-form-item label="产品名称:">
            <el-tag effect="plain" size="large">{{ form.productName || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="品牌:">
            <el-tag effect="plain" size="large">{{ form.productBrand || '---' }}</el-tag>
          </el-form-item>
          <el-form-item label="规格型号:">
            <el-tag effect="plain" size="large">{{ form.productSpecification || '---' }}</el-tag>
          </el-form-item>
          <!-- <el-form-item label="成本价">
            <el-tag effect='plain' size="large">{{ form.costPrice }}</el-tag>
          </el-form-item> -->
          <!-- <el-form-item label="已付款金额:">
              <el-tag effect='plain' size="large">{{ form.contractCode }}</el-tag>
            </el-form-item> -->
        </el-form>
      </div>
      <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
        <el-button
          type="primary"
          icon="Edit"
          v-if="!isEdit && form.businessPerson == $store.getters.userInfo.user_id"
          @click="isEdit = true"
          >编辑</el-button
        >
        <el-button type="primary" icon="close" v-else-if="isEdit" @click="isEdit = false"
          >取消</el-button
        >
      </div>
    </div>
    <!-- <div style="display: flex">
              <div class="left_content">
                <div class="main_box">
                  <div
                    class="item"
                    v-for="(item, index) in tabArr"
                    :class="{ active: currentIndex == index }"
                    @click="handleClick(index)"
                  >
                    <div class="arrow"></div>
                    {{ item }}
                  </div>
                </div>
              </div>
              <div style="width: calc(100% - 100px)">
                <component
                  :is="currentCompoent"
                  :form="form"
                  :isEdit="isEdit"
                  @getDetail="getDetail"
                ></component>
              </div>
            </div> -->
    <el-tabs v-model="activeName" type="card" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane label="基本信息" name="baseInfo">
        <div>
          <productBaseInfo
            :form="form"
            v-loading="loading"
            @getDetail="getDetail"
          ></productBaseInfo>
        </div>
      </el-tab-pane>
      <el-tab-pane :label="item.label" :name="item.name" v-for="item in tabArray" :key="item.name">
      </el-tab-pane>
    </el-tabs>
    <component
      v-if="conpletArr.includes(activeName) && activeName != 'baseInfo'"
      :is="tabArray.find(item => item.name == activeName).component"
      :productId="form.id"
    ></component>
    <el-empty v-if="!conpletArr.includes(activeName) && activeName != 'baseInfo'"></el-empty>
    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, shallowRef, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { computed } from 'vue';
import productBaseInfo from '../detail/productBaseInfo.vue';
import historyPrice from './historyPrice.vue';
// 供应商信息
import supplierInfo from '../detail/supplier.vue';
// // 合同信息
// import supplierContract from '@/views/SRM/procure/contract.vue';
import axios from 'axios';
import productHistoryList from '../detail/productHistoryList.vue';
let route = useRoute();
let router = useRouter();
let form = ref({});
let isEdit = ref(false);
let loading = ref(false);
let { proxy } = getCurrentInstance();
const props = defineProps(['id', 'activeName']);
onMounted(() => {
  getDetail();

  setTimeout(() => {
    proxy.$nextTick(() => {
      if (props.activeName) {
        activeName.value = props.activeName;
      }
    });
  }, 200);
});
watchEffect(() => {
  if (props.id) {
    getDetail();
  }
});
const conpletArr = ['supplierInfo', 'historyPrice', 'productHistoryList'];
const tabArray = [
  {
    label: '供应商信息',
    name: 'supplierInfo',
    component: supplierInfo,
  },
  {
    label: '产品历史价格',
    name: 'productHistoryList',
    component: productHistoryList,
  },
  {
    label: '产品价格走势',
    name: 'historyPrice',
    component: historyPrice,
  },
];

function getDetail() {
  isEdit.value = false;
  loading.value = true;
  axios
    .get('/api/vt-admin/product/detail', {
      params: {
        id: props.id,
      },
    })
    .then(res => {
      loading.value = false;
      // form.value = formatData(res.data.data)

      const propertyList = res.data.data.productPropertyVoS.map(item => {
        let radioSelect = '';
        let selectList = [];
        let value;
        if (item.type == 0) {
          if (item.entityList.filter(item => item.isCheck == 1).length > 0) {
            const { id, value } = item.entityList.filter(item => item.isCheck == 1)[0];
            radioSelect = `${id}-${value}`;
          }
        } else if (item.type == 1) {
          selectList = item.entityList
            .filter(item => item.isCheck == 1)
            .map(item => {
              const { id, value } = item;
              return `${id}-${value}`;
            });
        } else {
          value = item.value;
        }
        return {
          ...item,
          valuesEntityList: item.entityList,
          selectList,
          radioSelect,
          value,
        };
      });
      form.value = res.data.data;
      form.value.propertyList = propertyList;
      // 获取供应商信息
      //   getSupplierInfo(form.value.supplierId);
    });
}
let moreInfoDetail = ref(false);

let currentIndex = ref(0);
// let currentCompoent = shallowRef(BaseInfo);
function handleClick(value) {
  currentIndex.value = value;
  // currentCompoent.value = compoentArr[value];
}

function getSupplierInfo(id) {
  axios.get(`/api/vt-admin/supplier/detail?id=${id}`).then(res => {
    form.value = {
      ...form.value,
      ...res.data.data,
    };
  });
}
const activeName = ref('baseInfo' || props.activeName);
</script>

<style lang="scss" scoped>
@use 'element-plus/theme-chalk/src/common/var.scss' as *;
.header {
  display: flex;
  margin-left: 20px;
  justify-content: space-between;
  align-items: flex-start;
}

.title {
  margin: 0;
  margin-right: 20px;
}
.left_content {
  .main_box {
    margin-right: 10px;
    .item {
      width: 100px;
      cursor: pointer;
      background-color: #fff;
      box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
      text-align: center;
      line-height: 50px;
      font-weight: bolder;
      height: 50px;
      font-size: 12px;
      margin-bottom: 10px;
      transition: all 0.2s;
      position: relative;
    }
    .item.active {
      box-shadow: inset 5px 5px 10px rgba(0, 0, 0, 0.1);
      color: $color-primary;
      .arrow {
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        right: -12px;
        border: 6px solid transparent;
        border-left-color: $color-primary;
        height: 0;
        width: 0;
      }
    }
  }
}
</style>
