<template>
  <div>
    <el-row :gutter="20" style="height: 100%">
      <el-col :span="24" style="height: 100%">
        <el-card body-style="padding:5px" style="height: 100%" shadow="never">
          <avue-crud
            :option="option"
            :data="tableData"
            v-model:page="page"
            v-model:search="params"
            @on-load="onLoad"
            :table-loading="loading"
            ref="crud"
            @keyup.enter="onLoad"
            @row-del="rowDel"
            @search-reset="reset"
            @search-change="searchChange"
            @refresh-change="onLoad"
            @current-change="onLoad"
            @size-change="onLoad"
            :cell-style="cellStyle"
            v-model="form"
          >
            <template #januaryPrice="{ row }">
              <el-link @click="handlePriceClick('01', row)">{{ row.januaryPrice }} </el-link>
            </template>
            <template #februaryPrice="{ row }">
              <el-link @click="handlePriceClick('02', row)">{{ row.februaryPrice }} </el-link>
            </template>

            <template #marchPrice="{ row }">
              <el-link @click="handlePriceClick('03', row)">{{ row.marchPrice }} </el-link>
            </template>
            <template #aprilPrice="{ row }">
              <el-link @click="handlePriceClick('04', row)">{{ row.aprilPrice }} </el-link>
            </template>
            <template #mayPrice="{ row }">
              <el-link @click="handlePriceClick('05', row)">{{ row.mayPrice }} </el-link>
            </template>
            <template #junePrice="{ row }">
              <el-link @click="handlePriceClick('06', row)">{{ row.junePrice }} </el-link>
            </template>
            <template #julyPrice="{ row }">
              <el-link @click="handlePriceClick('07', row)">{{ row.julyPrice }} </el-link>
            </template>
            <template #augustPrice="{ row }">
              <el-link @click="handlePriceClick('08', row)">{{ row.augustPrice }} </el-link>
            </template>
            <template #septemberPrice="{ row }">
              <el-link @click="handlePriceClick('09', row)">{{ row.septemberPrice }} </el-link>
            </template>

            <template #octoberPrice="{ row }">
              <el-link @click="handlePriceClick('10', row)">{{ row.octoberPrice }} </el-link>
            </template>
            <template #novemberPrice="{ row }">
              <el-link @click="handlePriceClick('11', row)">{{ row.novemberPrice }} </el-link>
            </template>
            <template #decemberPrice="{ row }">
              <el-link @click="handlePriceClick('12', row)">{{ row.decemberPrice }} </el-link>
            </template>
          </avue-crud>
        </el-card>
      </el-col>
    </el-row>
    <el-row :gutter="10" style="min-width: 1300px; overflow-y: scroll">
      <!-- <el-col :span="8">
        <el-card style="height: 100%" body-style="padding:5px" shadow="never">
          <template #header>收入总计</template>
          <div>
            <div ref="chartsRefPie" style="width: 100%; height: 300px"></div>
          </div>
          <div></div>
        </el-card>
      </el-col> -->
      <el-col :span="24">
        <template #header>
          收入支出
        </template>
        <el-card style="height: 100%" body-style="padding:5px" shadow="never">
          <div>
            <div ref="chartsRefBar" style="width: 100%; height: 300px"></div>
          </div>
          <div></div>
        </el-card>
      </el-col>
      <el-col :span="24">
        <template #header>
          利率
        </template>
        <el-card style="height: 100%" body-style="padding:5px" shadow="never">
          <div>
            <div ref="chartsRefLine" style="width: 100%; height: 300px"></div>
          </div>
          <div></div>
        </el-card>
      </el-col>
    </el-row>
    <dialogForm ref="dialogForm"></dialogForm>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';
let option = ref({
  align: 'center',
  addBtn: false,
  editBtn: true,
  delBtn: true,
  size: 'small',
  searchMenuSpan: 4,
  searchSpan: 4,
  menuWidth: 270,
  menu: false,
  rowKey: 'incomeTypeStr',
  rowParentKey: 'parentId',
 

  header: false,

  border: true,
  searchSpan: 6,
  searchMenuSpan: 4,
  column: {
    // incomeType: {
    //   label: '类目',
    //   type: 'select',
    //   dicData: [
    //     {
    //       value: 0,
    //       label: '营业外收入',
    //     },
    //     {
    //       value: 1,
    //       label: '主营收入',
    //     },
    //   ],
    // },
    businessUserName: {
      label: '业务员',
      hide: true,
      //   component: 'wf-user-drop',

      //   search: true,
      searchSpan: 4,
    },

    incomeTypeStr: {
      label: '类目',

      //   component: 'wf-user-drop',

      //   search: true,
      searchSpan: 4,
    },
    januaryPrice: {
      label: '一月',
      
    },
    februaryPrice: {
      label: '二月',
    },
    marchPrice: {
      label: '三月',
    },
    aprilPrice: {
      label: '四月',
    },
    mayPrice: {
      label: '五月',
    },
    junePrice: {
      label: '六月',
    },
    julyPrice: {
      label: '七月',
    },
    augustPrice: {
      label: '八月',
    },
    septemberPrice: {
      label: '九月',
    },
    octoberPrice: {
      label: '十月',
    },
    novemberPrice: {
      label: '十一月',
    },
    decemberPrice: {
      label: '十二月',
    },
    totalPrice: {
      label: '本年累计',
    },
    // lastYearTotalPrice: {
    //   label: '上年累计',
    // },
    // growthPrice: {
    //   label: '增长额',
    //   html: true,
    //   formatter: row => {
    //     return `<div style='color:${row.growthPrice * 1 <= 0 ? 'green' : 'red'}'>${
    //       row.growthPrice ? row.growthPrice : '0'
    //     }</div>`;
    //   },
    // },
    // growthRate: {
    //   label: '增长率',
    //   html: true,
    //   formatter: row => {
    //     return `<div style='color:${row.growthRate * 1 <= 0 ? 'green' : 'red'}'>${
    //       row.growthRate ? row.growthRate : '0'
    //     }%</div>`;
    //   },
    // },
    // businessType: {
    //   label: '业务板块',
    //   type: 'select',
    //   dicUrl: '/blade-system/dict/dictionary?code=businessOpportunityType',
    //   props: {
    //     label: 'dictValue',
    //     value: 'id',
    //   },
    //   label: '业务类型',
    //   // multiple: true,
    //   span: 12,
    //   width: 250,
    //   searchSpan: 4,
    //   overHidden: true,
    //   parent: true,
    //   hide: true,
    //   search: true,
    // },
    // name: {
    //   label: '经营体名字',
    //   hide: true,
    //   search: true,
    //   searchLabelWidth: 120,
    //   searchSpan: 4,
    // },
    //   searchType: {
    //     label: '查询类型',
    //     hide: true,
    //     search: true,
    //     type: 'select',
    //     searchSpan: 4,
    //     group: true,
    //     size:'small',
    //     dicData: [
    //       {

    //         label: '全部收入',
    //         groups:[{
    //           value: '',
    //           label:'全部'
    //         }]
    //       },
    //       {
    //         label: '主营业务收入',
    //         groups: [
    //           {
    //             value: 0,
    //             label: '业务员',
    //           },
    //           {
    //             value: 1,
    //             label: '业务板块',
    //           },
    //           {
    //             value: 2,
    //             label: '经营体',
    //           },
    //         ],
    //       },
    //       {

    //         label: '营业外收入',
    //         groups:[{
    //         value: 3,
    //         label: '营业外',
    //       },]
    //       },
    //     ],
    //   },
    year: {
      label: '搜索日期',
      hide: true,
      search: true,
      type: 'year',
      searchSpan: 4,
      format: 'YYYY',
      valueFormat: 'YYYY',
    },
  },
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '';
const delUrl = '';
const updateUrl = '';
const tableUrl = '/api/vt-admin/statistics/annualIncomeExpense';
const urlObj = {
  ['']: '/api/vt-admin/statistics/annualSalesStatistics',
  0: '/api/vt-admin/statistics/annualBusinessPersonList',
  3: '/api/vt-admin/statistics/annualSecondaryIncomeList',
};
let params = ref({
  searchType: '',
  year: '' + new Date().getFullYear(),
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
const emits = defineEmits(['priceClick', 'updateDate']);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;

      tableData.value = res.data.data.map(item => {
        return {
          ...item,
          type: item.type == 0 ? '营业外收入' : '主营业务收入',
          januaryPrice:(item.januaryPrice * 1).toFixed(2),
          februaryPrice:(item.februaryPrice * 1).toFixed(2),
          marchPrice:(item.marchPrice * 1).toFixed(2),
          aprilPrice:(item.aprilPrice * 1).toFixed(2),
          mayPrice:(item.mayPrice * 1).toFixed(2),
          junePrice:(item.junePrice * 1).toFixed(2),
          julyPrice:(item.julyPrice * 1).toFixed(2),
          augustPrice:(item.augustPrice * 1).toFixed(2),
          septemberPrice:(item.septemberPrice * 1).toFixed(2),
          octoberPrice:(item.octoberPrice * 1).toFixed(2),
          novemberPrice:(item.novemberPrice * 1).toFixed(2),
          decemberPrice:(item.decemberPrice * 1).toFixed(2),
          totalPrice: (item.totalPrice * 1).toFixed(2),
          
        };
      });
      const fn = (key, label) => {
        let value = '';
        const income = tableData.value[0][label] * 1 + tableData.value[1][label] * 1;
        const cost = tableData.value[2][label] * 1 + tableData.value[3][label] * 1;
        const profit = income - cost;
        switch (key) {
          case '利润':
            value = profit;
            break;
          case '毛利率':
            value =
              ((tableData.value[1][label] * 1 - tableData.value[2][label] * 1) /
              (tableData.value[1][label] * 1)) * 100;
            break;
          case '费用率':
            value = ((tableData.value[3][label] * 1) / (tableData.value[1][label] * 1)) * 100;
            break;
          case '净利率':
            value = (profit / (tableData.value[1][label] * 1)) * 100;

            break;
          default:
            break;
        }
        return isNaN(value) ? '0' : (value * 1).toFixed(2) + (key == '利润' ? '' : '%');
      };
      const data = ['利润', '毛利率', '费用率', '净利率'].map(item => {
        return {
          incomeTypeStr: item,
          februaryPrice: fn(item, 'februaryPrice'),
          januaryPrice: fn(item, 'januaryPrice'),
          marchPrice: fn(item, 'marchPrice'),
          aprilPrice: fn(item, 'aprilPrice'),
          mayPrice: fn(item, 'mayPrice'),
          junePrice: fn(item, 'junePrice'),
          julyPrice: fn(item, 'julyPrice'),
          augustPrice: fn(item, 'augustPrice'),
          septemberPrice: fn(item, 'septemberPrice'),
          octoberPrice: fn(item, 'octoberPrice'),
          novemberPrice: fn(item, 'novemberPrice'),
          decemberPrice: fn(item, 'decemberPrice'),
          totalPrice: fn(item, 'totalPrice'),
        };
      });
      tableData.value.push(...data);
      //   initChartPie();
      initChartBar();
      initChartLine();
    });
}
let router = useRouter();

function reset() {
  params.value.searchType = '';
  params.value.year = '' + new Date().getFullYear();
  emits('updateDate', `${params.value.year}-01`);
  onLoad();
}
function searchChange(a, done) {
  if (params.value.businessName) {
    params.value.searchType = 0;
  }

  emits('updateDate', `${params.value.year}-01`);
  onLoad();
  done();
}
onMounted(() => {});
let chartsRefPie = ref();
function initChartPie() {
  var chartDom = chartsRefPie.value;
  var myChart = echarts.init(chartDom);
  var option;

  option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],
    tooltip: {
      trigger: 'item',
    },
    grid: {
      height: '70%',
    },
    legend: {
      show: false,
      left: 'center',
      top: 'bottom',
      data: tableData.value.map(item => {
        return params.value.searchType === ''
          ? item.type
          : params.value.searchType === 3
          ? item.incomeTypeStr
          : item.businessUserName;
      }),
    },
    // toolbox: {
    //   show: true,
    //   feature: {
    //     mark: { show: true },
    //     dataView: { show: true, readOnly: false },
    //     restore: { show: true },
    //     saveAsImage: { show: true },
    //   },
    // },
    series: [
      {
        name: '销售额',
        type: 'pie',
        radius: [20, 100],
        itemStyle: {
          borderRadius: 5,
        },
        data: tableData.value.map(item => {
          return {
            value: item.totalPrice,
            name:
              params.value.searchType === ''
                ? item.type
                : params.value.searchType === 3
                ? item.incomeTypeStr
                : item.businessUserName,
          };
        }),
      },
    ],
  };

  option && myChart.setOption(option);
}
let chartsRefBar = ref();
let chartsBar = '';
function initChartBar() {
  if (chartsBar) {
    chartsBar.dispose();
  }
  chartsBar = echarts.init(chartsRefBar.value);
  const data = tableData.value.slice(0, 4).map(item => {
    return {
      incomeTypeStr: item.incomeTypeStr,
      data: [
        item.januaryPrice * 1,
        item.februaryPrice * 1,
        item.marchPrice * 1,
        item.aprilPrice * 1,
        item.mayPrice * 1,
        item.junePrice * 1,
        item.julyPrice * 1,
        item.augustPrice * 1,
        item.septemberPrice * 1,
        item.octoberPrice * 1,
        item.novemberPrice * 1,
        item.decemberPrice * 1,
      ],
    };
  });
  console.log(data);

  let option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],
    grid: {
      bottom: 20,
    },
    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: true,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      type: 'scroll',
      width:'100%',
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: [
          '一月',
          '二月',
          '三月',
          '四月',
          '五月',
          '六月',
          '七月',
          '八月',
          '九月',
          '十月',
          '十一月',
          '十二月',
        ], //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
      //y轴
      {
        name: '金额', //名称
        type: 'value', //连续类型
        alignTicks: true,
        axisLine: {
          //坐标轴样式
          show: true, //不显示
        },
        splitLine: {
          show: false,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
     
    ],
    series: [
      ...data.map((item, index) => {
        return {
          name: item.incomeTypeStr, //名称
          type: 'bar', //类型
          barWidth: 12, //宽度
          data: item.data, //数值
          z: 1,
          barGap: 0,
          stack: index == 0 || index == 1 ? 'income' : 'out',
          barWidth: 30,
          label: {
            show: index == 1 || index == 3,
            position: 'top',
            fontSize: 10,
            formatter: val => {
              if (index == 1) {
                const value =
                  ((data[0].data[val.dataIndex] * 1 + data[1].data[val.dataIndex]) * 1) / 10000;
                return `${value == 0 ? '' : '收入:' + value.toFixed(2) + '万'}`;
              } else if (index == 3) {
                const value =
                  ((data[2].data[val.dataIndex] * 1 + data[3].data[val.dataIndex]) * 1) / 10000;

                return `${value == 0 ? '' : '支出:' + value.toFixed(2) + '万'}`;
              } else {
                return '';
              }
            },
          },
        };
      }),
    
    ],
  };
  chartsBar.setOption(option);
}
let chartsRefLine = ref();
let chartsLine = '';
function initChartLine() {
  if (chartsLine) {
    chartsLine.dispose();
  }
  chartsRefLine = echarts.init(chartsRefLine.value);
 


  let option = {
    color: ['#409EFF', '#c7615d', '#E6A23C', '#F56C6C', '#909399', '#303133'],
    grid: {
      bottom: 20,
    },
    tooltip: {
      //提示框组件
      trigger: 'axis', //触发类型 柱状图
      axisPointer: { type: 'shadow' }, //触发效果 移动上去 背景效果
    },
    legend: {
      show: true,
      right: 'center',
      itemWidth: 20,
      itemHeight: 16,
      // 两个之间的间隙大小
      itemGap: 50,
      gap: 50,
      type: 'scroll',
      width:'100%',
      textStyle: {
        // 图例文字的样式
        color: '#18191B',
        fontSize: 16,
      },
    },
    xAxis: [
      //x轴
      {
        type: 'category', //坐标轴类型 离散
        data: [
          '一月',
          '二月',
          '三月',
          '四月',
          '五月',
          '六月',
          '七月',
          '八月',
          '九月',
          '十月',
          '十一月',
          '十二月',
        ], //数据
        axisTick: false, //是否显示刻度
        axisLine: {
          //坐标轴样式
          show: true,
        },
        axisLabel: {
          fontSize: 14,
          color: '#18191B',
        },
      },
    ],
    yAxis: [
     
      {
        type: 'value',
       
        name: '利率(%)',
      },
    ],
    series: [
      
      {
        name: '毛利率',
        type: 'line',
       
        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        lineStyle:{
            width:1
        },
        showSymbol:false,
        data: [
          (parseFloat(tableData.value[5].januaryPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].februaryPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].marchPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].aprilPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].mayPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].junePrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].julyPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].augustPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].septemberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].octoberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].novemberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[5].decemberPrice) / 100).toFixed(2),
        ],
      },
      {
        name: '费用率',
        type: 'line',
       showSymbol:false,
        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        lineStyle:{
            width:1
        },
        data: [
          (parseFloat(tableData.value[6].januaryPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].februaryPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].marchPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].aprilPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].mayPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].junePrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].julyPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].augustPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].septemberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].octoberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].novemberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[6].decemberPrice) / 100).toFixed(2),
        ],
      },
      {
        name: '净利率',
        type: 'line',
      
        tooltip: {
          valueFormatter: function (value) {
            return value;
          },
        },
        lineStyle:{
            width:1
        },
        showSymbol:false,
        data: [
          (parseFloat(tableData.value[7].januaryPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].februaryPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].marchPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].aprilPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].mayPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].junePrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].julyPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].augustPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].septemberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].octoberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].novemberPrice) / 100).toFixed(2),
          (parseFloat(tableData.value[7].decemberPrice) / 100).toFixed(2),
        ],
      },
    ],
  };
  chartsRefLine.setOption(option);
}
function handlePriceClick(date, row) {
 
 
  emits('priceClick', {
    date: `${params.value.year}-${date}`,
  });
}
function cellStyle({ row, column, rowIndex, columnIndex }) {
  if (column.property == 'totalPrice' || column.property == 'incomeTypeStr') {
    return {
      fontWeight: 'bolder',
      fontSize: '14px',
    };
  }
}
</script>

<style lang="scss" scoped></style>
