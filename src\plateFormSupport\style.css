@import "tailwindcss";

/* 直接测试CSS */
.direct-test {
  background-color: red !important;
  color: white !important;
  padding: 20px !important;
  margin: 10px !important;
  font-size: 18px !important;
}

/* 测试Tailwind是否工作 */
.test-tailwind {
  @apply bg-blue-500 text-white p-4 rounded-lg;
}

/* 自定义Tailwind配置 */
@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
  .text-pretty {
    text-wrap: pretty;
  }
}

/* 自定义CSS变量 */
:root {
  --primary: #3b82f6;
  --primary-rgb: 59, 130, 246;
  --accent: #10b981;
  --accent-rgb: 16, 185, 129;
  --neutral: #f8fafc;
}

/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
}

/* 导航栏样式 */
.navbar {
  backdrop-filter: blur(10px);
}

/* 首页导航栏透明样式 */
.navbar.bg-transparent {
  background: linear-gradient(180deg, rgba(0, 0, 0, 0.3) 0%, rgba(0, 0, 0, 0.1) 50%, transparent 100%);
}

/* 其他页面导航栏样式 */
.navbar.bg-white {
  background-color: rgba(255, 255, 255, 0.9);
}

/* 滚动动画 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 产品卡片悬停效果 */
.product-card {
  transition: all 0.3s ease;
}

.product-card:hover {
  transform: translateY(-5px);
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background-color: #2563eb;
}

/* 响应式隐藏/显示 */
@media (max-width: 768px) {
  .mobile-menu {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .mobile-menu.active {
    transform: translateX(0);
  }
}