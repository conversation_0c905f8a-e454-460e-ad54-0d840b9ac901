<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:search="params"
      :table-loading="loading"
      @search-change="searchChange"
      @search-reset="onLoad"
      @selection-change="selectionChange"
      @current-change="onLoad"
      @refresh-change="onLoad"
      @size-change="onLoad"
      @row-del="rowDel"
      @keyup.enter="onLoad"
      v-model:page="page"
      ref="crud"
      
    >
      <template #menu-left="{}">
        <div style="display: flex">
          <div class="btn_group" style="margin-right: 20px; padding-right: 20px">
           <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增</el-button>
            <!--  <el-button type="" icon="el-icon-download">导入</el-button>
            <el-button type="" icon="el-icon-right" @click="translateOther(0)">转移他人</el-button>
            <el-button type="" icon="el-icon-right" @click="transformCustomer(3)"
              >转移公共池</el-button
            >
            <el-button type="" icon="el-icon-right" @click="transformCustomer(2)"
              >转移失效池</el-button
            > -->
          </div>
          <div class="status_group">
            <statusGroup @click="handleStatusClick"></statusGroup>
          </div>
        </div>
      </template>
      <template #menu="{ row, index }">
        <el-button type="primary" icon="View" @click="toDetail(row)" text>详情</el-button>
        <!-- <el-button type="primary" icon="Service" text>跟进</el-button> -->
        <el-button
          type="primary"
          icon="Delete"
          v-if="$store.getters.permission.customer_del"
          @click="$refs.crud.rowDel(row, index)"
          text
          >删除</el-button
        >
        <!-- <el-dropdown trigger="click" @command="handleCommand($event, row)">
          <el-button type="primary" icon="arrow-down" text>更多</el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item :command="0">转移他人</el-dropdown-item>
              <el-dropdown-item :command="3">转移公共池</el-dropdown-item>
              <el-dropdown-item :command="2">转移失效池</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown> -->
      </template>
      <template #followStatus="{ row, index }">
        <el-tag  effect="dark"  :color="followStatusName(row.followStatus).color">{{
          followStatusName(row.followStatus).name
        }}</el-tag>
      </template>
      <template #customerCode="{ row, index }">
        <el-link @click="toDetail(row)" type="primary">{{ row.customerCode }}</el-link>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <detail_drawer :type="customerType" :id="customerId" ref="detailRef" ></detail_drawer>
  </basic-container>
</template>

<script setup>
import { onMounted, ref, reactive, getCurrentInstance, computed } from 'vue';
import { useRouter } from 'vue-router';
import statusGroup from './compoents/statusGroup.vue';
import column from './common';
import axios from 'axios';
import detail_drawer from './detail/detail_drawer.vue';
import { followData, customerStatus } from '@/const/const';

const router = useRouter();
const columnBf = [...column];
columnBf.push({
  label: '客户状态',
  tyle: 'select',
  dicData: customerStatus,
  prop: 'customerStatus',
  props: {
    value: 'value',
    label: 'label',
  },
});
const option = reactive({
  height: 'auto',
  menu: true,
  border: true,
  addBtn: false,
  delBtn: false,
  editBtn: false,
  selection: true,
  stripe: true,
  calcHeight: 30,
  searchIndex: 4,
  align: 'center',
  menuWidth: 150,
  searchIcon: true,
  searchMenuSpan: 4,
  column: columnBf,
});
const { proxy } = getCurrentInstance();
let page = ref({
  total: 1000,
  currentPage: 1,
  pageSize: 10,
});
onMounted(() => {
  onLoad();
});
let params = ref({ province_city_area: [] });
let tableData = ref([{}]);
function handleAdd() {
  router.push('/CRM/customer/compoents/customerAdd?type=1');
}
function handleStatusClick(value) {
  params.value.followStatus = value;
  onLoad();
}
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  const data = {
    size,
    current,
    ...params.value,
    provinceCode: params.value.province_city_area[0],
    cityCode: params.value.province_city_area[1],
    areaCode: params.value.province_city_area[2],
    type: 7,
  };
  axios.get('/api/vt-admin/customer/page', { params: data }).then(res => {
    tableData.value = res.data.data.records;
    page.value.total = res.data.data.total;
    loading.value = false;
  });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      axios.post('/api/vt-admin/customer/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function searchChange(params, done) {
  onLoad();
  done();
}
let customerId = ref(null)
let customerType = ref(null)
let detailRef = ref(null)
// function toDetail(row) {
//   customerId.value = row.id
//   customerType.value = 5
//   detailRef.value.open()
// }
function toDetail(row) {
  router.push({
    path: '/CRM/customer/detail/detail',
    query: {
      customerId: row.id,
      type: 5,
    },
  });
}
function transformCustomer(transferType, row) {
  let id;
  if (row) {
    id = row.id;
  } else {
    if (selectList.value.length == 0) {
      return proxy.$message.warning('请选择一条数据');
    } else if (selectList.value.length > 1) {
      return proxy.$message.warning('只能选择一一条数据');
    } else {
      id = selectList.value[0].id;
    }
  }

  const data = {
    customerId: id,
    transferType,
  };

  proxy
    .$confirm('确定将该客户转移?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      transformSubmit(data);
      onLoad();
    });
}
function transformSubmit(data) {
  axios.post('/api/vt-admin/customerTransferRecord/save', data).then(res => {
    proxy.$message.success(res.data.msg);
  });
}
let selectList = ref([]);
function selectionChange(list) {
  selectList.value = list;
}

function followStatusName(value) {
  const a = followData.find(item => item.value == value);

  return {
    name: a && a.label,
    color: a && a.color,
  };
}

function handleCommand(value, row) {
  if (value === 0) {
    translateOther(value, row);
  } else {
    transformCustomer(value, row);
  }
}
function translateOther(value, row) {
  let id;
  if (row) {
    id = row.id;
  } else {
    if (selectList.value.length == 0) {
      return proxy.$message.warning('请选择一条数据');
    } else if (selectList.value.length > 1) {
      return proxy.$message.warning('只能选择一一条数据');
    } else {
      id = selectList.value[0].id;
    }
  }
  proxy.$refs.dialogForm.show({
    title: '转给他人',
    option: {
      column: [
        {
          label: '转移人',
          component: 'wf-user-select',
          prop: 'targetPerson',
          span: 24,
        },
      ],
    },
    callback(res) {
      const data = {
        customerId: id,
        ...res.data,
        transferType: value,
      };
      res.close();
      transformSubmit(data);
      onLoad();
    },
  });
}
</script>

<style lang="scss" scoped></style>
