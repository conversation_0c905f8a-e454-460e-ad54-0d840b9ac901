<template>
  <div class="home">
    <!-- Hero Section -->
    <section class="hero bg-gradient-to-r from-blue-600 to-blue-800 text-white py-20">
      <div class="container mx-auto px-4">
        <div class="max-w-3xl mx-auto text-center">
          <h1 class="text-4xl md:text-6xl font-bold mb-6 fade-in">
            智聚联云，引领未来
          </h1>
          <p class="text-xl md:text-2xl mb-8 fade-in">
            专注于为客户提供高质量的技术产品和解决方案
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center fade-in">
            <router-link 
              to="/products" 
              class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-200"
            >
              浏览产品
            </router-link>
            <router-link 
              to="/about" 
              class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors duration-200"
            >
              了解更多
            </router-link>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">我们的优势</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            凭借多年的行业经验和技术积累，我们为客户提供最优质的产品和服务
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="feature-card bg-white p-8 rounded-lg shadow-lg text-center fade-in">
            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">技术创新</h3>
            <p class="text-gray-600">
              持续投入研发，采用最新的技术栈，为客户提供前沿的解决方案
            </p>
          </div>
          
          <div class="feature-card bg-white p-8 rounded-lg shadow-lg text-center fade-in">
            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">品质保证</h3>
            <p class="text-gray-600">
              严格的质量控制体系，确保每一件产品都符合最高标准
            </p>
          </div>
          
          <div class="feature-card bg-white p-8 rounded-lg shadow-lg text-center fade-in">
            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold mb-3">专业服务</h3>
            <p class="text-gray-600">
              经验丰富的技术团队，提供全方位的技术支持和售后服务
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- Stats Section -->
    <section class="stats py-16 bg-blue-600 text-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div class="stat-item">
            <div class="text-4xl font-bold mb-2">1000+</div>
            <div class="text-lg">满意客户</div>
          </div>
          <div class="stat-item">
            <div class="text-4xl font-bold mb-2">500+</div>
            <div class="text-lg">成功项目</div>
          </div>
          <div class="stat-item">
            <div class="text-4xl font-bold mb-2">50+</div>
            <div class="text-lg">合作伙伴</div>
          </div>
          <div class="stat-item">
            <div class="text-4xl font-bold mb-2">10+</div>
            <div class="text-lg">年行业经验</div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="cta py-16 bg-gray-100">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
          准备开始您的项目？
        </h2>
        <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
          立即联系我们，获取专业的技术咨询和解决方案
        </p>
        <router-link 
          to="/contact" 
          class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 inline-block"
        >
          联系我们
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'

onMounted(() => {
  // 添加滚动动画
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible')
      }
    })
  }, observerOptions)

  document.querySelectorAll('.fade-in').forEach(el => {
    observer.observe(el)
  })
})
</script>

<style scoped>
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.feature-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}
</style>