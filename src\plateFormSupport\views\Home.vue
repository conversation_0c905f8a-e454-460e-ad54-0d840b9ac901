<template>
  <div class="home">
    <!-- 轮播图区域 -->
    <section class="hero-carousel">
      <el-carousel
        v-if="!loading && carouselItems.length > 0"
        height="50vh"
        :interval="4000"
        arrow="hover"
        indicator-position="outside"
        class="carousel-container"
      >
        <el-carousel-item v-for="(item, index) in carouselItems" :key="index">
          <div
            class="carousel-slide"
            :style="{ backgroundImage: `url(${item.pictureUrl})` }"
          >
            <div class="carousel-overlay">
              <div class="container mx-auto px-4 h-full flex items-center">
                <div class="max-w-2xl text-white">
                  <h1 class="text-4xl md:text-5xl font-bold mb-4 slide-in-left">
                    {{ item.title }}
                  </h1>
                  <p class="text-xl md:text-2xl mb-6 slide-in-left delay-200">
                    {{ item.introduction }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>

      <!-- 加载状态 -->
      <div v-if="loading" class="carousel-loading">
        <div class="flex items-center justify-center h-full">
          <el-icon class="is-loading text-white text-4xl">
            <Loading />
          </el-icon>
        </div>
      </div>
    </section>

    <!-- 品牌库宣传区域 -->
    <section class="brand-promotion py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row items-center gap-12">
          <!-- 左侧文字简介 -->
          <div class="lg:w-1/2 space-y-6">
            <div class="fade-in">
              <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {{ brandData.title }}
              </h2>
              <p class="text-lg text-gray-600 mb-6">
                {{ brandData.introduction }}
              </p>
              <div class="pt-6">
                <router-link
                  to="/brand"
                  class="inline-flex items-center gap-2 bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200"
                >
                  <span>探索品牌库</span>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </router-link>
              </div>
            </div>
          </div>

          <!-- 右侧宣传图 -->
          <div class="lg:w-1/2 fade-in">
            <div class="relative rounded-lg overflow-hidden shadow-xl">
              <img
                :src="brandData.pictureUrl"
                alt="品牌库宣传图"
                class="w-full h-80 object-cover hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 方案库宣传区域 -->
    <section class="solution-promotion py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="flex flex-col lg:flex-row-reverse items-center gap-12">
          <!-- 右侧文字简介 -->
          <div class="lg:w-1/2 space-y-6">
            <div class="fade-in">
              <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {{ solutionData.title }}
              </h2>
              <p class="text-lg text-gray-600 mb-6">
                {{ solutionData.introduction }}
              </p>
              <div class="pt-6">
                <router-link
                  to="/business-solutions"
                  class="inline-flex items-center gap-2 bg-orange-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-orange-700 transition-colors duration-200"
                >
                  <span>查看解决方案</span>
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                </router-link>
              </div>
            </div>
          </div>

          <!-- 左侧宣传图 -->
          <div class="lg:w-1/2 fade-in">
            <div class="relative rounded-lg overflow-hidden shadow-xl">
              <img
                :src="solutionData.pictureUrl"
                alt="方案库宣传图"
                class="w-full h-80 object-cover hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 数据统计区域 -->
    <!-- <section class="stats py-16 bg-blue-600 text-white">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          <div class="stat-item fade-in">
            <div class="text-4xl font-bold mb-2">1000+</div>
            <div class="text-lg">满意客户</div>
          </div>
          <div class="stat-item fade-in">
            <div class="text-4xl font-bold mb-2">500+</div>
            <div class="text-lg">成功项目</div>
          </div>
          <div class="stat-item fade-in">
            <div class="text-4xl font-bold mb-2">50+</div>
            <div class="text-lg">合作伙伴</div>
          </div>
          <div class="stat-item fade-in">
            <div class="text-4xl font-bold mb-2">10+</div>
            <div class="text-lg">年行业经验</div>
          </div>
        </div>
      </div>
    </section> -->

    <!-- CTA Section -->
    <!-- <section class="cta py-16 bg-gray-100">
      <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-gray-800 mb-4 fade-in">
          准备开始您的项目？
        </h2>
        <p class="text-lg text-gray-600 mb-8 max-w-2xl mx-auto fade-in">
          立即联系我们，获取专业的技术咨询和解决方案
        </p>
        <div class="fade-in">
          <router-link
            to="/contact"
            class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors duration-200 inline-block"
          >
            联系我们
          </router-link>
        </div>
      </div>
    </section> -->
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'
import { Loading } from '@element-plus/icons-vue'

// 加载状态
const loading = ref(true)

// 轮播图数据
const carouselItems = ref([])

// 品牌库数据
const brandData = ref({
  title: '品牌库',
  introduction: '汇聚全球优质品牌资源，为您提供一站式品牌解决方案',
  pictureUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
})

// 方案库数据
const solutionData = ref({
  title: '方案库',
  introduction: '为不同领域客户量身定制专业解决方案',
  pictureUrl: 'https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80'
})

// 默认轮播图数据
const defaultCarouselItems = [
  {
    title: '智聚联云，引领未来',
    introduction: '专注于为客户提供高质量的技术产品和解决方案',
    pictureUrl: 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'
  },
  {
    title: '创新技术，卓越品质',
    introduction: '以技术创新为驱动，为客户创造更大价值',
    pictureUrl: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'
  },
  {
    title: '专业服务，值得信赖',
    introduction: '经验丰富的团队，为您提供全方位的技术支持',
    pictureUrl: 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80'
  }
]

// 获取平台图片数据
const fetchPlatformPictures = async () => {
  try {
    loading.value = true
    const response = await axios.get('/api/vt-platform/platformPicture/list')
    const data = response.data.data || []

    // 轮播图数据 (type = 0)
    const carouselData = data.filter(item => item.type === 0)
    if (carouselData.length > 0) {
      carouselItems.value = carouselData.map(item => ({
        title: item.title || '智聚联云',
        introduction: item.introduction || '专注于为客户提供高质量的技术产品和解决方案',
        pictureUrl: item.pictureUrl
      }))
    } else {
      carouselItems.value = defaultCarouselItems
    }

    // 品牌库数据 (type = 1)
    const brandItem = data.find(item => item.type === 1)
    if (brandItem) {
      brandData.value = {
        title: brandItem.title || '品牌库',
        introduction: brandItem.introduction || '汇聚全球优质品牌资源，为您提供一站式品牌解决方案',
        pictureUrl: brandItem.pictureUrl || brandData.value.pictureUrl
      }
    }

    // 方案库数据 (type = 2)
    const solutionItem = data.find(item => item.type === 2)
    if (solutionItem) {
      solutionData.value = {
        title: solutionItem.title || '方案库',
        introduction: solutionItem.introduction || '为不同领域客户量身定制专业解决方案',
        pictureUrl: solutionItem.pictureUrl || solutionData.value.pictureUrl
      }
    }

  } catch (error) {
    console.error('获取平台图片数据失败:', error)
    // 使用默认数据
    carouselItems.value = defaultCarouselItems
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  // 获取平台图片数据
  await fetchPlatformPictures()

  // 添加滚动动画
  const observerOptions = {
    threshold: 0.1,
    rootMargin: '0px 0px -50px 0px'
  }

  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('visible')
      }
    })
  }, observerOptions)

  document.querySelectorAll('.fade-in').forEach(el => {
    observer.observe(el)
  })
})
</script>

<style scoped>
/* 首页容器 */
.home {
  overflow-x: hidden; /* 防止水平滚动条 */
}

/* 轮播图样式 */
.hero-carousel {
  position: relative;
  overflow: hidden;
  margin-top: 0; /* 顶到页面顶部 */
  height: 50vh; /* 占屏幕一半高度 */
  width: 100vw; /* 确保全宽 */
  margin-left: calc(-50vw + 50%); /* 突破容器限制，实现全宽 */
}

.carousel-container {
  width: 100%;
  height: 100%;
}

/* Element Plus 轮播图容器样式重写 */
:deep(.el-carousel) {
  height: 100% !important;
  overflow: hidden !important;
}

:deep(.el-carousel__container) {
  height: 100% !important;
}

.carousel-loading {
  width: 100%;
  height: 50vh;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.carousel-slide {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.carousel-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.6) 0%, rgba(0, 0, 0, 0.3) 100%);
  display: flex;
  align-items: center;
}

/* 滑入动画 */
.slide-in-left {
  opacity: 0;
  transform: translateX(-50px);
  animation: slideInLeft 0.8s ease-out forwards;
}

.slide-in-left.delay-200 {
  animation-delay: 0.2s;
}

.slide-in-left.delay-400 {
  animation-delay: 0.4s;
}

@keyframes slideInLeft {
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 淡入动画 */
.fade-in {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
  opacity: 1;
  transform: translateY(0);
}

/* 统计数据动画 */
.stat-item {
  transition: transform 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .hero-carousel {
    height: 50vh;
    margin-top: 0;
  }

  .carousel-container {
    height: 100%;
  }

  .carousel-loading {
    height: 50vh;
  }

  .carousel-slide h1 {
    font-size: 2rem;
  }

  .carousel-slide p {
    font-size: 1.125rem;
  }
}

/* Element Plus 轮播图指示器自定义样式 */
:deep(.el-carousel__indicator) {
  background-color: rgba(255, 255, 255, 0.3);
}

:deep(.el-carousel__indicator.is-active) {
  background-color: rgba(255, 255, 255, 0.8);
}

:deep(.el-carousel__arrow) {
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
}

:deep(.el-carousel__arrow:hover) {
  background-color: rgba(0, 0, 0, 0.5);
}
</style>