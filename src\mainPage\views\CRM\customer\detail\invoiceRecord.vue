<template>
  <div>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      :table-loading="loading"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    ></avue-crud>
  </div>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance ,onMounted} from 'vue';
import { useRoute } from 'vue-router';
let route = useRoute();
const props =defineProps(['type','id'])
let option = ref({
  labelWidth:140,
  // height:'auto',
  // calcHeight:30,
  addBtn : props.type == 0 || props.type == 2,
  editBtn : props.type == 0 || props.type == 2,
  delBtn : props.type == 0 || props.type == 2,
  border:true,
  column: [
    {
      label: '客户公司名称',
      prop: 'invoiceCompanyName',
      rules: [{ required: true, message: '请输入开票公司名称', trigger: 'blur' }],
    },
    {
      label: '纳税人识别号',
      prop: 'ratepayerIdentifyNumber',
      rules: [{ required: true, message: '请输入纳税人识别号', trigger: 'blur' }],
    },
    {
      label: '开户银行',
      prop: 'bankName',
      rules: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
    },
    {
      label: '银行账号',
      prop: 'bankAccount',
      rules: [{ required: true, message: '请输入银行账号', trigger: 'blur' }],
    },
    {
      label: '注册地址',
      prop: 'bankAddress',
    },
    {
      label: '电话',
      prop: 'phone',
    },
    {
      label: '期初末开票余额(元)',
      prop: 'endTermNoInvoiceAmount',
      type:'number'
    },
    
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});
let tableData = ref([]);
let { proxy } = getCurrentInstance();

let loading = ref(false);
watchEffect(() => {
  if (props.id) {
    onLoad();
  }
},{
  flush: 'post'
})

function onLoad() {
  loading.value = true;
    const { pageSize:size ,currentPage:current} = page.value
  axios
    .get('/api/vt-admin/customerInvoiceInfo/page', {
      params: {
        customerId: props.id,
        size ,
        current
      },
    })
    .then(res => {
      loading.value = false;
        tableData.value = res.data.data.records
        page.value.total = res.data.data.total
    });
}
function rowSave(form, done, loading) {
  const data = {
    ...form,
    customerId: props.id,
  };
  axios
    .post('/api/vt-admin/customerInvoiceInfo/save', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done()
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row,index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post('/api/vt-admin/customerInvoiceInfo/update', data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done()
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
     
      axios.post('/api/vt-admin/customerInvoiceInfo/remove?ids=' + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
</script>

<style lang="scss" scoped></style>
