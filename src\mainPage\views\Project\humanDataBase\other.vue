<template>
  <basic-container style="height: 100%">
    <el-radio-group style="margin-bottom: 10px" v-model="form.userType" size="normal">
      <el-radio-button :label="1">公司</el-radio-button>
      <el-radio-button :label="0">个人</el-radio-button>
    </el-radio-group>
    <el-row :gutter="20" style="height: calc(100% - 50px)">
      <el-col :span="4">
        <div class="box" style="height: 650px">
          <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" />
        </div>
      </el-col>
      <el-col :span="20"> 
      <otherSelf ref="otherSelfRef" :form="form" v-if="form.userType == 0"></otherSelf>
      <otherCompany ref="otherCompanyRef" :form="form" v-if="form.userType == 1"></otherCompany>
      </el-col>
    </el-row>

    <dialogForm ref="dialogForm"></dialogForm>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import { ref, getCurrentInstance, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { getLazyTree, getDetail, submit, remove } from '@/api/base/region';
import otherSelf from './otherSelf.vue'
import otherCompany from './otherCompany.vue'

let form = ref({
  userType: 1,
});






const { proxy} = getCurrentInstance() 
let treeData = ref([]);
let treeOption = ref({
  nodeKey: 'id',
  lazy: true,
  treeLoad: function (node, resolve) {
    const parentCode = node.level === 0 ? '00' : node.data.id;
    getLazyTree(parentCode).then(res => {
      resolve(
        res.data.data.map(item => {
          return {
            ...item,
            leaf: !item.hasChildren,
          };
        })
      );
    });
  },
  addBtn: false,
  menu: false,
  size: 'small',
  props: {
    labelText: '标题',
    label: 'title',
    value: 'value',
    children: 'children',
  },
});
onMounted(() => {
  initTree();
});
function initTree() {
  treeData.value = [];
  getLazyTree(this.topCode).then(res => {
    treeData.value = res.data.data.map(item => {
      return {
        ...item,
        leaf: !item.hasChildren,
      };
    });
  });
}
function nodeClick(node,data) {
  form.value.province = null
  form.value.city =null
  form.value.area = null
  if(node.id.length == 6){
    form.value.area = node.id
    form.value.city = `${node.id[0]}${node.id[1]}${node.id[2]}${node.id[3]}`
    form.value.province = `${node.id[0]}${node.id[1]}`
  }else if(node.id.length == 4){
    form.value.city = node.id
    form.value.province = `${node.id[0]}${node.id[1]}`
  }else{
    form.value.province = node.id
  }
  console.log(form.value);
  if(form.value.userType == 0){
    proxy.$refs.otherSelfRef.onLoad()
  }else{
    proxy.$refs.otherCompanyRef.onLoad()
  }
}
</script>

<style lang="scss" scoped></style>
