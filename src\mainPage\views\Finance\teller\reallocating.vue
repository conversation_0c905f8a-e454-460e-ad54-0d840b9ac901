<template>
  <basic-container>
    <avue-crud
      :option="option"
      :data="tableData"
      v-model:page="page"
      v-model:search="params"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @row-save="rowSave"
      :table-loading="loading"
      ref="crud"
      @keyup.enter="onLoad"
      @row-del="rowDel"
      @search-reset="reset"
      @search-change="searchChange"
      @refresh-change="onLoad"
      @current-change="onLoad"
      @size-change="onLoad"
      v-model="form"
    >
      <template #fileIds="{ row }">
        <File :fileList="row.attachList"></File>
      </template>
      <template #menu="{ row }">
        <el-button type="primary" text icon="view" @click="handleView(row)">明细</el-button>
      </template>
    </avue-crud>
    <dialogForm ref="dialogForm"></dialogForm>
    <el-drawer v-model="drawer" title="调拨明细" size="90%">
      <accountOutAndIn :logicIds="currentRow.id"></accountOutAndIn>
    </el-drawer>
  </basic-container>
</template>

<script setup>
import axios from 'axios';
import moment from 'moment';
import { ref, getCurrentInstance, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useStore } from 'vuex';
import accountOutAndIn from './accountOutAndIn.vue';
const store = useStore();
const userInfo = computed(() => store.getters.userInfo);

let option = ref({
  height: 'auto',
  align: 'center',
  addBtn: true,
  editBtn: false,
  delBtn: true,
  calcHeight: 10,
  searchMenuSpan: 4,
  searchSpan: 5,
  menuWidth: 160,
  border: true,
  column: [
    {
      type: 'select',
      label: '支出银行',
      
      search: true,
      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        return res.data.records;
      },
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      cascader: [],
      span: 12,

      // search: true,
      display: true,

      rules: [
        {
          required: true,
          message: '请选择付款账号',
          trigger: 'blur',
        },
        {
          validator: (rule, value, callback) => {
            if (value === form.value.inAccountId) {
              callback(new Error('不能和收入银行相同'));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ],
      prop: 'outAccountId',
    },
    {
      type: 'select',
      label: '收入银行',
      dicUrl: '/api/vt-admin/companyAccount/page?size=1000',
      dicFormatter: res => {
        console.log(res);

        return res.data.records;
      },
      search: true,
      props: {
        label: 'abbreviation',
        value: 'id',
        desc: 'desc',
      },
      cascader: [],
      span: 12,
      // search: true,
      display: true,

      rules: [
        {
          required: true,
          message: '请选择付款账号',
          trigger: 'blur',
        },
        {
          validator: (rule, value, callback) => {
            if (value === form.value.outAccountId) {
              callback(new Error('不能和支出银行相同'));
            } else {
              callback();
            }
          },
          trigger: 'change',
        },
      ],
      prop: 'inAccountId',
    },

    {
      label: '调拨金额',
      prop: 'amount',
      type: 'number',
      width: 110,
      rules: [
        {
          required: true,
          message: '请输入调拨金额',
          trigger: 'blur',
        },
      ],
    },

    {
      label: '调拨时间',
      prop: 'createTime',
      value: moment().format('YYYY-MM-DD'),
      searchSpan: 6,
      type: 'date',
      readonly: true,
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      width: 110,
    },

    {
      label: '操作人',
      prop: 'createUserName',
      readonly: true,
      width: 100,
      value: userInfo.value.real_name,
    },
    {
      label: '调拨附件',
      prop: 'fileIds',
      type: 'upload',
      overHidden: true,
      dataType: 'object',
      loadText: '附件上传中，请稍等',
      span: 12,
      slot: true,
      overHidden: true,
      // align: 'center',
      propsHttp: {
        res: 'data',
        url: 'id',
        name: 'originalName',
        // home: 'https://www.w3school.com.cn',
      },
      action: '/blade-resource/attach/upload',
    },
    {
      label: '备注',
      prop: 'remark',
      type: 'textarea',
      span: 24,
    },
  ],
});
let form = ref({});
let page = ref({
  pageSize: 10,
  currentPage: 1,
  total: 0,
});

const addUrl = '/api/vt-admin/companyAccountTurnover/save';
const delUrl = '/vt-admin/companyAccountTurnover/remove?id=';
const updateUrl = '';
const tableUrl = '/api/vt-admin/companyAccountTurnover/page';
let params = ref({});
let tableData = ref([]);
let { proxy } = getCurrentInstance();
let route = useRoute();
let loading = ref(false);
function onLoad() {
  loading.value = true;
  const { pageSize: size, currentPage: current } = page.value;
  axios
    .get(tableUrl, {
      params: {
        size,
        current,
        ...params.value,
      },
    })
    .then(res => {
      loading.value = false;
      tableData.value = res.data.data.records;
      page.value.total = res.data.data.total;
    });
}
let router = useRouter();

function rowSave(form, done, loading) {
  const data = {
    ...form,
    createTime: null,
    fileIds: form.fileIds && form.fileIds.map(item => item.value).join(','),
  };
  axios
    .post(addUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowUpdate(row, index, done, loading) {
  const data = {
    ...row,
  };
  axios
    .post(updateUrl, data)
    .then(res => {
      if (res.data.code == 200) {
        proxy.$message.success(res.data.msg);
        onLoad();
        done();
      }
    })
    .catch(err => {
      done();
    });
}
function rowDel(form) {
  proxy
    .$confirm('此操作将永久删除该数据, 是否继续?', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    })
    .then(() => {
      console.log(222);
      axios.post(delUrl + form.id).then(res => {
        proxy.$message({
          type: 'success',
          message: '删除成功',
        });
        onLoad();
      });
    })
    .catch(() => {});
}
function reset() {
  onLoad();
}
function searchChange(params, done) {
  onLoad();
  done();
}
let currentRow = ref('');
let drawer = ref(false);
function handleView(row) {
  currentRow.value = row;
  drawer.value = true;
}
</script>

<style lang="scss" scoped></style>
